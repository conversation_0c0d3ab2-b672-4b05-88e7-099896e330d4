{"visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659119400152, "to.datetime": 1659205799152, "duration": 86399}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "asc"}}}, "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "d8da995e-a57a-4295-b588-ff408b34bb2e", "session-id": "ebac6958-543c-47c0-af8c-82839c53aa3e", "user.name": "admin", "query.id": 2110467927537, "visualization.data.sources": {"visualization.result.by": ["event.source"], "type": "health", "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "engine.type", "operator": "in", "value": ["metric.poll", "metric.create"]}]}]}, "result.filter": {}}, "data.points": [{"data.point": "total.events", "aggregator": "sum", "entity.type": "all", "entities": {"10.20.40.140": "500014-health.metric", "10.20.40.141": "500014-health.metric", "10.20.40.142": "500014-health.metric", "10.20.40.143": "500014-health.metric", "10.20.40.144": "500014-health.metric"}, "plugins": ["500014-health.metric"]}], "plugins": ["500014-health.metric"], "entities": {"10.20.40.140": "500014-health.metric", "10.20.40.141": "500014-health.metric", "10.20.40.142": "500014-health.metric", "10.20.40.143": "500014-health.metric", "10.20.40.144": "500014-health.metric"}}, "admin.role": "yes", "sub.query.id": 2110467927538}