{"visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659292200617, "to.datetime": 1659378599617, "duration": 86399}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": []}}, "visualization.result.by": ["interface"], "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "7b04220d-ccdf-46da-aea8-f230e85964dc", "session-id": "a0b81062-80a4-47cd-8bd3-4a95d0c83145", "user.name": "admin", "visualization.data.sources": {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor", "interface"], "data.points": [{"data.point": "interface~in.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {"1^x^interface~in.packets": "2000-interface", "1^y^interface~in.packets": "2000-interface", "1^z^interface~in.packets": "2000-interface", "1^a^interface~in.packets": "2000-interface", "1^w^interface~in.packets": "2000-interface", "2^x^interface~in.packets": "2000-interface", "2^y^interface~in.packets": "2000-interface", "2^z^interface~in.packets": "2000-interface", "2^a^interface~in.packets": "2000-interface", "2^w^interface~in.packets": "2000-interface", "3^x^interface~in.packets": "2000-interface", "3^y^interface~in.packets": "2000-interface", "3^z^interface~in.packets": "2000-interface", "3^a^interface~in.packets": "2000-interface", "3^w^interface~in.packets": "2000-interface", "4^x^interface~in.packets": "2000-interface", "4^y^interface~in.packets": "2000-interface", "4^z^interface~in.packets": "2000-interface", "4^a^interface~in.packets": "2000-interface", "4^w^interface~in.packets": "2000-interface", "5^x^interface~in.packets": "2000-interface", "5^y^interface~in.packets": "2000-interface", "5^z^interface~in.packets": "2000-interface", "5^a^interface~in.packets": "2000-interface", "5^w^interface~in.packets": "2000-interface"}, "plugins": ["2000-interface"]}, {"data.point": "interface~out.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {"1^x^interface~out.packets": "2000-interface", "1^y^interface~out.packets": "2000-interface", "1^z^interface~out.packets": "2000-interface", "1^a^interface~out.packets": "2000-interface", "1^w^interface~out.packets": "2000-interface", "2^x^interface~out.packets": "2000-interface", "2^y^interface~out.packets": "2000-interface", "2^z^interface~out.packets": "2000-interface", "2^a^interface~out.packets": "2000-interface", "2^w^interface~out.packets": "2000-interface", "3^x^interface~out.packets": "2000-interface", "3^y^interface~out.packets": "2000-interface", "3^z^interface~out.packets": "2000-interface", "3^a^interface~out.packets": "2000-interface", "3^w^interface~out.packets": "2000-interface", "4^x^interface~out.packets": "2000-interface", "4^y^interface~out.packets": "2000-interface", "4^z^interface~out.packets": "2000-interface", "4^a^interface~out.packets": "2000-interface", "4^w^interface~out.packets": "2000-interface", "5^x^interface~out.packets": "2000-interface", "5^y^interface~out.packets": "2000-interface", "5^z^interface~out.packets": "2000-interface", "5^a^interface~out.packets": "2000-interface", "5^w^interface~out.packets": "2000-interface"}, "plugins": ["2000-interface"]}], "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "status": [], "instance.type": "interface", "plugins": ["2000-interface"]}, "admin.role": "yes", "query.id": 74205653047123, "sub.query.id": 74205653047124}