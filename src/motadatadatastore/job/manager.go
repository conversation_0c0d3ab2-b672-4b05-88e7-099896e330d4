/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-06-2024           Vedant Dokania         MOTADATA-4876  Changing the flow default views
* 2025-02-10			 <PERSON><PERSON><PERSON>-4913  Altered modulo operator with new modulo function
* 2025-03-05			 <PERSON><PERSON><PERSON>-5190  Migrated constants from datastore to utils to match SonarQube Standard
* 2025-04-09			 <PERSON><PERSON><PERSON>-4859  Refactoring "fmt.Sprintf" to update go version to 1.24
* 2025-06-03			 <PERSON><PERSON><PERSON>-6393  Updated With Master Branch
* 2025-06-10			 <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>-6392  manager view qualifying algorithm optimised.
* 2025-06-23             Dhaval Bera            MOTADATA-6642  Added comments and memory aligned the struct

 */

package job

import (
	"encoding/json"
	"fmt"
	"math"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/datastore/query"
	"motadatadatastore/utils"
	"os"
	"reflect"
	"runtime"
	"strings"
	"time"
)

/*

	Event Aggregation Note -> Support for probing past stores and background job for periodically aggregating the newly
	written data in event raw stores.

	Metric Aggregation Note -> Support for only probing past stores and aggregating single tick aggregation for handing
	errors occurred in online metric aggregator.

*/

/*
event-aggregations.ctx

aggregation context stored and updated for any changes.
contains default and user aggregations created after creating any widget in the application
contents :- indexable columns, group type, aggregation columns, plugin

event-aggregations.meta

aggregation metadata contains bitmap to track the aggregation progress.
bitmap contains the bit on for the corresponding pending aggregation for a single tick
*/

var (
	// managerLogger provides structured logging for the job manager component
	// All manager operations are logged with "Job Manager" prefix and "job" category
	managerLogger = utils.NewLogger("Job Manager", "job")

	// flowExplorerViews contains predefined flow explorer widget IDs that require special handling.
	// These widgets need additional indexable columns (SourceIFIndex, DestinationIFIndex) for
	// runtime filtering capabilities. The map acts as a set for O(1) lookup performance.
	//
	// Widget ID format: "plugin-flow-widgetId"
	// These are specific flow visualization widgets that support interface-based filtering
	flowExplorerViews = utils.MotadataMap{
		"500000-flow-10000000100024": struct{}{}, // Flow explorer widget 24
		"500000-flow-10000000100025": struct{}{}, // Flow explorer widget 25
		"500000-flow-10000000100026": struct{}{}, // Flow explorer widget 26
		"500000-flow-10000000100027": struct{}{}, // Flow explorer widget 27
		"500000-flow-10000000100017": struct{}{}, // Flow explorer widget 17
		"500000-flow-10000000100018": struct{}{}, // Flow explorer widget 18
		"500000-flow-10000000100019": struct{}{}, // Flow explorer widget 19
		"500000-flow-10000000100020": struct{}{}, // Flow explorer widget 20
		"500000-flow-10000000100039": struct{}{}, // Flow explorer widget 39
		"500000-flow-10000000100040": struct{}{}, // Flow explorer widget 40
	}
)

// flushOpsType defines the types of flush operations that can be performed
// to persist different categories of aggregation data to disk
type flushOpsType int

// dummyMaxScore is used as a sentinel value to indicate that an aggregation
// cannot be unified with existing aggregations due to resource constraints
const dummyMaxScore = math.MaxInt8

const (
	// AggregationContext represents flushing of complete horizontal aggregation configurations
	// This includes all plugin mappings, indexable columns, and aggregation metadata
	AggregationContext flushOpsType = iota

	// ExcludedAggregation represents flushing of excluded aggregation mappings
	// This tracks aggregations that should not be processed during cleanup operations
	ExcludedAggregation
)

const (
	// ExcludedAggregations is the filename for storing excluded aggregation mappings
	// Format: aggregation_name -> plugin_name
	ExcludedAggregations = "excluded-aggregations.json"

	// Aggregation is the key used in notification maps to identify aggregation names
	Aggregation = "aggregation"

	// currentAggregations is the key used in probe acknowledgements to identify
	// aggregations that were replaced during the probing process
	currentAggregations = "current.aggregations"
)

// Manager is the central coordinator for all background aggregation jobs in the system.
// It manages the lifecycle of aggregation contexts, coordinates data probing, and handles
// synchronization between raw data ingestion and aggregated data generation.
//
// The Manager operates as a state machine that processes various types of notifications:
// - Widget creation/deletion from the UI layer
// - Data recovery requests during system startup
// - Aggregation probe completions and synchronization acknowledgements
// - Raw data flush notifications from the writer components
//
// Memory layout is optimized for 64-bit systems with fields ordered by size to minimize padding.
type Manager struct {
	// === 8-byte aligned fields (pointers, slices, maps, channels) ===

	// tokenizer provides string tokenization functionality used throughout manager operations.
	// It maintains a reusable token buffer to avoid repeated memory allocations during
	// string parsing operations (e.g., splitting aggregation keys, plugin names).
	tokenizer *utils.Tokenizer

	// cleanupTimer triggers periodic cleanup of excluded aggregations that are no longer
	// referenced by any active probes. Runs every 30 minutes to prevent memory leaks.
	cleanupTimer *time.Ticker

	// qualifiedProbes tracks aggregation configurations that are currently in the probing phase.
	// Structure: plugin -> view_name -> aggregation_context
	// Probing analyzes data patterns to determine optimal aggregation strategies.
	// These contexts are not persisted until probing completes successfully.
	qualifiedProbes map[string]map[string]utils.MotadataMap

	// pendingPositions tracks aggregation work that needs to be completed.
	// Structure: aggregation_key -> position_bitmap -> reference_count
	// The aggregation_key format: "plugin^base-tick^interval^format"
	// Position bitmap represents time slots that need aggregation.
	// Reference count tracks how many times the same position was requested.
	pendingPositions map[string]map[uint32]int

	// horizontalAggregations stores the active aggregation configurations organized by plugin.
	// Structure: plugin -> aggregation_name -> aggregation_context
	// These are persisted aggregations that have completed the probing phase and are
	// actively being used for data aggregation.
	horizontalAggregations map[string]map[string]utils.MotadataMap

	// runningProbes tracks which aggregation views are currently being probed to prevent
	// duplicate probe operations. Structure: view_name -> empty_struct
	// Used as a set data structure for O(1) lookup performance.
	runningProbes map[string]struct{}

	// pendingPlugins prevents duplicate job notifications for the same aggregation context.
	// Structure: aggregation_key -> empty_struct
	// Acts as a semaphore to ensure only one aggregation job runs per context at a time.
	pendingPlugins map[string]struct{}

	// indices tracks the next available index number for creating new aggregation views
	// within each plugin. Structure: plugin_name -> next_index
	// Ensures unique naming of aggregation views: "plugin-index"
	indices map[string]int

	// excludedHorizontalAggregations maps aggregations that should be excluded from processing
	// back to their originating plugins. Structure: aggregation_name -> plugin_name
	// Used during cleanup operations to remove unused aggregation configurations.
	excludedHorizontalAggregations map[string]string

	// === 1-byte aligned fields (booleans) ===

	// ShutdownNotifications receives shutdown signals from external components.
	// Buffered channel (capacity: 5) to prevent blocking during shutdown sequences.
	ShutdownNotifications chan bool

	// shutdown indicates whether the manager should stop processing and exit.
	// Set to true when a shutdown notification is received via ShutdownNotifications channel.
	shutdown bool
}

// NewManager creates and initializes a new Manager instance with all required data structures.
// This constructor performs the following initialization steps:
//
// 1. Loads existing aggregation configurations from persistent storage
// 2. Initializes all internal data structures with appropriate capacities
// 3. Sets up cleanup timer for periodic maintenance operations
// 4. Creates communication channels for shutdown coordination
//
// The Manager is returned in a ready-to-start state but requires calling Start() to begin processing.
func NewManager() *Manager {
	// Load existing aggregation configurations, indices, and exclusions from disk
	// This ensures continuity across system restarts and maintains aggregation state
	aggregations, indices, excludedAggregations := populateHorizontalAggregations()

	return &Manager{
		// Load pending aggregation contexts from persistent storage
		// These represent work that was interrupted during previous shutdown
		pendingPositions: datastore.GetAggregationContexts(),

		// Initialize pending plugins map with reasonable capacity (100 entries)
		// This prevents duplicate job execution for the same aggregation context
		pendingPlugins: make(map[string]struct{}, 100),

		// Set up index tracking for generating unique aggregation view names
		indices: indices,

		// Load active horizontal aggregation configurations
		horizontalAggregations: aggregations,

		// Load aggregations that should be excluded from processing
		excludedHorizontalAggregations: excludedAggregations,

		// Initialize empty qualified probes map for tracking ongoing probe operations
		qualifiedProbes: map[string]map[string]utils.MotadataMap{},

		// Initialize running probes set with reasonable capacity (100 entries)
		// Used to prevent concurrent probing of the same aggregation view
		runningProbes: make(map[string]struct{}, 100),

		// Set up cleanup timer to run every 30 minutes
		// Removes excluded aggregations that are no longer referenced
		cleanupTimer: time.NewTicker(time.Minute * 30),

		// Create buffered shutdown notification channel (capacity: 5)
		// Buffering prevents blocking during shutdown coordination
		ShutdownNotifications: make(chan bool, 5),
	}
}

// Start initializes the Manager and begins processing aggregation jobs in a separate goroutine.
// This method performs the following operations:
//
// 1. Initializes the tokenizer for string parsing operations
// 2. Starts the main processing loop in a background goroutine
// 3. Registers with the global shutdown coordination mechanism
//
// The method returns immediately after starting the background processing.
// The Manager will continue running until shutdown is requested via ShutdownNotifications channel.
func (manager *Manager) Start() {
	// Initialize the tokenizer with a pre-allocated token buffer
	// This avoids repeated memory allocations during string parsing operations
	manager.tokenizer = &utils.Tokenizer{
		Tokens: make([]string, utils.TokenizerLength),
	}

	// Start the main processing loop in a separate goroutine
	go func() {
		// Register this goroutine with the global shutdown coordination mechanism
		// This ensures proper cleanup during system shutdown
		utils.JobEngineShutdownMutex.Add(1)

		// Main processing loop - continues until shutdown is requested
		for {
			// Check for shutdown conditions:
			// - Local shutdown flag set via ShutdownNotifications
			// - Global shutdown flag set by system-wide shutdown process
			if manager.shutdown || utils.GlobalShutdown {
				break
			}

			// Execute the main processing logic
			// This method handles all notification processing and job coordination
			manager.run()
		}

		// Signal completion to the global shutdown coordination mechanism
		// This allows the system to wait for all job engines to complete before exiting
		utils.JobEngineShutdownMutex.Done()
	}()
}

// run is the main execution loop for the job manager.
//
// This method continuously listens for notifications from various components of the system
// and coordinates the execution of aggregation jobs. It handles widget creation/deletion,
// data recovery, aggregation probing, synchronization, and raw data flushing.
//
// The job manager acts as a central coordinator for all background jobs, ensuring that:
// - Aggregation jobs are scheduled and executed at appropriate times
// - Data consistency is maintained during aggregation operations
// - System resources are used efficiently by coordinating job execution
// - Recovery from failures is handled gracefully
func (manager *Manager) run() {
	// Create a timer for triggering background aggregation jobs
	// This timer ensures regular execution of aggregation tasks
	timer := time.NewTicker(time.Second * time.Duration(utils.GetHorizontalAggregationTimerSeconds()))

	// Set up panic recovery to ensure the job manager can recover from unexpected errors
	// This is critical for system stability as the job manager is a central component
	defer func() {
		if err := recover(); err != nil {
			// Stop the timer to prevent further triggers during recovery
			timer.Stop()

			// Capture and log the stack trace for debugging
			stackTraceBytes := make([]byte, 1<<20)
			managerLogger.Error(fmt.Sprintf("error %v occurred", err))
			managerLogger.Error(fmt.Sprintf("!!!STACK TRACE for job manager !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			// Log that the manager is restarting
			// The manager will be restarted by the caller (Start method)
			managerLogger.Error("restarting job manager...")
		}
	}()

	// NOTIFICATION HANDLING OVERVIEW
	//
	// The job manager processes several types of notifications from different system components:
	//
	// 1. Widget Creation (WidgetCreate):
	//    - Received when a new visualization widget is created in the UI
	//    - Triggers creation of new aggregation configurations
	//    - May initiate probing of data to determine optimal aggregation strategy
	//
	// 2. Data Recovery (Recover):
	//    - Received during system startup or after a failure
	//    - Initiates recovery of aggregated data that might have been lost
	//    - Ensures data consistency after system interruptions
	//
	// 3. Aggregation Probing (Probe):
	//    - Received when an aggregation job has completed probing data
	//    - Probing analyzes data patterns to determine optimal aggregation strategy
	//    - After probing, aggregation contexts are persisted for future use
	//
	// 4. Aggregation Synchronization (Sync):
	//    - Received when an aggregation job has completed writing aggregated data
	//    - Updates metadata to reflect the new aggregated data
	//    - Removes completed contexts from the pending list
	//
	// 5. Raw Data Flush (Flush):
	//    - Received when raw data has been written to storage
	//    - Triggers aggregation of the newly written data
	//    - Ensures aggregated data stays current with raw data
	//
	// 6. Widget Deletion (WidgetDelete):
	//    - Received when a visualization widget is deleted from the UI
	//    - Removes associated aggregation configurations
	//    - May trigger cleanup of unused aggregated data
	//
	// Note: Probing contexts are not persisted until probing is finished.
	// This prevents inconsistent states if the system shuts down during probing.

	// Main event loop
	for {
		select {
		// Process notifications from various system components
		// These notifications drive the job manager's actions
		case request := <-utils.ManagerNotifications:

			manager.processNotifications(request)

		// Handle periodic cleanup timer events
		// This timer fires every 30 minutes to clean up unused aggregation configurations
		case <-manager.cleanupTimer.C:
			// Iterate through all excluded aggregations to check if they can be cleaned up
			for aggregation, plugin := range manager.excludedHorizontalAggregations {
				// Skip cleanup if the plugin still has active probes running
				// This prevents removal of aggregations that might still be needed
				if _, ok := manager.qualifiedProbes[plugin]; ok {
					continue
				}

				// Remove the aggregation from the datastore
				// This cleans up any persistent storage associated with the aggregation
				datastore.RemoveHorizontalAggregation(plugin, aggregation)

				// Remove the aggregation from the in-memory horizontal aggregations map
				delete(manager.horizontalAggregations[plugin], aggregation)

				// Remove the aggregation from the excluded aggregations map
				delete(manager.excludedHorizontalAggregations, aggregation)

				// Persist the updated excluded aggregations to disk
				// This ensures the cleanup is maintained across system restarts
				manager.flush(ExcludedAggregation)
			}

		// Handle aggregation job timer events
		// This timer triggers periodic processing of pending aggregation jobs
		case <-timer.C:
			// Process all pending aggregation jobs and send notifications to workers
			// This ensures that aggregation work doesn't accumulate indefinitely
			manager.sendPendingJobNotifications()

		// Handle shutdown notification events
		// This allows for graceful shutdown of the manager
		case <-manager.ShutdownNotifications:
			managerLogger.Info("shutting down...")

			// Set the shutdown flag to stop the main processing loop
			manager.shutdown = true

			// Exit the run method and ultimately the goroutine
			return

		}

	}

}

// populateHorizontalAggregations loads aggregation configurations from persistent storage
// and reconstructs the Manager's internal data structures during system startup.
//
// This function performs the following operations:
// 1. Creates the job directory if it doesn't exist
// 2. Loads horizontal aggregation configurations from disk
// 3. Loads excluded aggregation mappings from disk
// 4. Reconstructs index mappings for generating unique aggregation view names
//
// Returns:
// - aggregations: plugin -> aggregation_name -> aggregation_context mapping
// - indices: plugin -> next_available_index mapping for view name generation
// - excludedAggregations: aggregation_name -> plugin mapping for cleanup operations
func populateHorizontalAggregations() (aggregations map[string]map[string]utils.MotadataMap, indices map[string]int, excludedAggregations map[string]string) {
	// Initialize return value data structures
	aggregations = map[string]map[string]utils.MotadataMap{}
	excludedAggregations = map[string]string{}
	indices = map[string]int{}

	// Ensure the job directory exists for storing aggregation metadata
	_, err := os.Stat(utils.JobDir)
	if os.IsNotExist(err) {
		// Create the directory with appropriate permissions (755 = rwxr-xr-x)
		_ = os.Mkdir(utils.JobDir, 0755)
	}

	// Load horizontal aggregation configurations from persistent storage
	bytes, err := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.HorizontalAggregations)
	if len(bytes) > 0 {
		// Unmarshal the JSON data into the aggregations map
		// Ignore errors as we can continue with empty aggregations if file is corrupted
		_ = json.Unmarshal(bytes, &aggregations)
	}

	// Load excluded aggregation mappings from persistent storage
	bytes, _ = os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + ExcludedAggregations)
	if len(bytes) > 0 {
		// Unmarshal the JSON data into the excluded aggregations map
		_ = json.Unmarshal(bytes, &excludedAggregations)
	}

	// Reconstruct index mappings for generating unique aggregation view names
	// This ensures that new aggregation views get unique names that don't conflict with existing ones
	for plugin := range aggregations {
		valid := false

		// Process each aggregation view for the current plugin
		for view := range aggregations[plugin] {
			// Check if this is a filtered view (contains filters) or a qualified probe view (contains part separator)
			// These views have different naming conventions and index tracking requirements
			if _, ok := aggregations[plugin][view][utils.Filters]; ok || strings.Contains(view, utils.PartSeparator) {
				// Parse the view name to extract the index for qualified probe views
				// Format: "plugin-widget_id^part^index"
				tokens := strings.Split(view, utils.PartSeparator)
				if len(tokens) >= 1 {
					// Extract the index from the second token
					index := codec.StringToINT(tokens[1])

					// Store the index for this plugin-widget combination
					// tokens[0] format: "plugin" + utils.AggregationSeparator + "widgetId"
					indices[tokens[0]] = index
				}

				// Skip further processing for this view as it's a special case
				continue
			}

			// Mark this plugin as having valid (non-filtered) aggregation views
			valid = true

			// Extract the index from the standard aggregation view name
			// Format: "plugin" + utils.AggregationSeparator + "index"
			index := codec.StringToINT(strings.Split(view, utils.AggregationSeparator)[1])

			// Initialize the index for this plugin if not already set
			if _, ok := indices[plugin]; !ok {
				indices[plugin] = index
				continue
			}

			// Update the index to the highest value seen for this plugin
			// This ensures new views get unique indices
			if indices[plugin] < index {
				indices[plugin] = index
			}
		}

		// Increment the index for plugins with valid aggregation views
		// This prepares the next available index for new view creation
		if valid {
			indices[plugin]++
		}
	}

	return
}

// flush persists aggregation-related data structures to disk for durability across system restarts.
// This function handles different types of aggregation data that need to be persisted:
//
//  1. AggregationContext: Complete horizontal aggregation configurations
//     Structure: plugin -> (aggregation -> {indexableColumns(map), aggregationColumns(dataType), type(dataStoreType)})
//
//  2. ExcludedAggregation: Mappings of excluded aggregations to their plugins
//     Structure: aggregation_name -> plugin_name
//
// The function uses atomic write operations (write to temp file, then rename) to ensure data consistency.
func (manager *Manager) flush(opsType flushOpsType) {
	switch opsType {
	case AggregationContext:
		// Serialize horizontal aggregation configurations to JSON with indentation for readability
		bytes, err := json.MarshalIndent(manager.horizontalAggregations, "", " ")
		if err != nil {
			managerLogger.Error("failed to sync aggregation contexts")
		}

		// Remove any existing temporary file to ensure clean state
		_ = os.Remove(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.Temp + utils.HorizontalAggregations)

		// Write to temporary file first to ensure atomic operation
		if err = os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.Temp+utils.HorizontalAggregations, bytes, 0644); err != nil {
			managerLogger.Error(fmt.Sprintf("failed to update horizontal aggregation context, reason : %s", err.Error()))
		} else {
			// Atomically replace the original file with the temporary file
			// This ensures that the file is never in a partially written state
			if err = os.Rename(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.Temp+utils.HorizontalAggregations, utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.HorizontalAggregations); err != nil {
				managerLogger.Error(fmt.Sprintf("failed to overwrite horizontal aggregation context, reason : %s", err.Error()))
			}
		}

	case ExcludedAggregation:
		// Serialize excluded horizontal aggregations to JSON with indentation
		bytes, err := json.MarshalIndent(manager.excludedHorizontalAggregations, "", " ")
		if err != nil {
			managerLogger.Error("failed to sync aggregation contexts")
		}

		// Write excluded aggregations directly (no atomic operation needed for this smaller file)
		_ = os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+ExcludedAggregations, bytes, 0644)
	}
}

// updateHorizontalAggregations processes new aggregation requests and determines which existing
// aggregations are affected by the changes. This is the first phase of aggregation management.
//
// The function performs the following operations:
// 1. Sorts incoming aggregations by plugin for organized processing
// 2. Attempts to unify new aggregations with existing ones to optimize resource usage
// 3. Identifies which existing aggregations need to be updated or recreated
//
// Parameters:
// - currentAggregations: Map of new aggregation requests from widget creation
//
// Returns:
// - updatedAggregations: Map of plugin -> affected_aggregation_names that need probing
func (manager *Manager) updateHorizontalAggregations(currentAggregations utils.MotadataMap) (updatedAggregations map[string]map[string]struct{}) {
	// Log the incoming request for debugging purposes
	if utils.DebugEnabled() {
		bytes, _ := json.Marshal(currentAggregations)
		managerLogger.Debug(fmt.Sprintf("update phase 1 request received : %v", string(bytes)))
	}

	// Sort and validate aggregations by plugin, filtering out invalid configurations
	assortedAggregations := manager.assortAggregations(currentAggregations)

	// Initialize the result map to track affected aggregations
	updatedAggregations = map[string]map[string]struct{}{}

	// Process each plugin's aggregations to determine unification strategy
	for plugin, aggregations := range assortedAggregations {
		// Attempt to unify new aggregations with existing ones
		// This optimizes resource usage by combining compatible aggregations
		affectedAggregations := manager.unifyAggregations(plugin, aggregations)

		// Only include plugins that have aggregations requiring updates
		if len(affectedAggregations) > 0 {
			updatedAggregations[plugin] = affectedAggregations
		}
	}

	// Log the final result for debugging purposes
	if utils.DebugEnabled() {
		managerLogger.Debug(fmt.Sprintf("affected aggregations against qualified aggregations %v", updatedAggregations))
	}

	return updatedAggregations
}

func (manager *Manager) assortAggregations(currentAggregations utils.MotadataMap) (aggregations map[string]map[string]utils.MotadataMap) {

	aggregations = make(map[string]map[string]utils.MotadataMap)

	var widgetId string

	var fulltextSearchingView bool

	for aggregation := range currentAggregations {

		fulltextSearchingView = false

		valid := true

		context := currentAggregations.GetMapValue(aggregation)

		filters := context.GetMapValue(utils.Filters)

		if len(filters) > 0 {

			delete(context.GetMapValue(utils.Filters), query.ResultFilter)

			dataFilter := filters.GetMapValue(utils.DataFilter)

			for _, group := range dataFilter.GetMapListValue("groups") {

				for _, condition := range group.GetMapListValue("conditions") {

					if condition.GetStringValue("operand") == utils.Message {

						fulltextSearchingView = true

						break

					}

				}

			}
		}

		if fulltextSearchingView {

			utils.Split(aggregation, utils.HyphenSeparator, manager.tokenizer)

			widgetId = manager.tokenizer.Tokens[manager.tokenizer.Counts-1]

		} else {

			context.Delete(utils.Filters)
		}

		if utils.EnvironmentType != utils.DatastoreTestEnvironment && !fulltextSearchingView && strings.Contains(aggregation, datastore.EventSearchPlugin) {

			currentAggregations.Delete(aggregation)

			if utils.DebugEnabled() {

				managerLogger.Debug(fmt.Sprintf("discarding the view %v from aggregation of log search event ", aggregation))
			}

			continue
		}

		indexableColumns := context.GetSliceValue(utils.IndexableColumns)

		delete(context, utils.IndexableColumns)

		context[utils.IndexableColumns] = utils.MotadataMap{}

		for key := range context {

			if key != utils.Type && datastore.IsSearchableColumn(key) && !fulltextSearchingView {

				if utils.DebugEnabled() {

					managerLogger.Debug(fmt.Sprintf("aggregation %v has searchable column %v in it hence ignoring the request", aggregation, key))
				}

				valid = false

				break
			}

		}

		if !valid {

			continue
		}

		for _, column := range indexableColumns {

			if datastore.IsSearchableColumn(column.(string)) {

				if !fulltextSearchingView {

					if utils.DebugEnabled() {

						managerLogger.Debug(fmt.Sprintf("aggregation %v has searchable column %v in it hence ignoring the request", aggregation, column))
					}

					valid = false

					break

				} else {

					continue

				}
			}

			context[utils.IndexableColumns].(utils.MotadataMap)[column.(string)] = struct{}{}

		}

		if !valid {

			continue
		}

		storeType := utils.DatastoreType(context.GetIntValue(utils.Type))

		//we need to add the eventsource column
		if storeType != utils.MetricPolicy && storeType != utils.EventPolicy {

			context.GetMapValue(utils.IndexableColumns)[utils.EventSource] = struct{}{}

		}

		/*
			Improvement id : MOTADATA-2642
			discard widget which contains more than 8 indexable columns
		*/

		if storeType == utils.Flow {

			context[utils.VolumeBytes] = float64(datastore.IntegerColumn)

			/*
				flow explorer widgets with possible source if index and destination if index filters at runtime.
				These columns were previously not added to grouping causing the widgets to be served from raw table
				hence, after adding these columns to the particular widgets the existing views would be discarded,
				and probed again and new views would be formed (1-time probing).
			*/
			if _, ok := flowExplorerViews[aggregation]; ok {

				context.GetMapValue(utils.IndexableColumns)[utils.SourceIFIndex] = struct{}{}

				context.GetMapValue(utils.IndexableColumns)[utils.DestinationIFIndex] = struct{}{}

			}

		}

		if len(context.GetMapValue(utils.IndexableColumns)) > utils.AggregationViewIndexColumnLimit {

			continue
		}

		utils.Split(aggregation, utils.HyphenSeparator, manager.tokenizer)

		plugin := strings.Join(manager.tokenizer.Tokens[:manager.tokenizer.Counts-1], utils.HyphenSeparator)

		if _, ok := aggregations[plugin]; !ok {

			aggregations[plugin] = map[string]utils.MotadataMap{}
		}

		aggregations[plugin][aggregation] = context

		if fulltextSearchingView {

			valid = false

			for existingAggregation, existingContext := range manager.horizontalAggregations[plugin] {

				valid = false

				if strings.Contains(existingAggregation, widgetId) {

					//compare filters first

					if reflect.DeepEqual(existingContext.GetMapValue(utils.Filters), filters) {

						valid = true

						for key := range context {

							if key == utils.Type || key == utils.Filters {

								continue
							}

							if key == utils.IndexableColumns {

								existingColumns := existingContext.GetMapValue(utils.IndexableColumns).GetMapKeys()

								columns := context.GetMapValue(utils.IndexableColumns).GetMapKeys()

								utils.SortStringValues(existingColumns)

								utils.SortStringValues(columns)

								if !reflect.DeepEqual(existingColumns, columns) {

									valid = false

									break
								}

								continue

							}

							if !(existingContext.Contains(key) && context.Contains(key) && existingContext[key] == context[key]) {

								valid = false

								break

							}

						}

					}

					if valid {

						//means view is same so no need to populate this view

						if utils.DebugEnabled() {

							managerLogger.Debug(fmt.Sprintf("%v aggreation already exists for the plugin %v , hence discarding the request", aggregation, plugin))
						}

						delete(aggregations[plugin], aggregation)

						break

					} else {

						if utils.DebugEnabled() {

							managerLogger.Debug(fmt.Sprintf("%v aggreation is discarded as widget is edited for plugin %v , hence discarding the request", aggregation, plugin))
						}
						//means view is edited so delete the view
						manager.deleteAggregations(plugin, widgetId)

					}

				}
			}

			if !valid {

				view := plugin + utils.AggregationSeparator + widgetId

				if _, ok := manager.indices[view]; !ok {

					manager.indices[view] = 0

				}

				manager.indices[view] += 1

				if manager.qualifiedProbes[plugin] == nil {

					manager.qualifiedProbes[plugin] = map[string]utils.MotadataMap{}
				}

				manager.qualifiedProbes[plugin][view+utils.PartSeparator+codec.INTToStringValue(manager.indices[view])] = context

				if utils.DebugEnabled() {

					managerLogger.Debug(fmt.Sprintf("sending %v view for probing", view))

				}

				delete(aggregations[plugin], aggregation)

			}

		}

	}

	return aggregations

}

// deleteAggregations removes all aggregation configurations associated with a specific widget.
// This function is called when a widget is deleted from the UI and cleans up all related data.
//
// The function performs the following operations:
// 1. Finds and removes aggregations that contain the specified widget ID
// 2. Cleans up the plugin's aggregation map if it becomes empty
// 3. Removes pending plugin jobs related to the deleted widget
// 4. Removes the aggregation from persistent storage
// 5. Cleans up pending position tracking for the deleted aggregation
//
// Parameters:
// - plugin: The plugin name that owns the aggregations
// - widgetId: The widget ID whose aggregations should be deleted
func (manager *Manager) deleteAggregations(plugin, widgetId string) {
	// Only proceed if the plugin has horizontal aggregations
	if datastore.IsHorizontalAggregationFound(plugin) {
		// Search for aggregations that belong to the specified widget
		for aggregation := range manager.horizontalAggregations[plugin] {
			// Check if this aggregation belongs to the widget being deleted
			if strings.Contains(aggregation, widgetId) {
				// Remove the aggregation from the in-memory map
				delete(manager.horizontalAggregations[plugin], aggregation)

				// If this was the last aggregation for the plugin, clean up the plugin entry
				if len(manager.horizontalAggregations[plugin]) == 0 {
					delete(manager.horizontalAggregations, plugin)

					// Clean up any pending plugin jobs related to this widget
					for key := range manager.pendingPlugins {
						if strings.Contains(key, widgetId) {
							delete(manager.pendingPlugins, key)
						}
					}
				}

				// Remove the aggregation from persistent storage
				datastore.RemoveHorizontalAggregation(plugin, aggregation)

				// Clean up pending position tracking for this aggregation
				for key := range manager.pendingPositions {
					if strings.Contains(key, aggregation) {
						delete(manager.pendingPositions, key)
					}
				}

				// Break after finding and deleting the first matching aggregation
				// Each widget should only have one aggregation per plugin
				break
			}
		}
	}
}

// === MANAGER NOTIFICATION FUNCTIONS ===
// These functions handle communication between the Manager and aggregation job workers

// notifyHorizontalAggregationProbes sends probe notifications to aggregation job workers
// for horizontal (time-series) aggregations that need data analysis.
//
// The function performs the following operations:
// 1. Iterates through all qualified probes that need to be executed
// 2. Skips probes that are already running to prevent duplicates
// 3. Distributes probe jobs across available aggregation workers using round-robin
// 4. Marks probes as running to prevent concurrent execution
//
// Parameters:
// - aggregations: Map of plugin -> affected_aggregation_names for context
func (manager *Manager) notifyHorizontalAggregationProbes(aggregations map[string]map[string]struct{}) {
	// Use round-robin distribution across aggregation job workers
	aggregationJobId := 0

	// Process all qualified probes that need to be executed
	for plugin := range manager.qualifiedProbes {
		for view, context := range manager.qualifiedProbes[plugin] {
			// Skip probes that are already running to prevent duplicate execution
			if _, ok := manager.runningProbes[view]; ok {
				continue
			}

			// Mark this probe as running to prevent concurrent execution
			manager.runningProbes[view] = struct{}{}

			// Send probe notification to the next available aggregation job worker
			utils.AggregationJobWriteNotifications[aggregationJobId] <- utils.MotadataMap{
				utils.DatastoreFormat: utils.HorizontalFormat,
				utils.Plugin:          plugin,
				utils.OperationType:   utils.Probe,
				utils.EventContext:    context,
				currentAggregations:   aggregations[plugin],
				Aggregation:           view,
			}

			// Move to the next aggregation job worker (round-robin distribution)
			aggregationJobId++
			if aggregationJobId == utils.AggregationJobs {
				aggregationJobId = 0
			}
		}
	}
}

// notifyVerticalAggregationProbes sends probe notifications to aggregation job workers
// for vertical (entity-centric) aggregations that need data analysis.
//
// Vertical aggregations are simpler than horizontal ones as they aggregate data by entity
// rather than by time. Each column in the aggregation context becomes a separate probe job.
//
// The function performs the following operations:
// 1. Validates each aggregation context to ensure it contains aggregatable data
// 2. Creates separate probe jobs for each column in the context
// 3. Distributes jobs using hash-based routing for consistent worker assignment
//
// Parameters:
// - aggregations: Map of plugin -> aggregation_context for vertical aggregations
func (manager *Manager) notifyVerticalAggregationProbes(aggregations utils.MotadataMap) {
	// Process each plugin's vertical aggregation request
	for plugin := range aggregations {
		// Extract the aggregation context for this plugin
		context := aggregations.GetMapValue(plugin)

		// Remove filters as they're not needed for vertical aggregations
		delete(context, utils.Filters)

		// Validate that the context contains aggregatable data
		if valid := manager.validateVerticalAggregation(context); valid {
			// Extract the datastore type for this aggregation
			datastoreType := context.GetIntValue(utils.Type)

			// Create a separate probe job for each column in the context
			for column, datatype := range context {
				// Skip the type field as it's metadata, not a column to aggregate
				if column == utils.Type {
					continue
				}

				// Send probe notification using hash-based routing for consistent worker assignment
				// This ensures that the same plugin always goes to the same worker for better cache locality
				utils.AggregationJobWriteNotifications[utils.GetFastModN(utils.GetHash64([]byte(plugin)), utils.AggregationJobs)] <- utils.MotadataMap{
					utils.DatastoreFormat: utils.VerticalFormat,
					utils.Plugin:          plugin,
					utils.EventContext: utils.MotadataMap{
						utils.Type: datastoreType,
						column:     datatype,
					},
					utils.OperationType: utils.Probe,
				}
			}
		}
	}
}

// validateVerticalAggregation checks if a vertical aggregation context contains valid aggregatable data.
// This function performs cleanup and validation to ensure only meaningful aggregations are created.
//
// The function performs the following operations:
// 1. Handles shadow counter conversion to original counters
// 2. Removes metrics that are already being aggregated to prevent duplicates
// 3. Validates that the context still contains aggregatable data after cleanup
//
// Parameters:
// - context: The aggregation context to validate and clean up
//
// Returns:
// - bool: true if the context contains valid aggregatable data, false otherwise
func (manager *Manager) validateVerticalAggregation(context utils.MotadataMap) bool {
	var shadowCounter string

	// Check for duplicate aggregation requests and clean up the context
	// This prevents creation of redundant aggregations for already aggregated metrics
	for key := range context {
		// Handle shadow counters by converting them to original counter names
		// Shadow counters are temporary placeholders that need to be converted
		if datastore.IsShadowCounter(key) {
			shadowCounter = key
			key = datastore.FlipShadowCounter(key)
		}

		// Remove metrics that are already being aggregated to prevent duplicates
		// Skip the Type field as it's metadata, not a metric to aggregate
		if key != utils.Type && datastore.IsAggregationMetric(key) {
			// Remove both the shadow counter and the original metric
			delete(context, shadowCounter)
			delete(context, key)
		}
	}

	// Return true only if the context still contains aggregatable data after cleanup
	return context.IsNotEmpty()
}

// processProbeAcknowledgement handles notifications from aggregation jobs indicating that
// data probing has completed successfully. This finalizes the aggregation configuration.
//
// The function performs the following operations:
// 1. Validates that this is a horizontal aggregation (vertical ones don't need this processing)
// 2. Moves the aggregation from qualified probes to active horizontal aggregations
// 3. Handles exclusion of conflicting aggregations that were replaced
// 4. Cleans up probe tracking data structures
// 5. Persists the updated aggregation configurations
//
// Parameters:
// - dataStoreFormat: The format of the datastore (horizontal/vertical)
// - request: The probe acknowledgement containing aggregation context and metadata
func (manager *Manager) processProbeAcknowledgement(dataStoreFormat string, request utils.MotadataMap) {
	// Only process horizontal format aggregations
	// Vertical aggregations don't require this acknowledgement processing
	if dataStoreFormat == utils.VerticalFormat {
		return
	}

	// Extract the plugin name from the request
	plugin := request.GetStringValue(utils.Plugin)

	// Initialize the horizontal aggregations map for this plugin if it doesn't exist
	if _, ok := manager.horizontalAggregations[plugin]; !ok {
		manager.horizontalAggregations[plugin] = map[string]utils.MotadataMap{}
	}

	// Extract the finalized aggregation context from the probe results
	context := request.GetMapValue(utils.EventContext)

	// Handle exclusion of aggregations that were replaced during the probing process
	if request.Contains(currentAggregations) {
		// Mark replaced aggregations as excluded to prevent conflicts
		for aggregation := range request[currentAggregations].(map[string]struct{}) {
			manager.excludedHorizontalAggregations[aggregation] = plugin
		}

		// Remove the current aggregations from the request as they're now processed
		delete(request, currentAggregations)

		// Persist the updated excluded aggregations list
		manager.flush(ExcludedAggregation)
	}

	// Move the aggregation from qualified probes to active horizontal aggregations
	manager.horizontalAggregations[plugin][request.GetStringValue(Aggregation)] = context

	// Clean up the qualified probes tracking
	delete(manager.qualifiedProbes[plugin], request.GetStringValue(Aggregation))

	// Clean up the running probes tracking
	delete(manager.runningProbes, request.GetStringValue(Aggregation))

	// Remove the plugin from qualified probes if no more probes are pending
	if len(manager.qualifiedProbes[plugin]) == 0 {
		delete(manager.qualifiedProbes, plugin)
	}

	// Persist the updated horizontal aggregation configurations
	manager.flush(AggregationContext)
}

func (manager *Manager) processAggregationAcknowledgement(dataStoreFormat string, request utils.MotadataMap) {

	//received from the aggregation job notifying the aggregation for a particular tick is complete

	dataStoreFormat = request.GetStringValue(utils.DatastoreFormat)

	tick := request.GetInt64Value(utils.Tick)

	interval := request.GetStringValue(utils.Interval)

	plugin := request.GetStringValue(utils.Plugin)

	key := utils.Empty

	if dataStoreFormat == utils.VerticalFormat {

		for column := range request.GetMapValue(utils.EventContext) {

			if column == utils.Type {

				continue
			}

			key = plugin + utils.AggregationSeparator + column + utils.KeySeparator + utils.INT64ToStringValue(utils.GetBaseTickv1(tick)) + utils.KeySeparator + interval + utils.KeySeparator + dataStoreFormat

			break
		}

	} else {

		key = request.GetStringValue(Aggregation) + utils.KeySeparator + utils.INT64ToStringValue(utils.GetBaseTickv1(tick)) + utils.KeySeparator + interval + utils.KeySeparator + dataStoreFormat
	}

	position := utils.GetPosition(tick, codec.StringToINT(interval))

	//while the aggregation job was aggregating the tick it can happen that the writer
	//might send the flush notification for the same tick again which means that data for same tick
	//is updated. So, do not remove the bitmap index as we need to re-aggregate it.
	if _, ok := manager.pendingPositions[key]; ok {

		manager.pendingPositions[key][position] = manager.pendingPositions[key][position] - request.GetIntValue(utils.Position)

		if manager.pendingPositions[key][position] <= 0 {

			delete(manager.pendingPositions[key], position)

			datastore.UpdateAggregationContexts(key, position, utils.Remove)

		}

		if len(manager.pendingPositions[key]) == 0 {

			delete(manager.pendingPositions, key)

		}

	}

	//this is used to prevent duplicate triggering of the job for same tick.
	// if the job was aggregating and the timer re-triggers we need to prevent
	//from notifying the duplicate request as the same plugin aggregation is in progress
	delete(manager.pendingPlugins, key)
}

func (manager *Manager) processRawDataFlushNotification(dataStoreFormat string, request utils.MotadataMap) {

	//received from writer notifying the data has been flushed for a particular tick

	plugin := request.GetStringValue(utils.Plugin)

	var key string

	if request.Contains(utils.Tick) && len(plugin) > 0 {

		tick := request.GetInt64Value(utils.Tick) //resolving unix tick

		for aggregation, aggregations := range manager.horizontalAggregations[plugin] {

			if aggregations.Contains(utils.Filters) {

				for _, interval := range utils.FullTextViewAggregationIntervals { //setting the bitmap index for the particular tick

					tick = utils.RoundOffUnixSeconds(tick, interval)

					key = aggregation + utils.KeySeparator + utils.INT64ToStringValue(utils.GetBaseTickv1(tick)) + utils.KeySeparator + codec.INTToStringValue(interval) + utils.KeySeparator + dataStoreFormat

					position := utils.GetPosition(tick, interval)

					datastore.UpdateAggregationContexts(key, position, utils.Add)

					if _, ok := manager.pendingPositions[key]; !ok {

						manager.pendingPositions[key] = map[uint32]int{}
					}

					manager.pendingPositions[key][position] = manager.pendingPositions[key][position] + 1

				}

			}

		}
	}
}

func (manager *Manager) processDataRecoveryNotification(dataStoreFormat string, request utils.MotadataMap) {

	plugin := request.GetStringValue(utils.Plugin)

	//received from aggregator notifying there was panic in particular tick hence re-aggregate same tick

	// Metric aggregator and event aggregator sends tick using seconds to unix
	tick := request.GetInt64Value(utils.Tick) //resolving unix tick

	interval := request.GetIntValue(utils.Interval)

	eventContext := request.GetMapValue(utils.EventContext)

	key := utils.Empty

	/*
		key -> plugin^base-tick^interval^format
			update the bitmap index for the particular tick

			update pending position data structure so that if we are here to update the same bitmap index it will not get
			un-set on completion of already running aggregation for the same tick
	*/
	if dataStoreFormat == utils.VerticalFormat {

		//only single metric is there in case of recovery

		for column := range eventContext {

			if column == utils.Type {

				continue
			}

			key = plugin + utils.AggregationSeparator + column + utils.KeySeparator + utils.INT64ToStringValue(utils.GetBaseTickv1(tick)) + utils.KeySeparator + codec.INTToStringValue(interval) + utils.KeySeparator + dataStoreFormat

			break

		}

	} else {

		key = plugin + utils.KeySeparator + utils.INT64ToStringValue(utils.GetBaseTickv1(tick)) + utils.KeySeparator + codec.INTToStringValue(interval) + utils.KeySeparator + dataStoreFormat

	}

	position := utils.GetPosition(tick, interval)

	datastore.UpdateAggregationContexts(key, position, utils.Add)

	if _, ok := manager.pendingPositions[key]; !ok {

		manager.pendingPositions[key] = map[uint32]int{}
	}

	manager.pendingPositions[key][position] = manager.pendingPositions[key][position] + 1

	//for vertical format send directly to the aggregation not wait for timer
	if dataStoreFormat == utils.VerticalFormat {

		notifyAggregationJob(utils.GetFastModN(utils.GetHash64([]byte(plugin+codec.INT64ToStringValue(tick))), utils.AggregationJobs), utils.MotadataMap{

			utils.DatastoreFormat: dataStoreFormat,

			utils.Plugin: plugin,

			utils.Tick: request.GetInt64Value(utils.Tick),

			utils.EventContext: request.GetMapValue(utils.EventContext),

			utils.Interval: interval,

			utils.OperationType: utils.Sync,

			utils.Position: manager.pendingPositions[key][position],
		})

	}
}

func (manager *Manager) sendPendingJobNotifications() {

	for key, pendingPositions := range manager.pendingPositions {

		utils.Split(key, utils.KeySeparator, manager.tokenizer)

		aggregation := manager.tokenizer.Tokens[0]

		if _, ok := manager.pendingPlugins[key]; ok {

			//this is to avoid re-calling the method for the same timer triggered twice due to any delay
			continue
		}

		dataStoreFormat := manager.tokenizer.Tokens[manager.tokenizer.Counts-1]

		//no need to send for vertical format as vertical format data is send directly during recovery
		if dataStoreFormat == utils.VerticalFormat {

			continue
		}

		tick := codec.StringToINT64(manager.tokenizer.Tokens[1])

		interval := codec.StringToINT(manager.tokenizer.Tokens[2])

		manager.pendingPlugins[key] = struct{}{}

		utils.Split(aggregation, utils.AggregationSeparator, manager.tokenizer)

		for position := range pendingPositions {

			//HASH According to tokens[0] + tokens[1] (aggregation + tick)

			roundedTick := tick + (int64(position) * int64(interval) * 60)

			notifyAggregationJob(utils.GetFastModN(utils.GetHash64([]byte(aggregation+codec.INT64ToStringValue(roundedTick))), utils.AggregationJobs), utils.MotadataMap{

				utils.DatastoreFormat: dataStoreFormat,

				utils.Plugin: manager.tokenizer.Tokens[0],

				Aggregation: aggregation,

				utils.Interval: interval,

				utils.Tick: roundedTick,

				utils.OperationType: utils.Sync,

				utils.Position: pendingPositions[position],

				utils.EventContext: manager.horizontalAggregations[manager.tokenizer.Tokens[0]][aggregation].DeepClone(),
			})

		}

	}

}

func notifyAggregationJob(id int, context utils.MotadataMap) {

	utils.AggregationJobWriteNotifications[id] <- context
}

func (manager *Manager) unifyAggregations(plugin string, aggregations map[string]utils.MotadataMap) map[string]struct{} {

	newAggregations := map[string]utils.MotadataMap{}

	affectedAggregations := map[string]struct{}{}

	assignedIds := map[string]struct{}{}

	for view, context := range manager.horizontalAggregations[plugin] {

		if _, excluded := manager.excludedHorizontalAggregations[view]; excluded {

			continue
		}

		newAggregations[view] = context.DeepClone()
	}

	for _, context := range aggregations {

		placed := false

		if manager.exist(plugin, context) || manager.running(plugin, context) {

			continue
		}

		minScore := utils.AggregationViewAggregationColumnLimit + utils.AggregationViewIndexColumnLimit

		qualifiedView := utils.Empty

		for view, currentContext := range newAggregations {

			//check if the aggregation is already affected

			//check if the aggregation view can be adjusted to accommodate new widget
			if score, fit := manager.qualified(context, currentContext); fit && score < minScore {

				minScore = score

				qualifiedView = view

				if manager.horizontalAggregations[plugin] != nil && manager.horizontalAggregations[plugin][view] != nil {

					affectedAggregations[view] = struct{}{}
				}
			}

		}

		if _, exists := affectedAggregations[qualifiedView]; exists || qualifiedView != utils.Empty {

			//create new merged aggregation view
			addWidget(context, nil, newAggregations[qualifiedView])

			placed = true

		}

		//if the widget cannot be adjusted to any of the previously created view create a new view
		if !placed {

			viewName := plugin + utils.AggregationSeparator + codec.INTToStringValue(manager.indices[plugin])

			newAggregations[viewName] = context

			assignedIds[viewName] = struct{}{}

			manager.indices[plugin]++
		}

	}

	for view, aggregation := range newAggregations {

		if manager.horizontalAggregations[plugin] != nil && manager.horizontalAggregations[plugin][view] != nil {

			if _, exists := affectedAggregations[view]; !exists {

				continue
			}
		}

		utils.Split(view, utils.AggregationSeparator, manager.tokenizer)

		if manager.qualifiedProbes[plugin] == nil {

			manager.qualifiedProbes[plugin] = map[string]utils.MotadataMap{}
		}

		if _, assigned := assignedIds[view]; assigned {

			manager.qualifiedProbes[plugin][view] = aggregation

		} else {

			manager.qualifiedProbes[plugin][manager.tokenizer.Tokens[0]+utils.AggregationSeparator+codec.INTToStringValue(manager.indices[plugin])] = aggregation

			manager.indices[plugin]++
		}
	}

	return affectedAggregations

}

func addWidget(context utils.MotadataMap, aggregationView utils.MotadataMap, result utils.MotadataMap) {

	for k, v := range context {

		if k == utils.IndexableColumns {

			continue
		}

		result[k] = v
	}

	for k, v := range context.GetMapValue(utils.IndexableColumns) {

		result.GetMapValue(utils.IndexableColumns)[k] = v
	}

	if aggregationView != nil {

		for k, v := range aggregationView {

			if k == utils.IndexableColumns {

				continue
			}

			result[k] = v
		}

		for k, v := range aggregationView.GetMapValue(utils.IndexableColumns) {

			result.GetMapValue(utils.IndexableColumns)[k] = v
		}
	}

}

func (manager *Manager) qualified(context utils.MotadataMap, aggregationView utils.MotadataMap) (int, bool) {

	newAggregations := 0

	for key := range context {

		if key == utils.Type || key == utils.IndexableColumns {

			continue
		}

		if _, exists := aggregationView[key]; !exists {

			newAggregations++
		}

	}

	if len(aggregationView)-2+newAggregations > utils.AggregationViewAggregationColumnLimit {

		return dummyMaxScore, false
	}

	newGroups := 0

	for key := range context.GetMapValue(utils.IndexableColumns) {

		if _, exists := aggregationView.GetMapValue(utils.IndexableColumns)[key]; !exists {

			newGroups++
		}
	}

	if len(aggregationView.GetMapValue(utils.IndexableColumns))+newGroups > utils.AggregationViewIndexColumnLimit {

		return dummyMaxScore, false
	}

	return newGroups + newAggregations, true

}

func (manager *Manager) running(plugin string, context utils.MotadataMap) bool {

	running := true

	for _, currentContext := range manager.qualifiedProbes[plugin] {

		running = true

		columns := currentContext.GetMapValue(utils.IndexableColumns)

		for column := range context.GetMapValue(utils.IndexableColumns) {

			if !columns.Contains(column) {

				running = false

				break
			}
		}

		if !running {

			continue
		}

		for key, value := range context {

			if !(key == utils.Type || key == utils.IndexableColumns) {

				if currentValue, ok := currentContext[key]; !ok || value != currentValue {

					running = false

					break
				}
			}

		}

		if running {

			return true
		}

	}

	return false
}

func (manager *Manager) exist(plugin string, context utils.MotadataMap) bool {

	exist := false

	for _, currentContext := range manager.horizontalAggregations[plugin] {

		columns := currentContext.GetMapValue(utils.IndexableColumns)

		exist = true

		for column := range context.GetMapValue(utils.IndexableColumns) {

			if !columns.Contains(column) {

				exist = false

				break
			}
		}

		if !exist {

			continue
		}

		for key, value := range context {

			if !(key == utils.Type || key == utils.IndexableColumns) {

				if currentValue, ok := currentContext[key]; !ok || value != currentValue {

					exist = false

					break
				}
			}

		}

		if exist {

			return true
		}

	}

	return exist

}

func (manager *Manager) processNotifications(request utils.MotadataMap) {

	defer func() {

		if r := recover(); r != nil {

			managerLogger.Error(fmt.Sprintf("err %v occurred while processing notification", r))
		}

	}()

	// Extract the operation type and datastore format from the request
	// These determine how the request will be processed
	operationType := request.GetIntValue(utils.OperationType)
	dataStoreFormat := request.GetStringValue(utils.DatastoreFormat)

	// Process the request based on its operation type
	switch operationType {
	// Handle widget creation notifications
	// These come from the UI when a user creates a new visualization
	case utils.WidgetCreate:
		// Remove metadata fields from the request
		// This leaves only the widget configuration data
		delete(request, utils.DatastoreFormat)
		delete(request, utils.OperationType)

		// Process the widget creation based on the datastore format
		if dataStoreFormat == utils.HorizontalFormat {
			// For horizontal format (time-series data):
			// 1. Update horizontal aggregation configurations
			// 2. Notify aggregation probes about the new configurations
			// This initiates the process of analyzing data for efficient aggregation
			manager.notifyHorizontalAggregationProbes(manager.updateHorizontalAggregations(request))
		} else {
			// For vertical format (entity-centric data):
			// Notify vertical aggregation probes directly
			// Vertical aggregations have simpler configuration requirements
			manager.notifyVerticalAggregationProbes(request)
		}

	// Handle data recovery notifications
	// These occur during system startup or after failures
	case utils.Recover:
		// Process the recovery notification
		// This ensures that any lost aggregated data is regenerated
		manager.processDataRecoveryNotification(dataStoreFormat, request)

	// Handle aggregation probe acknowledgements
	// These indicate that a probe has completed analyzing data
	case utils.Probe:
		// Process the probe acknowledgement
		// This finalizes the aggregation configuration based on probe results
		manager.processProbeAcknowledgement(dataStoreFormat, request)

	// Handle aggregation synchronization notifications
	// These indicate that aggregated data has been written
	case utils.Sync:
		// Process the synchronization acknowledgement
		// This updates metadata and removes completed contexts
		manager.processAggregationAcknowledgement(dataStoreFormat, request)

	// Handle raw data flush notifications
	// These indicate that new raw data is available for aggregation
	case utils.Flush:
		// Process the flush notification
		// This triggers aggregation of the newly written data
		manager.processRawDataFlushNotification(dataStoreFormat, request)

	// Handle widget deletion notifications
	// These come from the UI when a user deletes a visualization
	case utils.WidgetDelete:
		// Remove metadata fields from the request
		delete(request, utils.OperationType)
		delete(request, utils.DatastoreFormat)

		// Process each plugin in the request
		// Each plugin may have different aggregation configurations to delete
		for plugin := range request {
			// Delete the aggregations for the specified plugin and widget ID
			// This removes unused aggregation configurations
			manager.deleteAggregations(plugin, request.GetStringValue(plugin))
		}
	}
}

func (manager *Manager) cleanup() {

	for aggregation, plugin := range manager.excludedHorizontalAggregations {

		if _, ok := manager.qualifiedProbes[plugin]; ok {

			continue
		}

		datastore.RemoveHorizontalAggregation(plugin, aggregation)

		delete(manager.horizontalAggregations[plugin], aggregation)

		delete(manager.excludedHorizontalAggregations, aggregation)

		manager.flush(ExcludedAggregation)
	}
}
