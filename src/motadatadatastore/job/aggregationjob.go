/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-04             Swapnil <PERSON>. Dave       	MOTADATA-4885  Visualization Time Range Conditions Removed.
* 2025-03-05			 A<PERSON>l Shah			Motadata-5190  Added HenceSkipping constant and Migrated constants from datastore to utils according to SonarQube standard
* 2025-03-21			 Dhaval Bera			Motadata-5452  Changed Column from "object.id" to "netroute.id" for NetRoute Status Metric Plugin
* 2025-04-02			 <PERSON>haval <PERSON>ra			<PERSON>-4859  Added NetRoute Status Metric Aggregation Datastore Type
* 2025-04-09			 <PERSON><PERSON><PERSON>ra			<PERSON>-4859  Refactoring "fmt.Sprintf" to update go version to 1.24
* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-10             Dhaval Bera            MOTADATA-6100  Added Unmap function to release memory-mapped resources
* 2025-06-04             Aashil Shah            MOTADATA-5780 Called custom MmapAnonymous and Munmap functions
* 2025-06-23             Dhaval Bera            MOTADATA-6642  Added comments and memory aligned the struct

 */

/*
Package job provides aggregation job functionality for the Motadata datastore system.

AGGREGATION JOB OVERVIEW

Aggregation jobs are specialized workers that handle two critical functions in the data processing pipeline:

1. DATA PROBING (Historical Analysis):
   - Triggered when new widgets are created in the UI
   - Analyzes historical data across all past datastores
   - Determines optimal aggregation strategies based on data patterns
   - Generates aggregation configurations for efficient future queries
   - Ensures new widgets can display historical data immediately

2. SPECIFIC TICK AGGREGATION (Recovery & Backfill):
   - Handles aggregation for specific time intervals when needed
   - Triggered when metric/event aggregators encounter errors
   - Provides data recovery capabilities for failed aggregation operations
   - Ensures data consistency across the entire time series

OPERATIONAL FLOW

When a new widget is created:
1. The widget creation triggers a probe operation
2. Aggregation job scans historical datastores
3. Optimal aggregation strategy is determined
4. Aggregated data is generated and stored
5. Future metric/event aggregators use the established pattern

For ongoing operations:
1. Metric/event aggregators handle real-time aggregation
2. If errors occur, aggregation jobs provide recovery
3. Specific tick aggregation ensures no data loss
4. System maintains data consistency and availability

PERFORMANCE CONSIDERATIONS

- Memory-mapped buffers for high-performance I/O
- Reusable data structures to minimize allocations
- Concurrent processing with proper synchronization
- Efficient encoding/decoding using memory pools
- Optimized sorting algorithms for aggregated results
*/

package job

import (
	bytes2 "bytes"
	"encoding/binary"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/kelindar/bitmap"
	"github.com/tidwall/gjson"
	"math"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/datastore/query"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"runtime"
	"sort"
	"strings"
	"sync"
	"time"
)

const (
	// visualizationQuery contains a template JSON query structure used for aggregation operations.
	// This template defines the standard format for visualization queries including:
	// - Timeline specifications (from/to datetime)
	// - Visualization category and data sources
	// - Data points with aggregators (sum, count, min, max)
	// - Entity types and plugin configurations
	//
	// The template is parsed during AggregationJob initialization to create a reusable query context.
	visualizationQuery = "{\n  \"visualization.timeline\": {\n    \"from.datetime\": \"2022/07/15 12:00:00\",\n    \"to.datetime\": \"2022/07/15 11:59:59\"\n  },\n  \"visualization.category\": \"Grid\",\n  \"visualization.data.sources\": {\n    \"type\": \"metric\",\n    \"filters\": {\n      \"data.filter\": {},\n      \"result.filter\": {}\n    },\n    \"data.points\": [\n      {\n        \"data.point\": \"system.cpu.percent\",\n        \"aggregator\": \"sum\",\n        \"entity.type\": \"Monitor\",\n        \"entities\": {\n          \"1\": \"\"\n        },\n        \"plugins\": [\n          502\n        ]\n      },\n      {\n        \"data.point\": \"system.cpu.percent\",\n        \"aggregator\": \"count\",\n        \"entity.type\": \"Monitor\",\n        \"entities\": {\n          \"1\": \"\"\n        },\n        \"plugins\": [\n          502\n        ]\n      },\n      {\n        \"data.point\": \"system.cpu.percent\",\n        \"aggregator\": \"min\",\n        \"entity.type\": \"Monitor\",\n        \"entities\": {\n          \"1\": \"\"\n        },\n        \"plugins\": [\n          502\n        ]\n      },\n      {\n        \"data.point\": \"system.cpu.percent\",\n        \"aggregator\": \"max\",\n        \"entity.type\": \"Monitor\",\n        \"entities\": {\n          \"1\": \"\"\n        },\n        \"plugins\": [\n          502\n        ]\n      }\n    ]\n  }\n}"

	// HenceSkipping is a standard message used in log entries when operations are skipped
	// due to various conditions (e.g., invalid data, missing prerequisites)
	HenceSkipping = "hence skipping"
)

// Int64Column represents a sortable column of int64 values with associated group identifiers.
// This structure is used for sorting aggregated data by numeric values while maintaining
// the relationship between values and their corresponding group classifications.
//
// The sorting is performed in descending order (largest values first) to optimize
// for typical aggregation result presentation where highest values are most relevant.
type Int64Column struct {
	// values contains the int64 data points to be sorted
	values []int64

	// groups contains the group identifiers corresponding to each value
	// These maintain the association between sorted values and their original groupings
	groups []int32
}

// Len returns the number of elements in the column (required by sort.Interface)
func (item Int64Column) Len() int { return len(item.values) }

// Less compares two elements for sorting in descending order (required by sort.Interface)
// Returns true if element i should come before element j in the sorted order
func (item Int64Column) Less(i, j int) bool {
	return item.values[i] > item.values[j]
}

// Swap exchanges two elements in both the values and groups slices (required by sort.Interface)
// This maintains the correspondence between values and their group identifiers during sorting
func (item Int64Column) Swap(i, j int) {
	item.values[i], item.values[j] = item.values[j], item.values[i]
	item.groups[i], item.groups[j] = item.groups[j], item.groups[i]
}

// Float64Column represents a sortable column of float64 values with associated group identifiers.
// This structure is used for sorting aggregated data by floating-point values while maintaining
// the relationship between values and their corresponding group classifications.
//
// The sorting is performed in descending order (largest values first) to optimize
// for typical aggregation result presentation where highest values are most relevant.
type Float64Column struct {
	// values contains the float64 data points to be sorted
	values []float64

	// groups contains the group identifiers corresponding to each value
	// These maintain the association between sorted values and their original groupings
	groups []int32
}

// Len returns the number of elements in the column (required by sort.Interface)
func (item Float64Column) Len() int { return len(item.values) }

// Less compares two elements for sorting in descending order (required by sort.Interface)
// Returns true if element i should come before element j in the sorted order
func (item Float64Column) Less(i, j int) bool {
	return item.values[i] > item.values[j]
}

// Swap exchanges two elements in both the values and groups slices (required by sort.Interface)
// This maintains the correspondence between values and their group identifiers during sorting
func (item Float64Column) Swap(i, j int) {
	item.values[i], item.values[j] = item.values[j], item.values[i]
	item.groups[i], item.groups[j] = item.groups[j], item.groups[i]
}

// AggregationJob represents a worker that processes aggregation tasks for time-series and entity data.
// It handles data probing, aggregation execution, and result persistence across different storage formats.
//
// The struct is memory-aligned for optimal performance on 64-bit systems, with fields ordered by size
// to minimize padding and reduce memory footprint.
type AggregationJob struct {
	// === 8-byte aligned fields (pointers, slices, maps, channels, int64) ===

	// store provides access to the underlying storage system for reading and writing data
	store *storage.Store

	// excludedGroups is a bitmap tracking which data groups should be excluded from aggregation
	excludedGroups *bitmap.Bitmap

	// waitGroup coordinates shutdown synchronization across multiple goroutines
	waitGroup *sync.WaitGroup

	// executors contains query execution engines for processing aggregation operations
	executors []*query.Executor

	// tokenizers provide string parsing capabilities for different aggregation contexts
	tokenizers []*utils.Tokenizer

	// Notifications receives aggregation job requests from the manager
	Notifications chan utils.MotadataMap

	// ShutdownNotifications receives shutdown signals for graceful termination
	ShutdownNotifications chan bool

	// JobQueryAcks sends acknowledgements back to query processors
	JobQueryAcks chan int

	// txnEntries maps transaction IDs to their corresponding transaction entries
	txnEntries map[uint64]utils.TxnEntry

	// entityKeys maps entity identifiers to their key-value representations
	entityKeys map[string]map[string]string

	// stringTokens tracks string-based tokens for aggregation grouping operations
	stringTokens []map[string]map[int64]struct{}

	// numericTokens tracks numeric-based tokens for aggregation grouping operations
	numericTokens []map[int64]map[int64]struct{}

	// stringValues contains reusable string buffers for aggregation operations
	stringValues []string

	// keyBytes provides reusable byte buffer for key serialization operations
	keyBytes []byte

	// valueBytes provides reusable byte buffer for value serialization operations
	valueBytes []byte

	// txnBufferBytes provides reusable byte buffer for transaction data
	txnBufferBytes []byte

	// int32Bytes provides reusable byte buffer for int32 serialization
	int32Bytes []byte

	// query contains the current aggregation query configuration
	query utils.MotadataMap

	// context contains the current aggregation execution context
	context utils.MotadataMap

	// transactions maps transaction identifiers to their metadata
	transactions utils.MotadataStringMap

	// abortedTransactions tracks transactions that failed and need cleanup
	abortedTransactions utils.MotadataStringMap

	// instanceType identifies the type of aggregation instance (e.g., "horizontal", "vertical")
	instanceType string

	// plugin identifies the data plugin this job is processing
	plugin string

	// key contains the current aggregation key being processed
	key string

	// aggregation identifies the specific aggregation configuration name
	aggregation string

	// queryType specifies the type of query operation being performed
	queryType string

	// metric identifies the specific metric being aggregated
	metric string

	// queryId uniquely identifies the current query operation
	queryId int64

	// subQueryId identifies sub-operations within a larger query
	subQueryId int64

	// === 4-byte aligned fields (int, uint32, enums) ===

	// indexSoreType specifies the storage type for index data
	indexSoreType utils.DatastoreType

	// dataStoreType specifies the storage type for aggregated data
	dataStoreType utils.DatastoreType

	// aggregationJobId uniquely identifies this aggregation job instance
	aggregationJobId int

	// executorId identifies which executor is currently being used
	executorId int

	// overflowLength tracks the length of overflow data during processing
	overflowLength int

	// records counts the number of records processed in the current operation
	records int

	// txnOffset tracks the current offset within transaction data
	txnOffset int

	// txnPartition identifies which transaction partition is being processed
	txnPartition int

	// stringColumnElementSize defines the size of string column elements
	stringColumnElementSize int

	// numericColumnElementSize defines the size of numeric column elements
	numericColumnElementSize int

	// === 2-byte aligned fields (uint16) ===

	// txnPart identifies the current transaction part being processed
	txnPart uint16

	// === 1-byte aligned fields (booleans, interfaces) ===

	// executorDecoder handles decoding operations for executor data
	executorDecoder Decoder

	// decoder handles general decoding operations
	decoder Decoder

	// executorEncoder handles encoding operations for executor data
	executorEncoder Encoder

	// encoder handles general encoding operations
	encoder Encoder

	// event tracks disk I/O events for performance monitoring
	event storage.DiskIOEvent

	// logger provides structured logging for this aggregation job
	logger utils.Logger

	// cleanupExecutor indicates whether executor cleanup is required
	cleanupExecutor bool

	// fulltextSearchingView indicates if this job processes full-text search operations
	fulltextSearchingView bool

	// shutdown indicates whether this job should terminate
	shutdown bool

	// garbage indicates whether this job instance should be garbage collected
	garbage bool

	// probe indicates whether this job is in data probing mode
	probe bool
}

// NewAggregationJob creates and initializes a new AggregationJob instance with the specified ID and executors.
// This constructor performs comprehensive initialization including:
//
// 1. Memory pool setup for efficient data encoding/decoding
// 2. Memory-mapped buffer allocation for high-performance I/O operations
// 3. Query context initialization from the visualization template
// 4. Channel and data structure initialization with appropriate capacities
//
// Parameters:
// - id: Unique identifier for this aggregation job instance
// - executors: Pre-configured query executors for processing aggregation operations
//
// Returns:
// - *AggregationJob: Fully initialized aggregation job ready for processing
func NewAggregationJob(id int, executors []*query.Executor) *AggregationJob {
	// Initialize query context from the visualization template
	// This provides a standard query structure for aggregation operations
	queryContext := make(utils.MotadataMap, 100)

	// Parse the visualization query template and populate the query context
	// This creates a reusable query structure that can be customized for specific aggregations
	gjson.ParseBytes([]byte(visualizationQuery)).ForEach(func(key, value gjson.Result) bool {
		queryContext[key.String()] = value.Value()
		return true
	})

	// Create a memory pool for efficient encoding/decoding operations
	// Parameters: 6 pools, 100K bytes each, no compression, default blob pools
	pool := utils.NewMemoryPool(6, 100_000, false, utils.DefaultBlobPools)

	// Attempt to create memory-mapped buffer for value operations
	// Memory mapping provides better performance for large data operations
	bytes, err := utils.MmapAnonymous(utils.MaxValueBufferBytes)
	if err != nil {
		// Fall back to regular byte slice if memory mapping fails
		bytes = make([]byte, utils.MaxValueBufferBytes)
	}

	// Attempt to create memory-mapped buffer for transaction operations
	// Separate buffer for transaction data to avoid contention
	txnBufferBytes, err := utils.MmapAnonymous(utils.GetDataWriterTxnBufferBytes())
	if err != nil {
		// Fall back to regular byte slice if memory mapping fails
		txnBufferBytes = make([]byte, utils.GetDataWriterTxnBufferBytes())
	}

	// Create and return the fully initialized AggregationJob instance
	return &AggregationJob{
		// Initialize encoder with the memory pool for efficient data serialization
		encoder: NewEncoder(pool),

		// Initialize decoder with the memory pool for efficient data deserialization
		decoder: NewDecoder(pool),

		// Initialize entity keys map for tracking entity relationships
		entityKeys: map[string]map[string]string{},

		// Initialize string values slice with initial capacity
		stringValues: make([]string, 1),

		// Create logger with unique identifier for this job instance
		logger: utils.NewLogger("Aggregation Job-"+INTToStringValue(id), "job"),

		// Create buffered notification channel with high capacity (10,000) to prevent blocking
		// This channel receives aggregation requests from the manager
		Notifications: make(chan utils.MotadataMap, 1_00_00),

		// Set the unique identifier for this aggregation job
		aggregationJobId: id,

		// Initialize string token tracking maps for grouping operations
		// Size limited by maximum indexable columns to prevent excessive memory usage
		stringTokens: make([]map[string]map[int64]struct{}, utils.AggregationViewIndexColumnLimit),

		// Initialize numeric token tracking maps for grouping operations
		// Size limited by maximum indexable columns to prevent excessive memory usage
		numericTokens: make([]map[int64]map[int64]struct{}, utils.AggregationViewIndexColumnLimit),

		// Assign the provided query executors for processing aggregation operations
		executors: executors,

		// Create buffered shutdown notification channel (capacity: 5)
		ShutdownNotifications: make(chan bool, 5),

		// Set the parsed query context for aggregation operations
		query: queryContext,

		// Create unbuffered channel for query acknowledgements
		JobQueryAcks: make(chan int),

		// Initialize transaction tracking map with reasonable capacity
		transactions: make(utils.MotadataStringMap, 100),

		// Initialize aborted transaction tracking map with reasonable capacity
		abortedTransactions: make(utils.MotadataStringMap, 100),

		// Initialize bitmap for tracking excluded data groups
		excludedGroups: &bitmap.Bitmap{0},

		// Initialize wait group for coordinating concurrent operations
		waitGroup: &sync.WaitGroup{},

		// Initialize tokenizer slice for string parsing operations (2 tokenizers)
		tokenizers: make([]*utils.Tokenizer, 2),

		// Assign the memory-mapped or allocated value buffer
		valueBytes: bytes,

		// Initialize disk I/O event tracking structure
		event: storage.DiskIOEvent{},

		// Initialize transaction entries map with reasonable capacity
		txnEntries: make(map[uint64]utils.TxnEntry, 50),

		// Set transaction partition to not available initially
		txnPartition: utils.NotAvailable,

		// Assign the memory-mapped or allocated transaction buffer
		txnBufferBytes: txnBufferBytes,

		// Initialize 4-byte buffer for int32 serialization operations
		int32Bytes: make([]byte, 4),
	}
}

// Start initializes the AggregationJob and begins processing aggregation requests in a background goroutine.
// This method performs final initialization of data structures and starts the main processing loop.
//
// The method performs the following operations:
// 1. Initializes tokenizers for string parsing operations
// 2. Initializes token tracking maps for grouping operations
// 3. Starts the main processing loop in a separate goroutine
// 4. Registers with the global shutdown coordination mechanism
//
// The method returns immediately after starting the background processing.
func (job *AggregationJob) Start() {
	// Initialize all tokenizers with pre-allocated token buffers
	// This avoids repeated memory allocations during string parsing operations
	for i := range job.tokenizers {
		job.tokenizers[i] = &utils.Tokenizer{
			Tokens: make([]string, utils.TokenizerLength),
		}
	}

	// Initialize all token tracking maps for grouping operations
	// These maps track string and numeric tokens used in aggregation grouping
	for i := range job.stringTokens {
		job.stringTokens[i] = map[string]map[int64]struct{}{}
		job.numericTokens[i] = map[int64]map[int64]struct{}{}
	}

	// Start the main processing loop in a separate goroutine
	go func() {
		// Register this goroutine with the global shutdown coordination mechanism
		// This ensures proper cleanup during system shutdown
		utils.JobEngineShutdownMutex.Add(1)

		// Main processing loop - continues until shutdown is requested
		for {
			// Check for shutdown conditions:
			// - Local shutdown flag set via ShutdownNotifications
			// - Global shutdown flag set by system-wide shutdown process
			if job.shutdown || utils.GlobalShutdown {
				break
			}

			// Execute the main processing logic
			// This method handles all notification processing and aggregation execution
			job.run()
		}

		// Signal completion to the global shutdown coordination mechanism
		// This allows the system to wait for all job engines to complete before exiting
		utils.JobEngineShutdownMutex.Done()
	}()
}

// run is the main execution loop for the AggregationJob that processes incoming requests.
// This method continuously listens for notifications and handles different types of aggregation operations.
//
// The method handles two main types of operations:
// 1. Probe operations: Analyze historical data to determine optimal aggregation strategies
// 2. Sync operations: Execute aggregation for specific time intervals
//
// The method includes comprehensive error recovery to ensure system stability.
func (job *AggregationJob) run() {
	// Set up panic recovery to ensure the job can recover from unexpected errors
	// This is critical for system stability as aggregation jobs are long-running processes
	defer func() {
		// Handle any panics that occur during aggregation processing
		if err := recover(); err != nil {
			// Capture and log the stack trace for debugging
			stackTraceBytes := make([]byte, 1<<20)
			job.logger.Error(fmt.Sprintf("error %v occurred", err))
			job.logger.Error(fmt.Sprintf(utils.ErrorJobStackTrace, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
			job.logger.Error(fmt.Sprintf("err %v occurred while writing aggregation for plugin %v restarting the job...", err, job.plugin))
			job.logger.Error("restarting aggregation job....")

			// Clean up any partial state and prepare for restart
			job.cleanUp()
		}
	}()

	// Main event processing loop
	for {
		select {
		// Process aggregation requests from the manager
		case context := <-job.Notifications:
			// Determine the type of operation requested
			operationType := context.GetIntValue(utils.OperationType)

			if operationType == utils.Probe {
				// Probe operation: Analyze historical data to determine aggregation strategy
				// This involves scanning past datastores and generating optimal aggregation configurations
				job.prepareQueryContext(context)
			} else {
				// Sync operation: Execute aggregation for a specific time interval
				// For vertical aggregations, this is typically triggered when the metric aggregator
				// encounters an error and needs the aggregation job to handle specific ticks
				job.aggregate(context)
			}

		// Handle shutdown notifications for graceful termination
		case <-job.ShutdownNotifications:
			// Clean up memory-mapped buffers
			if err := utils.Munmap(job.valueBytes); err != nil {
				job.logger.Warn(fmt.Sprintf("failed to unmap anonymous mapped buffer,reason: %v", err.Error()))
			}

			if err := utils.Munmap(job.txnBufferBytes); err != nil {
				job.logger.Warn(fmt.Sprintf("failed to unmap anonymous mapped txn buffer,reason: %v", err.Error()))
			}

			// Clean up memory pool resources
			job.encoder.MemoryPool.Unmap()

			job.logger.Info("Shutting Down...")

			// Set shutdown flag and exit the processing loop
			job.shutdown = true
			return
		}
	}
}

// aggregate executes aggregation operations for a specific time interval.
// This method processes aggregation requests for specific ticks, typically triggered when:
// 1. The metric aggregator encounters an error and needs recovery
// 2. Manual aggregation is requested for data consistency
// 3. Backfill operations are needed for missing aggregated data
//
// The method handles both horizontal (time-series) and vertical (entity-centric) aggregations.
//
// Parameters:
// - context: Contains aggregation parameters including tick, plugin, and aggregation context
func (job *AggregationJob) aggregate(context utils.MotadataMap) {
	// Ensure cleanup and notification regardless of how the function exits
	defer func() {
		// Send acknowledgement back to the manager indicating completion
		// This allows the manager to update its tracking and schedule next operations
		utils.ManagerNotifications <- context

		// Reset full-text search flag for next operation
		job.fulltextSearchingView = false
	}()

	// Record start time for performance monitoring
	timestamp := time.Now().UnixMilli()

	// Reset job context to clean state for new aggregation operation
	job.resetContext()

	// Extract aggregation parameters from the request context
	baseTick := context.GetInt64Value(utils.Tick)
	plugin := context.GetStringValue(utils.Plugin)

	// Log aggregation start for debugging purposes
	if utils.DebugEnabled() {
		job.logger.Debug(fmt.Sprintf("starting aggregation for plugin %v", plugin))
	}

	// Set the query type (horizontal or vertical format)
	job.queryType = context.GetStringValue(utils.DatastoreFormat)

	// Extract the aggregation context containing column definitions and metadata
	job.context = context.GetMapValue(utils.EventContext)
	if job.context == nil {
		job.logger.Warn(fmt.Sprintf("invalid context for plugin %v", plugin))
		return
	}

	// Set job state for this aggregation operation
	job.plugin = plugin
	job.dataStoreType = utils.DatastoreType(job.context.GetIntValue(utils.Type))

	job.indexSoreType = job.dataStoreType

	job.overflowLength = utils.LogOverflowLength

	job.fulltextSearchingView = job.dataStoreType == utils.Log && job.context.Contains(query.Filters)

	if job.dataStoreType == utils.PerformanceMetric || job.dataStoreType == utils.ObjectStatusMetric || job.dataStoreType == utils.Flow || job.dataStoreType == utils.NetRouteStatusMetric {

		job.overflowLength = utils.OverflowLength
	}

	if job.dataStoreType == utils.None {

		job.logger.Error(fmt.Sprintf("failed to qualify visualization type for plugin %v", plugin))

		return
	}

	if utils.DebugEnabled() {

		job.logger.Debug(fmt.Sprintf("plugin: %v, visualization type  %v", plugin, job.dataStoreType))

	}

	fromTimeMillis := baseTick * 1000

	toTimeMillis := fromTimeMillis + (context.GetInt64Value(utils.Interval) * 60 * 1000)

	if job.queryType == utils.VerticalFormat {

		for column, dataType := range job.context {

			if column == utils.Type {

				continue

			} else if strings.Contains(column, utils.InstanceSeparator) {

				utils.Split(column, utils.InstanceSeparator, job.tokenizers[0])

				job.instanceType = job.tokenizers[0].Tokens[0]
			}

			job.metric = column

			if int(dataType.(float64)) == datastore.StringColumn {

				job.garbage = true
			}
		}

		job.setEntities(utils.UnixMillisToSeconds(fromTimeMillis))

		job.prepareVerticalQueryContext(context.GetIntValue(utils.Interval), fromTimeMillis, toTimeMillis)

	} else {

		job.aggregation = context.GetStringValue(Aggregation)

		dataPoints := job.prepareHorizontalQueryContext()

		job.executeQuery(fromTimeMillis, toTimeMillis, dataPoints, context.GetIntValue(utils.Interval))
	}

	if utils.DebugEnabled() {

		job.logger.Debug(fmt.Sprintf("job took %v ms to complete aggregation for plugin %v for interval %v", time.Now().UnixMilli()-timestamp, plugin, context.GetStringValue(utils.Interval)))

	}

}

func (job *AggregationJob) prepareQuery(intervalMillis, startTimeMillis, endTimeMillis int64, dataPoints []utils.MotadataMap) {

	fromTimeMillis := startTimeMillis

	toTimeMillis := fromTimeMillis + intervalMillis

	for fromTimeMillis < endTimeMillis {

		if utils.GlobalShutdown {

			return
		}

		if toTimeMillis > endTimeMillis {

			toTimeMillis = endTimeMillis
		}

		if job.queryType == utils.VerticalFormat {

			// for one interval aggregation will be written
			job.prepareVerticalQueryContext(int(intervalMillis/1000)/60, fromTimeMillis, toTimeMillis) //interval minutes

		} else {

			job.executeQuery(fromTimeMillis, toTimeMillis, dataPoints, int(intervalMillis/1000)/60) //interval minutes

		}

		fromTimeMillis = toTimeMillis

		toTimeMillis = fromTimeMillis + intervalMillis
	}

	return
}

// qualifyStores performs historical data analysis by probing datastores from past to present.
// This method is the core of the data probing process that determines optimal aggregation strategies
// by analyzing existing data patterns across multiple time periods.
//
// The method performs the following operations:
// 1. Determines the probe duration based on aggregation type and search requirements
// 2. Prepares query contexts for horizontal aggregations
// 3. Iterates through historical datastores day by day
// 4. Executes aggregation queries for each time period and interval
// 5. Builds aggregated data that serves as the foundation for future queries
//
// Probe Duration Strategy:
// - Vertical aggregations: Limited days for entity-based analysis
// - Horizontal aggregations: Extended days for time-series pattern analysis
// - Full-text search views: Reduced days due to processing complexity
func (job *AggregationJob) qualifyStores() {
	// Initialize probe duration based on aggregation type
	days := utils.NotAvailable
	var dataPoints []utils.MotadataMap

	if job.queryType == utils.VerticalFormat {
		// Vertical aggregations require fewer days as they focus on entity relationships
		days = utils.VerticalStoreProbeDays
	} else {
		// Horizontal aggregations need more days to establish time-series patterns
		days = utils.HorizontalStoreProbeDays

		// Full-text search views require special handling with reduced probe duration
		// due to the computational complexity of text analysis
		if job.fulltextSearchingView {
			days = utils.FulltextSearchingProbeDays
		}

		// Prepare query context for horizontal aggregations
		// This creates the data points that will be used for probing
		dataPoints = job.prepareHorizontalQueryContext()
		if dataPoints == nil {
			// No valid data points found, cannot proceed with probing
			return
		}
	}

	// Calculate the time range for probing operations
	currentTimeMillis := time.Now().UnixMilli()
	timestamp := time.UnixMilli(currentTimeMillis).UTC()

	// Set the starting point for probing: go back 'days' from current date
	// Start at midnight of the target day for consistent time boundaries
	timestamp = time.Date(timestamp.Year(), timestamp.Month(), timestamp.Day()-days+1, 0, 0, 0, 0, time.UTC)

	startTimeMillis := timestamp.UnixMilli()
	endTimeMillis := int64(0)

	// Iterate through each day in the probe period
	// This day-wise approach ensures systematic coverage of historical data
	for i := 0; i < days; i++ {
		// Check for global shutdown to allow graceful termination
		if utils.GlobalShutdown {
			return
		}

		// Set the start time for the current day
		startTimeMillis = timestamp.UnixMilli()

		// Determine the end time for the current day
		if i != days-1 {
			// For historical days: end at midnight of the next day
			endTimeMillis = timestamp.Add(time.Hour * 24).UnixMilli()
		} else {
			// For the current day: end at current time plus 5-minute buffer
			// The buffer accounts for any recent data that might still be processing
			endTimeMillis = time.Now().UnixMilli() + 300_000
		}

		if job.queryType == utils.VerticalFormat {

			clear(job.entityKeys)

			// will set entity keys from the raw datastore
			qualified := job.setEntities(utils.UnixMillisToSeconds(startTimeMillis))

			if qualified { // means there is entities

				for _, interval := range utils.AggregationIntervals {

					job.prepareQuery(int64(interval*60*1000), startTimeMillis, endTimeMillis, nil)

				}
			}

		} else {

			storeName := utils.SecondsToDate(utils.UnixToSeconds(timestamp.Unix())) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + job.plugin

			if datastore.IsStoreAvailable(storeName) {

				intervals := utils.EventAggregationIntervals

				if job.fulltextSearchingView {

					//skipping 360,15 duration view
					intervals = utils.FullTextViewAggregationIntervals
				}

				for index := range intervals {

					job.prepareQuery(int64(intervals[index]*60*1000), startTimeMillis, endTimeMillis, dataPoints)
				}

				if utils.DebugEnabled() {

					job.logger.Debug(fmt.Sprintf("probing complete for store %v from %v, to %v", storeName, utils.UnixMillisToTime(startTimeMillis), utils.UnixMillisToTime(endTimeMillis)))

				}

			}
		}

		timestamp = timestamp.AddDate(0, 0, 1)

	}
}

// prepareVerticalQueryContext creates and executes query context for vertical (entity-centric) aggregations.
// This method handles metric-based aggregations where data is organized by entities rather than time series.
//
// Vertical aggregations focus on:
// - Entity relationships and hierarchies
// - Metric values across different entities
// - Last-value semantics for garbage collection scenarios
// - Comprehensive aggregation functions for regular metrics
//
// Parameters:
// - intervalMinutes: The aggregation interval in minutes
// - fromTimeMillis: Start time of the aggregation period in milliseconds
// - toTimeMillis: End time of the aggregation period in milliseconds
func (job *AggregationJob) prepareVerticalQueryContext(intervalMinutes int, fromTimeMillis, toTimeMillis int64) {
	// Check for global shutdown to allow graceful termination
	if utils.GlobalShutdown {
		return
	}

	// Log the query context preparation for debugging
	if utils.DebugEnabled() {
		job.logger.Debug(fmt.Sprintf("building query context for plugin %v, metric %v, timeline: %v-%v",
			job.plugin, job.metric, utils.UnixMillisToTime(fromTimeMillis), utils.UnixMillisToTime(toTimeMillis)))
	}

	// Reset cleanup flag for this operation
	job.cleanupExecutor = false

	// Initialize data points array for query execution
	var dataPoints []utils.MotadataMap

	// Set the plugin name for all data points
	job.stringValues[0] = job.plugin

	// Handle garbage collection scenarios with last-value aggregation
	if job.garbage {
		// For garbage metrics, only use the last value to avoid accumulating stale data
		if len(job.entityKeys[job.metric]) > 0 {
			datapoint := utils.MotadataMap{
				query.DataPoint:  job.metric,
				query.Aggregator: utils.Last, // Last value semantics for garbage collection
				utils.Plugins:    job.stringValues,
				query.EntityKeys: job.entityKeys[job.metric],
			}
			dataPoints = append(dataPoints, datapoint)
		}
	} else {
		// Handle regular metrics with comprehensive aggregation functions
		for _, aggregator := range utils.VerticalAggregationFuncs {
			// Only create data points if entity keys are available
			if len(job.entityKeys[job.metric]) > 0 {
				datapoint := utils.MotadataMap{
					query.DataPoint:  job.metric,
					query.Aggregator: aggregator, // Apply all vertical aggregation functions
					utils.Plugins:    job.stringValues,
					query.EntityKeys: job.entityKeys[job.metric],
				}
				dataPoints = append(dataPoints, datapoint)
			}
		}
	}

	// Execute the query if data points were successfully created
	if dataPoints != nil {
		job.executeQuery(fromTimeMillis, toTimeMillis, dataPoints, intervalMinutes)
	}
}

// prepareHorizontalQueryContext creates data points for horizontal (time-series) aggregation queries.
// This method analyzes the aggregation context and generates appropriate query data points
// based on the column types and datastore characteristics.
//
// The method handles different datastore types with specific strategies:
// - Log/Flow datastores: Optimized for event counting and numeric aggregations
// - Other datastores: Full aggregation coverage for all applicable columns
//
// Returns:
// - []utils.MotadataMap: Array of data points ready for query execution
func (job *AggregationJob) prepareHorizontalQueryContext() []utils.MotadataMap {
	// Initialize data points array for storing query configurations
	var dataPoints []utils.MotadataMap

	// Set the plugin name for all data points
	job.stringValues[0] = job.plugin

	// Handle Log and Flow datastores with optimized aggregation strategy
	if job.dataStoreType == utils.Log || job.dataStoreType == utils.Flow {
		updated := false // Track if event source count has been added

		// Process each column in the aggregation context
		for key, value := range job.context {
			// Skip metadata fields that are not aggregatable columns
			if key == utils.Type || key == utils.IndexableColumns || key == query.Filters {
				continue
			}

			// Handle integer columns with full aggregation functions
			if int(value.(float64)) == datastore.IntegerColumn {
				// Apply all horizontal aggregation functions to integer columns
				for _, aggregator := range utils.HorizontalAggregationFuncs {
					// Special handling for count function to avoid duplication
					if aggregator == query.CountFunc {
						if updated {
							continue // Skip if event source count already added
						}
						updated = true

						// Add event source count data point
						dataPoints = append(dataPoints, utils.MotadataMap{
							query.DataPoint:  utils.EventSource,
							query.Aggregator: utils.Count,
							utils.Plugins:    job.stringValues,
						})
						continue
					}

					// Add aggregation data point for the integer column
					dataPoints = append(dataPoints, utils.MotadataMap{
						query.DataPoint:  key,
						query.Aggregator: aggregator,
						utils.Plugins:    job.stringValues,
					})
				}
			} else if !updated {
				// For non-integer columns, add event source count if not already added
				updated = true
				dataPoints = append(dataPoints, utils.MotadataMap{
					query.DataPoint:  utils.EventSource,
					query.Aggregator: utils.Count,
					utils.Plugins:    job.stringValues,
				})
			}
		}

	} else {
		// Handle other datastore types with comprehensive aggregation strategy
		// These datastores support full aggregation coverage for all columns

		// Process each column in the aggregation context
		for key, value := range job.context {
			// Skip metadata fields that are not aggregatable columns
			if key == utils.Type || key == utils.IndexableColumns || key == query.Filters {
				continue
			}

			// Handle integer columns with all aggregation functions
			if int(value.(float64)) == datastore.IntegerColumn {
				// Apply all horizontal aggregation functions to integer columns
				// This provides comprehensive analytics for numeric data
				for _, aggregator := range utils.HorizontalAggregationFuncs {
					dataPoints = append(dataPoints, utils.MotadataMap{
						query.DataPoint:  key,
						query.Aggregator: aggregator,
						utils.Plugins:    job.stringValues,
					})
				}
			} else {
				// For non-integer columns, only count aggregation is meaningful
				// String and other data types are typically counted rather than summed/averaged
				dataPoints = append(dataPoints, utils.MotadataMap{
					query.DataPoint:  key,
					query.Aggregator: utils.Count,
					utils.Plugins:    job.stringValues,
				})
			}
		}
	}

	return dataPoints
}

// prepareQueryContext initializes the aggregation job for data probing operations.
// This method is called when a new widget is created and historical data needs to be analyzed
// to determine the optimal aggregation strategy.
//
// The method handles both vertical (entity-centric) and horizontal (time-series) aggregations:
// - Vertical: Processes individual metrics for entity-based aggregation
// - Horizontal: Analyzes time-series data patterns for efficient aggregation views
//
// Parameters:
// - context: Contains probing parameters including plugin, aggregation name, and context
func (job *AggregationJob) prepareQueryContext(context utils.MotadataMap) {
	// Ensure cleanup and notification regardless of how the function exits
	defer func() {
		// Send acknowledgement back to the manager indicating probe completion
		// This allows the manager to finalize the aggregation configuration
		utils.ManagerNotifications <- context

		// Reset full-text search flag for next operation
		job.fulltextSearchingView = false
	}()

	// Extract the datastore format to determine processing strategy
	category := context.GetStringValue(utils.DatastoreFormat)

	// Reset job context to clean state for new probing operation
	job.resetContext()

	// Extract and validate the plugin name
	job.plugin = context.GetStringValue(utils.Plugin)
	if job.plugin == utils.Empty {
		job.logger.Warn(fmt.Sprintf("invalid plugin received from context: %v", context))
		return
	}

	// Extract and validate the aggregation context
	job.context = context.GetMapValue(utils.EventContext)
	if job.context == nil {
		job.logger.Warn(fmt.Sprintf("invalid context for plugin %v", job.plugin))
		return
	}

	// Set default overflow length for data processing
	job.overflowLength = utils.OverflowLength

	if category == utils.VerticalFormat {

		for column, dataType := range job.context {

			if column == utils.Type {

				continue

			} else if strings.Contains(column, utils.InstanceSeparator) {

				utils.Split(column, utils.InstanceSeparator, job.tokenizers[0])

				job.instanceType = job.tokenizers[0].Tokens[0]
			}

			job.metric = column

			if int(dataType.(float64)) == datastore.StringColumn {

				job.garbage = true
			}
		}

		job.dataStoreType = utils.DatastoreType(job.context.GetIntValue(utils.Type))

		job.queryType = utils.VerticalFormat

		job.qualifyStores()

		for key := range job.context {

			if key == utils.Type {

				continue
			}

			// From now on The vertical writer will now submit batches to the metric aggregator.
			datastore.UpdateVerticalAggregations(key, true) //this is for the sake of parser

		}

	} else {

		job.probe = true //toggle the flag in case of probing.

		job.dataStoreType = utils.DatastoreType(job.context.GetIntValue(utils.Type))

		job.queryType = utils.HorizontalFormat

		if job.dataStoreType != utils.Flow {

			job.overflowLength = utils.LogOverflowLength
		}

		job.aggregation = context.GetStringValue(Aggregation)

		// manager remove's filter unless it is specific filter with message contain
		job.fulltextSearchingView = job.dataStoreType == utils.Log && job.context.Contains(query.Filters)

		job.qualifyStores()

		datastore.AddHorizontalAggregation(job.plugin, job.aggregation, job.context.DeepClone())
	}

}

// write processes aggregated data from the executor pool and persists it to the appropriate storage.
// This method is the core data persistence function that handles the final stage of aggregation processing.
//
// The method performs the following operations:
// 1. Sets up encoder/decoder for data serialization
// 2. Determines the appropriate storage type and configuration
// 3. Processes aggregated groups and columns
// 4. Handles data partitioning and overflow management
// 5. Writes data to storage with transaction support
// 6. Creates necessary indexes for query optimization
//
// Parameters:
// - tick: The time tick (32-bit seconds) for which data is being written
// - intervalMinutes: The aggregation interval in minutes
func (job *AggregationJob) write(tick int32, intervalMinutes int) {
	// Set up encoder and decoder from the assigned executor
	// These handle data serialization/deserialization for storage operations
	job.executorDecoder = job.executors[job.executorId].GetDecoder()
	job.executorEncoder = job.executors[job.executorId].GetEncoder()

	// Set up cleanup and error recovery for the write operation
	defer func() {
		// Handle any panics that occur during write operations
		if r := recover(); r != nil {
			// Capture and log the stack trace for debugging
			stackTraceBytes := make([]byte, 1<<20)
			job.logger.Error(fmt.Sprintf("panic occurred while writing aggregation for plugin- %v , tick - %v , err - %v", job.plugin, tick, r))
			job.logger.Error(fmt.Sprintf(utils.ErrorJobStackTrace, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

		// Clean up resources and reset state for next operation
		job.excludedGroups.Clear() // Clear excluded groups bitmap
		job.store = nil            // Release store reference
		job.key = utils.Empty      // Reset current key
		job.records = 0            // Reset record counter
		job.txnPart = uint16(0)    // Reset transaction part
		job.cleanupTxn()           // Clean up transaction state

		// For vertical aggregations, create time range filter index
		if job.queryType == utils.VerticalFormat {
			// Write dummy posting list for time range filtering
			// This enables efficient time-based queries on vertical aggregations
			job.writeTimeRangeFilterIndex(tick)
		}
	}()

	if job.queryType == utils.HorizontalFormat {

		if job.dataStoreType == utils.Flow {

			job.indexSoreType = utils.FlowIndex

			job.store = datastore.GetStore(datastore.GetAggregatedStoreName(tick, job.aggregation, INTToStringValue(intervalMinutes), datastore.HorizontalStore), utils.FlowAggregation, true, true, job.executorEncoder, job.tokenizers[1])

		} else if job.dataStoreType == utils.Trap {

			job.indexSoreType = utils.TrapIndex

			job.store = datastore.GetStore(datastore.GetAggregatedStoreName(tick, job.aggregation, INTToStringValue(intervalMinutes), datastore.HorizontalStore), utils.TrapAggregation, true, true, job.executorEncoder, job.tokenizers[1])

		} else if job.dataStoreType == utils.MetricPolicy || job.dataStoreType == utils.EventPolicy || job.dataStoreType == utils.PolicyFlapHistory {

			job.indexSoreType = utils.PolicyIndex

			job.store = datastore.GetStore(datastore.GetAggregatedStoreName(tick, job.aggregation, INTToStringValue(intervalMinutes), datastore.HorizontalStore), utils.PolicyAggregation, true, true, job.executorEncoder, job.tokenizers[1])

		} else {

			job.indexSoreType = utils.LogIndex

			job.store = datastore.GetStore(datastore.GetAggregatedStoreName(tick, job.aggregation, INTToStringValue(intervalMinutes), datastore.HorizontalStore), utils.LogAggregation, true, true, job.executorEncoder, job.tokenizers[1])
		}

	} else {

		resolvedDatastoreType := utils.MetricAggregation

		if job.dataStoreType == utils.NetRouteStatusMetric {

			resolvedDatastoreType = utils.NetRouteStatusMetricAggregation
		}

		if job.garbage {

			job.store = datastore.GetStore(datastore.GetAggregatedStoreName(tick, job.metric+utils.HyphenSeparator+utils.Garbage, INTToStringValue(intervalMinutes), datastore.HorizontalStore), resolvedDatastoreType, true, true, job.executorEncoder, job.tokenizers[1])

		} else {

			job.store = datastore.GetStore(datastore.GetAggregatedStoreName(tick, job.metric, INTToStringValue(intervalMinutes), datastore.HorizontalStore), resolvedDatastoreType, true, true, job.executorEncoder, job.tokenizers[1])
		}
	}

	groupPoolIndex, groups := job.executorDecoder.MemoryPool.AcquireUINT64Pool(job.executors[job.executorId].Groups.Len())

	resolvedGroupPoolIndex, resolvedGroups := job.executorDecoder.MemoryPool.AcquireStringPool(len(groups))

	defer job.executorEncoder.MemoryPool.ReleaseUINT64Pool(groupPoolIndex)

	defer job.executorEncoder.MemoryPool.ReleaseStringPool(resolvedGroupPoolIndex)

	// The indices indicate the index of ordinal value's of each individual column in a group of columns.
	var indices []int

	poolIndex := utils.NotAvailable

	if job.queryType == utils.VerticalFormat {

		for group, offset := range job.executors[job.executorId].Groups.All() {

			groups[offset] = group

			resolvedGroups[offset], _ = job.executors[job.executorId].ResolvedGroups.Get(group)
		}

	} else {

		// The indices indicate the index of ordinal value's of each individual column in a group of columns.
		poolIndex, indices = job.executorEncoder.MemoryPool.AcquireINTPool(job.executors[job.executorId].GroupColumnElementSize)

		defer job.executorEncoder.MemoryPool.ReleaseINTPool(poolIndex)

		for i := range indices {

			indices[i], _ = job.executorEncoder.MemoryPool.AcquireINT32Pool(len(groups))

			defer job.executorEncoder.MemoryPool.ReleaseINT32Pool(indices[i])
		}

		for group, offset := range job.executors[job.executorId].Groups.All() {

			groups[offset] = group

			resolvedGroups[offset], _ = job.executors[job.executorId].ResolvedGroups.Get(group)

			utils.Split(resolvedGroups[offset], utils.GroupSeparator, job.tokenizers[0])

			for i, ordinal := range job.tokenizers[0].Tokens[:job.tokenizers[0].Counts] {

				job.executorEncoder.MemoryPool.GetINT32Pool(indices[i])[offset] = StringToINT32(ordinal)
			}

		}
	}

	if utils.DebugEnabled() {

		job.logger.Debug(fmt.Sprintf("plugin: %v, groups: %v, tick: %v, interval: %v", job.plugin, job.executors[job.executorId].Groups.Len(), tick, intervalMinutes))
	}

	qualifiedTick := INT32ToStringValue(tick)

	parts := 1

	if len(groups) > job.overflowLength {

		parts = int(math.Ceil(float64(len(groups)) / float64(job.overflowLength)))
	}

	for columnIndex, column := range job.executors[job.executorId].Columns[:job.executors[job.executorId].ColumnElementSize] { //contains the data-points according to aggregations applied on a column

		/*
			looping over the column pools and checking for any dummy value entry
			if there are any dummy values encountered then the group need to be ignored for all columns for maintaining
			same array length
		*/

		if job.dataStoreType == utils.Flow && job.plugin != datastore.FlowStatPlugin && strings.EqualFold(column, datastore.FlowTopColumn) {

			if job.executors[job.executorId].ColumnPoolDataTypes[columnIndex] == Int64 {

				values := job.executorDecoder.MemoryPool.GetINT64Pool(job.executors[job.executorId].ColumnPoolIndices[columnIndex])

				valuePoolIndex, tempValues := job.executorDecoder.MemoryPool.AcquireINT64Pool(len(groups))

				tempValuePoolIndex, tempGroups := job.executorDecoder.MemoryPool.AcquireINT32Pool(len(groups))

				elementSize := 0

				for i, value := range values {

					if value != utils.DummyINT64Value {

						tempGroups[elementSize] = int32(i)

						tempValues[elementSize] = value

						elementSize++
					} else {

						job.excludedGroups.Set(uint32(i))
					}
				}

				if elementSize > utils.MaxFlowTopNInsertionGroups {

					sort.Sort(Int64Column{groups: tempGroups[:elementSize], values: tempValues[:elementSize]})

					qualifiedGroups := &bitmap.Bitmap{}

					for i := 0; i < utils.MaxFlowTopNInsertionGroups/2; i++ {

						qualifiedGroups.Set(uint32(tempGroups[i]))
					}

					for i := elementSize - (utils.MaxFlowTopNInsertionGroups / 2); i < elementSize; i++ {

						qualifiedGroups.Set(uint32(tempGroups[i]))
					}

					for i := range tempGroups[:elementSize] {

						if !qualifiedGroups.Contains(uint32(i)) {

							job.excludedGroups.Set(uint32(i))
						}
					}
				}

				job.executorDecoder.MemoryPool.ReleaseINT64Pool(valuePoolIndex)

				job.executorDecoder.MemoryPool.ReleaseINT32Pool(tempValuePoolIndex)

			} else if job.executors[job.executorId].ColumnPoolDataTypes[columnIndex] == Float64 {

				values := job.executorDecoder.MemoryPool.GetFLOAT64Pool(job.executors[job.executorId].ColumnPoolIndices[columnIndex])

				valuePoolIndex, tempValues := job.executorDecoder.MemoryPool.AcquireFLOAT64Pool(len(groups))

				tempValuePoolIndex, tempGroups := job.executorDecoder.MemoryPool.AcquireINT32Pool(len(groups))

				elementSize := 0

				for i, value := range values {

					if value != utils.DummyFLOAT64Value {

						tempGroups[elementSize] = int32(i)

						tempValues[elementSize] = value

						elementSize++
					} else {

						job.excludedGroups.Set(uint32(i))
					}
				}

				if elementSize > utils.MaxFlowTopNInsertionGroups {

					sort.Sort(Float64Column{groups: tempGroups[:elementSize], values: tempValues[:elementSize]})

					qualifiedGroups := &bitmap.Bitmap{}

					for i := 0; i < utils.MaxFlowTopNInsertionGroups/2; i++ {

						qualifiedGroups.Set(uint32(tempGroups[i]))
					}

					for i := elementSize - (utils.MaxFlowTopNInsertionGroups / 2); i < elementSize; i++ {

						qualifiedGroups.Set(uint32(tempGroups[i]))
					}

					for i := range tempGroups[:elementSize] {

						if !qualifiedGroups.Contains(uint32(i)) {

							job.excludedGroups.Set(uint32(i))
						}
					}
				}

				job.executorDecoder.MemoryPool.ReleaseFLOAT64Pool(valuePoolIndex)

				job.executorDecoder.MemoryPool.ReleaseINT32Pool(tempValuePoolIndex)

			}

		} else {

			if job.executors[job.executorId].ColumnPoolDataTypes[columnIndex] == Int64 {

				for i, value := range job.executorDecoder.MemoryPool.GetINT64Pool(job.executors[job.executorId].ColumnPoolIndices[columnIndex]) {

					if value == utils.DummyINT64Value {

						job.excludedGroups.Set(uint32(i))
					}
				}

			} else if job.executors[job.executorId].ColumnPoolDataTypes[columnIndex] == Float64 {

				for i, value := range job.executorDecoder.MemoryPool.GetFLOAT64Pool(job.executors[job.executorId].ColumnPoolIndices[columnIndex]) {

					if value == utils.DummyFLOAT64Value {

						job.excludedGroups.Set(uint32(i))
					}
				}
			}
		}
	}

	from := 0

	to := len(groups)

	if to > job.overflowLength {

		to = job.overflowLength
	}

	var err error

	found := false

	keyBytes := []byte(INT32ToStringValue(tick))

	/*
		mappings according to the group column name
	*/

	for j := range job.stringTokens {

		clear(job.stringTokens[j])

		clear(job.numericTokens[j])
	}

	part := 0

	for i := 0; i < parts; i++ {

		if i == parts-1 {

			to = len(groups)
		}

		job.txnPart = uint16(part)

		job.cleanupTxn()

		job.txnPartition = job.store.GetPartition(keyBytes, job.tokenizers[0])

		valid := true

		columnPoolIndex := utils.NotAvailable

		for columnIndex, column := range job.executors[job.executorId].Columns[:job.executors[job.executorId].ColumnElementSize] {

			if !valid {

				break
			}

			switch job.executors[job.executorId].ColumnPoolDataTypes[columnIndex] {

			case Int64:

				values := job.executorDecoder.MemoryPool.GetINT64Pool(job.executors[job.executorId].ColumnPoolIndices[columnIndex])

				var int64Values []int64

				columnPoolIndex, int64Values = job.executorEncoder.MemoryPool.AcquireINT64Pool(to - from)

				job.records = 0

				maxValue := int64(0)

				for j := from; j < to; j++ {

					if !job.excludedGroups.Contains(uint32(j)) {

						int64Values[job.records] = values[j]

						if maxValue < int64Values[job.records] {

							maxValue = int64Values[job.records]

						}

						job.records++
					}
				}

				if job.records > 0 {

					found = true

					job.key = job.getKey(qualifiedTick, column)

					err = job.writeAggregationValues(GetDataTypeINT(int(maxValue)), int64Values[:job.records], nil, nil, job.store)

					job.records = 0

					maxValue = 0

					if err != nil {

						valid = false

						job.logger.Error(err.Error())

						break
					}
				}

				job.executorDecoder.MemoryPool.ReleaseINT64Pool(columnPoolIndex)

			case Float64:

				values := job.executorDecoder.MemoryPool.GetFLOAT64Pool(job.executors[job.executorId].ColumnPoolIndices[columnIndex])

				var float64Values []float64

				columnPoolIndex, float64Values = job.executorEncoder.MemoryPool.AcquireFLOAT64Pool(to - from)

				job.records = 0

				maxValue := float64(0)

				for j := from; j < to; j++ {

					if !job.excludedGroups.Contains(uint32(j)) {

						float64Values[job.records] = values[j]

						if maxValue < float64Values[job.records] {

							maxValue = float64Values[job.records]

						}

						job.records++
					}
				}

				if job.records > 0 {

					found = true

					job.key = job.getKey(qualifiedTick, column)

					err = job.writeAggregationValues(GetDataTypeFloat(maxValue), nil, float64Values[:job.records], nil, job.store)

					job.records = 0

					maxValue = 0

					if err != nil {

						valid = false

						job.logger.Error(err.Error())

						break
					}

				}

				job.executorDecoder.MemoryPool.ReleaseFLOAT64Pool(columnPoolIndex)

			case String:

				values := job.executorDecoder.MemoryPool.GetStringPool(job.executors[job.executorId].ColumnPoolIndices[columnIndex])

				var stringValues []string

				columnPoolIndex, stringValues = job.executorEncoder.MemoryPool.AcquireStringPool(to - from)

				job.records = 0

				for j := from; j < to; j++ {

					if !job.excludedGroups.Contains(uint32(j)) {

						stringValues[job.records] = values[j]

						job.records++
					}
				}

				if job.records > 0 {

					found = true

					job.key = job.getKey(qualifiedTick, column)

					// garbage value
					err = job.writeAggregationValues(String, nil, nil, stringValues[:job.records], job.store)

					job.records = 0

					if err != nil {

						valid = false

						job.logger.Error(err.Error())

						break
					}
				}

				job.executorDecoder.MemoryPool.ReleaseStringPool(columnPoolIndex)
			}
		}

		// grouping columns value will be written
		if found && job.executors[job.executorId].Groups != nil && job.executors[job.executorId].Groups.Len() > 0 {

			if job.queryType == utils.VerticalFormat {

				/*

					in-case of instance metric we have two mappings object and objectid
					in-case of scalar metric we have only object-id mappings
				*/

				mapping := utils.ObjectId

				if job.plugin == datastore.NetRouteStatusPlugin {

					mapping = utils.NetRouteId

				}

				instanceMappings := datastore.GetStore(job.instanceType+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, job.executorEncoder, job.tokenizers[1])

				mappings := datastore.GetStore(mapping+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, job.executorEncoder, job.tokenizers[1])

				job.records = 0

				ordinalPoolIndex := utils.NotAvailable

				var ordinals []int32

				if job.instanceType == utils.Empty {

					objectPoolIndex, objects := job.executorDecoder.MemoryPool.AcquireINT64Pool(to - from)

					for j := from; j < to; j++ {

						if !job.excludedGroups.Contains(uint32(j)) {

							objects[job.records] = StringToINT64(resolvedGroups[j])

							job.records++

						}
					}

					if job.records > 0 {

						job.key = qualifiedTick + utils.KeySeparator + job.plugin + utils.KeySeparator + mapping + utils.OrdinalSuffix + utils.KeySeparator + UINT16ToStringValue(job.txnPart)

						err, ordinalPoolIndex, ordinals = mappings.MapNumericValues(objectPoolIndex, job.executorEncoder, job.records)

						if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

							valid = false

							job.logger.Error(fmt.Sprintf(utils.ErrorGetOrdinal, err, job.key, job.store.GetName(), job.records))

							return
						}

						err = job.writeGroups(ordinals[:job.records], job.store)

						job.executorEncoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

						job.records = 0

						if err != nil {

							valid = false

							job.logger.Error(err.Error())

							break
						}
					}

					job.executorDecoder.MemoryPool.ReleaseINT64Pool(objectPoolIndex)

				} else {

					instancePoolIndex, instances := job.executorEncoder.MemoryPool.AcquireStringPool(to - from)

					objectPoolIndex, objects := job.executorEncoder.MemoryPool.AcquireINT64Pool(to - from)

					for j := from; j < to; j++ {

						if !job.excludedGroups.Contains(uint32(j)) {

							instances[job.records] = resolvedGroups[j]

							utils.Split(resolvedGroups[j], utils.GroupSeparator, job.tokenizers[0])

							objects[job.records] = StringToINT64(job.tokenizers[0].Tokens[0])

							job.records++
						}
					}

					if job.records > 0 {

						job.key = qualifiedTick + utils.KeySeparator + job.plugin + utils.KeySeparator + datastore.Object + utils.OrdinalSuffix + utils.KeySeparator + UINT16ToStringValue(job.txnPart)

						err, ordinalPoolIndex, ordinals = instanceMappings.MapStringValues(instancePoolIndex, job.executorEncoder, job.records)

						if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

							if ordinalPoolIndex != utils.NotAvailable {

								job.executorEncoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)
							}

							valid = false

							job.logger.Error(fmt.Sprintf(utils.ErrorGetOrdinal, err, job.key, job.store.GetName(), job.records))

							break
						}

						err = job.writeGroups(ordinals[:job.records], job.store)

						job.executorEncoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

						if err != nil {

							valid = false

							job.logger.Error(err.Error())

							break
						}

						job.key = qualifiedTick + utils.KeySeparator + job.plugin + utils.KeySeparator + mapping + utils.OrdinalSuffix + utils.KeySeparator + UINT16ToStringValue(job.txnPart)

						err, ordinalPoolIndex, ordinals = mappings.MapNumericValues(objectPoolIndex, job.executorEncoder, job.records)

						if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

							if ordinalPoolIndex != utils.NotAvailable {

								job.executorEncoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)
							}

							valid = false

							job.logger.Error(fmt.Sprintf(utils.ErrorGetOrdinal, err, job.key, job.store.GetName(), job.records))

							break
						}

						err = job.writeGroups(ordinals[:job.records], job.store)

						job.executorEncoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

						job.records = 0

						if err != nil {

							valid = false

							job.logger.Error(err.Error())

							break
						}
					}

					job.executorEncoder.MemoryPool.ReleaseStringPool(instancePoolIndex)

					job.executorEncoder.MemoryPool.ReleaseINT64Pool(objectPoolIndex)
				}

			} else {

				for k, groupColumn := range job.executors[job.executorId].GroupColumns[:job.executors[job.executorId].GroupColumnElementSize] {

					mappings := datastore.GetStore(groupColumn+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, job.executorEncoder, job.tokenizers[1])

					if mappings == nil {

						valid = false

						break
					}

					job.records = 0

					// event id consist of tick + part of key
					eventId := utils.GetAggregationEventId(tick, job.txnPart)

					ordinalPoolIndex, ordinals := job.executorEncoder.MemoryPool.AcquireINT32Pool(to - from)

					values := job.executorEncoder.MemoryPool.GetINT32Pool(indices[k])

					for j := from; j < to; j++ {

						if !job.excludedGroups.Contains(uint32(j)) {

							ordinals[job.records] = values[j]

							job.records++
						}
					}

					if job.records > 0 {

						job.key = qualifiedTick + utils.KeySeparator + groupColumn + utils.OrdinalSuffix + utils.KeySeparator + UINT16ToStringValue(job.txnPart)

						dataType := String

						mapperPoolIndex := utils.NotAvailable

						err, dataType, mapperPoolIndex = mappings.ResolveMapping(ordinalPoolIndex, job.executorEncoder, job.executors[job.executorId].GetStringOrdinalMappings(), job.executors[job.executorId].GetNumericOrdinalMappings(), job.records)

						if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

							job.executorEncoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

							valid = false

							job.logger.Error(fmt.Sprintf(utils.ErrorGetOrdinal, err, job.key, job.store.GetName(), job.records))

							break
						}

						// string token and numeric token will be written in posting list
						if dataType == String {

							for _, value := range job.executorEncoder.MemoryPool.GetStringPool(mapperPoolIndex) {

								if _, ok := job.stringTokens[k][value]; !ok {

									job.stringTokens[k][value] = map[int64]struct{}{}
								}

								job.stringTokens[k][value][eventId] = struct{}{}
							}

							job.executorEncoder.MemoryPool.ReleaseStringPool(mapperPoolIndex)

						} else {

							for _, value := range job.executorEncoder.MemoryPool.GetINT64Pool(mapperPoolIndex) {

								if _, ok := job.numericTokens[k][value]; !ok {

									job.numericTokens[k][value] = map[int64]struct{}{}
								}

								job.numericTokens[k][value][eventId] = struct{}{}
							}

							job.executorEncoder.MemoryPool.ReleaseINT64Pool(mapperPoolIndex)
						}

						err = job.writeGroups(ordinals[:job.records], job.store)

						job.records = 0

						if err != nil {

							valid = false

							job.logger.Error(err.Error())

							break
						}
					}

					job.executorEncoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

				}
			}
		}

		err = job.commit(i, tick)

		if err == nil {

			if part > 0 {

				if job.store.GetDatastoreType() == utils.MetricAggregation || job.store.GetDatastoreType() == utils.NetRouteStatusMetricAggregation {

					err = job.store.AddMultipartKey(utils.GetHash64([]byte(INT32ToStringValue(tick)+utils.KeySeparator+job.plugin)), 1)

				} else {

					err = job.store.AddMultipartKey(uint64(tick), 1)

				}

				if err != nil {

					job.logger.Error(fmt.Sprintf(utils.ErrorUpdateMultipartMetadata, job.store.GetName(), err.Error()))
				}
			}

			part++
		}

		from = to

		to += job.overflowLength
	}

	return

}

func (job *AggregationJob) writeAggregationValues(dataType DataType, int64Values []int64, float64Values []float64, stringValues []string, store *storage.Store) error {

	bytePoolIndex := utils.NotAvailable

	var bufferBytes []byte

	var err error

	if int64Values != nil {

		switch {

		case dataType == Int8:

			poolIndex, int8Values := job.executorDecoder.MemoryPool.AcquireINT8Pool(len(int64Values))

			INT64ToINT8Values(int64Values, int8Values)

			bytePoolIndex, bufferBytes, err = job.executorEncoder.EncodeINT8Values(None, int8Values, utils.MaxValueBytes)

			job.executorDecoder.MemoryPool.ReleaseINT8Pool(poolIndex)

		case dataType == Int16:

			poolIndex, int16Values := job.executorDecoder.MemoryPool.AcquireINT16Pool(len(int64Values))

			INT64ToINT16Values(int64Values, int16Values)

			bytePoolIndex, bufferBytes, err = job.executorEncoder.EncodeINT16Values(None, int16Values, utils.MaxValueBytes)

			job.executorDecoder.MemoryPool.ReleaseINT16Pool(poolIndex)

		case dataType == Int24 || dataType == Int32:

			poolIndex, int32Values := job.executorDecoder.MemoryPool.AcquireINT32Pool(len(int64Values))

			INT64ToINT32Values(int64Values, int32Values)

			bytePoolIndex, bufferBytes, err = job.executorEncoder.EncodeINT32Values(int32Values, None, dataType, GetDataTypeBits(dataType), utils.MaxValueBytes)

			job.executorDecoder.MemoryPool.ReleaseINT32Pool(poolIndex)

		case dataType == Int40 || dataType == Int48 || dataType == Int56 || dataType == Int64:

			bytePoolIndex, bufferBytes, err = job.executorEncoder.EncodeINT64Values(int64Values, None, dataType, utils.MaxValueBytes)

		}

	} else if float64Values != nil {

		bytePoolIndex, bufferBytes, err = job.executorEncoder.EncodeFLOAT64Values(float64Values, None, dataType, utils.MaxValueBytes)

	} else if stringValues != nil {

		bytePoolIndex, bufferBytes, err = job.executorEncoder.EncodeStringValues(stringValues, None, utils.MaxValueBytes, job.key)

	}

	if err != nil {

		return errors.New(fmt.Sprintf(utils.ErrorWriteKey, job.key, store.GetName(), err) + HenceSkipping + INTToStringValue(job.records))
	}

	if bytePoolIndex != utils.NotAvailable && len(bufferBytes) > 0 {

		err = job.writeTxn([]byte(job.key), bufferBytes)

		job.executorDecoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

		if err != nil {

			return errors.New(fmt.Sprintf(utils.ErrorWriteKey, job.key, store.GetName(), err) + HenceSkipping + INTToStringValue(job.records))
		}
	}

	return nil
}

func (job *AggregationJob) writeGroups(groups []int32, store *storage.Store) error {

	if groups != nil {

		bufferIndex, bufferBytes, err := job.executorEncoder.EncodeINT32Values(groups, None, Int32, 32, utils.MaxValueBytes)

		if err != nil {

			job.executorDecoder.MemoryPool.ReleaseBytePool(bufferIndex)

			return errors.New(fmt.Sprintf(utils.ErrorWriteKey, job.key, store.GetName(), err) + HenceSkipping + INTToStringValue(job.records))
		}

		err = job.writeTxn([]byte(job.key), bufferBytes)

		job.executorDecoder.MemoryPool.ReleaseBytePool(bufferIndex)

		if err != nil {

			return errors.New(fmt.Sprintf(utils.ErrorWriteKey, job.key, store.GetName(), err) + HenceSkipping + INTToStringValue(job.records))
		}
	}

	return nil
}

func (job *AggregationJob) writeTimeRangeFilterIndex(tick int32) {

	// dummy posting list for metric aggregation

	position := utils.CalculateTickPosition(tick)

	store := datastore.GetStore(datastore.GetStoreName(tick, utils.DummyPostingListStoreName, datastore.HorizontalStore)+utils.HyphenSeparator+INTToStringValue(int(String)), utils.Index, true, true, job.executorEncoder, job.tokenizers[1])

	if store == nil {

		return
	}

	indexBitmap := bitmap.Bitmap{}

	job.executors[job.executorId].KeyBuffers[0] = []byte(job.plugin)

	found, valueBytes, err := store.Get(job.executors[job.executorId].KeyBuffers[0], job.executors[job.executorId].ValueBuffers[0], job.executorEncoder, job.event, job.waitGroup, job.tokenizers[1], false)

	if err != nil {

		store.Delete(job.executors[job.executorId].KeyBuffers[0], job.executorEncoder, job.tokenizers[1])

		job.logger.Error(fmt.Sprintf(utils.ErrorGetKey, string(job.executors[job.executorId].KeyBuffers[0]), store.GetName(), err.Error()))

		found = false
	}

	if found && valueBytes != nil && len(valueBytes) > 0 {

		poolIndex, bufferBytes, err := job.executorDecoder.DecodeSnappy(valueBytes)

		if err != nil {

			return
		}

		defer job.executorDecoder.MemoryPool.ReleaseBytePool(poolIndex)

		indexBitmap = bitmap.FromBytes(bufferBytes)

		indexBitmap.Set(uint32(position))

		poolIndex, bufferBytes = job.executorEncoder.EncodeSnappy(indexBitmap.ToBytes(), utils.MaxValueBytes)

		defer job.executorEncoder.MemoryPool.ReleaseBytePool(poolIndex)

		err = store.Put(job.executors[job.executorId].KeyBuffers[0], bufferBytes, job.executorEncoder, job.tokenizers[1])

		if err != nil {

			return
		}

	} else {

		indexBitmap.Set(uint32(position))

		poolIndex, bufferBytes := job.executorEncoder.EncodeSnappy(indexBitmap.ToBytes(), utils.MaxValueBytes)

		defer job.executorEncoder.MemoryPool.ReleaseBytePool(poolIndex)

		err = store.Put(job.executors[job.executorId].KeyBuffers[0], bufferBytes, job.executorEncoder, job.tokenizers[1])

		if err != nil {

			return
		}
	}
}

/*
	posting list

	key (search token as the actual value) -> event id generated according to tick and part

*/

func (job *AggregationJob) writeStringPostingListIndex(tick int32, intervalMinutes, index int, column, plugin string) error {

	storeName := datastore.GetStoreName(tick, column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + INTToStringValue(int(String)) + utils.HyphenSeparator + INTToStringValue(intervalMinutes)

	store := datastore.GetStore(storeName, job.indexSoreType, true, true, job.encoder, job.tokenizers[1])

	if store == nil {

		return errors.New(fmt.Sprintf(utils.ErrorAcquireStore, storeName))
	}

	for token, eventIds := range job.stringTokens[index] {

		if token == utils.Empty {

			continue
		}

		job.keyBytes = []byte(strings.ToLower(token))

		found, valueBytes, err := store.Get(job.keyBytes, job.valueBytes, job.encoder, job.event, job.waitGroup, job.tokenizers[1], false)

		if err != nil {

			store.Delete(job.keyBytes, job.encoder, job.tokenizers[1])

			job.logger.Error(fmt.Sprintf(utils.ErrorUpdatePostingListIndex, token, store.GetName(), err))

			found = false
		}

		bytePoolIndex := utils.NotAvailable

		poolIndex := utils.NotAvailable

		var int64Values []int64

		var bufferBytes []byte

		if found && len(valueBytes) > 0 {

			poolIndex, int64Values, err = job.decoder.DecodeINT64Values(GetEncoding(valueBytes[0]), GetDataType(valueBytes[0]), valueBytes[1:], token, store.GetName(), 0)

			if err != nil {

				if poolIndex != utils.NotAvailable {

					job.decoder.MemoryPool.ReleaseINT64Pool(poolIndex)
				}

				job.logger.Error(fmt.Sprintf(utils.ErrorUpdatePostingListIndex, token, store.GetName(), err.Error()))

				continue
			}

			valuePoolIndex, values := job.encoder.MemoryPool.AcquireINT64Pool(utils.NotAvailable)

			position := len(int64Values)

			copy(values[:position], int64Values)

			job.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			for eventId := range eventIds {

				if !utils.ContainsINT64Value(int64Values, eventId) {

					values[position] = eventId

					position++
				}
			}

			utils.SortINT64Values(values[:position])

			bytePoolIndex, bufferBytes, err = job.encoder.EncodeINT64Values(values[:position], None, Int64, utils.MaxValueBytes)

			if valuePoolIndex != utils.NotAvailable {

				job.encoder.MemoryPool.ReleaseINT64Pool(valuePoolIndex)
			}
		} else {

			poolIndex, int64Values = job.encoder.MemoryPool.AcquireINT64Pool(len(eventIds))

			position := 0

			for eventId := range eventIds {

				int64Values[position] = eventId

				position++
			}

			utils.SortINT64Values(int64Values[:position])

			bytePoolIndex, bufferBytes, err = job.encoder.EncodeINT64Values(int64Values[:position], None, Int64, utils.MaxValueBytes)

			if poolIndex != utils.NotAvailable {

				job.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)
			}
		}

		if bytePoolIndex != utils.NotAvailable && len(bufferBytes) > 0 {

			err = store.Put(job.keyBytes, bufferBytes, job.encoder, job.tokenizers[1])

			if err != nil {

				job.logger.Error(fmt.Sprintf(utils.ErrorWriteKey, token, store.GetName(), err.Error()))
			}

			job.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)
		}
	}

	return nil
}

func (job *AggregationJob) writeNumericPostingListIndex(tick int32, intervalMinutes, index int, column, plugin string) error {

	storeName := datastore.GetStoreName(tick, column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + INTToStringValue(int(Int64)) + utils.HyphenSeparator + INTToStringValue(intervalMinutes)

	store := datastore.GetStore(storeName, job.indexSoreType, true, true, job.encoder, job.tokenizers[1])

	if store == nil {

		return errors.New(fmt.Sprintf(utils.ErrorAcquireStore, storeName))
	}

	job.keyBytes = make([]byte, utils.AggregationViewIndexColumnLimit)

	for token, eventIds := range job.numericTokens[index] {

		binary.BigEndian.PutUint64(job.keyBytes, uint64(token))

		found, valueBytes, err := store.Get(job.keyBytes, job.valueBytes, job.encoder, job.event, job.waitGroup, job.tokenizers[1], false)

		if err != nil {

			store.Delete(job.keyBytes, job.encoder, job.tokenizers[1])

			job.logger.Error(fmt.Sprintf(utils.ErrorUpdatePostingListIndex, token, store.GetName(), err))

			found = false
		}

		bytePoolIndex := utils.NotAvailable

		poolIndex := utils.NotAvailable

		var int64Values []int64

		var bufferBytes []byte

		if found && len(valueBytes) > 0 {

			poolIndex, int64Values, err = job.decoder.DecodeINT64Values(GetEncoding(valueBytes[0]), GetDataType(valueBytes[0]), valueBytes[1:], INT64ToStringValue(token), store.GetName(), 0)

			if err != nil {

				if poolIndex != utils.NotAvailable {

					job.decoder.MemoryPool.ReleaseINT64Pool(poolIndex)
				}

				job.logger.Error(fmt.Sprintf(utils.ErrorUpdatePostingListIndex, token, store.GetName(), err.Error()))

				continue
			}

			valuePoolIndex, values := job.encoder.MemoryPool.AcquireINT64Pool(utils.NotAvailable)

			position := len(int64Values)

			copy(values[:position], int64Values)

			job.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			for eventId := range eventIds {

				if !utils.ContainsINT64Value(int64Values, eventId) {

					values[position] = eventId

					position++
				}
			}

			utils.SortINT64Values(values[:position])

			bytePoolIndex, bufferBytes, err = job.encoder.EncodeINT64Values(values[:position], None, Int64, utils.MaxValueBytes)

			if valuePoolIndex != utils.NotAvailable {

				job.encoder.MemoryPool.ReleaseINT64Pool(valuePoolIndex)
			}
		} else {

			poolIndex, int64Values = job.encoder.MemoryPool.AcquireINT64Pool(len(eventIds))

			position := 0

			for eventId := range eventIds {

				int64Values[position] = eventId

				position++
			}

			utils.SortINT64Values(int64Values[:position])

			bytePoolIndex, bufferBytes, err = job.encoder.EncodeINT64Values(int64Values[:position], None, Int64, utils.MaxValueBytes)

			if poolIndex != utils.NotAvailable {

				job.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)
			}
		}

		if bytePoolIndex != utils.NotAvailable && len(bufferBytes) > 0 {

			err = store.Put(job.keyBytes, bufferBytes, job.encoder, job.tokenizers[1])

			if err != nil {

				job.logger.Error(fmt.Sprintf(utils.ErrorWriteKey, token, store.GetName(), err.Error()))
			}

			job.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)
		}
	}

	return nil
}

func (job *AggregationJob) getKey(tick, column string) (key string) {

	if job.queryType == utils.VerticalFormat {

		utils.Split(column, utils.KeySeparator, job.tokenizers[0])

		key = tick + utils.KeySeparator + job.plugin + utils.KeySeparator + job.tokenizers[0].Tokens[1] + utils.KeySeparator + UINT16ToStringValue(job.txnPart)

	} else {

		/*
			in-case of event count for every column will be same hence the key is unified using the wild character
		*/
		if strings.HasSuffix(column, utils.KeySeparator+utils.Count) {

			column = "*" + utils.KeySeparator + utils.Count

		}

		key = tick + utils.KeySeparator + column + utils.KeySeparator + UINT16ToStringValue(job.txnPart)

	}

	return
}

/*
	query sender
*/

func (job *AggregationJob) executeQuery(fromTimeMillis, toTimeMillis int64, dataPoints []utils.MotadataMap, intervalMinutes int) {

	defer func() {

		job.queryId = utils.NotAvailable

		job.subQueryId = utils.NotAvailable
	}()

	job.cleanupExecutor = false

	timeLine := utils.MotadataMap{

		query.FromDateTime: fromTimeMillis,

		// from time inclusive to time exclusive
		query.ToDateTime: toTimeMillis,
	}

	dataSources := utils.MotadataMap{}

	dataSources[query.DataPoints] = dataPoints

	if job.queryType == utils.VerticalFormat {

		if job.instanceType != utils.Empty {

			groupPoolIndex, groups := job.encoder.MemoryPool.AcquireStringPool(2)

			defer job.encoder.MemoryPool.ReleaseStringPool(groupPoolIndex)

			groups[0] = datastore.Monitor

			groups[1] = job.instanceType

			dataSources[query.VisualizationResultBy] = groups

		} else {

			groupPoolIndex, groups := job.encoder.MemoryPool.AcquireStringPool(1)

			groups[0] = datastore.Monitor

			defer job.encoder.MemoryPool.ReleaseStringPool(groupPoolIndex)

			dataSources[query.VisualizationResultBy] = groups
		}

	} else {

		groupPoolIndex, groups := job.encoder.MemoryPool.AcquireStringPool(len(job.context.GetMapValue(utils.IndexableColumns)))

		defer job.encoder.MemoryPool.ReleaseStringPool(groupPoolIndex)

		count := 0

		for group := range job.context.GetMapValue(utils.IndexableColumns) {

			groups[count] = group

			count++
		}

		dataSources[query.VisualizationResultBy] = groups

		// only filtered data, aggregated
		if job.fulltextSearchingView {

			dataSources[query.Filters] = job.context.GetMapValue(query.Filters)

		}

	}

	//common for both types of query
	dataSources[query.AdminRole] = utils.Yes

	dataSources.Delete(query.Entities)

	dataSources[utils.Plugins] = job.stringValues

	dataSources[utils.Type] = job.dataStoreType

	job.query[query.VisualizationDataSources] = dataSources

	job.query[query.VisualizationTimeline] = timeLine

	job.query[utils.AggregationJobId] = job.aggregationJobId

	intervals := utils.EventAggregationIntervals //horizontal intervals

	if job.fulltextSearchingView { //intervals are diff in full text views

		intervals = utils.FullTextViewAggregationIntervals
	}

	// in case of horizontal query if the view interval is other than the least available intervals
	// probe the query from the aggregation view of the next least available interval.
	//this will only apply in case of probe flag is true.
	if job.queryType == utils.HorizontalFormat && job.probe && intervalMinutes != intervals[0] {

		index, _ := utils.SearchINTValue(intervals, intervalMinutes)

		index--

		job.query[utils.Interval] = intervals[index]

		job.query[utils.AggregationView] = job.aggregation

	} else { //in other case delete these two keys as the query is reusable map.

		delete(job.query, utils.Interval)

		delete(job.query, utils.AggregationView)
	}

	job.queryId = utils.GenerateQueryId()

	job.subQueryId = utils.GenerateQueryId()

	job.query[query.SubQueryId] = job.subQueryId //to avoid messing up the router queue

	job.query[query.QueryId] = job.queryId //to avoid messing up the router queue

	job.query[utils.QueryPriority] = utils.P1

	bytes, _ := json.Marshal(job.query)

	if utils.DebugEnabled() {

		job.logger.Debug(fmt.Sprintf("sending query for plugin %v to router", job.plugin))

	}

	utils.Requests <- append([]byte{utils.DatastoreRead}, bytes...)

	job.executorId = <-job.JobQueryAcks

	if utils.DebugEnabled() {

		job.logger.Debug(fmt.Sprintf("query execution completed for plugin %v", job.plugin))
	}

	errElementSize := job.executors[job.executorId].GetErrorElementSize()

	if job.executorId == utils.NotAvailable || (errElementSize > 0 && (job.executors[job.executorId].Groups == nil || job.executors[job.executorId].Groups.Len() == 0)) {

		if utils.DebugEnabled() {

			job.logger.Debug(fmt.Sprintf("error %v occurred for plugin %v for time range: %v-%v", utils.CombineErrors(job.executors[job.executorId].GetErrors()[:errElementSize]), job.plugin, timeLine[query.FromDateTime], timeLine[utils.ToDateTime]))
		}

		job.cleanUp()

		return
	}

	if utils.DebugEnabled() {

		job.logger.Debug("query execution completed successfully....")
	}

	tick := utils.UnixMillisToSeconds(fromTimeMillis)

	// result will be written to aggregation store
	job.write(tick, intervalMinutes)

	poolIndex, groups := job.encoder.MemoryPool.AcquireStringPool(job.executors[job.executorId].GroupColumnElementSize)

	defer job.encoder.MemoryPool.ReleaseStringPool(poolIndex)

	copy(groups, job.executors[job.executorId].GroupColumns[:job.executors[job.executorId].GroupColumnElementSize])

	job.cleanUp()

	//writing posting list for event source only for data security in full text search view
	if job.queryType == utils.HorizontalFormat && job.fulltextSearchingView {

		for i := range groups {

			if len(job.stringTokens[i]) > 0 && groups[i] == utils.EventSource {

				err := job.writeStringPostingListIndex(tick, intervalMinutes, i, groups[i], job.aggregation)

				if err != nil {

					job.logger.Error(fmt.Sprintf("failed to update the string posting list index, reason :%v", err.Error()))
				}
			}

		}

	}

	//avoid writing posting list for message contains view
	if job.queryType == utils.HorizontalFormat && !job.fulltextSearchingView {

		// posting list for horizontal format query
		for i := range groups {

			if len(job.stringTokens[i]) > 0 {

				err := job.writeStringPostingListIndex(tick, intervalMinutes, i, groups[i], job.aggregation)

				if err != nil {

					job.logger.Error(fmt.Sprintf("failed to update the string posting list index, reason :%v", err.Error()))
				}
			}

			if len(job.numericTokens[i]) > 0 {

				err := job.writeNumericPostingListIndex(tick, intervalMinutes, i, groups[i], job.aggregation)

				if err != nil {

					job.logger.Error(fmt.Sprintf("failed to update the int64 posting list index, reason :%v", err.Error()))
				}
			}
		}
	}
}

// resetContext clears all job state variables and prepares the job for a new operation.
// This method ensures that no state from previous operations affects the current one.
//
// The method resets the following categories of state:
// 1. String identifiers (instanceType, queryType, aggregation, metric)
// 2. Numeric identifiers and counters
// 3. Boolean flags for operation modes
// 4. Data structure maps and collections
//
// This method is called at the beginning of each aggregation operation to ensure clean state.
func (job *AggregationJob) resetContext() {
	// Reset string identifiers to empty state
	job.instanceType = utils.Empty
	job.queryType = utils.Empty

	// Reset datastore type to none
	job.dataStoreType = utils.None

	// Reset cleanup flag
	job.cleanupExecutor = false

	// Clear aggregation context
	job.context = nil

	// Reset executor identifier
	job.executorId = utils.NotAvailable

	// Reset column size counters
	job.numericColumnElementSize = 0
	job.stringColumnElementSize = 0

	// Reset aggregation identifier
	job.aggregation = utils.Empty

	// Clear all token tracking maps to prevent memory leaks and state pollution
	for i := range job.stringTokens {
		clear(job.stringTokens[i])
		clear(job.numericTokens[i])
	}

	// Reset metric identifier
	job.metric = utils.Empty

	// Reset boolean operation flags
	job.garbage = false
	job.probe = false

	// Clear entity keys map to prevent memory leaks
	clear(job.entityKeys)
}

// setEntities discovers and loads entity keys for the current metric from the appropriate datastore.
// This method is crucial for vertical aggregations as it establishes the entity relationships
// that will be used for aggregation operations.
//
// The method handles two scenarios:
// 1. Garbage metrics: Uses current plugin store for real-time entity discovery
// 2. Regular metrics: Uses historical vertical stores for entity discovery
//
// Entity Discovery Strategy:
// - For garbage metrics on current day: Query the plugin's live store
// - For historical data: Query the specific day's vertical store
// - Populate entity keys map for use in aggregation queries
//
// Parameters:
// - tick: The time tick (32-bit seconds) for which to discover entities
//
// Returns:
// - bool: true if entities were found and loaded, false otherwise
func (job *AggregationJob) setEntities(tick int32) bool {
	found := false

	// Initialize entity keys map for this metric if not already present
	if _, found = job.entityKeys[job.metric]; !found {
		job.entityKeys[job.metric] = map[string]string{}
	}

	// Handle garbage metrics with current-day entity discovery
	if job.garbage && utils.SecondsToDate(tick) == utils.SecondsToDate(utils.UnixToSeconds(time.Now().Unix())) {
		// For garbage metrics on the current day, use the plugin's live store
		// This ensures we get the most recent entity information
		if datastore.IsStoreAvailable(job.plugin) {
			if store := datastore.GetStore(job.plugin, utils.None, false, true, job.encoder, job.tokenizers[1]); store != nil {
				if job.setMetricEntityKeys(store) {
					found = true
				}
			}
		}
	} else {
		// Handle regular metrics with historical entity discovery
		// Use the vertical store for the specific day to get entity information
		storeName := datastore.GetStoreName(tick, job.plugin, datastore.VerticalStore)

		if datastore.IsStoreAvailable(storeName) {
			if store := datastore.GetStore(storeName, utils.None, false, true, job.encoder, job.tokenizers[1]); store != nil {
				if job.setMetricEntityKeys(store) {
					found = true
				}
			}
		}
	}

	return found
}

// cleanUp performs cleanup operations for the aggregation job, particularly for executor resources.
// This method is called when an error occurs during aggregation processing to ensure proper
// resource cleanup and prevent memory leaks.
//
// The method performs the following operations:
// 1. Checks if cleanup is needed (valid executor ID and not already cleaned up)
// 2. Notifies the executor to clean up its resources
// 3. Handles any panics that occur during cleanup
// 4. Sets the cleanup flag to prevent duplicate cleanup attempts
//
// This method is critical for maintaining system stability during error conditions.
func (job *AggregationJob) cleanUp() {
	// Only perform cleanup if we have a valid executor and haven't already cleaned up
	if job.executorId != utils.NotAvailable && !job.cleanupExecutor {
		// Set up panic recovery for cleanup operations
		defer func() {
			// Handle any panics that occur during cleanup
			if r := recover(); r != nil {
				// Capture and log the stack trace for debugging
				stackTraceBytes := make([]byte, 1<<20)
				job.logger.Error(fmt.Sprintf("failed to cleanup executor contexts, reason %v", r))
				job.logger.Error(fmt.Sprintf(utils.ErrorJobStackTrace, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
			}

			// Set the cleanup flag to prevent duplicate cleanup attempts
			job.cleanupExecutor = true
		}()

		// Notify the executor to clean up its resources
		// The 'true' parameter indicates this is a cleanup notification
		job.executors[job.executorId].NotifySubscriber(true)
	}
}

// setMetricEntityKeys extracts entity keys for the current metric from the specified store.
// This method performs the actual entity discovery by querying the store for keys
// that contain the metric name and filtering out time-based keys.
//
// Entity Key Filtering:
// - Includes: Entity-specific metric keys (e.g., "metric.name^entity.id")
// - Excludes: Time-based metric keys (e.g., "metric.name^time.suffix")
//
// The filtering ensures that only actual entity relationships are captured,
// not temporal data points that would skew aggregation results.
//
// Parameters:
// - store: The storage instance to query for entity keys
//
// Returns:
// - bool: true if entity keys were found and loaded, false otherwise
func (job *AggregationJob) setMetricEntityKeys(store *storage.Store) bool {
	// Initialize local entity keys map
	entityKeys := map[string]string{}

	// Query the store for all keys containing the metric name
	buffers, err := store.GetContainKeys([]byte(job.metric), false)
	if err != nil {
		job.logger.Error(fmt.Sprintf("error occurred while setting metric entity keys for store- %v, keyBytes - %v",
			store.GetName(), job.metric))
	}

	// Process the returned key buffers
	if len(buffers) > 0 {
		for index := 0; index < len(buffers); index++ {
			// Filter out time-based metric keys to keep only entity keys
			// Time-based keys have specific suffixes that identify them as temporal data
			keyWithoutTimestamp := buffers[index][:bytes2.LastIndexByte(buffers[index], utils.KeySeparatorByte)]

			if !bytes2.HasSuffix(keyWithoutTimestamp, utils.MetricTimeKeySuffixBytes) {
				// This is an entity key, not a time key - include it
				entityKeys[string(buffers[index])] = job.plugin
			}
		}

		// Store the discovered entity keys for this metric
		job.entityKeys[job.metric] = entityKeys
		return true
	}

	return false
}

// === TRANSACTION MANAGEMENT FUNCTIONS ===
// These functions handle transactional operations for aggregated data persistence

// cleanupTxn resets transaction state to prepare for a new transaction.
// This method ensures that transaction buffers and tracking structures are clean
// before starting a new batch of write operations.
//
// The method resets:
// - Transaction offset to 4 bytes (after header)
// - Transaction partition to unassigned state
// - Transaction entries map to prevent memory leaks
func (job *AggregationJob) cleanupTxn() {
	// Reset transaction offset to 4 bytes (space for transaction header)
	job.txnOffset = 4

	// Reset transaction partition to unassigned state
	job.txnPartition = -1

	// Clear transaction entries map to prevent memory leaks
	clear(job.txnEntries)
}

// writeTxn adds a key-value pair to the current transaction buffer.
// This method handles the low-level details of transaction formatting including
// checksums, length prefixes, and buffer management.
//
// Transaction Format:
// [4-byte key length][key bytes][checksum][4-byte value length][value bytes]
//
// Parameters:
// - keyBytes: The key data to be written
// - bufferBytes: The value data to be written (includes space for checksum and length)
//
// Returns:
// - error: Any error that occurred during the write operation
func (job *AggregationJob) writeTxn(keyBytes, bufferBytes []byte) (err error) {
	// Calculate total size needed for this transaction entry
	requiredSize := len(bufferBytes) + 4 + len(utils.EOTBytes) + len(keyBytes) + job.txnOffset

	// Expand transaction buffer if needed
	if requiredSize > len(job.txnBufferBytes) {
		job.logger.Info(fmt.Sprintf("remapping anonymous txn buffers with current length %v and required size %v",
			len(job.txnBufferBytes), requiredSize))
		job.txnBufferBytes = utils.RemapBytes(job.txnBufferBytes, requiredSize)
	}

	// Add checksum to the beginning of the value buffer
	copy(bufferBytes, utils.CheckSumV1Bytes)

	// Add value length after the checksum (4 bytes into the buffer)
	WriteINT32Value(int32(len(bufferBytes)-utils.MaxValueBytes), 4, bufferBytes)

	// Write key length to the transaction buffer
	WriteINT32Value(int32(len(keyBytes)), 0, job.int32Bytes)
	copy(job.txnBufferBytes[job.txnOffset:], job.int32Bytes)
	job.txnOffset += 4

	// Write key bytes to the transaction buffer
	copy(job.txnBufferBytes[job.txnOffset:], keyBytes)
	job.txnOffset += len(keyBytes)

	// Record the offset where value data will be written
	offset := job.txnOffset

	// Write value bytes to the transaction buffer
	copy(job.txnBufferBytes[job.txnOffset:], bufferBytes)
	job.txnOffset += len(bufferBytes)

	// Track this entry for transaction management
	job.txnEntries[utils.GetHash64(keyBytes)] = utils.TxnEntry{
		Length: len(bufferBytes),
		Offset: offset,
	}

	return
}

// commit finalizes and persists the current transaction to storage.
// This method completes the transaction by adding termination markers,
// updating the transaction header, and delegating to the storage layer.
//
// Transaction Finalization Process:
// 1. Add end-of-transaction marker
// 2. Update transaction header with total length
// 3. Commit to storage with all transaction entries
// 4. Clean up transaction state
//
// Parameters:
// - part: The partition number for this transaction
// - tick: The time tick associated with this transaction
//
// Returns:
// - error: Any error that occurred during the commit operation
func (job *AggregationJob) commit(part int, tick int32) error {
	// Ensure transaction cleanup regardless of success or failure
	defer job.cleanupTxn()

	// Add end-of-transaction marker to signal completion
	copy(job.txnBufferBytes[job.txnOffset:], utils.EOTBytes)
	job.txnOffset += len(utils.EOTBytes)

	// Write the total transaction length to the header (first 4 bytes)
	// Length excludes the 4-byte header itself
	WriteINT32Value(int32(job.txnOffset-4), 0, job.txnBufferBytes)

	// Commit the transaction to storage
	err := job.store.CommitTxn(
		job.txnBufferBytes[:job.txnOffset], // Transaction buffer with all data
		job.txnEntries,                     // Transaction entry metadata
		job.encoder,                        // Encoder for any additional serialization
		job.txnPartition,                   // Target partition for this transaction
	)

	// Log any errors that occur during commit
	if err != nil {
		job.logger.Error(fmt.Sprintf("error %v occurred while committing txn for store %v, part %v and tick %v",
			err, job.store.GetName(), part, tick))
	}

	return err
}
