/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-21			 D<PERSON>val <PERSON>ra			Motad<PERSON>-5452  Refactored Test Case For NetRouteMetric DatastoreType
* 2025-05-05			 Swapnil A. Dave		M<PERSON>ADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-06-04             <PERSON><PERSON>l Shah            MOTADATA-5780 Test Case Refactoring
* 2025-06-24			 <PERSON><PERSON><PERSON>		    MOTADATA-6543 Added test cases for aggregation store, ObjectStatusFlapMetric store retention
* 2025-06-24			 <PERSON><PERSON><PERSON>ra			<PERSON>-6639  Refactoring For Retention Check In Query Parsing

 */

package job

import (
	"github.com/stretchr/testify/assert"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/utils"
	"os"
	"strings"
	"testing"
	"time"
)

func TestGetStoreEpochDateTime(t *testing.T) {

	assertions := assert.New(t)

	tick, err := getTimeTick("12102023", utils.StoreDateFormat)

	assertions.Nil(err)

	assertions.NotNil(tick)

	assertions.True(tick.Unix() < time.Now().Unix())

	assertions.True(tick.Unix() < time.Now().Unix())

}

func TestLogRetentionJob(t *testing.T) {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions := assert.New(t)

	logger := utils.NewLogger("test", "test1")

	logger.Info("info message")

	logger.Error("error message")

	logger.Debug("error message")

	logger.Trace("trace message")

	utils.EnvironmentType = utils.DatastoreTestEnvironment

	utils.SetLogLevel(1)

	utils.LogRetentionJobDays = 0

	RetentionJobTimer = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute(), time.Now().Second()+3, time.Now().Nanosecond(), time.Local)

	retentionJob := NewRetentionJob()

	retentionJob.Start()

	time.Sleep(time.Second * 4)

	directories, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	assertions.Nil(err)

	assertions.NotNil(directories)

	for _, directory := range directories {

		if directory.Name() != "test1" {

			continue

		}

		files, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory + utils.PathSeparator + directory.Name())

		assertions.Nil(err)

		assertions.Equal(0, len(files))
	}

}

func TestLogRetentionJobMultipleDays(t *testing.T) {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions := assert.New(t)

	utils.SetLogLevel(1)

	logger := utils.NewLogger("test", "test1")

	logger.Info("info message")

	logger.Error("error message")

	logger.Debug("error message")

	logger.Trace("trace message")

	utils.EnvironmentType = utils.DatastoreTestEnvironment

	utils.LogRetentionJobDays = 1

	directories, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	for _, directory := range directories {

		if directory.Name() != "test1" {

			continue

		}

		path := utils.CurrentDir + utils.PathSeparator + utils.LogDirectory + utils.PathSeparator + directory.Name()

		files, err := os.ReadDir(path)

		assertions.Nil(err)

		tokens := strings.Split(files[0].Name(), utils.SpaceSeparator)

		currentTime := utils.UnixSeconds(tokens[0], "Local")

		timestamp := time.Unix(currentTime, 0)

		// 1 day after

		advanceTime := timestamp.AddDate(0, 0, 1)

		var day string

		day = codec.ToString(advanceTime.Day())

		if len(day) != 2 {

			day = "0" + day
		}

		tokens[0] = codec.ToString(day) + utils.HyphenSeparator + advanceTime.Month().String() + utils.HyphenSeparator + codec.ToString(advanceTime.Year())

		advanceDate := strings.Join(tokens, utils.SpaceSeparator)

		err = os.WriteFile(path+utils.PathSeparator+advanceDate, []byte{}, 0666)

		assertions.Nil(err)

		//1 day before

		beforeTime := timestamp.AddDate(0, 0, -1)

		day = codec.ToString(beforeTime.Day())

		if len(day) != 2 {

			day = "0" + day
		}

		tokens[0] = day + utils.HyphenSeparator + beforeTime.Month().String() + utils.HyphenSeparator + codec.ToString(beforeTime.Year())

		beforeDate := strings.Join(tokens, utils.SpaceSeparator)

		err = os.WriteFile(path+utils.PathSeparator+beforeDate, []byte{}, 0666)

		assertions.Nil(err)

		//2 day before

		day = codec.ToString(beforeTime.Day())

		if len(day) != 2 {

			day = "0" + day
		}

		tokens[0] = day + utils.HyphenSeparator + beforeTime.Month().String() + utils.HyphenSeparator + codec.ToString(beforeTime.Year())

		beforeDate = strings.Join(tokens, utils.SpaceSeparator)

		err = os.WriteFile(path+utils.PathSeparator+beforeDate, []byte{}, 0666)

		assertions.Nil(err)

	}

	RetentionJobTimer = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute(), time.Now().Second()+3, time.Now().Nanosecond(), time.Local)

	retentionJob := NewRetentionJob()

	retentionJob.Start()

	time.Sleep(time.Second * 4)

	directories, err = os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	assertions.Nil(err)

	assertions.NotNil(directories)

	for _, directory := range directories {

		if directory.Name() != "test1" {

			continue

		}

		files, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory + utils.PathSeparator + directory.Name())

		assertions.Nil(err)

		//2 files current day and advance day
		assertions.Equal(2, len(files))
	}

}

func TestStoreRetentionJob(t *testing.T) {

	err := os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	err = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	utils.StoreRetentionJobs = make(chan map[int]map[string]int, 10000)

	utils.SetLogLevel(1)

	assertions := assert.New(t)

	event := make(map[int]map[string]int)

	rawRetentionTime := 1

	aggregationRetentionTime := 2

	// raw datastore type
	event[int(utils.Log)] = map[string]int{

		utils.Raw: rawRetentionTime,

		utils.Aggregated: aggregationRetentionTime,
	}

	event[int(utils.Flow)] = map[string]int{

		utils.Raw: rawRetentionTime,

		utils.Aggregated: aggregationRetentionTime,
	}

	event[int(utils.PerformanceMetric)] = map[string]int{

		utils.Raw: rawRetentionTime,

		utils.Aggregated: aggregationRetentionTime,
	}

	event[int(utils.MetricPolicy)] = map[string]int{

		utils.Raw: rawRetentionTime,
	}

	event[int(utils.Trap)] = map[string]int{

		utils.Raw: rawRetentionTime,

		utils.Aggregated: aggregationRetentionTime,
	}

	event[int(utils.Audit)] = map[string]int{

		utils.Raw: rawRetentionTime,
	}

	event[int(utils.Notification)] = map[string]int{

		utils.Raw: rawRetentionTime,
	}

	event[int(utils.NetRouteMetric)] = map[string]int{

		utils.Raw: rawRetentionTime,
	}

	DefaultRetentionJobTick = 1

	DefaultHealthMetricRetentionJobTick = 3

	encoder := codec.NewEncoder(utils.NewMemoryPool(2, utils.MaxPoolLength, false, utils.DefaultBlobPools))

	RetentionJobTimer = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute(), time.Now().Second()+3, time.Now().Nanosecond(), time.Local)

	retentionJob := NewRetentionJob()

	retentionJob.shutdown = true

	retentionJob.Start()

	retentionJob.updateStoreRetentionContext(event)

	//-----------------------------Raw Retention------------------------------------------------------//

	store := datastore.GetStore("24102023-PerformanceMetric", utils.PerformanceMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-PerformanceMetric", utils.PerformanceMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-NetRouteMetric", utils.NetRouteMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-NetRouteMetric", utils.NetRouteMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-NetRouteStatusMetric", utils.NetRouteStatusMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-NetRouteStatusMetric", utils.NetRouteStatusMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-NetRouteStatusMetricAggregation", utils.NetRouteStatusMetricAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-NetRouteStatusMetricAggregation", utils.NetRouteStatusMetricAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-StatusMetric", utils.ObjectStatusMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-StatusMetric", utils.ObjectStatusMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-Audit", utils.Audit, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-Audit", utils.Audit, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-Notification", utils.Notification, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-Notification", utils.Notification, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-PolicyFlapHistory", utils.PolicyFlapHistory, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-PolicyFlapHistory", utils.PolicyFlapHistory, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-StatusFlapHistory", utils.StatusFlapHistory, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-StatusFlapHistory", utils.StatusFlapHistory, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-TrapFlapHistory", utils.TrapFlapHistory, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-TrapFlapHistory", utils.TrapFlapHistory, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-Log", utils.Log, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-Log", utils.Log, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-Flow", utils.Flow, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-Flow", utils.Flow, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-Trap", utils.Trap, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-Trap", utils.Trap, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-MetricPolicy", utils.MetricPolicy, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-MetricPolicy", utils.MetricPolicy, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-EventPolicy", utils.EventPolicy, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-EventPolicy", utils.EventPolicy, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-CorrelationMetric", utils.CorrelationMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-CorrelationMetric", utils.CorrelationMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-RunbookWorklog", utils.RunbookWorklog, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-RunbookWorklog", utils.RunbookWorklog, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-PolicyResult", utils.PolicyResult, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-PolicyResult", utils.PolicyResult, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-None", utils.None, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-None", utils.None, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-Config", utils.StaticMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-Config", utils.StaticMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23092023-Config-history", utils.ConfigHistory, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23092023-Config-corrupted", utils.ConfigHistory, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store.Close(encoder)

	store = datastore.GetStore("23092023-compliance", utils.Compliance, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("25092023-compliance", utils.Compliance, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23092023-Config-mappings", utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = retentionJob.deleteStores(time.Date(2023, 10, 25, 0, 0, 0, 0, time.Local))

	assertions.Nil(err)

	dirs, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Nil(err)

	assertions.Equal(24, len(dirs))

	//-----------------------------------Aggregation Retention---------------------------------//

	store = datastore.GetStore("23102023-MetricAggregation", utils.MetricAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("22102023-MetricAggregation", utils.MetricAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-LogAggregation", utils.LogAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("22102023-LogAggregation", utils.LogAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-FlowAggregation", utils.FlowAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("22102023-FlowAggregation", utils.FlowAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-PolicyAggregation", utils.PolicyAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("22102023-PolicyAggregation", utils.PolicyAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-TrapAggregation", utils.TrapAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("22102023-TrapAggregation", utils.TrapAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-Mapping", utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("22102023-Mapping", utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-Health", utils.HealthMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("21102023-Health", utils.HealthMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = retentionJob.deleteStores(time.Date(2023, 10, 25, 0, 0, 0, 0, time.Local))

	assertions.Nil(err)

	dirs, err = os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Nil(err)

	assertions.Equal(31, len(dirs))

	datastore.Close()

}

func TestIndexStoreRetentionJob(t *testing.T) {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	utils.SetLogLevel(1)

	utils.StoreRetentionJobs = make(chan map[int]map[string]int, 10000)

	assertions := assert.New(t)

	event := make(map[int]map[string]int)

	rawRetentionTime := 1

	aggregationRetentionTime := 2

	DefaultRetentionJobTick = 3

	DefaultHealthMetricRetentionJobTick = 3

	DefaultComplianceJobTick = 3

	utils.IdleStoreDetectionThresholdSeconds = 0

	// raw datastore type
	event[int(utils.Log)] = map[string]int{

		utils.Raw: rawRetentionTime,

		utils.Aggregated: aggregationRetentionTime,
	}

	event[int(utils.Flow)] = map[string]int{

		utils.Raw: rawRetentionTime,

		utils.Aggregated: aggregationRetentionTime,
	}

	event[int(utils.PerformanceMetric)] = map[string]int{

		utils.Raw: rawRetentionTime,

		utils.Aggregated: aggregationRetentionTime,
	}

	event[int(utils.MetricPolicy)] = map[string]int{

		utils.Raw: rawRetentionTime,
	}

	event[int(utils.Trap)] = map[string]int{

		utils.Raw: rawRetentionTime,

		utils.Aggregated: aggregationRetentionTime,
	}

	event[int(utils.Audit)] = map[string]int{

		utils.Raw: rawRetentionTime,
	}

	event[int(utils.Notification)] = map[string]int{

		utils.Raw: rawRetentionTime,
	}

	encoder := codec.NewEncoder(utils.NewMemoryPool(2, utils.MaxPoolLength, false, utils.DefaultBlobPools))

	RetentionJobTimer = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute(), time.Now().Second()+3, time.Now().Nanosecond(), time.Local)

	retentionJob := NewRetentionJob()

	retentionJob.shutdown = true

	retentionJob.Start()

	retentionJob.updateStoreRetentionContext(event)

	store := datastore.GetStore("searchableMetric", utils.MetricIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023"+utils.HyphenSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.DummyPostingListStoreName+utils.HyphenSeparator+codec.INTToStringValue(int(codec.String)), utils.Index, true, true, encoder, tokenizer)

	assertions.NotNil(store) //should not get deleted

	store = datastore.GetStore("23102023-1-column-plugin-policy.flap.history-112-5", utils.PolicyIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-1-column-plugin-policy.flap.history-112-5", utils.PolicyIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-1-column-plugin-status.flap.history-112", utils.MetricIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-1-column-plugin-status.flap.history-112", utils.MetricIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("22102023-1-column-plugin-logaggregation-112-5", utils.LogIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store) //should get deleted

	store = datastore.GetStore("23102023-1-column-plugin-logaggregation-112-5", utils.LogIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store) //should not get deleted

	store = datastore.GetStore("22102023-1-column-plugin-flowAggregation-112-5", utils.FlowIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store) //should get deleted

	store = datastore.GetStore("23102023-1-column-plugin-flowAggregation-112-5", utils.FlowIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store) //should get deleted

	store = datastore.GetStore("22102023-1-column-plugin-trapAggregation-112-5", utils.TrapIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-1-column-plugin-trapAggregation-112-5", utils.TrapIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err := retentionJob.deleteStores(time.Date(2023, 10, 25, 0, 0, 0, 0, time.Local))

	assertions.Nil(err)

	dirs, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Nil(err)

	assertions.Equal(7, len(dirs))

	///-----------------------------------Raw retention---------------------------------------////

	store = datastore.GetStore("23102023-1-column-plugin-log-112", utils.LogIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-1-column-plugin-log-112", utils.LogIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-1-column-plugin-flow-112", utils.FlowIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-1-column-plugin-flow-112", utils.FlowIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-1-column-plugin-trap-112", utils.TrapIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-1-column-plugin-trap-112", utils.TrapIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-1-column-plugin-notification-112", utils.NotificationIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-1-column-plugin-notification-112", utils.NotificationIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("21102023-1-column-plugin-health-112", utils.HealthMetricIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("22102023-1-column-plugin-health-112", utils.HealthMetricIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("22102023-1-column-plugin-audit-112", utils.AuditIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("21102023-1-column-plugin-compliance-112", utils.ComplianceIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("22102023-1-column-plugin-compliance-112", utils.ComplianceIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = retentionJob.deleteStores(time.Date(2023, 10, 25, 0, 0, 0, 0, time.Local))

	assertions.Nil(err)

	dirs, err = os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Nil(err)

	assertions.Equal(13, len(dirs))

	datastore.Close()

}

func TestClosedStoreRetentionJob(t *testing.T) {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	utils.StoreRetentionJobs = make(chan map[int]map[string]int, 10000)

	utils.SetLogLevel(1)

	assertions := assert.New(t)

	event := make(map[int]map[string]int)

	retentionTime := 1

	// raw datastore type
	event[int(utils.Log)] = map[string]int{

		utils.Raw: retentionTime,

		utils.Aggregated: retentionTime,
	}

	event[int(utils.Flow)] = map[string]int{

		utils.Raw: retentionTime,

		utils.Aggregated: retentionTime,
	}

	event[int(utils.PerformanceMetric)] = map[string]int{

		utils.Raw: retentionTime,

		utils.Aggregated: retentionTime,
	}

	event[int(utils.MetricPolicy)] = map[string]int{

		utils.Raw: retentionTime,
	}

	event[int(utils.Trap)] = map[string]int{

		utils.Raw: retentionTime,

		utils.Aggregated: retentionTime,
	}

	event[int(utils.Audit)] = map[string]int{

		utils.Raw: retentionTime,
	}

	event[int(utils.Notification)] = map[string]int{

		utils.Raw: retentionTime,
	}

	DefaultRetentionJobTick = 1

	datastore.Init()

	encoder := codec.NewEncoder(utils.NewMemoryPool(2, utils.MaxPoolLength, false, utils.DefaultBlobPools))

	store := datastore.GetStore("24102023-PerformanceMetric", utils.PerformanceMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store.Close(encoder)

	datastore.RemoveStore("24102023-PerformanceMetric")

	RetentionJobTimer = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute(), time.Now().Second()+3, time.Now().Nanosecond(), time.Local)

	retentionJob := NewRetentionJob()

	retentionJob.Start()

	utils.StoreRetentionJobs <- event

	time.Sleep(time.Second * 7)

	dirs, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Nil(err)

	assertions.Equal(0, len(dirs))
}

func TestObjectStatusFlapMetricRetention(t *testing.T) {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	utils.StoreRetentionJobs = make(chan map[int]map[string]int, 10000)

	utils.SetLogLevel(1)

	assertions := assert.New(t)

	event := make(map[int]map[string]int)

	rawRetentionTime := 1

	aggregationRetentionTime := 2

	event[int(utils.PerformanceMetric)] = map[string]int{

		utils.Raw: rawRetentionTime,

		utils.Aggregated: aggregationRetentionTime,
	}

	DefaultRetentionJobTick = 1

	DefaultHealthMetricRetentionJobTick = 3

	encoder := codec.NewEncoder(utils.NewMemoryPool(2, utils.MaxPoolLength, false, utils.DefaultBlobPools))

	RetentionJobTimer = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute(), time.Now().Second()+3, time.Now().Nanosecond(), time.Local)

	retentionJob := NewRetentionJob()

	retentionJob.shutdown = true

	retentionJob.Start()

	retentionJob.updateStoreRetentionContext(event)

	//-----------------------------Raw Retention------------------------------------------------------//

	store := datastore.GetStore("24102023-PerformanceMetric", utils.ObjectStatusFlapMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("15102023-PerformanceMetric", utils.ObjectStatusFlapMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err := retentionJob.deleteStores(time.Date(2023, 10, 25, 0, 0, 0, 0, time.Local))

	assertions.Nil(err)

	dirs, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Nil(err)

	assertions.Equal(1, len(dirs))

	store = datastore.GetStore("15102023-PerformanceMetric", utils.ObjectStatusFlapMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("11102023-PerformanceMetric", utils.ObjectStatusFlapMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	event[int(utils.PerformanceMetric)] = map[string]int{

		utils.Raw: 5,

		utils.Aggregated: 1,
	}

	retentionJob.updateStoreRetentionContext(event)

	err = retentionJob.deleteStores(time.Date(2023, 10, 25, 0, 0, 0, 0, time.Local))

	assertions.Nil(err)

	dirs, err = os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Nil(err)

	assertions.Equal(2, len(dirs))

	datastore.Close()

}

func TestHorizontalAggregationRetention(t *testing.T) {

	err := os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	err = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	utils.StoreRetentionJobs = make(chan map[int]map[string]int, 10000)

	utils.SetLogLevel(1)

	assertions := assert.New(t)

	event := make(map[int]map[string]int)

	rawRetentionTime := 1

	aggregationRetentionTime := 2

	event[int(utils.PerformanceMetric)] = map[string]int{

		utils.Raw: rawRetentionTime,

		utils.Aggregated: aggregationRetentionTime,
	}

	DefaultRetentionJobTick = 1

	DefaultHealthMetricRetentionJobTick = 3

	encoder := codec.NewEncoder(utils.NewMemoryPool(2, utils.MaxPoolLength, false, utils.DefaultBlobPools))

	RetentionJobTimer = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute(), time.Now().Second()+3, time.Now().Nanosecond(), time.Local)

	retentionJob := NewRetentionJob()

	retentionJob.shutdown = true

	retentionJob.Start()

	retentionJob.updateStoreRetentionContext(event)

	plugin := "499998-policy.flap"

	datastore.AddHorizontalAggregation(plugin, plugin+utils.AggregationSeparator+"0", nil)

	plugin = "499997-event.history"

	datastore.AddHorizontalAggregation(plugin, plugin+utils.AggregationSeparator+"123456789#@#0", nil)

	//-----------------------------Raw Retention------------------------------------------------------//

	store := datastore.GetStore("24102023-1-499998-policy.flap@@@0-5", utils.PolicyAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-1-499998-policy.flap@@@0-5", utils.PolicyAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	datastore.RemoveStore(store.GetName())

	store.Close(encoder)

	err = os.Remove(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + "23102023-1-499998-policy.flap@@@0-5" + utils.PathSeparator + utils.MetadataFile)

	store = datastore.GetStore("23102023-1-policy.id-499998-policy.flap@@@1-112-5", utils.PolicyIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("2315102023-1-499998-policy.flap@@@1-5", utils.PolicyAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("24102023-1-499997-event.history@@@123456789#@#0-5", utils.PolicyAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-1-499997-event.history@@@123456789#@#1-5", utils.PolicyAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore("23102023-1-policy.id-499997-event.history@@@123456789#@#1-112-5", utils.PolicyIndex, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = retentionJob.deleteStores(time.Date(2023, 10, 25, 0, 0, 0, 0, time.Local))

	assertions.Nil(err)

	dirs, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Nil(err)

	assertions.Equal(2, len(dirs))
}

func TestCleanup(t *testing.T) {

	assertions := assert.New(t)

	defer func() {

		bytes, err := utils.ReadLogFile("Retention Job", "job")

		assertions.Nil(err)

		assertions.True(strings.Contains(string(bytes), "restarting retention job"))
	}()

	defer cleanup(time.NewTicker(time.Second * 5))

	panic("error")
}
