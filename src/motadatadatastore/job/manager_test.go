/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*Change Logs:
* 2025-06-10			 Swapnil <PERSON><PERSON> Dave		MOTADATA-6392  manager tests refactored and moved to this file with some additional tests.
* 2025-06-23             Dhaval Bera            MOTADATA-6642  Refactoring Test cases

 */

package job

import (
	"encoding/json"
	"motadatadatastore/cache"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/datastore/writer"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func testSetup() *Manager {

	shutdownPending := false

	for i := 0; i < utils.AggregationJobs; i++ {

		if !aggregationJobs[i].shutdown {

			shutdownPending = true

			aggregationJobs[i].shutdown = true

			aggregationJobs[i].ShutdownNotifications <- true
		}
	}

	if shutdownPending {

		time.Sleep(time.Second * 5)

		utils.AggregationJobWriteNotifications = nil

		utils.AggregationJobWriteNotifications = make([]chan utils.MotadataMap, utils.AggregationJobs)

		for i := 0; i < utils.AggregationJobs; i++ {

			utils.AggregationJobWriteNotifications[i] = make(chan utils.MotadataMap, 5)
		}
	}

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	manager := NewManager()

	manager.shutdown = true

	manager.Start()

	utils.EnvironmentType = utils.DatastoreDevEnvironment

	return manager
}

func testSetupDestroy() {

	utils.EnvironmentType = utils.DatastoreTestEnvironment
}

// TestUnifyAggregationsEmpty tests the unifyAggregations method with empty aggregations
func TestUnifyAggregationsEmpty(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	// Call unifyAggregations with empty aggregations
	plugin := "test-plugin"

	aggregations := map[string]utils.MotadataMap{}

	result := manager.unifyAggregations(plugin, aggregations)

	// Assert that the result is empty
	assert.Empty(t, result, "Expected empty result for empty aggregations")
}

// TestUnifyAggregationsNewView tests the unifyAggregations method with a new view
func TestUnifyAggregationsNewView(t *testing.T) {
	// Create a new manager
	manager := testSetup()

	defer testSetupDestroy()

	manager.indices = map[string]int{
		"test-plugin": 1,
	}

	// Create test data
	plugin := "test-plugin"

	aggregations := map[string]utils.MotadataMap{
		"test-view": {
			utils.Type: float64(0),
			utils.IndexableColumns: utils.MotadataMap{
				"column1": struct{}{},
			},
			"metric1": float64(1),
		},
	}

	// Call unifyAggregations
	result := manager.unifyAggregations(plugin, aggregations)

	// Assert that the result is empty (no affected aggregations)
	assert.Empty(t, result, "Expected empty result for new view")

	// Assert that the view was added to qualifiedProbes
	assert.NotEmpty(t, manager.qualifiedProbes, "Expected qualifiedProbes to be populated")
}

// TestUnifyAggregationsExistingView tests the unifyAggregations method with an existing view
func TestUnifyAggregationsExistingView(t *testing.T) {
	// Create a new manager
	manager := testSetup()
	defer testSetupDestroy()

	manager.indices = map[string]int{
		"test-plugin": 1,
	}

	// Setup existing aggregations
	plugin := "test-plugin"

	existingView := "test-plugin@@@1"

	manager.horizontalAggregations = map[string]map[string]utils.MotadataMap{
		plugin: {
			existingView: {
				utils.Type: float64(0),
				utils.IndexableColumns: utils.MotadataMap{
					"column1": struct{}{},
				},
				"metric1": float64(1),
			},
		},
	}

	// Create test data that can fit in the existing view
	aggregations := map[string]utils.MotadataMap{
		"new-view": {
			utils.Type: float64(0),
			utils.IndexableColumns: utils.MotadataMap{
				"column2": struct{}{},
			},
			"metric2": float64(2),
		},
	}

	// Call unifyAggregations
	result := manager.unifyAggregations(plugin, aggregations)

	// Assert that the result contains the affected view
	assert.Contains(t, result, existingView, "Expected result to contain the affected view")
}

// TestUnifyAggregationsCannotFit tests the unifyAggregations method with a view that cannot fit
func TestUnifyAggregationsCannotFit(t *testing.T) {
	// Create a new manager
	manager := testSetup()
	defer testSetupDestroy()

	manager.indices = map[string]int{
		"test-plugin": 1,
	}

	previousAggregationLimit := utils.AggregationViewAggregationColumnLimit

	previousGroupingLimit := utils.AggregationViewIndexColumnLimit

	utils.AggregationViewAggregationColumnLimit = 3

	utils.AggregationViewIndexColumnLimit = 3

	defer func() {

		utils.AggregationViewAggregationColumnLimit = previousAggregationLimit

		utils.AggregationViewIndexColumnLimit = previousGroupingLimit
	}()

	// Setup existing aggregations with max groups
	plugin := "test-plugin"
	existingView := "test-plugin@@@1"
	manager.horizontalAggregations = map[string]map[string]utils.MotadataMap{
		plugin: {
			existingView: {
				utils.Type: float64(0),
				utils.IndexableColumns: utils.MotadataMap{
					"column1": struct{}{},
					"column2": struct{}{},
					"column3": struct{}{}, // Max groups is 3
				},
				"metric1": float64(1),
			},
		},
	}

	// Create test data that cannot fit in the existing view (exceeds max groups)
	aggregations := map[string]utils.MotadataMap{
		"new-view": {
			utils.Type: float64(0),
			utils.IndexableColumns: utils.MotadataMap{
				"column4": struct{}{}, // This would make 4 groups, exceeding maxGroups (3)
			},
			"metric2": float64(2),
		},
	}

	// Call unifyAggregations
	result := manager.unifyAggregations(plugin, aggregations)

	// Assert that the result is empty (no affected aggregations)
	assert.Empty(t, result, "Expected empty result when view cannot fit")

	// Assert that a new view was created in qualifiedProbes
	assert.True(t, len(manager.qualifiedProbes) == 0, "Expected qualifiedProbes to be populated with a new view")
}

// TestUnifyAggregationsMaxAggregations tests the unifyAggregations method with max aggregations
func TestUnifyAggregationsMaxAggregations(t *testing.T) {
	// Create a new manager
	manager := testSetup()
	defer testSetupDestroy()

	manager.indices = map[string]int{
		"test-plugin": 1,
	}

	previousAggregationLimit := utils.AggregationViewAggregationColumnLimit

	previousGroupingLimit := utils.AggregationViewIndexColumnLimit

	utils.AggregationViewAggregationColumnLimit = 3

	utils.AggregationViewIndexColumnLimit = 3

	defer func() {

		utils.AggregationViewAggregationColumnLimit = previousAggregationLimit

		utils.AggregationViewIndexColumnLimit = previousGroupingLimit
	}()

	// Setup existing aggregations with max aggregations
	plugin := "test-plugin"
	existingView := "test-plugin@@@1"
	manager.horizontalAggregations = map[string]map[string]utils.MotadataMap{
		plugin: {
			existingView: {
				utils.Type: float64(0),
				utils.IndexableColumns: utils.MotadataMap{
					"column1": struct{}{},
				},
				"metric1": float64(1),
				"metric2": float64(2),
				"metric3": float64(3), // Max aggregations is 3
			},
		},
	}

	// Create test data that cannot fit in the existing view (exceeds max aggregations)
	aggregations := map[string]utils.MotadataMap{
		"new-view": {
			utils.Type: float64(0),
			utils.IndexableColumns: utils.MotadataMap{
				"column1": struct{}{},
			},
			"metric4": float64(4), // This would make 4 aggregations, exceeding maxAggregations (3)
		},
	}

	// Call unifyAggregations
	result := manager.unifyAggregations(plugin, aggregations)

	// Assert that the result is empty (no affected aggregations)
	assert.Empty(t, result, "Expected empty result when view cannot fit due to max aggregations")

	// Assert that a new view was created in qualifiedProbes
	assert.True(t, len(manager.qualifiedProbes) == 0, "Expected qualifiedProbes to be populated with a new view")
}

// TestUnifyAggregationsCheckRunningProbes tests the unifyAggregations method with running probes
func TestUnifyAggregationsCheckRunningProbes(t *testing.T) {
	// Create a new manager
	manager := testSetup()
	defer testSetupDestroy()

	manager.indices = map[string]int{
		"test-plugin": 1,
	}

	// Setup running probes
	plugin := "test-plugin"
	manager.qualifiedProbes = map[string]map[string]utils.MotadataMap{
		plugin: {
			"test-plugin@@@1": {
				utils.Type: float64(0),
				utils.IndexableColumns: utils.MotadataMap{
					"column1": struct{}{},
				},
				"metric1": float64(1),
			},
		},
	}

	// Create test data that matches the running probe
	aggregations := map[string]utils.MotadataMap{
		"new-view": {
			utils.Type: float64(0),
			utils.IndexableColumns: utils.MotadataMap{
				"column1": struct{}{},
			},
			"metric1": float64(1),
		},
	}

	// Call unifyAggregations
	result := manager.unifyAggregations(plugin, aggregations)

	// Assert that the result is empty (no affected aggregations)
	assert.Empty(t, result, "Expected empty result when view matches running probe")

	// Assert that no new views were created
	assert.Equal(t, 1, len(manager.qualifiedProbes[plugin]), "Expected no new views to be created")
}

// TestUnifyAggregationsMultipleViews tests the unifyAggregations method with multiple views
func TestUnifyAggregationsMultipleViews(t *testing.T) {
	// Create a new manager
	manager := testSetup()
	defer testSetupDestroy()

	manager.indices = map[string]int{
		"test-plugin": 3,
	}

	// Setup existing aggregations with multiple views
	plugin := "test-plugin"
	view1 := "test-plugin@@@1"
	view2 := "test-plugin@@@2"
	manager.horizontalAggregations = map[string]map[string]utils.MotadataMap{
		plugin: {
			view1: {
				utils.Type: float64(0),
				utils.IndexableColumns: utils.MotadataMap{
					"column1": struct{}{},
				},
				"metric1": float64(1),
			},
			view2: {
				utils.Type: float64(0),
				utils.IndexableColumns: utils.MotadataMap{
					"column2": struct{}{},
				},
				"metric2": float64(2),
			},
		},
	}

	// Create test data with multiple views
	aggregations := map[string]utils.MotadataMap{
		"new-view1": {
			utils.Type: float64(0),
			utils.IndexableColumns: utils.MotadataMap{
				"column1": struct{}{},
			},
			"metric3": float64(3),
		},
		"new-view2": {
			utils.Type: float64(0),
			utils.IndexableColumns: utils.MotadataMap{
				"column3": struct{}{},
			},
			"metric4": float64(4),
		},
	}

	// Call unifyAggregations
	result := manager.unifyAggregations(plugin, aggregations)

	// Assert that the result contains the affected view
	assert.Contains(t, result, view1, "Expected result to contain the first affected view")

	// Assert that a new view was created for the second aggregation
	assert.True(t, len(manager.qualifiedProbes[plugin]) >= 1, manager.qualifiedProbes)
}

// TestAddWidget tests the addWidget function
func TestAddWidget(t *testing.T) {

	testSetup()

	defer testSetupDestroy()

	// Create test data
	context := utils.MotadataMap{
		utils.Type: float64(0),
		utils.IndexableColumns: utils.MotadataMap{
			"column1": struct{}{},
		},
		"metric1": float64(1),
	}

	aggregationView := utils.MotadataMap{
		utils.Type: float64(0),
		utils.IndexableColumns: utils.MotadataMap{
			"column2": struct{}{},
		},
		"metric2": float64(2),
	}

	mergedResult := utils.MotadataMap{
		utils.Type:             float64(0),
		utils.IndexableColumns: utils.MotadataMap{},
	}

	// Call addWidget
	addWidget(context, aggregationView, mergedResult)

	// Assert that the merged result contains all keys from both inputs
	assert.Equal(t, float64(0), mergedResult[utils.Type], "Expected Type to be preserved")
	assert.Equal(t, float64(1), mergedResult["metric1"], "Expected metric1 to be added")
	assert.Equal(t, float64(2), mergedResult["metric2"], "Expected metric2 to be added")

	// Assert that the merged result contains all indexable columns
	indexableColumns := mergedResult.GetMapValue(utils.IndexableColumns)
	assert.Contains(t, indexableColumns, "column1", "Expected column1 to be added to indexable columns")
	assert.Contains(t, indexableColumns, "column2", "Expected column2 to be added to indexable columns")
}

// TestCanFitAggregation tests the canFitAggregation method
func TestCanFitAggregation(t *testing.T) {
	// Create a new manager
	manager := testSetup()

	defer testSetupDestroy()

	previousAggregationLimit := utils.AggregationViewAggregationColumnLimit

	previousGroupingLimit := utils.AggregationViewIndexColumnLimit

	utils.AggregationViewAggregationColumnLimit = 3

	utils.AggregationViewIndexColumnLimit = 3

	defer func() {

		utils.AggregationViewAggregationColumnLimit = previousAggregationLimit

		utils.AggregationViewIndexColumnLimit = previousGroupingLimit
	}()

	// Test case 1: Can fit (within limits)
	context1 := utils.MotadataMap{
		utils.Type: float64(0),
		utils.IndexableColumns: utils.MotadataMap{
			"column1": struct{}{},
		},
		"metric1": float64(1),
	}

	aggregationView1 := utils.MotadataMap{
		utils.Type: float64(0),
		utils.IndexableColumns: utils.MotadataMap{
			"column2": struct{}{},
		},
		"metric2": float64(2),
	}

	_, fit := manager.qualified(context1, aggregationView1)

	assert.True(t, fit, "Expected to fit within limits")

	// Test case 2: Cannot fit (exceeds max aggregations)
	context2 := utils.MotadataMap{
		utils.Type: float64(0),
		utils.IndexableColumns: utils.MotadataMap{
			"column1": struct{}{},
		},
		"metric1": float64(1),
		"metric3": float64(3),
	}

	aggregationView2 := utils.MotadataMap{
		utils.Type: float64(0),
		utils.IndexableColumns: utils.MotadataMap{
			"column2": struct{}{},
		},
		"metric2": float64(2),
		"metric4": float64(4),
	}

	_, fit = manager.qualified(context2, aggregationView2)

	assert.False(t, fit, "Expected not to fit due to max aggregations")

	// Test case 3: Cannot fit (exceeds max groups)
	context3 := utils.MotadataMap{
		utils.Type: float64(0),
		utils.IndexableColumns: utils.MotadataMap{
			"column1": struct{}{},
			"column3": struct{}{},
		},
		"metric1": float64(1),
	}

	aggregationView3 := utils.MotadataMap{
		utils.Type: float64(0),
		utils.IndexableColumns: utils.MotadataMap{
			"column2": struct{}{},
			"column4": struct{}{},
		},
		"metric2": float64(2),
	}

	_, fit = manager.qualified(context3, aggregationView3)

	assert.False(t, fit, "Expected not to fit due to max groups")
}

// TestCheckRunningProbes tests the checkRunningProbes method
func TestCheckRunningProbes(t *testing.T) {
	// Create a new manager
	manager := testSetup()
	defer testSetupDestroy()

	// Setup running probes
	plugin := "test-plugin"
	manager.qualifiedProbes = map[string]map[string]utils.MotadataMap{
		plugin: {
			"test-plugin@@@1": {
				utils.Type: float64(0),
				utils.IndexableColumns: utils.MotadataMap{
					"column1": struct{}{},
				},
				"metric1": float64(1),
			},
		},
	}

	// Test case 1: Matches running probe
	context1 := utils.MotadataMap{
		utils.Type: float64(0),
		utils.IndexableColumns: utils.MotadataMap{
			"column1": struct{}{},
		},
		"metric1": float64(1),
	}

	assert.True(t, manager.running(plugin, context1), "Expected to match running probe")

	// Test case 2: Different column, doesn't match
	context2 := utils.MotadataMap{
		utils.Type: float64(0),
		utils.IndexableColumns: utils.MotadataMap{
			"column2": struct{}{},
		},
		"metric1": float64(1),
	}

	assert.False(t, manager.running(plugin, context2), "Expected not to match running probe due to different column")

	// Test case 3: Different metric value, doesn't match
	context3 := utils.MotadataMap{
		utils.Type: float64(0),
		utils.IndexableColumns: utils.MotadataMap{
			"column1": struct{}{},
		},
		"metric1": float64(2), // Different value
	}

	assert.False(t, manager.running(plugin, context3), "Expected not to match running probe due to different metric value")

	// Test case 4: Different metric key, doesn't match
	context4 := utils.MotadataMap{
		utils.Type: float64(0),
		utils.IndexableColumns: utils.MotadataMap{
			"column1": struct{}{},
		},
		"metric2": float64(1), // Different key
	}

	assert.False(t, manager.running(plugin, context4), "Expected not to match running probe due to different metric key")
}

// TestUnifyAggregationsWithAffectedAggregations tests the unifyAggregations method with affected aggregations
func TestUnifyAggregationsWithAffectedAggregations(t *testing.T) {
	// Create a new manager
	manager := testSetup()
	defer testSetupDestroy()

	manager.indices = map[string]int{
		"test-plugin": 1,
	}

	// Setup existing aggregations
	plugin := "test-plugin"
	existingView := "test-plugin@@@1"
	manager.horizontalAggregations = map[string]map[string]utils.MotadataMap{
		plugin: {
			existingView: {
				utils.Type: float64(0),
				utils.IndexableColumns: utils.MotadataMap{
					"column1": struct{}{},
				},
				"metric1": float64(1),
			},
		},
	}

	// Create test data that can fit in the existing view
	aggregations := map[string]utils.MotadataMap{
		"new-view": {
			utils.Type: float64(0),
			utils.IndexableColumns: utils.MotadataMap{
				"column2": struct{}{},
			},
			"metric2": float64(2),
		},
	}

	// Call unifyAggregations
	result := manager.unifyAggregations(plugin, aggregations)

	// Assert that the result contains the affected view
	assert.Contains(t, result, existingView, "Expected result to contain the affected view")

	// Assert that the affected view is in qualifiedProbes
	assert.NotEmpty(t, manager.qualifiedProbes, "Expected qualifiedProbes to be populated")

	// Get the new view name
	var newViewName string
	for name := range manager.qualifiedProbes[plugin] {
		newViewName = name
		break
	}

	// Assert that the new view contains both columns and metrics
	newView := manager.qualifiedProbes[plugin][newViewName]
	assert.Contains(t, newView.GetMapValue(utils.IndexableColumns), "column1", "Expected new view to contain column1")
	assert.Contains(t, newView.GetMapValue(utils.IndexableColumns), "column2", "Expected new view to contain column2")
	assert.Equal(t, float64(1), newView["metric1"], "Expected new view to contain metric1")
	assert.Equal(t, float64(2), newView["metric2"], "Expected new view to contain metric2")
}

func TestManagerUpdateHorizontalAggregationsMiscellaneousNotificationVersion1(t *testing.T) {

	testSetup()
	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	bytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator + "existing-horizontal-aggregations-version1.json")

	clear(manager.horizontalAggregations)

	clear(manager.qualifiedProbes)

	clear(manager.indices)

	_ = os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.HorizontalAggregations, bytes, 0666)

	manager := NewManager()

	manager.shutdown = true

	manager.Start()

	bytes, _ = os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator + "manager-request-version8.json")

	request := utils.MotadataMap{}

	request = utils.UnmarshalJson(bytes, request)

	assertions := assert.New(t)

	assertions.Empty(manager.updateHorizontalAggregations(request.DeepClone()))
}

func TestManagerUpdateHorizontalAggregationsMiscellaneousNotificationVersion2(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	bytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator + "existing-horizontal-aggregations-version2.json")

	clear(manager.horizontalAggregations)

	clear(manager.qualifiedProbes)

	clear(manager.indices)

	_ = os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.HorizontalAggregations, bytes, 0666)

	bytes, _ = os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator + "manager-request-version9.json")

	request := utils.MotadataMap{}

	request = utils.UnmarshalJson(bytes, request)

	assertions := assert.New(t)

	assertions.Empty(manager.updateHorizontalAggregations(request.DeepClone()))
}

//manager testcases

func TestAggregate(t *testing.T) {

	testSetup()

	defer testSetupDestroy()

	utils.SetLogLevel(utils.LogLevelTrace)

	assertions := assert.New(t)

	aggregationJobs[0].aggregate(utils.MotadataMap{})

	bytes, err := utils.ReadLogFile("Aggregation Job-"+codec.INTToStringValue(0), "job")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "invalid context for plugin"))

	aggregationJobs[0].aggregate(utils.MotadataMap{
		utils.EventContext: utils.MotadataMap{

			utils.Type: 100,
		},
	})

	bytes, err = utils.ReadLogFile("Aggregation Job-"+codec.INTToStringValue(0), "job")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "failed to qualify visualization type"))

	aggregationJobs[0].queryType = utils.VerticalFormat

	aggregationJobs[0].aggregate(utils.MotadataMap{
		utils.EventContext: utils.MotadataMap{

			utils.Type:             1,
			"interface~in.packets": float64(datastore.StringColumn),
		},
		utils.DatastoreFormat: utils.VerticalFormat,
	})

	bytes, err = utils.ReadLogFile("Aggregation Job-"+codec.INTToStringValue(0), "job")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "visualization type"))

}

func TestManagerSyncAggregationContextVersion1(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	aggregation := "21245-dummy.plugin-1"

	plugin := "21245-dummy.plugin"

	aggregationColumn := "aggregation.column"

	datastore.UpdateVerticalAggregations(aggregationColumn, true)

	valid := manager.validateVerticalAggregation(utils.MotadataMap{
		aggregationColumn: float64(datastore.StringColumn)})

	assertions := assert.New(t)

	assertions.False(valid)

	var indexableColumns []interface{}

	eventContext := utils.MotadataMap{
		utils.Type:             float64(utils.Log),
		utils.IndexableColumns: indexableColumns,
		aggregationColumn:      float64(datastore.StringColumn),
	}

	manager.updateHorizontalAggregations(utils.MotadataMap{

		aggregation: eventContext.DeepClone(),
	})

	assertions.NotEmpty(manager.qualifiedProbes[plugin])

	viewContext, found := manager.qualifiedProbes[plugin][plugin+utils.AggregationSeparator+"0"]

	assertions.True(found)

	manager.horizontalAggregations[plugin] = map[string]utils.MotadataMap{

		plugin + utils.AggregationSeparator + "18": viewContext,
	}

	assertions.Empty(manager.updateHorizontalAggregations(utils.MotadataMap{
		aggregation: eventContext.DeepClone(),
	}))
}

func TestManagerSyncAggregationContextVersion2(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	clear(manager.horizontalAggregations)

	clear(manager.indices)

	clear(manager.qualifiedProbes)

	plugin := "700000-flow"

	aggregation := "700000-flow-1"

	aggregationContextV1 := utils.MotadataMap{

		utils.Type: float64(utils.Flow),
		utils.IndexableColumns: []interface{}{
			writer.SourceIP,
			writer.DestinationIP,
			writer.SourcePort,
			writer.DestinationPort,
		},
		writer.VolumeBytesPerSec: datastore.IntegerColumn,
		writer.PacketsPerSec:     datastore.IntegerColumn,
	}

	assertions := assert.New(t)

	manager.updateHorizontalAggregations(utils.MotadataMap{

		aggregation: aggregationContextV1.DeepClone(),
	})

	qualifiedView := ""

	for qualifiedView = range manager.qualifiedProbes[plugin] {

		if strings.Contains(qualifiedView, plugin) {

			break

		}

	}

	assertions.NotEmpty(qualifiedView)

	manager.horizontalAggregations[plugin] = map[string]utils.MotadataMap{}

	manager.horizontalAggregations[plugin][qualifiedView] = manager.qualifiedProbes[plugin][qualifiedView]

	assertions.Empty(manager.updateHorizontalAggregations(utils.MotadataMap{

		aggregation: aggregationContextV1.DeepClone(),
	}))

	/*
		This aggregation context contains all the columns that are already in the existing aggregation
		but also contains one extra column. grouping columns are same as the aggregation
		Hence we get a new aggregation, i.e. this one and the previous aggregation which is similar to this one gets destroyed.
	*/

	aggregationContextV1[writer.SourceIFIndex] = datastore.IntegerColumn // adding a new aggregation column

	manager.updateHorizontalAggregations(utils.MotadataMap{

		aggregation: aggregationContextV1.DeepClone(),
	})

	qualifiedView = ""

	for qualifiedView = range manager.qualifiedProbes[plugin] {

		if strings.Contains(qualifiedView, plugin) {

			break

		}

	}

	assertions.NotEmpty(qualifiedView)

	delete(manager.horizontalAggregations[plugin], plugin+utils.AggregationSeparator+"0") //mimic the aggregation gets deleted

	manager.horizontalAggregations[plugin][qualifiedView] = manager.qualifiedProbes[plugin][qualifiedView] //mimic the entry of the merged aggregation

	assertions.True(len(manager.horizontalAggregations[plugin]) >= 1 && len(manager.horizontalAggregations) < 3, manager.horizontalAggregations)

	/*
		This aggregation context contains one column that is not in the existing aggregation. Grouping columns are same as the
		existing aggregation, hence we get a new aggregation, i.e. previous aggregation + new aggregation gets merged and previous aggregation gets destroyed.
	*/

	delete(aggregationContextV1, writer.VolumeBytesPerSec)

	delete(aggregationContextV1, writer.PacketsPerSec)

	delete(aggregationContextV1, utils.VolumeBytes)

	aggregationContextV1[writer.Packets] = datastore.IntegerColumn

	manager.updateHorizontalAggregations(utils.MotadataMap{

		aggregation: aggregationContextV1.DeepClone(),
	})

	qualifiedView = ""

	for qualifiedView = range manager.qualifiedProbes[plugin] {

		if strings.Contains(qualifiedView, plugin) {

			break

		}

	}

	assertions.NotEmpty(qualifiedView)

	manager.horizontalAggregations[plugin][qualifiedView] = manager.qualifiedProbes[plugin][qualifiedView] //mimic the entry of the qualified aggregation

	delete(manager.horizontalAggregations[plugin], plugin+utils.AggregationSeparator+"1") //mimic the deleting entry of the aggregation

	/*scenario subset of aggregation column is available in existing aggregation hence the widget request must be invalid*/

	invalidContext := utils.MotadataMap{

		utils.Type: float64(utils.Flow),
		utils.IndexableColumns: []interface{}{
			writer.SourceIP,
			writer.DestinationIP,
			writer.SourcePort,
			writer.DestinationPort,
		},
		writer.Packets: datastore.IntegerColumn, //same as aggContextV1 only volumebytespersec column in aggregation
	}

	assertions.Empty(manager.updateHorizontalAggregations(utils.MotadataMap{

		aggregation: invalidContext.DeepClone(),
	}))

	/*
		scenario the grouping columns are a subset of the existing aggregation
	*/

	invalidContext[utils.IndexableColumns] = []interface{}{
		writer.SourceIP,
		writer.DestinationIP,
	}

	assertions.Empty(manager.updateHorizontalAggregations(utils.MotadataMap{

		aggregation: invalidContext.DeepClone(),
	}))

	/*
		scenario 2 where the grouping columns differ and there are multiple views that can be merged with.
	*/

	aggregationContextV2 := aggregationContextV1.DeepClone()

	aggregationContextV2[utils.IndexableColumns] = []interface{}{

		writer.SourceIFIndex,
		writer.DestinationIFIndex,
		writer.Application,
		writer.ApplicationProtocol,
		writer.Protocol,
		writer.TCPFlags,
	}

	manager.updateHorizontalAggregations(utils.MotadataMap{

		aggregation: aggregationContextV2.DeepClone(),
	})

	qualifiedView = ""

	for qualifiedView = range manager.qualifiedProbes[plugin] {

		if strings.Contains(qualifiedView, plugin) {

			break

		}

	}

	assertions.NotEmpty(qualifiedView)

	aggregationContextV3 := utils.MotadataMap{

		utils.Type: float64(utils.Flow),
		utils.IndexableColumns: []interface{}{
			writer.TCPFlags,
			writer.SourceASN,
		},
		writer.Flows:             datastore.IntegerColumn,
		writer.VolumeBytesPerSec: datastore.StringColumn,
	}

	manager.updateHorizontalAggregations(utils.MotadataMap{

		aggregation: aggregationContextV3.DeepClone(),
	})

	qualifiedView = ""

	for qualifiedView = range manager.qualifiedProbes[plugin] {

		if strings.Contains(qualifiedView, plugin) {

			break

		}

	}

	assertions.NotEmpty(qualifiedView)

}

func TestManagerSyncAggregationContextVersion3(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator + "manager-request-version1.json")

	clear(manager.horizontalAggregations)

	clear(manager.qualifiedProbes)

	clear(manager.indices)

	plugin := "600005-fortinet.traffic"

	aggregations := utils.MotadataMap{}

	aggregations = utils.UnmarshalJson(bytes, aggregations)

	manager.updateHorizontalAggregations(aggregations.DeepClone())

	assertions.NotEmpty(manager.qualifiedProbes[plugin])

	for aggregation, context := range manager.qualifiedProbes[plugin] {

		if _, ok := manager.horizontalAggregations[plugin]; !ok {

			manager.horizontalAggregations[plugin] = map[string]utils.MotadataMap{}
		}

		manager.horizontalAggregations[plugin][aggregation] = context
	}

	assertions.NotEmpty(manager.qualifiedProbes[plugin])

	for aggregation, context := range aggregations {

		if strings.Contains(aggregation, "fortinet") {

			assertions.Empty(manager.updateHorizontalAggregations(utils.MotadataMap{

				aggregation: context,
			}))
		}
	}

}

func TestManagerSyncAggregationContextVersion4(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator + "manager-request-version2.json")

	clear(manager.horizontalAggregations)

	clear(manager.qualifiedProbes)

	clear(manager.indices)

	aggregations := utils.MotadataMap{}

	aggregations = utils.UnmarshalJson(bytes, aggregations)

	manager.updateHorizontalAggregations(aggregations.DeepClone())

	plugin := "500000-flow"

	assertions.NotEmpty(manager.qualifiedProbes[plugin])

	for aggregation, context := range manager.qualifiedProbes[plugin] {

		plugin := strings.Split(aggregation, utils.AggregationSeparator)[0]

		if _, ok := manager.horizontalAggregations[plugin]; !ok {

			manager.horizontalAggregations[plugin] = map[string]utils.MotadataMap{}
		}

		manager.horizontalAggregations[plugin][aggregation] = context
	}

	assertions.NotEmpty(manager.qualifiedProbes[plugin])

	for aggregation, context := range aggregations {

		if strings.Contains(aggregation, "flow") {

			assertions.Empty(manager.updateHorizontalAggregations(utils.MotadataMap{

				aggregation: context,
			}))
		}
	}

}

func TestManagerSyncAggregationContextVersion5(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator + "manager-request-version3.json")

	clear(manager.horizontalAggregations)

	clear(manager.qualifiedProbes)

	clear(manager.indices)

	aggregations := utils.MotadataMap{}

	aggregations = utils.UnmarshalJson(bytes, aggregations)

	manager.updateHorizontalAggregations(aggregations.DeepClone())

	assertions.NotEmpty(manager.qualifiedProbes)

	for plugin, views := range manager.qualifiedProbes {

		for aggregation, context := range views {

			if _, ok := manager.horizontalAggregations[plugin]; !ok {

				manager.horizontalAggregations[plugin] = map[string]utils.MotadataMap{}
			}

			manager.horizontalAggregations[plugin][aggregation] = context

		}

	}

	assertions.NotEmpty(manager.qualifiedProbes)

	clear(manager.qualifiedProbes)

	for aggregation, context := range aggregations {

		if strings.Contains(aggregation, "flow") {

			assertions.Empty(manager.updateHorizontalAggregations(utils.MotadataMap{

				aggregation: context,
			}))
		}
	}

	for aggregation, context := range aggregations {

		if strings.Contains(aggregation, "fortinet") {

			assertions.Empty(manager.updateHorizontalAggregations(utils.MotadataMap{

				aggregation: context,
			}))
		}
	}

}

func TestManagerSyncAggregationContextVersion6(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator + "manager-request-version2.json")

	clear(manager.horizontalAggregations)

	clear(manager.qualifiedProbes)

	clear(manager.indices)

	currentAggregations := utils.MotadataMap{}

	currentAggregations = utils.UnmarshalJson(bytes, currentAggregations)

	aggregations := []string{"500000-flow-4", "500000-flow-2", "500000-flow-27", "500000-flow-24", "500000-flow-22", "500000-flow-21", "500000-flow-19", "500000-flow-17", "500000-flow-16", "500000-flow-12"}

	for _, aggregation := range aggregations {

		context := currentAggregations[aggregation]

		tokens := strings.Split(aggregation, utils.HyphenSeparator)

		plugin := strings.Join(tokens[:len(tokens)-1], utils.HyphenSeparator)

		excludedAggregations := manager.updateHorizontalAggregations(utils.MotadataMap{

			aggregation: utils.ToMap(context).DeepClone(),
		})

		if _, ok := manager.horizontalAggregations[plugin]; !ok {

			manager.horizontalAggregations[plugin] = map[string]utils.MotadataMap{}
		}

		for plugin, views := range manager.qualifiedProbes {

			for viewName, viewContext := range views {

				manager.horizontalAggregations[plugin][viewName] = viewContext

			}

		}

		for aggregation := range excludedAggregations {

			for excludedAggregation := range excludedAggregations[aggregation] {

				delete(manager.horizontalAggregations[plugin], excludedAggregation)

				delete(manager.excludedHorizontalAggregations, excludedAggregation)
			}

		}

	}

	for aggregation, context := range currentAggregations {

		if utils.StringContains(aggregations, aggregation) {

			continue
		}

		tokens := strings.Split(aggregation, utils.HyphenSeparator)

		plugin := strings.Join(tokens[:len(tokens)-1], utils.HyphenSeparator)

		excludedAggregations := manager.updateHorizontalAggregations(utils.MotadataMap{

			aggregation: utils.ToMap(context).DeepClone(),
		})

		if _, ok := manager.horizontalAggregations[plugin]; !ok {

			manager.horizontalAggregations[plugin] = map[string]utils.MotadataMap{}
		}

		for aggregation, context := range manager.qualifiedProbes[plugin] {

			manager.horizontalAggregations[plugin][aggregation] = context
		}

		for _, aggregations := range excludedAggregations {

			for aggregation := range aggregations {

				delete(manager.horizontalAggregations[plugin], aggregation)

				delete(manager.excludedHorizontalAggregations, aggregation)
			}

		}

	}

	clear(manager.qualifiedProbes)

	for aggregation, context := range currentAggregations {

		manager.updateHorizontalAggregations(utils.MotadataMap{

			aggregation: context,
		})

		assertions.True(len(manager.qualifiedProbes) == 0)
	}

}

func TestManagerSyncAggregationContextVersion7(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator + "manager-request-version5.json")

	aggregations := utils.MotadataMap{}

	aggregations = utils.UnmarshalJson(bytes, aggregations)

	clear(manager.horizontalAggregations)

	clear(manager.qualifiedProbes)

	clear(manager.indices)

	for i := 0; i < 1000; i++ {

		_ = manager.updateHorizontalAggregations(aggregations.DeepClone())

		if i == 0 {

			assertions.Equal(1, len(manager.qualifiedProbes))
		} else {

			assertions.Equal(0, len(manager.qualifiedProbes))

		}

		for plugin, views := range manager.qualifiedProbes {

			for aggregation, context := range views {

				if manager.horizontalAggregations[plugin] == nil {

					manager.horizontalAggregations[plugin] = map[string]utils.MotadataMap{}
				}

				manager.horizontalAggregations[plugin][aggregation] = context
			}

			delete(manager.qualifiedProbes, plugin)
		}

		assertions.Equal(2, len(manager.horizontalAggregations["600005-fortinet.utm"]))
	}

}

func TestManagerSyncAggregationContextVersion8(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator + "manager-request-version5.json")

	aggregations := utils.MotadataMap{}

	aggregations = utils.UnmarshalJson(bytes, aggregations)

	clear(manager.horizontalAggregations)

	clear(manager.indices)

	clear(manager.qualifiedProbes)

	for i := 0; i < 1000; i++ {

		for aggregation := range aggregations {

			context := aggregations[aggregation]

			tokens := strings.Split(aggregation, utils.HyphenSeparator)

			plugin := strings.Join(tokens[:len(tokens)-1], utils.HyphenSeparator)

			excludedAggregations := manager.updateHorizontalAggregations(utils.MotadataMap{

				aggregation: utils.ToMap(context).DeepClone(),
			})

			if _, ok := manager.horizontalAggregations[plugin]; !ok {

				manager.horizontalAggregations[plugin] = map[string]utils.MotadataMap{}
			}

			for aggregation, context := range manager.qualifiedProbes[plugin] {

				manager.horizontalAggregations[plugin][aggregation] = context

				delete(manager.qualifiedProbes[plugin], aggregation)
			}

			if len(manager.qualifiedProbes[plugin]) == 0 {

				delete(manager.qualifiedProbes, plugin)
			}

			for _, aggregations := range excludedAggregations {

				for aggregation := range aggregations {

					delete(manager.horizontalAggregations[plugin], aggregation)

					delete(manager.excludedHorizontalAggregations, aggregation)
				}

			}

			assertions.Equal(0, len(manager.qualifiedProbes))
		}

		assertions.Equal(2, len(manager.horizontalAggregations["600005-fortinet.utm"]))
	}

}

func TestManagerSyncAggregationContextVersion9(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator + "manager-request-version4.json")

	clear(manager.horizontalAggregations)

	clear(manager.qualifiedProbes)

	clear(manager.indices)

	aggregations := utils.MotadataMap{}

	aggregations = utils.UnmarshalJson(bytes, aggregations)

	plugin := "600005-fortinet.traffic"

	manager.updateHorizontalAggregations(aggregations.DeepClone())

	assertions.Equal(1, len(manager.qualifiedProbes[plugin]))
}

func TestMaxIndexableColumnTypeV1(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	clear(manager.horizontalAggregations)

	clear(manager.indices)

	clear(manager.qualifiedProbes)

	assertions := assert.New(t)

	aggregationContextV1 := utils.MotadataMap{

		utils.Type: float64(utils.Flow),
		utils.IndexableColumns: []interface{}{
			"u1",
			"a2",
			"a3",
			"a4",
			"a5",
			"a6",
			"a7",
			"a8",
		},
		writer.VolumeBytesPerSec: datastore.IntegerColumn,
		writer.PacketsPerSec:     datastore.IntegerColumn,
	}

	aggregationContextV2 := utils.MotadataMap{

		utils.Type: float64(utils.Flow),
		utils.IndexableColumns: []interface{}{
			"b1",
			"b2",
			"b3",
			"b4",
			"b5",
			"b6",
			"b7",
			"b8",
		},
		writer.VolumeBytesPerSec: datastore.IntegerColumn,
		writer.PacketsPerSec:     datastore.IntegerColumn,
	}

	aggregationContextV3 := utils.MotadataMap{

		utils.Type: float64(utils.Flow),
		utils.IndexableColumns: []interface{}{
			"c1",
			"c2",
			"c3",
			"c4",
			"c5",
			"c6",
			"c7",
		},
		writer.VolumeBytesPerSec: datastore.IntegerColumn,
		writer.PacketsPerSec:     datastore.IntegerColumn,
	}

	manager.updateHorizontalAggregations(utils.MotadataMap{

		"700000-flow-2": aggregationContextV1.DeepClone(),
		"700000-flow-3": aggregationContextV2.DeepClone(),
		"700000-flow-4": aggregationContextV3.DeepClone(),
	})

	assertions.Equal(len(manager.qualifiedProbes["700000-flow"]), 1)

	for _, view := range manager.qualifiedProbes["700000-flow"] {

		assertions.LessOrEqual(len(view.GetMapValue(utils.IndexableColumns)), 8)

	}
}

func TestMaxIndexableColumnTypeV2(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	clear(manager.horizontalAggregations)

	clear(manager.indices)

	clear(manager.qualifiedProbes)

	assertions := assert.New(t)

	aggregationContextV1 := utils.MotadataMap{

		utils.Type: float64(utils.Flow),
		utils.IndexableColumns: []interface{}{
			"u1",
			"a2",
			"a3",
			"a4",
			"a5",
			"a6",
			"a7",
			"a8",
			"a9",
		},
		writer.VolumeBytesPerSec: datastore.IntegerColumn,
		writer.PacketsPerSec:     datastore.IntegerColumn,
	}

	aggregationContextV2 := utils.MotadataMap{

		utils.Type: float64(utils.Flow),
		utils.IndexableColumns: []interface{}{
			"b1",
			"b2",
			"b3",
			"b4",
			"b5",
			"b6",
			"b7",
			"b8",
			"b9",
		},
		writer.VolumeBytesPerSec: datastore.IntegerColumn,
		writer.PacketsPerSec:     datastore.IntegerColumn,
	}

	aggregationContextV3 := utils.MotadataMap{

		utils.Type: float64(utils.Flow),
		utils.IndexableColumns: []interface{}{
			"c1",
			"c2",
			"c3",
			"c4",
			"c5",
			"c6",
		},
		writer.VolumeBytesPerSec: datastore.IntegerColumn,
		writer.PacketsPerSec:     datastore.IntegerColumn,
	}

	manager.updateHorizontalAggregations(utils.MotadataMap{

		"700000-flow-2": aggregationContextV1.DeepClone(),
		"700000-flow-3": aggregationContextV2.DeepClone(),
		"700000-flow-4": aggregationContextV3.DeepClone(),
	})

	assertions.Equal(len(manager.qualifiedProbes["700000-flow"]), 1)

	for _, view := range manager.qualifiedProbes["700000-flow"] {

		assertions.LessOrEqual(len(view.GetMapValue(utils.IndexableColumns)), 8)

	}
}

func TestManagerSyncAggregationContextVersion10(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator + "manager-request-version6.json")

	clear(manager.horizontalAggregations)

	clear(manager.qualifiedProbes)

	clear(manager.indices)

	aggregations := utils.MotadataMap{}

	aggregations = utils.UnmarshalJson(bytes, aggregations)

	manager.updateHorizontalAggregations(aggregations.DeepClone())

	assertions.Equal(1, len(manager.qualifiedProbes["600005-fortinet.traffic"]))
}

func TestManagerSyncAggregationContextVersion11(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator + "manager-request-version7.json")

	clear(manager.horizontalAggregations)

	clear(manager.qualifiedProbes)

	clear(manager.indices)

	aggregations := utils.MotadataMap{}

	aggregations = utils.UnmarshalJson(bytes, aggregations)

	manager.updateHorizontalAggregations(aggregations.DeepClone())

	assertions.Equal(1, len(manager.qualifiedProbes["600005-fortinet.traffic"]))
}

func TestManagerSyncAggregationContextVersion12(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	aggregation := "21245-dummy.plugin-1"

	plugin := "21245-dummy.plugin"

	aggregationColumn := "interface~traffic.bits.per.sec"

	datastore.UpdateVerticalAggregations("interface~traffic.bytes.per.sec", true)

	valid := manager.validateVerticalAggregation(utils.MotadataMap{
		aggregationColumn: float64(datastore.StringColumn)})

	assertions := assert.New(t)

	assertions.False(valid)

	var indexableColumns []interface{}

	eventContext := utils.MotadataMap{
		utils.Type:             float64(utils.Log),
		utils.IndexableColumns: indexableColumns,
		aggregationColumn:      float64(datastore.StringColumn),
	}

	manager.updateHorizontalAggregations(utils.MotadataMap{

		aggregation: eventContext.DeepClone(),
	})

	assertions.NotEmpty(manager.qualifiedProbes[plugin])

	viewContext, found := manager.qualifiedProbes[plugin][plugin+utils.AggregationSeparator+"0"]

	assertions.True(found)

	manager.horizontalAggregations[plugin] = map[string]utils.MotadataMap{

		plugin + utils.AggregationSeparator + "0": viewContext,
	}

	clear(manager.qualifiedProbes)

	manager.updateHorizontalAggregations(utils.MotadataMap{
		aggregation: eventContext.DeepClone(),
	})

	assertions.Empty(manager.qualifiedProbes)
}

func TestAggregationJobDuplicateWidgetContextType1(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	//sending same request again for aggregation
	utils.SetLogLevel(0)

	assertions := assert.New(t)

	plugin := "50006-fortinet.utm.traffic"

	aggregations := make(map[string]utils.MotadataMap)

	widgetId := "123"

	aggregations[plugin+utils.AggregationSeparator+widgetId+utils.PartSeparator+"1"] = logMessageContainsView1Table

	manager.horizontalAggregations[plugin] = aggregations

	eventContext := utils.MotadataMap{

		utils.Type:                 float64(utils.Log),
		FortinetTrafficBytesPerSec: float64(datastore.IntegerColumn),

		utils.IndexableColumns: []interface{}{
			utils.EventSource,
			utils.EventCategory,
		},
		utils.Filters: utils.MotadataMap{
			utils.DataFilter: utils.MotadataMap{

				"operator": "and",
				"filter":   "include",
				"groups": []interface{}{

					map[string]interface{}{

						"filter":   "include",
						"operator": "or",
						"conditions": []interface{}{

							map[string]interface{}{

								"operand":  "message",
								"operator": "contain",
								"value":    "message",
							},
						},
					},
				},
			},
		},
	}

	manager.processNotifications(utils.MotadataMap{
		utils.OperationType:                       utils.WidgetCreate,
		utils.DatastoreFormat:                     utils.HorizontalFormat,
		plugin + utils.HyphenSeparator + widgetId: eventContext.Clone(),
	})

	assertions.NotNil(plugin)

	utils.AssertLogMessage(assertions, "Job Manager", "job", "aggreation already exists for the plugin")

}

func TestAggregationJobDuplicateWidgetContextType2(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	//sending same request again for aggregation , change in indexable column
	utils.SetLogLevel(0)

	assertions := assert.New(t)

	plugin := "50007-fortinet.utm.traffic"

	aggregations := make(map[string]utils.MotadataMap)

	widgetId := "123"

	aggregations[plugin+utils.AggregationSeparator+widgetId+utils.PartSeparator+"1"] = logMessageContainsView1Table

	manager.horizontalAggregations[plugin] = aggregations

	eventContext := utils.MotadataMap{

		utils.Type:                 float64(utils.Log),
		FortinetTrafficBytesPerSec: float64(datastore.IntegerColumn),

		utils.IndexableColumns: []interface{}{
			utils.EventSource,
		},
		utils.Filters: utils.MotadataMap{
			utils.DataFilter: utils.MotadataMap{

				"operator": "and",
				"filter":   "include",
				"groups": []interface{}{

					map[string]interface{}{

						"filter":   "include",
						"operator": "or",
						"conditions": []interface{}{

							map[string]interface{}{

								"operand":  "message",
								"operator": "contain",
								"value":    "message",
							},
						},
					},
				},
			},
		},
	}

	manager.processNotifications(utils.MotadataMap{
		utils.OperationType:                       utils.WidgetCreate,
		utils.DatastoreFormat:                     utils.HorizontalFormat,
		plugin + utils.HyphenSeparator + widgetId: eventContext.Clone(),
	})

	<-utils.AggregationJobWriteNotifications[0]

	utils.AssertLogMessage(assertions, "Job Manager", "job", "aggreation is discarded")

}

func TestAggregationJobInvalidContext(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	assertions := assert.New(t)

	aggregationJobs[0].prepareQueryContext(utils.MotadataMap{})

	bytes, err := utils.ReadLogFile("Aggregation Job-"+codec.INTToStringValue(aggregationJobs[0].aggregationJobId), "job")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "invalid plugin received from context"))

	aggregationJobs[0].prepareQueryContext(utils.MotadataMap{utils.Plugin: "dummy-plugin"})

	bytes, err = utils.ReadLogFile("Aggregation Job-"+codec.INTToStringValue(aggregationJobs[0].aggregationJobId), "job")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "invalid context for plugin dummy-plugin"))

	manager.excludedHorizontalAggregations["dummyPlugin1"] = ""

	manager.processNotifications(utils.MotadataMap{

		utils.OperationType: utils.Recover,

		utils.Plugin: "dummyPlugin1",
	})

	bytes, err = utils.ReadLogFile("Job Manager", "job")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "occurred while processing notification"))

}

// Make sure to run these 4 testcases at last
func TestManagerVerticalAggregationNotification(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	tick := int64(1706097235)

	interval := 5

	plugin := "181-linux.plugin"

	expectedContext := utils.MotadataMap{

		utils.Interval: interval,
		utils.Tick:     tick,
		utils.Plugin:   plugin,
		utils.EventContext: utils.MotadataMap{
			utils.Type:              int(utils.PerformanceMetric),
			writer.SystemCPUPercent: float64(datastore.IntegerColumn),
		},
		utils.DatastoreFormat: utils.VerticalFormat,
		utils.OperationType:   utils.Recover,
	}

	id := utils.GetFastModN(utils.GetHash64([]byte(plugin+codec.INT64ToStringValue(tick))), utils.AggregationJobs)

	manager.processNotifications(expectedContext)

	actualContext := <-utils.AggregationJobWriteNotifications[id]

	assertions := assert.New(t)

	assertions.Equal(utils.Sync, actualContext.GetIntValue(utils.OperationType))

	assertions.Equal(expectedContext.GetStringValue(utils.Plugin), actualContext.GetStringValue(utils.Plugin))

	assertions.Equal(expectedContext.GetIntValue(utils.Interval), actualContext.GetIntValue(utils.Interval))

	assertions.Equal(expectedContext.GetInt64Value(utils.Tick), actualContext.GetInt64Value(utils.Tick))

	assertions.Equal(expectedContext.GetMapValue(utils.EventContext), actualContext.GetMapValue(utils.EventContext))

	assertions.Equal(expectedContext.GetIntValue(utils.DatastoreFormat), actualContext.GetIntValue(utils.DatastoreFormat))

}

func TestManagerSyncVerticalAggregationNotification(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	tick := int64(1706097235)

	interval := 5

	plugin := "181-linux.plugin"

	expectedContext := utils.MotadataMap{

		utils.Interval: interval,
		utils.Tick:     tick,
		utils.Plugin:   plugin,
		utils.EventContext: utils.MotadataMap{
			utils.Type:              int(utils.PerformanceMetric),
			writer.SystemCPUPercent: float64(datastore.IntegerColumn),
		},
		utils.DatastoreFormat: utils.VerticalFormat,
		utils.OperationType:   utils.Sync,
	}

	manager.processNotifications(expectedContext)

}

func TestManagerHorizontalAggregationNotification(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	_ = os.RemoveAll(utils.JobDir)

	tick := utils.RoundOffUnixSeconds(time.Now().Unix(), 15)

	interval := 15

	plugin := "500005-fortinet"

	expectedContext := utils.MotadataMap{

		utils.Interval:        interval,
		utils.Tick:            tick,
		utils.Plugin:          plugin,
		utils.DatastoreFormat: utils.HorizontalFormat,
		utils.OperationType:   utils.Recover,
	}

	id := utils.GetFastModN(utils.GetHash64([]byte(plugin+codec.INT64ToStringValue(tick))), utils.AggregationJobs)

	manager.processNotifications(expectedContext)

	manager.sendPendingJobNotifications()

	actualContext := <-utils.AggregationJobWriteNotifications[id]

	assertions := assert.New(t)

	assertions.Equal(utils.Sync, actualContext.GetIntValue(utils.OperationType))

	assertions.Equal("500005-fortinet", actualContext.GetStringValue(utils.Plugin))

	assertions.Equal(expectedContext.GetIntValue(utils.Interval), actualContext.GetIntValue(utils.Interval))

	assertions.Equal(tick, actualContext.GetInt64Value(utils.Tick))

	assertions.Equal(expectedContext.GetIntValue(utils.DatastoreFormat), actualContext.GetIntValue(utils.DatastoreFormat))

	expectedContext = utils.MotadataMap{

		utils.Interval:        interval,
		utils.Tick:            tick,
		utils.Plugin:          plugin + utils.AggregationSeparator + "1",
		utils.DatastoreFormat: utils.HorizontalFormat,
		utils.OperationType:   utils.Sync,
		Aggregation:           plugin + utils.AggregationSeparator + "1",
		utils.Position:        1,
	}

	manager.processNotifications(expectedContext)

	key := plugin + utils.AggregationSeparator + "1" + utils.KeySeparator + utils.INT64ToStringValue(utils.GetBaseTickv1(tick)) + utils.KeySeparator + codec.INTToStringValue(interval) + utils.KeySeparator + utils.HorizontalFormat

	assertions.Equal(len(manager.pendingPositions[key]), 0)

}

// Scenario firstly we send the flush notification and after syncing of data we send delete notification to delete that particular widget
func TestManagerHorizontalAggregationFlushAndDeleteNotification(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	_ = os.RemoveAll(utils.JobDir)

	tick := utils.RoundOffUnixSeconds(time.Now().Unix(), 15)

	plugin := "500005-fortinet1"

	expectedContext := utils.MotadataMap{

		utils.Tick:            tick,
		utils.Plugin:          plugin,
		utils.DatastoreFormat: utils.HorizontalFormat,
		utils.OperationType:   utils.Flush,
	}

	aggregation := map[string]utils.MotadataMap{}

	aggregation[plugin+utils.AggregationSeparator+"1234567"+utils.PartSeparator+"1"] = utils.MotadataMap{

		utils.Filters: struct {
		}{},
	}

	manager.horizontalAggregations[plugin] = aggregation

	id := 0

	manager.processNotifications(expectedContext)

	manager.sendPendingJobNotifications()

	var actualContext utils.MotadataMap

	for key, pendingPositions := range manager.pendingPositions {

		utils.Split(key, utils.KeySeparator, manager.tokenizer)
		tick := codec.StringToINT64(manager.tokenizer.Tokens[1])
		interval := codec.StringToINT(manager.tokenizer.Tokens[2])

		for position := range pendingPositions {

			roundedTick := tick + (int64(position) * int64(interval) * 60)

			id = utils.GetFastModN(utils.GetHash64([]byte(plugin+utils.AggregationSeparator+"1234567"+utils.PartSeparator+"1"+codec.INT64ToStringValue(roundedTick))), utils.AggregationJobs)

			actualContext = <-utils.AggregationJobWriteNotifications[id]

		}
	}

	assertions := assert.New(t)

	assertions.Equal(utils.Sync, actualContext.GetIntValue(utils.OperationType))

	assertions.Equal("500005-fortinet1", actualContext.GetStringValue(utils.Plugin))

	assertions.Equal(expectedContext.GetIntValue(utils.DatastoreFormat), actualContext.GetIntValue(utils.DatastoreFormat))

	datastore.AddHorizontalAggregation(plugin, plugin+utils.AggregationSeparator+"1234567"+utils.PartSeparator+"1", map[string]interface{}{})

	expectedContext = utils.MotadataMap{

		plugin:              "1234567",
		utils.OperationType: utils.WidgetDelete,
	}

	manager.processNotifications(expectedContext)

	assertions.Equal(len(manager.pendingPositions), 0)

	assertions.Equal(len(manager.horizontalAggregations[plugin]), 0)

}

func TestManagerCleanUpTimerNotification(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	clear(manager.excludedHorizontalAggregations)

	manager.excludedHorizontalAggregations["view1"] = "plugin1"

	manager.cleanup()

	assertions := assert.New(t)

	time.Sleep(time.Second * 3)

	assertions.Equal(0, len(manager.excludedHorizontalAggregations))
}

func TestManagerLoadIndices(t *testing.T) {

	manager := testSetup()

	defer testSetupDestroy()

	defer cache.Clear()

	defer verifyFailure(t)

	plugin := "500-plugin"

	aggregations := map[string]map[string]utils.MotadataMap{

		plugin: {

			plugin + utils.AggregationSeparator + "5": {},
			plugin + utils.AggregationSeparator + "6": {},
			plugin + utils.AggregationSeparator + "7": {},
			plugin + utils.AggregationSeparator + "0": {},
			plugin + utils.AggregationSeparator + "1234567" + utils.PartSeparator + "1": {

				utils.Filters: struct{}{},
			},
		},
	}

	bytes, _ := json.MarshalIndent(aggregations, "", " ")

	_ = os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.HorizontalAggregations, bytes, 0666)

	manager = NewManager()

	assertions := assert.New(t)

	assertions.Equal(8, manager.indices[plugin])

	assertions.Equal(1, manager.indices[plugin+utils.AggregationSeparator+"1234567"])
}

func TestWriteTxn(t *testing.T) {

	assertions := assert.New(t)

	job := NewAggregationJob(1, nil)

	keyBytes := []byte("dummy-key-1")

	valueBytes := []byte("12345678dummy-value")

	store, err := storage.OpenOrCreateStore("dummy-store-type-2", utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	job.txnOffset = 4

	for i := 0; i < 1000000; i++ {

		err = job.writeTxn(keyBytes, valueBytes)

		assertions.Nil(err)
	}

	job.store = store

	job.txnPartition = store.GetPartition(keyBytes, job.tokenizers[1])

	err = job.commit(0, 0)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	wg := sync.WaitGroup{}

	valueBytes = make([]byte, 100000)

	found, data, err := store.Get(keyBytes, valueBytes, encoder, storage.DiskIOEvent{}, &wg, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(data, []byte("dummy-value"))

	utils.AssertLogMessage(assertions, "Aggregation Job-"+codec.INTToStringValue(1), "job", "remapping anonymous txn buffers with current length")

	store.Close(encoder)

}

func TestManagerExcludedAggregations(t *testing.T) {

	manager := testSetup()

	manager.Start()

	clear(manager.horizontalAggregations)

	clear(manager.excludedHorizontalAggregations)

	clear(manager.qualifiedProbes)

	clear(manager.indices)

	assertions := assert.New(t)

	aggregationContextV1 := utils.MotadataMap{

		utils.Type: float64(utils.Flow),
		utils.IndexableColumns: []interface{}{
			"a1",
			"a2",
		},
		writer.VolumeBytesPerSec: datastore.IntegerColumn,
		writer.PacketsPerSec:     datastore.IntegerColumn,
	}

	aggregationContextV2 := utils.MotadataMap{

		utils.Type: float64(utils.Flow),
		utils.IndexableColumns: []interface{}{
			"b1",
			"b2",
		},
		writer.VolumeBytesPerSec: datastore.IntegerColumn,
		writer.PacketsPerSec:     datastore.IntegerColumn,
	}

	aggregationContextV3 := utils.MotadataMap{

		utils.Type: float64(utils.Flow),
		utils.IndexableColumns: []interface{}{
			"c1",
			"c2",
		},
		writer.VolumeBytesPerSec: datastore.IntegerColumn,
		writer.PacketsPerSec:     datastore.IntegerColumn,
	}

	aggregations := manager.updateHorizontalAggregations(utils.MotadataMap{

		"700000-flow-5": aggregationContextV1.DeepClone(),
	})

	for aggregation, context := range manager.qualifiedProbes["700000-flow"] {

		utils.Split(aggregation, utils.AggregationSeparator, manager.tokenizer)

		manager.processProbeAcknowledgement(utils.HorizontalFormat, utils.MotadataMap{

			utils.Plugin: manager.tokenizer.Tokens[0],

			utils.EventContext: context,

			currentAggregations: aggregations["700000-flow"],

			Aggregation: aggregation,
		})

	}

	aggregations = manager.updateHorizontalAggregations(utils.MotadataMap{

		"700000-flow-5": aggregationContextV2.DeepClone(),
	})

	for aggregation, context := range manager.qualifiedProbes["700000-flow"] {

		utils.Split(aggregation, utils.AggregationSeparator, manager.tokenizer)

		manager.processProbeAcknowledgement(utils.HorizontalFormat, utils.MotadataMap{

			utils.Plugin: manager.tokenizer.Tokens[0],

			utils.EventContext: context,

			currentAggregations: aggregations["700000-flow"],

			Aggregation: aggregation,
		})

	}

	_, err := os.Stat(utils.ConfigDir + utils.PathSeparator + ExcludedAggregations)

	assertions.NoError(err)

	bytes, err := os.ReadFile(utils.ConfigDir + utils.PathSeparator + ExcludedAggregations)

	assertions.NoError(err)

	excludedAggregations := utils.MotadataMap{}

	assertions.NoError(json.Unmarshal(bytes, &excludedAggregations))

	assertions.Contains(excludedAggregations, "700000-flow@@@0")

	aggregations = manager.updateHorizontalAggregations(utils.MotadataMap{

		"700000-flow-5": aggregationContextV3.DeepClone(),
	})

	for aggregation, context := range manager.qualifiedProbes["700000-flow"] {

		utils.Split(aggregation, utils.AggregationSeparator, manager.tokenizer)

		manager.processProbeAcknowledgement(utils.HorizontalFormat, utils.MotadataMap{

			utils.Plugin: manager.tokenizer.Tokens[0],

			utils.EventContext: context,

			currentAggregations: aggregations["700000-flow"],

			Aggregation: aggregation,
		})

	}

	manager.flush(ExcludedAggregation)

	_, err = os.Stat(utils.ConfigDir + utils.PathSeparator + ExcludedAggregations)

	assertions.NoError(err)

	bytes, err = os.ReadFile(utils.ConfigDir + utils.PathSeparator + ExcludedAggregations)

	assertions.NoError(err)

	excludedAggregations = utils.MotadataMap{}

	assertions.NoError(json.Unmarshal(bytes, &excludedAggregations))

	assertions.Contains(excludedAggregations, "700000-flow@@@0")

	assertions.Contains(excludedAggregations, "700000-flow@@@1")

}

func TestFLOAT64Column(t *testing.T) {

	assertions := assert.New(t)

	item := Float64Column{}

	item.Len()

	item.values = []float64{1, 2}

	item.groups = []int32{1, 2}

	item.Swap(0, 1)

	item.Swap(0, 1)

	assertions.False(item.Less(0, 1))
}

func TestINT64Column(t *testing.T) {

	assertions := assert.New(t)

	item := Int64Column{}

	item.Len()

	item.values = []int64{1, 2}

	item.groups = []int32{1, 2}

	item.Swap(0, 1)

	item.Swap(0, 1)

	assertions.False(item.Less(0, 1))
}
