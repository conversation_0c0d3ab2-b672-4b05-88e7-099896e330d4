/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-10			 <PERSON><PERSON>val Bera			Motadata-4913  Altered modulo operator with new modulo function
  2025-03-01             Vedant Dokania	        Motadata-5194  Empty valueBuffer check
* 2025-05-05			 Swapnil <PERSON>. <PERSON>		M<PERSON>ADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-10             <PERSON><PERSON><PERSON>ra            MOTADATA-6174  Added Unmap function to release memory-mapped resources
* 2025-06-04             <PERSON><PERSON><PERSON>            MOTADATA-5780 Called custom MmapAnonymous and Munmap functions. Also, closed mappingStore while recovering temp mappings
* 2025-06-10			 Swapnil A. Dave		suppressing error logs for no store qualification while running probe for datastoretype.
* 2025-06-23             Dhaval Bera            MOTADATA-6642  Added comments and memory aligned the struct

*/

package job

import (
	"errors"
	"fmt"
	"github.com/kelindar/bitmap"
	"github.com/tidwall/gjson"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"runtime"
	"strings"
	"sync"
	"time"
)

var (
	// mappingCleanupJobLogger provides logging functionality for mapping cleanup operations
	// Used throughout the job to record events, errors, and performance metrics
	mappingCleanupJobLogger = utils.NewLogger("Mapping Cleanup Job", "job")

	// MappingCleanupJobTimer defines when the mapping cleanup job should run
	// Scheduled for 5 AM the next day to run after retention job (which runs at 3 AM)
	// This ensures proper sequencing of cleanup operations
	MappingCleanupJobTimer = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day()+1, 05, 00, 00, 00, time.Local).Local()
)

const (
	// errorNoStoresQualified is returned when no data stores meet the criteria for cleanup
	// This is a normal condition when no stores need processing
	errorNoStoresQualified = "no stores qualified"
)

// MappingCleanupJob manages the cleanup of unused string and numeric mappings in data stores.
//
// This job performs critical maintenance by:
// 1. Identifying mapping ordinals that are still in use across all qualified stores
// 2. Removing unused mappings to reclaim storage space and improve performance
// 3. Repairing mapping stores to maintain consistency and optimize access patterns
// 4. Processing stores in batches to manage memory usage and system load
//
// The job operates on a scheduled basis and processes stores based on column assignments
// using consistent hashing to distribute work across multiple job instances.
//
// Memory layout is optimized for 64-bit systems:
// - Pointers and slices (8 bytes each) are placed first
// - Maps and channels (8 bytes each) follow
// - Structs are placed next (variable size)
// - Smaller types (int, bool) are placed last to minimize padding
type MappingCleanupJob struct {
	// 8-byte aligned fields (pointers, slices, maps, channels)
	tokenizer             *utils.Tokenizer           // Tokenizer for processing text data during store operations
	usedMappingOrdinals   *bitmap.Bitmap             // Bitmap tracking which mapping ordinals are currently in use
	waitGroup             *sync.WaitGroup            // Synchronization primitive for coordinating concurrent operations
	keyBuffers            [][]byte                   // Pre-allocated buffers for storing keys during batch operations
	valueBuffers          [][]byte                   // Pre-allocated memory-mapped buffers for storing values
	events                []storage.DiskIOEventBatch // Batch events for efficient disk I/O operations
	metadata              utils.MotadataMap          // Metadata information extracted from store files
	qualifiedStores       utils.MotadataMap          // Map of store names that qualify for cleanup processing
	ShutdownNotifications chan bool                  // Channel for receiving shutdown signals from external components

	// Struct fields (variable size, typically larger than basic types)
	event   storage.DiskIOEvent // Single disk I/O event for individual operations
	encoder codec.Encoder       // Encoder for data serialization operations
	decoder codec.Decoder       // Decoder for data deserialization operations

	// 4-byte aligned fields
	id int // Unique identifier for this job instance

	// 1-byte aligned fields (placed last to minimize struct padding)
	shutdown bool // Flag indicating whether the job should terminate
}

// NewMappingCleanUpJob creates and initializes a new MappingCleanupJob instance.
//
// This constructor sets up all necessary components for efficient mapping cleanup:
// 1. Allocates memory-mapped buffers for high-performance I/O operations
// 2. Initializes disk I/O event batches for concurrent processing
// 3. Creates encoder/decoder with optimized memory pools
// 4. Sets up synchronization primitives and data structures
//
// Memory optimization features:
// - Uses memory-mapped anonymous buffers for value storage (faster than heap allocation)
// - Falls back to regular heap allocation if memory mapping fails
// - Pre-allocates all buffers to avoid runtime allocation overhead
// - Configures memory pools for efficient encoder/decoder operations
//
// Parameters:
//   - id: Unique identifier for this job instance (used for work distribution)
//
// Returns:
//   - *MappingCleanupJob: Fully initialized job ready to start processing
//
// Error handling:
// - Gracefully handles memory mapping failures by falling back to heap allocation
// - Ensures all critical components are properly initialized
func NewMappingCleanUpJob(id int) *MappingCleanupJob {

	// Initialize disk I/O event batches for concurrent processing
	// Each batch handles multiple operations to improve throughput
	events := make([]storage.DiskIOEventBatch, utils.MaxStoreParts)

	for i := 0; i < len(events); i++ {

		// Initialize each batch event structure
		events[i] = storage.DiskIOEventBatch{}
	}

	// Pre-allocate value buffers for efficient data processing
	valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	// Pre-allocate key buffers for batch operations
	keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	// Create memory-mapped buffers for optimal performance
	// Memory mapping provides faster access than heap-allocated buffers
	for i := 0; i < len(keyBuffers); i++ {

		// Attempt to create memory-mapped anonymous buffer
		// MAP_PRIVATE|MAP_ANON creates a private, anonymous mapping
		bytes, err := utils.MmapAnonymous(utils.MaxValueBufferBytes)

		if err != nil {

			// Fall back to heap allocation if memory mapping fails
			// This ensures the job can still function on systems with mmap restrictions
			bytes = make([]byte, utils.MaxValueBufferBytes)
		}

		valueBuffers[i] = bytes
	}

	// Create memory pool for encoder/decoder operations
	// Pool size: MaxStoreParts+2 for concurrent operations plus overhead
	// Uses blob pools for efficient large buffer management
	pool := utils.NewMemoryPool(utils.MaxStoreParts+2, utils.MaxPoolLength, true, utils.DefaultBlobPools)

	return &MappingCleanupJob{

		// Initialize tokenizer with pre-allocated token slice
		tokenizer: &utils.Tokenizer{

			Tokens: make([]string, utils.TokenizerLength),
		},

		// Assign pre-allocated buffers for efficient batch processing
		keyBuffers: keyBuffers,

		valueBuffers: valueBuffers,

		// Assign pre-initialized disk I/O event batches
		events: events,

		// Initialize single disk I/O event for individual operations
		event: storage.DiskIOEvent{},

		// Set unique job identifier for work distribution
		id: id,

		// Create encoder and decoder with shared memory pool
		encoder: codec.NewEncoder(pool),

		decoder: codec.NewDecoder(pool),

		// Initialize synchronization primitive for concurrent operations
		waitGroup: &sync.WaitGroup{},

		// Initialize bitmap for tracking used mapping ordinals
		usedMappingOrdinals: &bitmap.Bitmap{},

		// Initialize shutdown flag to false (job is ready to run)
		shutdown: false,

		// Initialize map for storing qualified store names
		qualifiedStores: make(utils.MotadataMap),

		// Create buffered channel for shutdown notifications (capacity: 5)
		// Buffer allows multiple shutdown signals without blocking senders
		ShutdownNotifications: make(chan bool, 5),

		// Initialize metadata map for storing store information
		metadata: map[string]interface{}{},
	}
}

// Start initializes and starts the MappingCleanupJob in a separate goroutine.
//
// This method performs the following operations:
// 1. Starts a background goroutine for continuous mapping cleanup processing
// 2. Registers with the job engine shutdown mutex for coordinated shutdown
// 3. Runs the main processing loop until shutdown is requested
// 4. Ensures proper cleanup and synchronization during shutdown
//
// The job will continue running until either:
// - A shutdown signal is received via ShutdownNotifications channel
// - The global shutdown flag is set
// - The job's internal shutdown flag is set
//
// Concurrency management:
// - Uses JobEngineShutdownMutex to coordinate with other jobs during shutdown
// - Ensures all jobs complete gracefully before system shutdown
// - Prevents race conditions during shutdown sequence
func (mappingCleanupJob *MappingCleanupJob) Start() {

	// Start background goroutine for continuous mapping cleanup processing
	go func() {

		// Register with job engine shutdown mutex for coordinated shutdown
		// This ensures all jobs complete gracefully during system shutdown
		utils.JobEngineShutdownMutex.Add(1)

		// Main processing loop - continues until shutdown is requested
		for {

			// Check for shutdown conditions:
			// - mappingCleanupJob.shutdown: Internal shutdown flag
			// - utils.GlobalShutdown: System-wide shutdown flag
			if mappingCleanupJob.shutdown || utils.GlobalShutdown {

				break
			}

			// Execute main processing logic
			// This handles the scheduled cleanup operations
			mappingCleanupJob.process()
		}

		// Signal completion to job engine shutdown coordinator
		// This allows the system to proceed with shutdown once all jobs are done
		utils.JobEngineShutdownMutex.Done()
	}()

}

// process executes the main processing loop for the MappingCleanupJob.
//
// This method handles the core functionality of the mapping cleanup job:
// 1. Sets up a timer to trigger cleanup operations at the scheduled time
// 2. Sets up panic recovery to handle unexpected errors gracefully
// 3. Processes cleanup operations and shutdown signals
// 4. Manages memory-mapped resources during shutdown
//
// The method uses a select statement to handle two types of events:
// - Timer events that trigger the actual cleanup operations
// - Shutdown notifications from ShutdownNotifications channel
//
// Timer behavior:
// - Calculates the duration until the next scheduled cleanup time
// - Triggers cleanup operations when the timer expires
// - Automatically reschedules for the next cleanup cycle
//
// Error handling:
// - Logs cleanup failures but continues operation
// - Recovers from panics using deferred recovery function
// - Ensures proper resource cleanup during shutdown
//
// Resource management:
// - Unmaps memory-mapped buffers during shutdown to prevent memory leaks
// - Releases encoder memory pool resources
// - Stops the timer to prevent further cleanup attempts
func (mappingCleanupJob *MappingCleanupJob) process() {

	// Create timer that triggers when the next cleanup should occur
	// Calculates duration from current time to scheduled cleanup time
	ticker := time.NewTicker(time.Second * time.Duration(MappingCleanupJobTimer.Sub(time.Now().Local()).Seconds()))

	// Set up panic recovery to handle unexpected errors gracefully
	// This prevents the entire job from crashing due to unhandled panics
	defer mappingCleanupJob.recover(ticker)

	// Main event processing loop
	// Continues until shutdown signal is received
	for {

		select {

		// Handle timer events that trigger cleanup operations
		case <-ticker.C:

			// Execute the mapping cleanup operation
			if err := mappingCleanupJob.cleanup(); err != nil {

				// Log cleanup errors but continue operation
				// Cleanup failures shouldn't stop the job from running
				mappingCleanupJobLogger.Error(fmt.Sprintf("failed to do cleanup of mappings reason %v", err.Error()))

			}

		// Handle shutdown notifications
		case <-mappingCleanupJob.ShutdownNotifications:

			// Log shutdown event for monitoring and debugging
			mappingCleanupJobLogger.Info(fmt.Sprintf("shutdown notification received for mapping clean up job %v", mappingCleanupJob.id))

			// Unmap all memory-mapped value buffers to prevent memory leaks
			// This is critical for proper resource cleanup
			for index := range mappingCleanupJob.valueBuffers {

				if err := utils.Munmap(mappingCleanupJob.valueBuffers[index]); err != nil {

					// Log unmapping failures but continue cleanup
					// Partial cleanup is better than no cleanup
					mappingCleanupJobLogger.Warn(fmt.Sprintf("failed to unamp the value buffers for mapping cleanup job %v id and %v index", mappingCleanupJob.id, index))
				}
			}

			// Stop the timer to prevent further cleanup attempts
			ticker.Stop()

			// Release encoder memory pool resources
			mappingCleanupJob.encoder.MemoryPool.Unmap()

			// Set internal shutdown flag to stop processing
			mappingCleanupJob.shutdown = true

			// Exit the processing loop
			return

		}
	}
}

// cleanup performs the core mapping cleanup operations for all configured columns.
//
// This method implements the main cleanup algorithm by:
// 1. Iterating through all columns configured for mapping cleanup
// 2. Qualifying stores that need cleanup based on their types
// 3. Using consistent hashing to distribute work across job instances
// 4. Identifying used mapping ordinals across all qualified stores
// 5. Repairing mapping stores to remove unused mappings
//
// Work distribution:
// - Uses consistent hashing (GetFastModN) to assign columns to specific job instances
// - Ensures each column is processed by exactly one job instance
// - Provides load balancing across multiple cleanup job instances
//
// Algorithm overview:
// 1. For each column, qualify stores that contain mapping data
// 2. Check if this job instance should process the column (work distribution)
// 3. Scan all qualified stores to identify used mapping ordinals
// 4. Repair the mapping store to remove unused mappings
// 5. Log performance metrics for monitoring
//
// Error handling:
// - Continues processing other columns if one column fails
// - Logs errors but doesn't stop the entire cleanup process
// - Handles missing stores and qualification failures gracefully
//
// Returns:
//   - error: Last error encountered during processing (may be nil)
func (mappingCleanupJob *MappingCleanupJob) cleanup() (err error) {

	// Iterate through all columns configured for mapping cleanup
	// Each column may have different store types that need processing
	for column, storeTypes := range utils.MappingCleanupColumns {

		// Qualify stores that need cleanup based on their types
		// This identifies which stores contain data for this column
		if err := mappingCleanupJob.qualifyStores(storeTypes); err != nil {

			// Only log errors that are not "no stores qualified"
			// Missing stores is a normal condition and shouldn't be logged as an error
			if !strings.EqualFold(err.Error(), errorNoStoresQualified) {

				mappingCleanupJobLogger.Error(fmt.Sprintf("failed to do cleanup of mappings reason %v", err))
			}

			// Continue with next column if store qualification fails
			continue
		}

		// Declare variable for storing keys from qualified stores
		var keyBuffers [][]byte

		// Log the number of qualified stores for monitoring and debugging
		mappingCleanupJobLogger.Info(fmt.Sprintf("total stores qualified for mapping is %v", len(mappingCleanupJob.qualifiedStores)))

		// Use consistent hashing to determine if this job instance should process this column
		// This distributes work evenly across multiple job instances
		if utils.GetFastModN(utils.GetHash64([]byte(column)), utils.MappingCleanupJobs) != mappingCleanupJob.id {

			// Skip this column as it's assigned to a different job instance
			continue
		}

		// Get the mapping store for this column
		// Mapping stores contain the string-to-ordinal and numeric-to-ordinal mappings
		mappingStore := datastore.GetStore(column+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, false, true, mappingCleanupJob.encoder, mappingCleanupJob.tokenizer)

		if mappingStore == nil {

			// Log error if mapping store cannot be acquired
			mappingCleanupJobLogger.Error(fmt.Sprintf(utils.ErrorAcquireStore, column))

			// Continue with next column if mapping store is unavailable
			continue
		}

		// Log start of cleanup operation for this store
		mappingCleanupJobLogger.Info(fmt.Sprintf("cleanupjob started for %v store", mappingStore.GetName()))

		// Record start time for performance monitoring
		startTime := time.Now()

		// Mark the mapping store as being cleaned up
		// This prevents other operations from interfering with cleanup
		mappingStore.MarkCleanup()

		// Get current maximum ordinals to understand the mapping space
		// These values define the range of ordinals that may be in use
		maxStringMappingOrdinal := mappingStore.GetMaxStringMappingOrdinal()

		maxNumericMappingOrdinal := mappingStore.GetMaxNumericMappingOrdinal()

		// Clear the bitmap that tracks used mapping ordinals
		// This will be populated by scanning all qualified stores
		mappingCleanupJob.usedMappingOrdinals.Clear()

		// Flag to track whether we opened a store that needs to be closed
		open := false

		// Process all qualified stores to identify used mapping ordinals
		// This scans each store to find which ordinals are actually referenced
		for storeName := range mappingCleanupJob.qualifiedStores {

			// Reset open flag for each store
			open = false

			// Try to get the store without opening it (if already in memory)
			// This is more efficient than always opening stores
			store := datastore.GetStore(storeName, utils.None, false, false, mappingCleanupJob.encoder, mappingCleanupJob.tokenizer)

			if store == nil {

				// Store is not in memory, need to open it
				open = true

				// Open the store in read-only mode for scanning
				store = datastore.GetStore(storeName, utils.None, false, true, mappingCleanupJob.encoder, mappingCleanupJob.tokenizer)

				if store == nil {

					// Skip this store if it cannot be opened
					continue
				}

			}

			// Get all keys that contain ordinal mappings for this column
			// OrdinalSuffix identifies keys that contain mapping ordinal references
			if keyBuffers, err = store.GetContainKeys([]byte(column+utils.OrdinalSuffix), false); err != nil {

				// Log error but continue with other stores
				mappingCleanupJobLogger.Error(fmt.Sprintf("failed to get keys from the store %v reason %v ", store.GetName(), err.Error()))

				continue

			}

			// Process keys in batches to manage memory usage
			index := 0

			for _, bytes := range keyBuffers {

				// Add key to batch buffer
				mappingCleanupJob.keyBuffers[index] = bytes

				index++

				// Process batch when it reaches maximum size
				if index == utils.MaxWorkerEventKeyGroupLength-1 {

					// Process the current batch of keys
					if err = mappingCleanupJob.processStore(store, mappingCleanupJob.usedMappingOrdinals, index); err != nil {

						// Log processing error but continue with remaining batches
						mappingCleanupJobLogger.Error(fmt.Sprintf("failed to process store %v of mappings reason %v", store.GetName(), err))
					}

					// Reset index for next batch
					index = 0

				}

			}

			// Process any remaining keys in the final partial batch
			if index != 0 {

				if err = mappingCleanupJob.processStore(store, mappingCleanupJob.usedMappingOrdinals, index); err != nil {

					// Log processing error but continue with other stores
					mappingCleanupJobLogger.Error(fmt.Sprintf("failed to process store %v of mappings reason %v", store.GetName(), err))
				}

			}

			// Close store if we opened it during this iteration
			// This prevents memory leaks and resource exhaustion
			if open {

				// Mark store as closed and perform cleanup
				store.MarkClosed(mappingCleanupJob.encoder)

				store.Close(mappingCleanupJob.encoder)

				// Remove store from memory if it closed successfully
				if store.IsClosed() {

					datastore.RemoveStore(storeName)
				}
			}

		}

		// Synchronize the mapping store to ensure all changes are persisted
		// This is critical before performing the repair operation
		if err = mappingStore.Sync(mappingCleanupJob.encoder); err != nil {

			// Log sync error and continue with next column
			// Sync failure prevents safe repair, so skip this column
			mappingCleanupJobLogger.Error(fmt.Sprintf("failed to sync mappings for the store %v reason %v", mappingStore.GetName(), err.Error()))

			continue

		}

		// Repair mappings only if we found used ordinals
		// Empty bitmap means no mappings are in use, so no repair is needed
		if mappingCleanupJob.usedMappingOrdinals.Count() > 0 {

			// Repair the mapping store by removing unused mappings
			// This operation reclaims storage space and optimizes access patterns
			if err = mappingStore.RepairMappings(mappingCleanupJob.usedMappingOrdinals, mappingCleanupJob.encoder, maxStringMappingOrdinal, maxNumericMappingOrdinal); err != nil {

				// Log repair error and continue with next column
				// Repair failure doesn't affect other columns
				mappingCleanupJobLogger.Error(fmt.Sprintf("failed to reconstruct mappings for the store %v reason %v", mappingStore.GetName(), err.Error()))

				continue
			}
		}

		// Log completion time for performance monitoring
		// This helps track cleanup performance and identify bottlenecks
		mappingCleanupJobLogger.Info(fmt.Sprintf("mappping cleanupjob took %v seconds to cleanup %v store", time.Since(startTime), mappingStore.GetName()))

	}

	// Return the last error encountered (may be nil if all operations succeeded)
	return err

}

// processStore extracts mapping ordinals from a batch of keys in a data store.
//
// This method performs the core operation of identifying which mapping ordinals
// are actually in use by:
// 1. Retrieving values for a batch of keys from the store
// 2. Decoding the values to extract mapping ordinal arrays
// 3. Adding each ordinal to the used mappings bitmap
// 4. Managing memory pools to prevent memory leaks
//
// The method processes keys in batches for efficiency and memory management.
// Each key contains an array of mapping ordinals that reference entries
// in the mapping store.
//
// Parameters:
//   - store: The data store to process
//   - mappingBitmap: Bitmap to track which ordinals are in use
//   - index: Number of keys to process from the key buffers
//
// Returns:
//   - error: Any error encountered during processing
//
// Memory management:
// - Uses pre-allocated buffers for efficient batch processing
// - Releases memory pool resources after each decode operation
// - Handles empty or invalid values gracefully
//
// Error handling:
// - Returns immediately on critical errors (store access, decode failures)
// - Skips individual keys that have errors or empty values
// - Ensures memory pools are released even when errors occur
func (mappingCleanupJob *MappingCleanupJob) processStore(store *storage.Store, mappingBitmap *bitmap.Bitmap, index int) error {

	// Retrieve values for the batch of keys using efficient batch I/O
	// This uses disk I/O events and wait groups for concurrent processing
	valueBuffers, errs, err := store.GetMultiples(mappingCleanupJob.keyBuffers[:index], mappingCleanupJob.valueBuffers[:index], mappingCleanupJob.encoder, mappingCleanupJob.events, mappingCleanupJob.waitGroup, mappingCleanupJob.tokenizer, true)

	if err != nil {

		// Return immediately on critical store access errors
		return err
	}

	// Process each retrieved value to extract mapping ordinals
	for index, err = range errs {

		// Skip keys that had errors or returned empty values
		if err != nil || len(valueBuffers[index]) == 0 {

			continue
		}

		// Decode the value buffer to extract INT32 mapping ordinals
		// First byte contains encoding and data type information
		poolIndex, values, err := mappingCleanupJob.decoder.DecodeINT32Values(codec.GetEncoding(valueBuffers[index][0]), codec.GetDataType(valueBuffers[index][0]), valueBuffers[index][1:], string(mappingCleanupJob.keyBuffers[index]), store.GetName(), 0)

		if err != nil {

			// Return immediately on decode errors
			return err
		}

		// Add each mapping ordinal to the used mappings bitmap
		// This tracks which ordinals are actually referenced
		for _, value := range values {

			mappingBitmap.Set(uint32(value))
		}

		// Release the memory pool resources used for decoding
		// This prevents memory leaks during long-running operations
		mappingCleanupJob.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)
	}

	// Return success if all keys were processed
	return nil

}

// qualifyStores identifies data stores that need mapping cleanup based on their types.
//
// This method scans the datastore directory to find stores that match the specified
// store types and adds them to the qualified stores list. It handles both stores
// that are currently loaded in memory and stores that exist only on disk.
//
// Store qualification process:
// 1. Scans the datastore directory for store subdirectories
// 2. For each store, determines its type (from memory or metadata file)
// 3. Checks if the store type matches any of the expected types
// 4. Adds matching stores to the qualified stores map
//
// Store type determination:
// - If store is in memory: Gets type directly from store object
// - If store is on disk only: Reads metadata file to extract type information
// - Handles missing or corrupted metadata files gracefully
//
// Parameters:
//   - storeTypes: Array of datastore types that should be processed
//
// Returns:
//   - error: errorNoStoresQualified if no stores match, other errors for critical failures
//
// Error handling:
// - Returns error if datastore directory cannot be read
// - Logs warnings for stores with missing metadata but continues processing
// - Returns specific error if no stores qualify (normal condition)
func (mappingCleanupJob *MappingCleanupJob) qualifyStores(storeTypes []utils.DatastoreType) error {

	// Clear the qualified stores map for fresh qualification
	mappingCleanupJob.qualifiedStores.Clear()

	// Read the datastore directory to find all available stores
	dirs, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	if err != nil {

		// Return error if datastore directory cannot be read
		// This is a critical failure that prevents any store processing
		return errors.New(fmt.Sprintf("error %v occurred while reading datstore directory", err.Error()))

	}

	// Variable to hold the store type for each store being evaluated
	var storeType utils.DatastoreType

	// Iterate through all entries in the datastore directory
	for dir := range dirs {

		// Only process directory entries (stores are stored as directories)
		if dirs[dir].IsDir() {

			// Get the store name from the directory name
			storeName := dirs[dir].Name()

			// Try to get store type from memory if store is already loaded
			// This is more efficient than reading metadata files
			store := datastore.GetStore(storeName, utils.None, false, false, mappingCleanupJob.encoder, mappingCleanupJob.tokenizer)

			if store == nil {

				// Store is not in memory, read metadata file to get store type
				bytes, err := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + storeName + utils.PathSeparator + utils.MetadataFile)

				if err != nil || len(bytes) == 0 {

					// Log warning for stores with missing metadata but continue
					// This prevents one bad store from stopping the entire process
					mappingCleanupJobLogger.Error(fmt.Sprintf("failed to open the store %v, reason: metadata file does not exist.", storeName))

					continue
				}

				// Parse the metadata JSON to extract store information
				gjson.ParseBytes(bytes).ForEach(func(key, value gjson.Result) bool {
					mappingCleanupJob.metadata[key.String()] = value.Value()
					return true
				})

				// Extract store type from metadata
				storeType = utils.DatastoreType(mappingCleanupJob.metadata.GetIntValue(datastore.DatastoreType))

			} else {

				// Get store type directly from the loaded store object
				storeType = store.GetDatastoreType()

			}

			// Check if this store type matches any of the expected types
			for _, expectedStoreType := range storeTypes {

				if storeType == expectedStoreType {

					// Add store to qualified list and break from inner loop
					mappingCleanupJob.qualifiedStores[storeName] = struct{}{}

					break
				}

			}

		}

	}

	// Return error if no stores qualified for processing
	// This is a normal condition when no stores need cleanup
	if len(mappingCleanupJob.qualifiedStores) == 0 {

		return errors.New(errorNoStoresQualified)
	}

	// Return success if at least one store qualified
	return nil
}

// recover handles panic recovery and cleanup for the MappingCleanupJob.
//
// This method is called as a deferred function to handle unexpected panics during
// job processing. It performs critical cleanup operations to ensure the system
// remains in a consistent state after a panic occurs.
//
// Recovery operations:
// 1. Logs the panic error and stack trace for debugging
// 2. Resets temporary mapping data structures to prevent corruption
// 3. Stops the timer to prevent further cleanup attempts
// 4. Ensures mapping stores are left in a consistent state
//
// Cleanup scope:
// - Only processes columns assigned to this job instance (using consistent hashing)
// - Resets temporary mappings for all assigned columns
// - Handles cases where mapping stores cannot be acquired
//
// Error handling:
// - Logs all errors but continues cleanup for other columns
// - Ensures partial cleanup doesn't leave system in worse state
// - Provides detailed stack trace for debugging panic causes
//
// Parameters:
//   - ticker: Timer that needs to be stopped to prevent further operations
//
// Critical for system stability:
// - Prevents corrupted mapping data from persisting after panics
// - Ensures resources are properly released
// - Maintains system consistency even after unexpected failures
func (mappingCleanupJob *MappingCleanupJob) recover(ticker *time.Ticker) {

	// Check if a panic occurred during processing
	if err := recover(); err != nil {

		// Log the panic error for debugging and monitoring
		mappingCleanupJobLogger.Error(fmt.Sprintf("error %v occurred while processing mapping cleanup job", err))

		// Capture and log stack trace for detailed debugging
		// Allocate 1MB buffer for stack trace (should be sufficient for most cases)
		stackTraceBytes := make([]byte, 1<<20)

		mappingCleanupJobLogger.Error(fmt.Sprintf("!!!STACK TRACE for mapping cleanup job !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		// Log recovery attempt for monitoring
		mappingCleanupJobLogger.Error("restarting mapping cleanup job ...")

		// Clean up all existing data structures to prevent corruption
		// This is critical to ensure mapping stores remain consistent
		for column := range utils.MappingCleanupColumns {

			// Only process columns assigned to this job instance
			// Use same consistent hashing as main cleanup logic
			if utils.GetFastModN(utils.GetHash64([]byte(column)), utils.MappingCleanupJobs) != mappingCleanupJob.id {

				continue
			}

			// Get the mapping store for this column
			mappingStore := datastore.GetStore(column+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, false, true, mappingCleanupJob.encoder, mappingCleanupJob.tokenizer)

			if mappingStore == nil {

				// Log error if mapping store cannot be acquired but continue cleanup
				mappingCleanupJobLogger.Error(fmt.Sprintf(utils.ErrorAcquireStore, column))

				continue
			}

			// Reset temporary mapping data to prevent corruption
			// This ensures the store is left in a consistent state
			mappingStore.ResetTempMapping()

			mappingStore.Close(mappingCleanupJob.encoder)
		}

		// Stop the timer to prevent further cleanup attempts
		// This prevents the job from continuing in an inconsistent state
		ticker.Stop()

	}

}
