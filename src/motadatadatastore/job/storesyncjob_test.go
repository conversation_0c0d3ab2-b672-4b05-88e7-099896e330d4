/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs

* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization

 */

package job

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/utils"
	"os"
	"strings"
	"testing"
	"time"
)

func TestStoreSyncJob(t *testing.T) {

	datastore.Init()

	pool := utils.NewMemoryPool(2, utils.MaxPoolLength, false, utils.DefaultBlobPools)

	encoder := NewEncoder(pool)

	tokenizer := &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	store := datastore.GetStore("syncStore", utils.PerformanceMetric, true, true, encoder, tokenizer)

	err := store.Put([]byte("1^system.cpu.percent^0"), []byte{0, 0, 0, 0, 0, 0, 0, 0, 45}, NewEncoder(utils.NewMemoryPool(6, utils.MaxPoolLength, false, utils.DefaultBlobPools)), tokenizer)

	assertions := assert.New(t)

	assertions.Nil(err)

	store.Close(encoder)

	dir := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + "syncStore"

	entries, err := os.ReadDir(dir)

	assertions.Nil(err)

	valid := false

	for _, entry := range entries {

		if entry.Name() == utils.MultipartIdxFile || entry.Name() == utils.MetadataFile {

			continue
		}

		files, err := os.ReadDir(dir + utils.PathSeparator + entry.Name())

		assertions.Nil(err)

		for _, file := range files {

			if file.Name() == "datastore.idx" {

				valid = true

				break
			}
		}

	}

	assertions.True(valid)
}

func TestStoreSyncJobManagerPanic(t *testing.T) {

	assertions := assert.New(t)

	storeSyncJobManger := NewStoreSyncJobManager()

	utils.StoreSyncTimerSeconds = 2

	storeSyncJobManger.Start()

	defer func() {

		storeSyncJobManger.ShutdownNotifications <- true
	}()

	storeSyncJobManger.stores = nil

	storeSyncJobManger.encoder.MemoryPool = nil

	time.Sleep(time.Second * 6)

	bytes, err := utils.ReadLogFile("Sync Job Manager", "job")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "STACK TRACE for store sync job manager"))
}

func TestStoreSyncJobPanicRecover(t *testing.T) {

	assertions := assert.New(t)

	syncJobId := 21

	syncJob := NewStoreSyncJob(syncJobId, make(chan StoreSyncEvent, 5), nil)

	configs := utils.MotadataMap{
		"datastore.memory.pool.shrink.timer.seconds": 1,
		"system.log.level":                           2,
		"task.logging":                               "no",
		"datastore.host":                             "localhost",
		"deployment.type":                            1,
		"remote.event.processor.uuid":                "",
	}

	bytes, _ := json.Marshal(configs)

	utils.InitConfigs(bytes)

	utils.StoreSyncTimerSeconds = 1

	syncJob.Start()

	defer func() {

		syncJob.ShutdownNotifications <- true
	}()

	syncJob.tokenizer = nil

	syncJob.encoder.MemoryPool = nil

	time.Sleep(time.Second * 3)

	utils.AssertLogMessage(assertions, "Store Sync Job", "job", "!!!STACK TRACE for store sync job !!!")

}
