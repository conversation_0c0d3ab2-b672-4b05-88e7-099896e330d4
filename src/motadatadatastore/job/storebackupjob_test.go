/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs
* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-06-04             A<PERSON>l Shah            MOTADATA-5780 Test Case Refactoring

 */

package job

import (
	"archive/zip"
	"errors"
	"fmt"
	"github.com/stretchr/testify/assert"
	"io"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"
	"time"
)

var encoder = codec.NewEncoder(utils.NewMemoryPool(10, utils.MaxPoolLength, true, utils.DefaultBlobPools))

// Do not use cleanUpStores before this testcase
func TestStoreBackupJobStoreClose(t *testing.T) {

	utils.SetLogLevel(0)

	utils.StoreSyncTimerSeconds = 1

	utils.StoreSyncDirtyNotifications = make(chan string, 10)

	storeName := "backup-1"

	index := int(utils.GetHash64([]byte(storeName)) % uint64(utils.StoreSyncJobs))

	loadBackupTestSetup()

	storeBackupJob = NewStoreBackupJob()

	storeBackupJob.Start()

	syncJobManager = NewStoreSyncJobManager()

	syncJobManager.Start()

	time.Sleep(time.Second * 5)

	store := datastore.GetStore(storeName, utils.PerformanceMetric, true, true, encoder, tokenizer)

	keyBytes := []byte("1^system.cpu.percent^0")

	valueBytes := make([]byte, utils.MaxValueBufferBytes)

	expectedValues := []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	err := store.Put(keyBytes, append(make([]byte, utils.MaxValueBytes), expectedValues...), encoder, tokenizer)

	assertions := assert.New(t)

	assertions.Nil(err)

	event := storage.DiskIOEvent{}

	waitGroup := &sync.WaitGroup{}

	found, resultBytes, err := store.Get(keyBytes, valueBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(expectedValues, resultBytes)

	time.Sleep(time.Second * 5)

	utils.CleanupThresholdSeconds = 0

	keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	for i := 0; i < len(keyBuffers); i++ {

		valueBuffers[i] = make([]byte, utils.MaxValueBufferBytes)
	}

	store.Close(encoder)

	datastore.RemoveStore(storeName)

	storeBackupJob.BackupProfileNotifications <- utils.MotadataMap{

		"datastore.types": []interface{}{"metric"},
	}

	time.Sleep(time.Millisecond * 500)

	storeBackupJob.SnapshotNotifications <- true

	time.Sleep(time.Millisecond * 500)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + store.GetName())

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir)

	unzip(utils.ConfigDir+utils.ZipExtension, utils.CurrentDir+utils.PathSeparator+utils.ConfigDir, "")

	unzip(storeName+utils.ZipExtension, utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+storeName, "metric")

	entries, _ := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir)

	assertions.Equal(4, len(entries), fmt.Sprintf("syncjob index: %v", index))

	datastore.Init()

	store = datastore.GetStore(storeName, utils.None, true, true, encoder, tokenizer)

	found, resultBytes, err = store.Get(keyBytes, valueBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(expectedValues, resultBytes)
}

func TestStoreBackupJobStoreOpen(t *testing.T) {

	utils.SetLogLevel(0)

	utils.StoreSyncTimerSeconds = 1

	utils.StoreSyncDirtyNotifications = make(chan string, 10)

	storeName := "backup-12"

	index := int(utils.GetHash64([]byte(storeName)) % uint64(utils.StoreSyncJobs))

	loadBackupTestSetup()

	storeBackupJob = NewStoreBackupJob()

	storeBackupJob.Start()

	syncJobManager = NewStoreSyncJobManager()

	syncJobManager.Start()

	time.Sleep(time.Second * 5)

	store := datastore.GetStore(storeName, utils.PerformanceMetric, true, true, encoder, tokenizer)

	keyBytes := []byte("1^system.cpu.percent^0")

	valueBytes := make([]byte, utils.MaxValueBufferBytes)

	expectedValues := []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	err := store.Put(keyBytes, append(make([]byte, utils.MaxValueBytes), expectedValues...), encoder, tokenizer)

	assertions := assert.New(t)

	assertions.Nil(err)

	event := storage.DiskIOEvent{}

	waitGroup := &sync.WaitGroup{}

	found, resultBytes, err := store.Get(keyBytes, valueBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(expectedValues, resultBytes)

	time.Sleep(time.Second * 5)

	utils.CleanupThresholdSeconds = 0

	keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	for i := 0; i < len(keyBuffers); i++ {

		valueBuffers[i] = make([]byte, utils.MaxValueBufferBytes)
	}

	storeBackupJob.BackupProfileNotifications <- utils.MotadataMap{

		"datastore.types": []interface{}{"metric"},
	}

	time.Sleep(time.Millisecond * 500)

	storeBackupJob.SnapshotNotifications <- true

	time.Sleep(1 * time.Second)

	store.Close(encoder)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + store.GetName())

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir)

	unzip(utils.ConfigDir+utils.ZipExtension, utils.CurrentDir+utils.PathSeparator+utils.ConfigDir, "")

	unzip(storeName+utils.ZipExtension, utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+storeName, "metric")

	entries, _ := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir)

	assertions.Equal(4, len(entries), fmt.Sprintf("syncjob index: %v", index))

	datastore.Init()

	store = datastore.GetStore(storeName, utils.None, true, true, encoder, tokenizer)

	found, resultBytes, err = store.Get(keyBytes, valueBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(expectedValues, resultBytes)
}

func TestStoreBackupJobMultipleStoresOpen(t *testing.T) {

	utils.SetLogLevel(0)

	utils.StoreSyncTimerSeconds = 1

	assertions := assert.New(t)

	utils.StoreSyncDirtyNotifications = make(chan string, 50)

	storeName := "backup-17"

	mappingStoreName := "event.source11-mappings"

	index := int(utils.GetHash64([]byte(storeName)) % uint64(utils.StoreSyncJobs))

	loadBackupTestSetup()

	storeBackupJob = NewStoreBackupJob()

	storeBackupJob.Start()

	syncJobManager = NewStoreSyncJobManager()

	syncJobManager.Start()

	time.Sleep(time.Second * 8)

	store := datastore.GetStore(storeName, utils.PerformanceMetric, true, true, encoder, tokenizer)

	mappingStore := datastore.GetStore(mappingStoreName, utils.Mapping, true, true, encoder, tokenizer)

	err := mappingStore.PutStringMapping("************", encoder)

	assertions.Nil(err)

	keyBytes := []byte("1^system.cpu.percent^0")

	valueBytes := make([]byte, utils.MaxValueBufferBytes)

	expectedValues := []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	err = store.Put(keyBytes, append(make([]byte, utils.MaxValueBytes), expectedValues...), encoder, tokenizer)

	assertions.Nil(err)

	event := storage.DiskIOEvent{}

	waitGroup := &sync.WaitGroup{}

	found, resultBytes, err := store.Get(keyBytes, valueBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(expectedValues, resultBytes)

	time.Sleep(time.Second * 5)

	utils.CleanupThresholdSeconds = 0

	keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	for i := 0; i < len(keyBuffers); i++ {

		valueBuffers[i] = make([]byte, utils.MaxValueBufferBytes)
	}

	storeBackupJob.BackupProfileNotifications <- utils.MotadataMap{

		"datastore.types": []interface{}{"metric"},
	}

	time.Sleep(time.Millisecond * 500)

	utils.StoreSyncDirtyNotifications <- mappingStoreName

	storeBackupJob.SnapshotNotifications <- true

	time.Sleep(time.Second * 4)

	store.Close(encoder)

	mappingStore.Close(encoder)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + store.GetName())

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + mappingStoreName)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir)

	unzip(utils.ConfigDir+utils.ZipExtension, utils.CurrentDir+utils.PathSeparator+utils.ConfigDir, "")

	unzip(storeName+utils.ZipExtension, utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+storeName, "metric")

	unzip(mappingStoreName+utils.ZipExtension, utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+mappingStoreName, "metric")

	entries, _ := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir)

	assertions.Equal(4, len(entries), fmt.Sprintf("syncjob index: %v", index))

	datastore.Init()

	store = datastore.GetStore(storeName, utils.None, true, true, encoder, tokenizer)

	found, resultBytes, err = store.Get(keyBytes, valueBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(expectedValues, resultBytes)

	store = datastore.GetStore(mappingStoreName, utils.None, false, true, encoder, tokenizer)

	assertions.NotNil(store)

	found, ordinal, err := store.GetStringMapping("************")

	assertions.True(found)

	assertions.Equal(ordinal, int32(3))

	assertions.Nil(err)
}

func TestStoreIncompleteBackup(t *testing.T) {

	utils.SetLogLevel(0)

	utils.StoreSyncTimerSeconds = 1

	utils.StoreSyncDirtyNotifications = make(chan string, 10)

	if storeBackupJob != nil {

		storeBackupJob.ShutdownNotifications <- true

		time.Sleep(time.Second)
	}

	storeBackupJob = NewStoreBackupJob()

	storeName := "incomplete-createSnapshot"

	loadBackupTestSetup()

	store := datastore.GetStore(storeName, utils.PerformanceMetric, true, true, encoder, tokenizer)

	keyBytes := []byte("1^system.cpu.percent^0")

	valueBytes := make([]byte, utils.MaxValueBufferBytes)

	expectedValues := []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	err := store.Put(keyBytes, append(make([]byte, utils.MaxValueBytes), expectedValues...), encoder, tokenizer)

	assertions := assert.New(t)

	assertions.Nil(err)

	event := storage.DiskIOEvent{}

	waitGroup := &sync.WaitGroup{}

	found, resultBytes, err := store.Get(keyBytes, valueBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(expectedValues, resultBytes)

	storeBackupJob.pendingStoreBackups[storeName] = struct{}{}

	_ = storeBackupJob.save()

	storeBackupJob = NewStoreBackupJob()

	syncJobManager = NewStoreSyncJobManager()

	syncJobManager.Start()

	storeBackupJob.Start()

	utils.CleanupThresholdSeconds = 0

	keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	for i := 0; i < len(keyBuffers); i++ {

		valueBuffers[i] = make([]byte, utils.MaxValueBufferBytes)
	}

	storeBackupJob.BackupProfileNotifications <- utils.MotadataMap{

		"datastore.types": []interface{}{"metric"},
	}

	time.Sleep(time.Millisecond * 500)

	store.Close(encoder)

	storeBackupJob.SnapshotNotifications <- true

	time.Sleep(time.Second * 2)

	entries, _ := os.ReadDir(utils.BackUpDir)

	assertions.Equal(2, len(entries))

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + store.GetName())

	unzip(storeName+utils.ZipExtension, utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+storeName, "metric")

	datastore.Init()

	store = datastore.GetStore(storeName, utils.None, true, true, encoder, tokenizer)

	found, resultBytes, err = store.Get(keyBytes, valueBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(expectedValues, resultBytes)

}

func TestStoreBackupJobError(t *testing.T) {

	assertions := assert.New(t)

	handleError("error occurred %v", "error", errors.New("error"))

	bytes, err := utils.ReadLogFile("Store Backup Job", "job")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "error occurred error"))

	handleError("", "", errors.New("error1"))

	bytes, err = utils.ReadLogFile("Store Backup Job", "job")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "error1"))
}

func unzip(zipName, dst, profile string) {

	var archive *zip.ReadCloser

	var err error

	if profile == utils.Empty {

		archive, err = zip.OpenReader(utils.BackUpDir + utils.PathSeparator + zipName)

	} else {

		archive, err = zip.OpenReader(utils.BackUpDir + utils.PathSeparator + profile + utils.PathSeparator + zipName)
	}

	if err != nil {

		panic(err)
	}

	defer archive.Close()

	for _, f := range archive.File {

		filePath := filepath.Join(dst, f.Name)

		if !strings.HasPrefix(filePath, filepath.Clean(dst)+string(os.PathSeparator)) {

			continue
		}

		if f.FileInfo().IsDir() {

			os.MkdirAll(filePath, os.ModePerm)

			continue
		}

		if err := os.MkdirAll(filepath.Dir(filePath), os.ModePerm); err != nil {

			panic(err)
		}

		file, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())

		if err != nil {

			panic(err)
		}

		archivedFile, err := f.Open()

		if err != nil {

			panic(err)
		}

		if _, err := io.Copy(file, archivedFile); err != nil {

			panic(err)
		}

		file.Close()

		archivedFile.Close()
	}
}

func loadBackupTestSetup() {

	syncJobManager.ShutdownNotifications <- true

	time.Sleep(time.Millisecond * 500)

	_ = os.RemoveAll(utils.BackUpDir)

	_ = os.Mkdir(utils.BackUpDir, 0777)

	_ = os.RemoveAll(utils.ConfigDir)

	utils.CloneDirectory(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))+utils.PathSeparator+utils.ConfigDir, utils.CurrentDir+utils.PathSeparator+utils.ConfigDir)
}
