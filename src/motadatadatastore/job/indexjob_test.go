/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>ata-5190  Migrated constants from datastore to utils according to SonarQube standard
* 2025-05-05			 Swapnil A. Dave		M<PERSON>ADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-06-04             <PERSON><PERSON><PERSON> Shah            MOTADATA-5780 Test Case Refactoring
 */

package job

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/utils"
	"os"
	"strings"
	"testing"
	"time"
)

func TestIndexerJob(t *testing.T) {

	datastore.Close()

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.ColumnConfigFile)

	datastore.Init()

	assertions := assert.New(t)

	plugin := "123-postingliststore1"

	column1 := "posting.list.store1"

	utils.MaxIndexProbes = 100

	_ = os.RemoveAll(utils.JobDir)

	utils.IndexJobRequests = make(chan utils.MotadataMap, 1_00_000)

	indexerJob := NewIndexJob()

	valid := indexerJob.loadJobs()

	assertions.True(valid)

	file, _ := os.Create(utils.JobDir + utils.PathSeparator + IndexJobCTX)

	valid = indexerJob.loadJobs()

	assertions.True(valid)

	err := os.WriteFile(utils.JobDir+utils.PathSeparator+IndexJobCTX, make([]byte, 5), 0755)

	assertions.Nil(err)

	valid = indexerJob.loadJobs()

	assertions.False(valid)

	context := utils.MotadataMap{

		plugin: utils.MotadataMap{
			utils.BatchSize: 1,
			utils.Columns: utils.MotadataMap{
				column1: utils.Empty,
			},
			utils.Plugin: plugin,
		},
	}

	bytes, _ := json.Marshal(context)

	err = os.WriteFile(utils.JobDir+utils.PathSeparator+IndexJobCTX, bytes, 0755)

	assertions.Nil(err)

	valid = indexerJob.loadJobs()

	assertions.True(valid)

	err = os.WriteFile(utils.JobDir+utils.PathSeparator+IndexJobCTX, make([]byte, 5), 0755)

	assertions.Nil(err)

	indexerJob.Start()

	utils.IndexJobRequests <- utils.MotadataMap{}

	time.Sleep(time.Second * 1)

	bytes, err = utils.ReadLogFile("Index Job", "job")

	assertions.True(strings.Contains(string(bytes), "failed to load indexing jobs..."))

	file.Close()

	err = os.RemoveAll(utils.JobDir)

	assertions.Nil(err)

	indexerJob.ShutdownNotifications <- true

	time.Sleep(time.Second)

	indexerJob = NewIndexJob()

	indexerJob.shutdown = true

	indexerJob.Start()

	pool := utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools)

	encoder := NewEncoder(pool)

	timestamp := time.Now().UTC()

	storeName := datastore.GetStoreName(utils.UnixToSeconds(timestamp.Unix()), column1+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + INTToStringValue(int(Int64))

	store := datastore.GetStore(storeName, utils.Index, true, true, encoder, &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	})

	searchTermPrefix := "prefix-"

	valueBytes := make([]byte, utils.MaxValueBytes+1)

	tokenizer := &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	for i := 0; i < utils.MaxIndexProbes+1; i++ {

		_ = store.Put([]byte(searchTermPrefix+INTToStringValue(i)), valueBytes, encoder, tokenizer)

		indexerJob.updateIndexerProbeRecords(utils.MotadataMap{

			utils.Columns: utils.MotadataMap{
				column1: utils.Empty,
			},

			utils.Plugin: plugin,

			utils.BatchSize: 1,
		})

	}

	indices := datastore.GetIndexableColumns()

	assertions.Contains(indices.GetMapValue(utils.EventContext).GetMapValue(plugin), utils.EventSource)

	assertions.Contains(indices.GetMapValue(utils.EventContext).GetMapValue(plugin), utils.EventCategory)

	assertions.Contains(indices.GetMapValue(utils.EventContext).GetMapValue(plugin), utils.EventPatternId)

	assertions.Contains(indices.GetMapValue(utils.EventContext).GetMapValue(plugin), utils.EventSourceType)

	store.Close(encoder)
}

func TestInvalidIndexableColumn(t *testing.T) {

	datastore.Close()

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.ColumnConfigFile)

	utils.CleanUpStores()

	datastore.Init()

	assertions := assert.New(t)

	plugin := "123-postingliststore2"

	indexableColumn := "indexable.column"

	invalidIndexableColumn := "invalid.indexable.column"

	utils.MaxIndexProbes = 100

	_ = os.RemoveAll(utils.JobDir)

	indexerJob := NewIndexJob()

	indexerJob.shutdown = true

	indexerJob.Start()

	_ = os.MkdirAll(utils.JobDir, 0755)

	_, _ = os.Create(utils.JobDir + utils.PathSeparator + IndexJobCTX)

	timestamp := time.Now().UTC()

	indexableColumnStoreName := datastore.GetStoreName(utils.UnixToSeconds(timestamp.Unix()), indexableColumn+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + INTToStringValue(int(Int64))

	indexableColumnStore := datastore.GetStore(indexableColumnStoreName, utils.Index, true, true, encoder, &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	})

	invalidIndexableColumnStoreName := datastore.GetStoreName(utils.UnixToSeconds(timestamp.Unix()), invalidIndexableColumn+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + INTToStringValue(int(Int64))

	invalidIndexableColumnStore := datastore.GetStore(invalidIndexableColumnStoreName, utils.Index, true, true, encoder, &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	})

	searchTermPrefix := "prefix-"

	valueBytes := make([]byte, utils.MaxValueBytes+1)

	tokenizer := &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	datastore.AddInvalidIndexableColumn(plugin, invalidIndexableColumn)

	limit := 30

	for i := 0; i < utils.MaxIndexProbes; i++ {

		_ = indexableColumnStore.Put([]byte(searchTermPrefix+INTToStringValue(i%limit)), valueBytes, encoder, tokenizer)

		_ = invalidIndexableColumnStore.Put([]byte(searchTermPrefix+INTToStringValue(i%limit)), valueBytes, encoder, tokenizer)

		indexerJob.updateIndexerProbeRecords(utils.MotadataMap{

			utils.Columns: utils.MotadataMap{
				indexableColumn:        utils.Empty,
				invalidIndexableColumn: utils.Empty,
			},

			utils.Plugin: plugin,

			utils.BatchSize: 1,
		})

	}

	store := datastore.GetStore(indexableColumnStoreName, utils.Index, false, true, encoder, &utils.Tokenizer{})

	assertions.NotNil(store)

	assertions.True(datastore.IsIndexableColumn(plugin, indexableColumn))

	assertions.False(datastore.IsIndexableColumn(plugin, invalidIndexableColumn))

	store.Close(encoder)
}
