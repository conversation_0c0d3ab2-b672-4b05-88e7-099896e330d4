/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-06-04             <PERSON><PERSON><PERSON> Shah            MOTADATA-5780  Skip Lock files while copying stores
 */

package patchv1_11

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/kelindar/bitmap"
	cp "github.com/otiai10/copy"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/patch/patches"
	"motadatadatastore/server"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"sync"
	"time"
)

var (
	logger = utils.NewLogger("Patch 1.11", "patch")

	ProbeTimerSeconds = 30
)

const variant = 1.11

func Patch(valueBuffers [][]byte, encoder codec.Encoder, tokenizer *utils.Tokenizer, events []storage.DiskIOEventBatch, waitGroup *sync.WaitGroup) (err error) {

	defer func() {

		if r := recover(); r != nil {

			stackTraceBytes := make([]byte, 1<<20)

			logger.Error(fmt.Sprintf("error %v occurred in patch 1.11", err))

			logger.Error(fmt.Sprintf("!!!STACK TRACE for patch 1.11!!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			err = errors.New(fmt.Sprintf("err %v occurred while applying patch 1.11", r))
		}

	}()

	if err = patches.AddIndexableColumnsByDataStoreType(utils.EventPolicy, utils.PolicyType, utils.EventSource); err != nil {

		return err
	}

	initQueryConfigParams()

	var keys map[string]struct{}

	if keys, err = createAggregationContexts(tokenizer); err != nil {

		return err
	}

	server.InitQueryEngine()

	if err = migrateCountKeys(valueBuffers, encoder, tokenizer, events, waitGroup); err != nil {

		server.ShutdownQueryEngine()

		if utils.EnvironmentType != utils.DatastoreTestEnvironment {

			time.Sleep(time.Second * 5)

		}

		return err
	}

	defer func() {

		if utils.GlobalShutdown {

			return
		}

		server.ShutdownJobEngine()

		if utils.EnvironmentType != utils.DatastoreTestEnvironment {

			time.Sleep(time.Second * 10)

		}

		server.ShutdownRequestRouters()

		server.ShutdownQueryEngine()
	}()

	server.InitJobEngine()

	server.InitRequestRouters()

	if err = probeCompletion(keys); err != nil {

		return err
	}

	return nil
}

func initQueryConfigParams() {

	utils.QueryExecutors *= 2

	utils.LogQueryExecutors = utils.QueryExecutors / 2

	utils.FlowQueryExecutors = utils.QueryExecutors / 2

	utils.MetricQueryExecutors = 0

	utils.AIOpsEngineQueryExecutors = 0

	utils.DrillDownQueryExecutors = 0

	utils.QueryWorkers *= 2

	utils.LogQueryWorkers = utils.QueryWorkers / 2

	utils.FlowQueryWorkers = utils.QueryWorkers / 2

	utils.MetricQueryWorkers = 0

	utils.DrillDownQueryWorkers = 0

	utils.AIOpsEngineQueryWorkers = 0

	utils.AggregationJobs *= 2
}

func migrateCountKeys(valueBuffers [][]byte, encoder codec.Encoder, tokenizer *utils.Tokenizer, events []storage.DiskIOEventBatch, waitGroup *sync.WaitGroup) error {

	/*
		finding all the policy and trap stores that have been populated
	*/

	bytes, err := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.HorizontalAggregations)

	aggregations := make(map[string]map[string]utils.MotadataMap)

	if err != nil {

		logger.Error("failed to apply patch 1.11, reason: unable to read horizontal aggregations file.")

		return err
	}

	_ = json.Unmarshal(bytes, &aggregations)

	if len(aggregations) == 0 {

		logger.Warn(fmt.Sprint("no horizontal aggregation view found."))

		return nil
	}

	poolIndex, paddingBytes := encoder.MemoryPool.AcquireBytePool(8)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	datastoreDir := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir

	dirs, _ := os.ReadDir(datastoreDir + utils.PathSeparator)

	for _, view := range aggregations {

		for name, context := range view {

			if utils.GlobalShutdown {

				return errors.New(fmt.Sprintf("datastore shutdown...failed to apply patch %v", variant))
			}

			datastoreType := utils.DatastoreType(context.GetIntValue(utils.Type))

			if datastoreType == utils.Log || datastoreType == utils.Flow {

				break
			}

			stores := make([]string, 0)

			regex, _ := regexp.Compile("(.)*" + name + "-[0-9]+$")

			for _, dir := range dirs {

				if regex.Match([]byte(dir.Name())) {

					stores = append(stores, dir.Name())

				}

			}

			logger.Info(fmt.Sprintf("stores qualified for migrating count keys: %v", stores))

			for index := range stores {

				if utils.GlobalShutdown {

					return errors.New(fmt.Sprintf("datastore shutdown...failed to apply patch %v", variant))
				}

				if utils.DebugEnabled() {

					logger.Debug(fmt.Sprintf("migrating count keys for store: %v", stores[index]))
				}

				store := datastore.GetStore(stores[index], utils.None, false, true, encoder, tokenizer)

				if store == nil {

					logger.Error(fmt.Sprintf("failed to apply patch %v, reason: cannot acquire store %v.", variant, stores[index]))

					continue
				}

				if codec.StringToFloat64(store.GetVariant()) >= variant {

					continue
				}

				if err = cp.Copy(datastoreDir+utils.PathSeparator+stores[index], datastoreDir+utils.PathSeparator+utils.TempPatch+stores[index], cp.Options{
					Skip: func(info os.FileInfo, src, dest string) (bool, error) {
						return info.Name() == "lock", nil
					},
				}); err != nil {

					return errors.New(fmt.Sprintf("failed to copy store, reason: %v", err.Error()))
				}

				newStore := datastore.GetStore(utils.TempPatch+stores[index], utils.None, true, true, encoder, tokenizer)

				keyBuffers, _ := store.GetContainKeys([]byte("\\^count\\^"), false)

				if len(keyBuffers) == 0 {

					continue
				}

				pendingRequests := 0

				if utils.DebugEnabled() {

					logger.Debug(fmt.Sprintf("%v keys qualified for count migration", len(keyBuffers)))
				}

				for bufferIndex := range keyBuffers {

					if utils.GlobalShutdown {

						return errors.New(fmt.Sprintf("datastore shutdown...failed to apply patch %v", variant))
					}

					keyBuffers[pendingRequests] = keyBuffers[bufferIndex]

					pendingRequests++

					if bufferIndex == len(keyBuffers)-1 || pendingRequests == len(valueBuffers) {

						buffers, errs, err := store.GetMultiples(keyBuffers[:pendingRequests], valueBuffers[:pendingRequests], encoder, events, waitGroup, tokenizer, false)

						if err != nil {

							logger.Error(fmt.Sprintf("failed to read values from store %v, reason: %v", stores[index], err.Error()))

							continue
						}

						for k := 0; k < pendingRequests; k++ {

							if errs[k] != nil {

								logger.Error(fmt.Sprintf(utils.ErrorGetKey, string(keyBuffers[k]), stores[index], errs[k]))

								continue
							}

							if utils.GlobalShutdown {

								return errors.New(fmt.Sprintf("datastore shutdown...failed to apply patch %v", variant))
							}

							utils.Split(string(keyBuffers[k]), utils.KeySeparator, tokenizer)

							keyBytes := []byte(tokenizer.Tokens[0] + utils.KeySeparator + utils.All + utils.KeySeparator + utils.Count + utils.KeySeparator + tokenizer.Tokens[tokenizer.Counts-1])

							bytePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

							copy(valueBytes, paddingBytes)

							copy(valueBytes[len(paddingBytes):], buffers[k])

							if err = newStore.Put(keyBytes, valueBytes[:len(buffers[k])+len(paddingBytes)], encoder, tokenizer); err != nil {

								encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

								logger.Error(fmt.Sprintf(utils.ErrorWriteKey, string(keyBytes), newStore.GetName(), err))

								continue
							}

							encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

							newStore.Delete(keyBuffers[k], encoder, tokenizer)
						}

						pendingRequests = 0

					}

				}

				newStore.Close(encoder)

				store.Close(encoder)

				datastore.RemoveStore(store.GetName())

				datastore.RemoveStore(utils.TempPatch + stores[index])

				if err = patches.UpdateStoreVariant(stores[index], utils.FLOAT64ToStringValue(variant)); err != nil {

					logger.Error(fmt.Sprintf("failed to update variant %v in store metadata, reason: %v", variant, err.Error()))

					continue
				}

				if err = os.Rename(datastoreDir+utils.PathSeparator+utils.TempPatch+stores[index], datastoreDir+utils.PathSeparator+utils.Patch+stores[index]); err != nil {

					logger.Error(fmt.Sprintf("failed to migrate count keys for store %v, reason: %v", stores[index], err.Error()))

					_ = os.RemoveAll(datastoreDir + utils.PathSeparator + utils.TempPatch + stores[index])

					continue
				}

				_ = os.RemoveAll(datastoreDir + utils.PathSeparator + stores[index])

				if err = os.Rename(datastoreDir+utils.PathSeparator+utils.Patch+stores[index], datastoreDir+utils.PathSeparator+stores[index]); err != nil {

					logger.Error(fmt.Sprintf("failed to migrate count keys for store %v, reason: %v", stores[index], err.Error()))

					_ = os.RemoveAll(datastoreDir + utils.PathSeparator + utils.TempPatch + stores[index])
				}

				if utils.DebugEnabled() {

					logger.Debug(fmt.Sprintf("count keys for store %v migrated successfully", stores[index]))
				}

			}
		}

	}

	return err

}

func probeCompletion(keys map[string]struct{}) error {

	if keys == nil || len(keys) == 0 {

		logger.Warn("failed to find any stores for populating 6 hours aggregation interval.")

		return nil
	}

	probeTimer := time.NewTicker(time.Second * time.Duration(ProbeTimerSeconds))

	pendingKeys := map[string]struct{}{}

	for {

		if utils.GlobalShutdown {

			return errors.New(fmt.Sprintf("datastore shutdown...failed to apply patch %v", variant))
		}

		select {

		case <-probeTimer.C:

			complete := true

			for key := range keys {

				if datastore.IsAggregationContextExists(key) {

					pendingKeys[key] = struct{}{}

					complete = false
				}
			}

			if complete {

				logger.Info("aggregation views for six hour interval have been successfully populated.")

				return nil

			}

			logger.Info(fmt.Sprintf("following views are pending to be populated for 6 hour aggregation interval: %v", pendingKeys))

		}

	}

}

func createAggregationContexts(tokenizer *utils.Tokenizer) (map[string]struct{}, error) {

	bytes, err := os.ReadFile(utils.JobDir + utils.PathSeparator + utils.AggregationContexts)

	if err != nil && !os.IsNotExist(err) {

		return nil, errors.New(fmt.Sprintf("failed to read the aggregation contexts file, reason: %v", err.Error()))
	}

	aggregationContexts := map[string]bitmap.Bitmap{}

	if len(bytes) > 0 {

		_ = json.Unmarshal(bytes, &aggregationContexts)
	}

	bytes, err = os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.HorizontalAggregations)

	if err != nil {

		return nil, errors.New(fmt.Sprintf("failed to read horizontal aggregations file, reason: %v", err.Error()))
	}

	configs := make(utils.MotadataMap)

	if len(bytes) > 0 {

		_ = json.Unmarshal(bytes, &configs)
	}

	dates := map[string]struct{}{}

	keys := map[string]struct{}{}

	for plugin := range configs {

		clear(dates)

		var files []string

		files, err = filepath.Glob(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + "*1-" + plugin)

		if len(files) == 0 {

			continue
		}

		for index := range files {

			utils.Split(files[index], utils.PathSeparator, tokenizer)

			storeName := tokenizer.Tokens[tokenizer.Counts-1]

			utils.Split(storeName, utils.HyphenSeparator, tokenizer)

			dates[tokenizer.Tokens[0]] = struct{}{}
		}

		for aggregation := range configs.GetMapValue(plugin) {

			aggregationContexts = updateAggregationMetadataBitmap(aggregation, dates, keys, aggregationContexts)
		}

	}

	bytes, err = json.MarshalIndent(aggregationContexts, "", " ")

	if err != nil {

		return nil, err
	}

	if err = os.WriteFile(utils.JobDir+utils.PathSeparator+utils.TempPatch+utils.AggregationContexts, bytes, 0666); err != nil {

		logger.Warn(fmt.Sprintf("failed to save aggregation contexts., reason: %v", err.Error()))
	}

	if err = os.Rename(utils.JobDir+utils.PathSeparator+utils.TempPatch+utils.AggregationContexts, utils.JobDir+utils.PathSeparator+utils.AggregationContexts); err != nil {

		_ = os.Remove(utils.JobDir + utils.PathSeparator + utils.TempPatch + utils.AggregationContexts)

		logger.Warn(fmt.Sprintf("failed to rename aggregation contexts., reason: %v", err.Error()))
	}

	return keys, nil
}

func updateAggregationMetadataBitmap(aggregation string, dates map[string]struct{}, keys map[string]struct{}, aggregationContexts map[string]bitmap.Bitmap) map[string]bitmap.Bitmap {

	for date := range dates {

		timestamp, _ := time.ParseInLocation(utils.DateFormat, date, time.UTC)

		key := aggregation + utils.KeySeparator + utils.INT64ToStringValue(timestamp.Unix()) + utils.KeySeparator + codec.INTToStringValue(360) + utils.KeySeparator + utils.HorizontalFormat

		metadataBitmap := bitmap.Bitmap{}

		for i := uint32(0); i < 4; i++ { //in 360 min interval there are only 4 ticks

			metadataBitmap.Set(i)

			datastore.UpdateAggregationContexts(key, i, utils.Add)
		}

		keys[key] = struct{}{}

		aggregationContexts[key] = metadataBitmap

	}

	return aggregationContexts
}
