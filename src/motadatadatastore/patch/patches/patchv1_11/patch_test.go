/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>tadata-5190  Migrated constants from datastore to utils according to SonarQube standard
* 2025-05-05			 Swapnil A. Dave		M<PERSON>ATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-06-04             <PERSON><PERSON><PERSON> <PERSON>            MOTADATA-5780 Test Case Refactoring and initiliazed IOCPWorkers. Changed pool length from 50000 to 10000
 */

package patchv1_11

import (
	"encoding/json"
	"fmt"
	"github.com/pbnjay/memory"
	"github.com/stretchr/testify/assert"
	"math"
	"motadatadatastore/cache"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/datastore/query"
	"motadatadatastore/datastore/writer"
	"motadatadatastore/job"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"strings"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

const (
	Today = "today" //start of the day to now

	Tomorrow = "tomorrow"

	Last24Hours = "last.24.hours"

	Last9Hours = "last.9.hours"

	Last1Hour = "last.hour"

	Last15Minutes = "last.15.minutes"

	Last30Minutes = "last.30.minutes"

	Last5Minutes = "last.5.minutes"

	Last48Hours = "last.48.hours"

	Last2Days = "last.2.days"

	Last7Days = "last.7.days"

	Last1Month = "last.1.month"

	LastQuarter = "last.quarter"

	Last6Months = "last.6.months"

	Custom = "custom"

	ToDateTime = "to.datetime"

	FromDateTime = "from.datetime"

	VisualizationTimeline = "visualization.timeline"

	RelativeTimeline = "relative.timeline"

	VisualizationGranularity = "visualization.granularity"

	Duration = "duration"

	_5MinGranularity  = "5 m"
	_30MinGranularity = "30 m"
	_15MinGranularity = "15 m"
	_1HourGranularity = "1 h"
	_6HourGranularity = "6 h"
)

const (
	FortinetTrafficBytesPerSec = "fortinet.traffic.bytes.per.sec"

	SumVolumeBytesPerSec   = "volume.bytes.per.sec^sum"
	CountVolumeBytesPerSec = "volume.bytes.per.sec^count"
	AvgVolumeBytesPerSec   = "volume.bytes.per.sec^avg"

	SumSentDiscardedPackets   = "sent.discarded.packets^sum"
	CountSentDiscardedPackets = "sent.discarded.packets^count"
	AvgSentDiscardedPackets   = "sent.discarded.packets^avg"

	CountTCPFlags = "tcp.flags^count"
	SumTCPFlags   = "tcp.flags^sum"
	AvgTCPFlags   = "tcp.flags^avg"

	SumFortinetTrafficBytesPerSec   = "fortinet.traffic.bytes.per.sec^sum"
	CountFortinetTrafficBytesPerSec = "fortinet.traffic.bytes.per.sec^count"
	AvgFortinetTrafficBytesPerSec   = "fortinet.traffic.bytes.per.sec^avg"
)

const (

	//plugin constants
	flowPlugin         = "600001-flow"
	metricPolicyPlugin = "600005-policy"
	eventPolicyPlugin  = "600006-policy"
	logPlugin          = "600007-fortinet.traffic"
)

var (
	flowView1Table = map[string]interface{}{

		writer.VolumeBytesPerSec:        float64(datastore.IntegerColumn),
		writer.SentDiscardedPackets:     float64(datastore.IntegerColumn),
		writer.ReceivedDiscardedPackets: float64(datastore.IntegerColumn),
		utils.VolumeBytes:               float64(datastore.IntegerColumn),
		datastore.Duration:              float64(datastore.IntegerColumn),
		writer.VolumeBytesPerPacket:     float64(datastore.IntegerColumn),
		writer.Packets:                  float64(datastore.IntegerColumn),
		writer.Flows:                    float64(datastore.IntegerColumn),
		writer.PacketsPerSec:            float64(datastore.IntegerColumn),
		writer.TCPFlags:                 float64(datastore.IntegerColumn),
		utils.Type:                      float64(utils.Flow),
		utils.IndexableColumns: map[string]interface{}{
			writer.SourceIP:           struct{}{},
			writer.DestinationIP:      struct{}{},
			writer.SourcePort:         struct{}{},
			writer.DestinationPort:    struct{}{},
			writer.DestinationCountry: struct{}{},
			writer.SourceCity:         struct{}{},
			writer.DestinationCity:    struct{}{},
			writer.EventSource:        struct{}{},
		},
	}

	flowView2Table = map[string]interface{}{

		writer.VolumeBytesPerSec:        float64(datastore.IntegerColumn),
		writer.SentDiscardedPackets:     float64(datastore.IntegerColumn),
		writer.ReceivedDiscardedPackets: float64(datastore.IntegerColumn),
		utils.VolumeBytes:               float64(datastore.IntegerColumn),
		datastore.Duration:              float64(datastore.IntegerColumn),
		writer.VolumeBytesPerPacket:     float64(datastore.IntegerColumn),
		writer.Packets:                  float64(datastore.IntegerColumn),
		writer.Flows:                    float64(datastore.IntegerColumn),
		writer.PacketsPerSec:            float64(datastore.IntegerColumn),
		writer.TCPFlags:                 float64(datastore.IntegerColumn),
		utils.Type:                      float64(utils.Flow),
		utils.IndexableColumns: map[string]interface{}{
			writer.SourceIFIndex:       struct{}{},
			writer.DestinationIFIndex:  struct{}{},
			writer.TCPFlags:            struct{}{},
			writer.Protocol:            struct{}{},
			writer.ApplicationProtocol: struct{}{},
			writer.Application:         struct{}{},
			utils.EventSource:          struct{}{},
		},
	}

	flowView3Table = map[string]interface{}{

		writer.VolumeBytesPerSec:        float64(datastore.IntegerColumn),
		writer.SentDiscardedPackets:     float64(datastore.IntegerColumn),
		writer.ReceivedDiscardedPackets: float64(datastore.IntegerColumn),
		utils.VolumeBytes:               float64(datastore.IntegerColumn),
		datastore.Duration:              float64(datastore.IntegerColumn),
		writer.VolumeBytesPerPacket:     float64(datastore.IntegerColumn),
		writer.Packets:                  float64(datastore.IntegerColumn),
		writer.Flows:                    float64(datastore.IntegerColumn),
		writer.PacketsPerSec:            float64(datastore.IntegerColumn),
		writer.TCPFlags:                 float64(datastore.IntegerColumn),
		utils.Type:                      float64(utils.Flow),
		utils.IndexableColumns: map[string]interface{}{
			writer.DummyINT16Column: struct{}{},
			writer.DummyINT32Column: struct{}{},
			writer.DummyINT64Column: struct{}{},
			utils.EventSource:       struct{}{},
		},
	}

	metricPolicyView1Table = map[string]interface{}{

		"severity": float64(datastore.StringColumn),
		utils.Type: float64(utils.MetricPolicy),
		utils.IndexableColumns: map[string]interface{}{
			"object.id": struct{}{},
			"severity":  struct{}{},
			"policy.id": struct{}{},
		},
	}

	eventPolicyViewTable = map[string]interface{}{

		"severity": float64(datastore.StringColumn),
		utils.Type: float64(utils.EventPolicy),
		utils.IndexableColumns: map[string]interface{}{
			"event.source": struct{}{},
			"severity":     struct{}{},
			"policy.id":    struct{}{},
			"policy.type":  struct{}{},
		},
	}
)

var (
	testDir = filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator

	executorAllocations = make(map[int]query.QueryEngineType)

	workerAllocations = make(map[int]query.QueryEngineType)

	executors []*query.Executor

	workers []*query.Worker

	queryResponses chan []byte

	syncJobManager *job.StoreSyncJobManager
)

var (
	testValueBuffers [][]byte

	testTokenizer *utils.Tokenizer

	testBatchIOEvents []storage.DiskIOEventBatch

	testWaitGroup *sync.WaitGroup

	testEncoder Encoder

	ioWorkers = make([]*storage.DiskIOWorker, utils.DiskIOWorkers)
)

func TestMain(m *testing.M) {

	if utils.SkipBenchmarkTest() {

		return
	}

	runtime.GC()

	debug.FreeOSMemory()

	memoryBytes := int64(memory.TotalMemory())

	debug.SetMemoryLimit((memoryBytes * 85) / 100)

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	if err != nil {

		panic(err)
	}

	utils.SystemBootSequence = utils.Datastore

	if utils.InitConfigs(utils.UpdateConfigs(bytes, utils.MotadataMap{

		"datastore.max.pool.length":                      10000,
		"datastore.disk.io.workers":                      256,
		"datastore.horizontal.aggregation.timer.seconds": 1,
	})) {

		utils.PublisherNotifications = make(chan utils.MotadataMap, 1_00_000)

		testValueBuffers = make([][]byte, utils.MaxWorkerEventKeyGroupLength)

		for i := 0; i < len(testValueBuffers); i++ {

			testValueBuffers[i] = make([]byte, utils.MaxValueBufferBytes)
		}

		testTokenizer = &utils.Tokenizer{Tokens: make([]string, utils.TokenizerLength)}

		testEncoder = NewEncoder(utils.NewMemoryPool(10, utils.MaxPoolLength, true, utils.DefaultBlobPools))

		testBatchIOEvents = make([]storage.DiskIOEventBatch, utils.MaxStoreParts)

		for i := 0; i < len(testBatchIOEvents); i++ {

			testBatchIOEvents[i] = storage.DiskIOEventBatch{}
		}

		ioWorkers = make([]*storage.DiskIOWorker, utils.DiskIOWorkers)

		for index := range ioWorkers {

			ioWorkers[index] = storage.NewIOWorker(index)

			ioWorkers[index].Start()
		}

		ProbeTimerSeconds = 2

		testWaitGroup = &sync.WaitGroup{}

		utils.CleanUpStores()

		_ = os.MkdirAll(utils.JobDir, 0777)

		_ = utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"patchv1_11.zip", utils.CurrentDir)

		cache.InitCacheEngine()

		datastore.Init()

		_ = Patch(testValueBuffers, testEncoder, testTokenizer, testBatchIOEvents, testWaitGroup)

		utils.InitConfigs(utils.UpdateConfigs(bytes, utils.MotadataMap{

			"datastore.max.pool.length": 10000,
		}))

		loadTestEnv()

		m.Run()

		for _, worker := range ioWorkers {

			worker.ShutdownNotifications <- true
		}

	}
}

func TestHistogramMetricPolicyCountSeverityGroupByGroupAggregationInterval6Hour(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "histogram-count-severity-groupby-group.json")

	timeline := GetTimeline(Custom)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(metricPolicyPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(metricPolicyPlugin)

	modifyAggregationTimeline(queryContext, true)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	_, aggregatedTableGroups := removeDummyINT64ValueTable(aggregationView)

	_, rawTableGroups := removeDummyINT64ValueTable(rawTable)

	assertions.Equal(rawTableGroups, aggregatedTableGroups)

}

func TestHistogramMetricPolicyCountSeverityGroupByGroupAggregationInterval1hour(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "histogram-count-severity-groupby-group.json")

	timeline := GetTimeline(Custom)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).Add(time.Hour * 1).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(metricPolicyPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(metricPolicyPlugin)

	modifyAggregationTimeline(queryContext, true)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	_, aggregatedTableGroups := removeDummyINT64ValueTable(aggregationView)

	_, rawTableGroups := removeDummyINT64ValueTable(rawTable)

	assertions.Equal(rawTableGroups, aggregatedTableGroups)

}

func TestHistogramMetricPolicyCountSeverityGroupByGroupAggregationInterval6hour(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "histogram-count-severity-groupby-group.json")

	timeline := GetTimeline(Custom)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).Add(time.Hour * 6).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(metricPolicyPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(metricPolicyPlugin)

	modifyAggregationTimeline(queryContext, true)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	_, aggregatedTableGroups := removeDummyINT64ValueTable(aggregationView)

	_, rawTableGroups := removeDummyINT64ValueTable(rawTable)

	assertions.Equal(rawTableGroups, aggregatedTableGroups)

}

func TestHistogramMetricPolicyCountSeverityGroupByGroupAggregationInterval10hour(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "histogram-count-severity-groupby-group.json")

	timeline := GetTimeline(Custom)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).Add(time.Hour * 10).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(metricPolicyPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(metricPolicyPlugin)

	modifyAggregationTimeline(queryContext, true)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	_, aggregatedTableGroups := removeDummyINT64ValueTable(aggregationView)

	_, rawTableGroups := removeDummyINT64ValueTable(rawTable)

	assertions.Equal(rawTableGroups, aggregatedTableGroups)

}

func TestHistogramEventPolicyCountSeverityGroupByGroupAggregationInterval6Hour(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "histogram-count-severity-groupby-group-eventpolicy.json")

	timeline := GetTimeline(Custom)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(eventPolicyPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(eventPolicyPlugin)

	modifyAggregationTimeline(queryContext, true)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	_, aggregatedTableGroups := removeDummyINT64ValueTable(aggregationView)

	_, rawTableGroups := removeDummyINT64ValueTable(rawTable)

	assertions.Equal(rawTableGroups, aggregatedTableGroups)

}

func TestHistogramEventPolicyCountSeverityGroupByGroupAggregationInterval1Hour(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "histogram-count-severity-groupby-group-eventpolicy.json")

	timeline := GetTimeline(Custom)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 1, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(eventPolicyPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(eventPolicyPlugin)

	modifyAggregationTimeline(queryContext, true)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	_, aggregatedTableGroups := removeDummyINT64ValueTable(aggregationView)

	_, rawTableGroups := removeDummyINT64ValueTable(rawTable)

	assertions.Equal(rawTableGroups, aggregatedTableGroups)

}

func TestHistogramEventPolicyCountSeverityGroupByGroupAggregationInterval10Hour(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "histogram-count-severity-groupby-group-eventpolicy.json")

	timeline := GetTimeline(Custom)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(eventPolicyPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(eventPolicyPlugin)

	modifyAggregationTimeline(queryContext, true)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	_, aggregatedTableGroups := removeDummyINT64ValueTable(aggregationView)

	_, rawTableGroups := removeDummyINT64ValueTable(rawTable)

	assertions.Equal(rawTableGroups, aggregatedTableGroups)

}

func TestGridSumVolumeBytesPerSecGroupBySourceIPDestinationIP(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-groupby-sourceip-destinationip.json")

	timeline := GetTimeline(Last48Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[sourceIp.(string)+utils.GroupSeparator+aggregationView[writer.DestinationIP][index].(string)] = aggregationView[SumVolumeBytesPerSec][index]
	}

	for index, sourceIp := range rawTable[writer.SourceIP] {

		rawResult[sourceIp.(string)+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = rawTable[SumVolumeBytesPerSec][index]
	}

	for key, value := range rawResult {

		assertions.Equal(value, aggregationResult[key])
	}

}

func TestGridCountVolumeBytesPerSecGroupBySourceIPDestinationIP(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-count-volumebytespersec-groupby-sourceip-destinationip.json")

	timeline := GetTimeline(Last9Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 9, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[sourceIp.(string)+utils.GroupSeparator+aggregationView[writer.DestinationIP][index].(string)] = aggregationView[CountVolumeBytesPerSec][index]

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = rawTable[CountVolumeBytesPerSec][index]
	}

	assertions.EqualValues(rawResult, aggregationResult)

}

func TestGridSumVolumeBytesPerSecGroupByDummyINT16ColumnDummyINT32ColumnDummyINT64Column(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-groupby-dummyint16column-dummyint32column-dummyint64column.json")

	timeline := GetTimeline(Last9Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 9, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, column := range aggregationView[writer.DummyINT16Column] {

		aggregationResult[ToString(column)+utils.GroupSeparator+ToString(aggregationView[writer.DummyINT32Column][index])+utils.GroupSeparator+ToString(aggregationView[writer.DummyINT64Column][index])] = aggregationView[SumVolumeBytesPerSec][index]
	}

	for index, column := range rawTable[writer.DummyINT16Column] {

		rawResult[ToString(column)+utils.GroupSeparator+ToString(rawTable[writer.DummyINT32Column][index])+utils.GroupSeparator+ToString(rawTable[writer.DummyINT64Column][index])] = rawTable[SumVolumeBytesPerSec][index]
	}

	for key, value := range rawResult {

		assertions.Equal(value, aggregationResult[key])
	}

}

func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsGroupBySourceIPDestinationIPSourcePortDestinationPort(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-groupby-sourceip-destinationip-sourceport-destinationport-sourcecity-destinationcountry.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 18, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])+utils.GroupSeparator+ToString(aggregationView[writer.SourcePort][index])+utils.GroupSeparator+ToString(aggregationView[writer.DestinationPort][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])+utils.GroupSeparator+ToString(rawTable[writer.SourcePort][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationPort][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	assertions.EqualValues(rawResult, aggregationResult)
}

func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourceCountryDestinationCountryGroupBySourceIPDestinationIPSourcePortDestinationPort(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourcecity-destinationcountry.json")

	timeline := GetTimeline(Last9Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 9, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[sourceIp.(string)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])+utils.GroupSeparator+ToString(aggregationView[writer.SourceCity][index])+utils.GroupSeparator+ToString(aggregationView[writer.DestinationCountry][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])+utils.GroupSeparator+ToString(rawTable[writer.SourceCity][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationCountry][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key], key)

	}

}

func TestGridSumVolumeBytesPerSecGroupBySourceIFIndexDestinationIFIndexTCPFlagsProtocolApplicationProtocolApplication(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-groupby-sourceifindex-destinationifindex-tcpflags-protocol-application-protocol-application.json")

	timeline := GetTimeline(Last9Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 9, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIfIndex := range aggregationView[writer.SourceIFIndex] {

		aggregationResult[ToString(sourceIfIndex)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIFIndex][index])+utils.GroupSeparator+ToString(aggregationView[writer.TCPFlags][index])+utils.GroupSeparator+ToString(aggregationView[writer.Protocol][index])] = aggregationView[SumVolumeBytesPerSec][index]

		rawResult[ToString(rawTable[writer.SourceIFIndex][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIFIndex][index])+utils.GroupSeparator+ToString(rawTable[writer.TCPFlags][index])+utils.GroupSeparator+ToString(rawTable[writer.Protocol][index])] = rawTable[SumVolumeBytesPerSec][index]
	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

//Log grid scenarios plugin testcases

func TestGridSumFortinetTrafficBytesPerSecGroupByEventSource(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-fortinettrafficbytes-groupby-eventsource.json")

	timeline := GetTimeline(Last48Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(logPlugin)

	aggregationTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationTable)

	datastore.DisableHorizontalAggregations(logPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationTable))

	for index, eventSource := range aggregationTable[writer.EventSource] {

		aggregationResult[eventSource.(string)] = aggregationTable[SumFortinetTrafficBytesPerSec][index]
	}

	for index, eventSource := range rawTable[writer.EventSource] {

		rawResult[eventSource.(string)] = rawTable[SumFortinetTrafficBytesPerSec][index]
	}

	for key, value := range rawResult {

		assertions.Equal(value, aggregationResult[key])
	}

}

func TestGridCountFortinetTrafficBytesPerSecGroupByEventSource(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-count-fortinettrafficbytes-groupby-eventsource.json")

	timeline := GetTimeline(Last48Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(logPlugin)

	aggregationTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationTable)

	datastore.DisableHorizontalAggregations(logPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationTable))

	for index, eventSource := range aggregationTable[writer.EventSource] {

		aggregationResult[eventSource.(string)] = aggregationTable[CountFortinetTrafficBytesPerSec][index]
	}

	for index, eventSource := range rawTable[writer.EventSource] {

		rawResult[eventSource.(string)] = rawTable[CountFortinetTrafficBytesPerSec][index]
	}

	for key, value := range rawResult {

		assertions.Equal(value, aggregationResult[key])
	}

}

func TestGridAvgFortinetTrafficBytesPerSecGroupByEventSource(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-avg-fortinettrafficbytes-groupby-eventsource.json")

	timeline := GetTimeline(Last48Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(logPlugin)

	aggregationTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationTable)

	datastore.DisableHorizontalAggregations(logPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationTable))

	for index, eventSource := range aggregationTable[writer.EventSource] {

		aggregationResult[eventSource.(string)] = aggregationTable[AvgFortinetTrafficBytesPerSec][index]
	}

	for index, eventSource := range rawTable[writer.EventSource] {

		rawResult[eventSource.(string)] = rawTable[AvgFortinetTrafficBytesPerSec][index]
	}

	for key, value := range rawResult {

		assertValueHavingErrorTolerance(utils.ToFlOAT(value), utils.ToFlOAT(aggregationResult[key]), assertions)
	}

}

func TestGridAvgFortinetTrafficBytesPerSecGroupByEventSourceFilterByEventSource(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-avg-fortinettrafficbytes-groupby-eventsource-filterby-eventsource.json")

	timeline := GetTimeline(Last48Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(logPlugin)

	aggregationTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationTable)

	datastore.DisableHorizontalAggregations(logPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationTable))

	for index, eventSource := range aggregationTable[writer.EventSource] {

		aggregationResult[eventSource.(string)] = aggregationTable[AvgFortinetTrafficBytesPerSec][index]
	}

	for index, eventSource := range rawTable[writer.EventSource] {

		rawResult[eventSource.(string)] = rawTable[AvgFortinetTrafficBytesPerSec][index]
	}

	for key, value := range rawResult {

		assertValueHavingErrorTolerance(utils.ToFlOAT(value), utils.ToFlOAT(aggregationResult[key]), assertions)
	}

}

// Log gauge scenario without filter
func TestGaugeSumFortinetTrafficBytesPerSecLast24Hours(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-fortinettrafficbytespersec.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(logPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(logPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(rawTable, aggregationView)
}

func TestGaugeAvgFortinetTrafficBytesPerSecLast24Hours(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-fortinettrafficbytespersec.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(logPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(logPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertValueHavingErrorTolerance(utils.ToFlOAT(rawTable), utils.ToFlOAT(aggregationView), assertions)
}

func TestGaugeCountEventSourceLast24Hours(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-event.source-logsearchplugin.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(datastore.EventSearchPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(datastore.EventSearchPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(rawTable, aggregationView)
}

//Gauge testcase with filter

func TestGaugeSumFortinetTrafficBytesPerSecFilterByEventSourceLast24Hours(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-fortinettrafficbytespersec-filterby-eventsource.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(logPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(logPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(rawTable, aggregationView)
}

func TestGaugeCountFortinetTrafficBytesPerSecFilterByEventSourceLast24Hours(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-fortinettrafficbytespersec-filterby-eventsource.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(logPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(logPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(rawTable, aggregationView)
}

func TestGaugeAvgFortinetTrafficBytesPerSecFilterByEventSourceLast24Hours(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-fortinettrafficbytespersec-filterby-eventsource.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(logPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(logPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertValueHavingErrorTolerance(utils.ToFlOAT(rawTable), utils.ToFlOAT(aggregationView), assertions)
}

//one datapoint avg one count

func TestGridAvgVolumeBytesPerSecCountSentDiscardedPacketsGroupBySourceIPDestinationIPSourcePortDestinationPortSourceCityDestinationCountry(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-avg-volumebytespersec-count-sentdiscardedpackets-groupby-sourceip-destinationip-sourceport-destinationport-sourcecity-destinationcountry.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 12, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])+utils.GroupSeparator+ToString(aggregationView[writer.SourcePort][index])+utils.GroupSeparator+ToString(aggregationView[writer.DestinationPort][index])] = []interface{}{aggregationView[AvgVolumeBytesPerSec][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])+utils.GroupSeparator+ToString(rawTable[writer.SourcePort][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationPort][index])] = []interface{}{rawTable[AvgVolumeBytesPerSec][index], rawTable[CountSentDiscardedPackets][index]}

	}

	assertions.EqualValues(rawResult, aggregationResult)
}

// one datapoint avg another avg

func TestGridAvgVolumeBytesPerSecAvgSentDiscardedPacketsGroupBySourceIPDestinationIPSourcePortDestinationPortSourceCityDestinationCountry(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-avg-volumebytespersec-avg-sentdiscardedpackets-groupby-sourceip-destinationip-sourceport-destinationport-sourcecity-destinationcountry.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])+utils.GroupSeparator+ToString(aggregationView[writer.SourcePort][index])+utils.GroupSeparator+ToString(aggregationView[writer.DestinationPort][index])] = []interface{}{aggregationView[AvgVolumeBytesPerSec][index], aggregationView[AvgSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])+utils.GroupSeparator+ToString(rawTable[writer.SourcePort][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationPort][index])] = []interface{}{rawTable[AvgVolumeBytesPerSec][index], rawTable[AvgSentDiscardedPackets][index]}

	}

	assertions.EqualValues(rawResult, aggregationResult)
}

//one datapoint count another count

func TestGridCountVolumeBytesPerSecCountSentDiscardedPacketsGroupBySourceIPDestinationIPSourcePortDestinationPortSourceCityDestinationCountry(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-count-volumebytespersec-count-sentdiscardedpackets-groupby-sourceip-destinationip-sourceport-destinationport-sourcecity-destinationcountry.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 13, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])+utils.GroupSeparator+ToString(aggregationView[writer.SourcePort][index])+utils.GroupSeparator+ToString(aggregationView[writer.DestinationPort][index])] = []interface{}{aggregationView[CountVolumeBytesPerSec][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])+utils.GroupSeparator+ToString(rawTable[writer.SourcePort][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationPort][index])] = []interface{}{rawTable[CountVolumeBytesPerSec][index], rawTable[CountSentDiscardedPackets][index]}

	}

	//cannot assert count as all value is 1 only
}

// Overflow testcase - avg count
func TestGridAvgVolumeBytesPerSecCountTCPFlagsGroupBySourceIPDestinationIPSourcePortDestinationPortSourceCityDestinationCountry(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-avg-volumebytespersec-count-tcpflags-groupby-sourceip-destinationip-sourceport-destinationport-sourcecity-destinationcountry.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])+utils.GroupSeparator+ToString(aggregationView[writer.SourcePort][index])+utils.GroupSeparator+ToString(aggregationView[writer.DestinationPort][index])] = []interface{}{aggregationView[AvgVolumeBytesPerSec][index], aggregationView[CountTCPFlags][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])+utils.GroupSeparator+ToString(rawTable[writer.SourcePort][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationPort][index])] = []interface{}{rawTable[AvgVolumeBytesPerSec][index], rawTable[CountTCPFlags][index]}

	}

	assertions.EqualValues(rawResult, aggregationResult)
}

// Avg Avg
func TestGridAvgVolumeBytesPerSecAvgTCPFlagsGroupBySourceIPDestinationIPSourcePortDestinationPortSourceCityDestinationCountry(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-avg-volumebytespersec-avg-tcpflags-groupby-sourceip-destinationip-sourceport-destinationport-sourcecity-destinationcountry.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 1, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 1, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])+utils.GroupSeparator+ToString(aggregationView[writer.SourcePort][index])+utils.GroupSeparator+ToString(aggregationView[writer.DestinationPort][index])] = []interface{}{aggregationView[AvgVolumeBytesPerSec][index], aggregationView[AvgTCPFlags][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])+utils.GroupSeparator+ToString(rawTable[writer.SourcePort][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationPort][index])] = []interface{}{rawTable[AvgVolumeBytesPerSec][index], rawTable[AvgTCPFlags][index]}

	}

	for key := range rawResult {

		assertions.Equal(rawResult[key], aggregationResult[key])
	}
}

// Avg Sum
func TestGridAvgVolumeBytesPerSecSumTCPFlagsGroupBySourceIPDestinationIPSourcePortDestinationPortSourceCityDestinationCountry(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-avg-volumebytespersec-sum-tcpflags-groupby-sourceip-destinationip-sourceport-destinationport-sourcecity-destinationcountry.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 1, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 1, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])+utils.GroupSeparator+ToString(aggregationView[writer.SourcePort][index])+utils.GroupSeparator+ToString(aggregationView[writer.DestinationCountry][index])] = []interface{}{aggregationView[AvgVolumeBytesPerSec][index], aggregationView[SumTCPFlags][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])+utils.GroupSeparator+ToString(rawTable[writer.SourcePort][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationCountry][index])] = []interface{}{rawTable[AvgVolumeBytesPerSec][index], rawTable[SumTCPFlags][index]}

	}

	for key := range rawResult {

		assertions.Equal(rawResult[key], aggregationResult[key])
	}
}

//string counter count

func TestGridCountTCPFlagsGroupBySourceIPDestinationIPSourcePortDestinationPortSourceCityDestinationCountry(t *testing.T) {

	grouping := utils.MaxFlowTopNSelectionGroups

	utils.MaxFlowTopNSelectionGroups = 500000

	defer func() {

		utils.MaxFlowTopNSelectionGroups = grouping

		cache.Clear()
	}()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-count-tcpflags-groupby-sourceip-destinationip-sourceport-destinationport-sourcecity-destinationcountry.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 11, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourcePort := range aggregationView[writer.SourcePort] {

		aggregationResult[ToString(sourcePort)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationPort][index])+utils.GroupSeparator+ToString(aggregationView[writer.SourceCity][index])+utils.GroupSeparator+ToString(aggregationView[writer.DestinationCountry][index])] = []interface{}{aggregationView[CountTCPFlags][index]}

		rawResult[ToString(rawTable[writer.SourcePort][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationPort][index])+utils.GroupSeparator+ToString(rawTable[writer.SourceCity][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationCountry][index])] = []interface{}{rawTable[CountTCPFlags][index]}

	}

	assertions.EqualValues(rawResult, aggregationResult)
}

//filter new testcases

// start with include
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourceCountryDestinationCountryGroupBySourceIPDestinationIP(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourcecity-destinationcountry-groupby-sourceip-destinationip.json")

	timeline := GetTimeline(Last9Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 9, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// end with include
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourceCountryGroupBySourceIPDestinationIP(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourcecity-groupby-sourceip-destinationip.json")

	timeline := GetTimeline(Last9Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 9, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// contain include
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterByDestinationCountryGroupBySourceIPDestinationIP(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-destinationcountry-groupby-sourceip-destinationip.json")

	timeline := GetTimeline(Last9Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 9, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 2, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// less than include
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourcePortGroupBySourceIPDestinationIP(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourceport-groupby-sourceip-destinationip.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// less than equal include
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourcePortGroupBySourceIPDestinationIPType1(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourceport-groupby-sourceip-destinationip-type1.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// greater than include
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourcePortGroupBySourceIPDestinationIPType2(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourceport-groupby-sourceip-destinationip-type2.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 14, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// greater than equal include
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourcePortGroupBySourceIPDestinationIPType3(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourceport-groupby-sourceip-destinationip-type3.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 18, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// start with exclude
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourceCountryDestinationCountryGroupBySourceIPDestinationIPType4(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourcecity-destinationcountry-groupby-sourceip-destinationip-type4.json")

	timeline := GetTimeline(Last9Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 9, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 18, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// end with exclude
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourceCountryGroupBySourceIPDestinationIPType5(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourcecity-groupby-sourceip-destinationip-type5.json")

	timeline := GetTimeline(Last9Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 9, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// contain exclude
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterByDestinationCountryGroupBySourceIPDestinationIPType6(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-destinationcountry-groupby-sourceip-destinationip-type6.json")

	timeline := GetTimeline(Last9Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 9, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// less than exclude
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourcePortGroupBySourceIPDestinationIPType7(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourceport-groupby-sourceip-destinationip-type7.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 17, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// less than equal exclude
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourcePortGroupBySourceIPDestinationIPType8(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourceport-groupby-sourceip-destinationip-type8.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// greater than exclude
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourcePortGroupBySourceIPDestinationIPType9(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourceport-groupby-sourceip-destinationip-type9.json")

	timeline := GetTimeline(Last9Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 9, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// greater than equal exclude
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourcePortGroupBySourceIPDestinationIPType10(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourceport-groupby-sourceip-destinationip-type10.json")

	timeline := GetTimeline(Last9Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 9, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// multiple in filters
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourceCountryDestinationCountryGroupBySourceIPDestinationIPType11(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourcecity-destinationcountry-groupby-sourceip-destinationip-type11.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

// greater than equal exclude
func TestGridSumVolumeBytesPerSecCountVolumeBytesPerSecSumSentDiscardedPacketsCountSentDiscardedPacketsFilterBySourcePortGroupBySourceIPDestinationIPType12(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-sum-volumebytespersec-count-volumebytespersec-sum-sentdiscardedpackets-count-sentdiscardedpackets-filterby-sourceport-groupby-sourceip-destinationip-type12.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])] = []interface{}{aggregationView[SumVolumeBytesPerSec][index], aggregationView[CountVolumeBytesPerSec][index], aggregationView[SumSentDiscardedPackets][index], aggregationView[CountSentDiscardedPackets][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])] = []interface{}{rawTable[SumVolumeBytesPerSec][index], rawTable[CountVolumeBytesPerSec][index], rawTable[SumSentDiscardedPackets][index], rawTable[CountSentDiscardedPackets][index]}

	}

	for key, values := range rawResult {

		assertions.EqualValues(values, aggregationResult[key])
	}
}

//histogram testcase

// For flow testcases pls refer to the timelines properly and granularity calculation in histogram
// Avg,Sum,Count single datatpoint histogram
// don't use last 8hours and last 24 hrs timeline in histogram testcases
func TestHistogramAvgVolumeBytesPerSecCountTCPFlagsGroupBySourceIPDestinationIPSourcePortDestinationPortSourceCityDestinationCountry(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "histogram-avg-volumebytespersec-count-tcpflags-groupby-sourceip-destinationip-sourceport-destinationport-sourcecity-destinationcountry.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = calculateGranularity(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), true, query.Flow)

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, true)

	queryContext[VisualizationGranularity] = calculateGranularity(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), true, query.Flow)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	_, aggregationResult := removeDummyINT64ValueTable(aggregationView)

	_, rawResult := removeDummyINT64ValueTable(rawTable)

	for key := range aggregationResult {

		assertions.Equal(rawResult[key], aggregationResult[key])
	}
}

func TestHistogramSumVolumeBytesPerSecCountTCPFlagsGroupBySourceIPDestinationIPSourcePortDestinationPortSourceCityDestinationCountry(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "histogram-sum-volumebytespersec-count-tcpflags-groupby-sourceip-destinationip-sourceport-destinationport-sourcecity-destinationcountry.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = calculateGranularity(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), true, query.Flow)

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, true)

	queryContext[VisualizationGranularity] = calculateGranularity(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), true, query.Flow)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	_, aggregationResult := removeDummyINT64ValueTable(aggregationView)

	_, rawResult := removeDummyINT64ValueTable(rawTable)

	for key := range aggregationResult {

		assertions.Equal(rawResult[key], aggregationResult[key])
	}
}

func TestHistogramCountVolumeBytesPerSecCountTCPFlagsGroupBySourceIPDestinationIPSourcePortDestinationPortSourceCityDestinationCountry(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "histogram-count-volumebytespersec-count-tcpflags-groupby-sourceip-destinationip-sourceport-destinationport-sourcecity-destinationcountry.json")

	timeline := GetTimeline(Last48Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = calculateGranularity(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), true, query.Flow)

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, true)

	queryContext[VisualizationGranularity] = calculateGranularity(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), true, query.Flow)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	_, aggregationResult := removeDummyINT64ValueTable(aggregationView)

	_, rawResult := removeDummyINT64ValueTable(rawTable)

	for key := range aggregationResult {

		assertions.Equal(rawResult[key], aggregationResult[key], key)
	}
}

func TestHistogramCountVolumeBytesPerSecCountTCPFlagsGroupBySourceIPDestinationIPSourcePortDestinationPortSourceCityDestinationCountryFilterBySourceCountryDestinationCountry(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "histogram-count-volumebytespersec-count-tcpflags-groupby-sourceip-destinationip-sourceport-destinationport-sourcecity-destinationcountry-filterby-sourcecountry-destinationcountry.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = calculateGranularity(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), true, query.Flow)

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, true)

	queryContext[VisualizationGranularity] = calculateGranularity(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), true, query.Flow)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(len(rawTable), len(aggregationView))

	_, aggregationResult := removeDummyINT64ValueTable(aggregationView)

	_, rawResult := removeDummyINT64ValueTable(rawTable)

	for key := range aggregationResult {

		assertions.Equal(rawResult[key], aggregationResult[key])
	}
}

// with filter
// Avg,Sum,Count Multiple histogram without group by
func TestHistogramAvgVolumeBytesPerSecSumTCPFlagsCountPacketsPerSec(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "histogram-avg-volumebytespersec-sum-tcpflags-count-packetspersec.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = calculateGranularity(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), true, query.Flow)

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, true)

	queryContext[VisualizationGranularity] = calculateGranularity(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), true, query.Flow)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(len(rawTable), len(aggregationView))

	_, aggregationResult := removeDummyINT64ValueTable(aggregationView)

	_, rawResult := removeDummyINT64ValueTable(rawTable)

	for key := range aggregationResult {

		assertions.Equal(rawResult[key], aggregationResult[key])
	}
}

// GaugeTestcases
func TestGaugeSumFieldVolumeBytesPerSecLast24Hours(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-volumebytespersec.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(rawTable, aggregationView)
}

func TestGaugeCountFieldVolumeBytesPerSecLast24Hours(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-volumebytespersec.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(rawTable, aggregationView)
}

func TestGaugeAvgFieldVolumeBytesPerSecLast24Hours(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-volumebytespersec.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(rawTable, aggregationView)
}

// Gauge testcases with filter
func TestGaugeSumFieldVolumeBytesPerSecLast24HoursFilterBySourceIPDestinationIPSourcePortDestinationPort(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-volumebytespersec-filterby-sourceip-destinationip-sourceport-destinationport.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(rawTable, aggregationView)
}

func TestGaugeCountFieldVolumeBytesPerSecLast24HoursFilterBySourceIPDestinationIPSourcePortDestinationPort(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-volumebytespersec-filterby-sourceip-destinationip-sourceport-destinationport.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(rawTable, aggregationView)
}

func TestGaugeAvgFieldVolumeBytesPerSecLast24HoursFilterBySourceIPDestinationIPSourcePortDestinationPort(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-volumebytespersec-filterby-sourceip-destinationip-sourceport-destinationport.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(rawTable, aggregationView)
}

// 9 in's
func TestGaugeSumFieldVolumeBytesPerSecLast24HoursFilterBySourceIPDestinationIP(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-volumebytespersec-filterby-sourceip-destinationip.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(rawTable, aggregationView)
}

// post filter
func TestGaugeCountFieldEventSourceLast24HoursPostFilterByEventSourceCount(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-eventsource-filterby-eventsource-filterby-eventsourcecount.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Log)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(rawTable, aggregationView)
}

//topN scenarios

// topN Grid
func TestTop10AvgVolumeBytesPerSecCountTCPFlagsGroupBySourceIPDestinationIPSourcePortDestinationPortSourceCityDestinationCountry(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "top10-avg-volumebytespersec-count-tcpflags-groupby-sourceip-destinationip-sourceport-destinationport-sourcecity-destinationcountry.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, sourceIp := range aggregationView[writer.SourceIP] {

		aggregationResult[ToString(sourceIp)+utils.GroupSeparator+ToString(aggregationView[writer.DestinationIP][index])+utils.GroupSeparator+ToString(aggregationView[writer.DestinationPort][index])+utils.GroupSeparator+ToString(aggregationView[writer.DestinationCountry][index])] = []interface{}{aggregationView[AvgVolumeBytesPerSec][index], aggregationView[CountTCPFlags][index]}

		rawResult[ToString(rawTable[writer.SourceIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationIP][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationPort][index])+utils.GroupSeparator+ToString(rawTable[writer.DestinationCountry][index])] = []interface{}{rawTable[AvgVolumeBytesPerSec][index], rawTable[CountTCPFlags][index]}

	}

	assertions.EqualValues(rawResult, aggregationResult)
}

// topN chart
func TestTop10CountVolumeBytesPerSecCountTCPFlagsGroupBySourceIPDestinationIPSourcePortDestinationPortSourceCityDestinationCountry(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "top10-count-volumebytespersec-count-tcpflags-groupby-sourceip-destinationip-sourceport-destinationport-sourcecity-destinationcountry.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(len(rawTable), len(aggregationView))

}

// topN chart All Counters
func TestTop10AvgVolumeBytesPerSecCountTCPFlagsAvgSentDiscardedPacketsSumReceivedDiscardedPacketsCountVolumeBytesSumDurationSumFlowsSumPacketsPerSecGroupBySourceIPDestinationIPSourcePortDestinationPortSourceCityDestinationCountry(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "top10-avg-volumebytespersec-count-tcpflags-avg-sentdiscardedpackets-sum-receiveddiscardedpackets-count-volumebytes-sum-duration-sum-flows-sum-packetspersec.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(len(rawTable), len(aggregationView))

}

// group by group grid
func TestGridCountVolumeBytesPerSecGroupByGroup(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-count-volumebytespersec-groupby-group.json")

	timeline := GetTimeline(Last9Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 9, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, group := range aggregationView["group"] {

		aggregationResult[group.(string)] = aggregationView[CountVolumeBytesPerSec][index]

		rawResult[rawTable["group"][index].(string)] = rawTable[CountVolumeBytesPerSec][index]
	}

	assertions.EqualValues(rawResult, aggregationResult)

}

// group by group grid + source city
func TestGridCountVolumeBytesPerSecGroupByGroupSourceCity(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "grid-count-volumebytespersec-groupby-group-sourcecity.json")

	timeline := GetTimeline(Last48Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, group := range aggregationView["group"] {

		aggregationResult[group.(string)] = aggregationView[CountVolumeBytesPerSec][index]

		rawResult[rawTable["group"][index].(string)] = rawTable[CountVolumeBytesPerSec][index]
	}

	assertions.EqualValues(rawResult, aggregationResult)

}

// group by group histogram + source city
func TestHistogramCountVolumeBytesPerSecGroupByGroupSourceCity(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "histogram-count-volumebytespersec-groupby-group-sourcecity.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = calculateGranularity(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), true, query.Flow)

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, true)

	queryContext[VisualizationGranularity] = calculateGranularity(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), true, query.Flow)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	_, aggregatedTableGroups := removeDummyINT64ValueTable(aggregationView)

	_, rawTableGroups := removeDummyINT64ValueTable(rawTable)

	assertions.Equal(rawTableGroups, aggregatedTableGroups)
}

// group by group histogram
func TestHistogramCountVolumeBytesPerSecGroupByGroup(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "histogram-count-volumebytespersec-groupby-group.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 8, 15, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = calculateGranularity(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), true, query.Flow)

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, true)

	queryContext[VisualizationGranularity] = calculateGranularity(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), true, query.Flow)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	_, aggregatedTableGroups := removeDummyINT64ValueTable(aggregationView)

	_, rawTableGroups := removeDummyINT64ValueTable(rawTable)

	assertions.Equal(rawTableGroups, aggregatedTableGroups)

}

// group by group topN
func TestTop10CountVolumeBytesPerSecGroupByGroup(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "top10-count-volumebytespersec-groupby-group.json")

	timeline := GetTimeline(Today)

	timeline[FromDateTime] = time.Date(2024, 02, 9, 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	aggregationResult := map[string]interface{}{}

	rawResult := map[string]interface{}{}

	assertions.Equal(len(rawTable), len(aggregationView))

	for index, group := range aggregationView["group"] {

		aggregationResult[group.(string)] = aggregationView[CountVolumeBytesPerSec][index]

		rawResult[rawTable["group"][index].(string)] = rawTable[CountVolumeBytesPerSec][index]
	}

	assertions.EqualValues(rawResult, aggregationResult)

}

// group by group topN + source city
func TestTop5CountVolumeBytesPerSecGroupByGroupSourceCity(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	bytes, _ := os.ReadFile(testDir + "top5-count-volumebytespersec-groupby-group-sourcecity.json")

	timeline := GetTimeline(Last24Hours)

	timeline[FromDateTime] = time.Date(2024, 02, 8, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(2024, 02, 9, 10, 0, 0, 0, time.UTC).UnixMilli()

	timeline[Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	rawTimeline := make(utils.MotadataMap)

	for key, value := range timeline {

		rawTimeline[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, query.Flow)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	enableHorizontalAggregation(flowPlugin)

	aggregationView, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(aggregationView)

	datastore.DisableHorizontalAggregations(flowPlugin)

	modifyAggregationTimeline(queryContext, false)

	rawTable, errs := executeQuery(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(rawTable)

	assertions.Equal(len(rawTable), len(aggregationView))

	aggregationCounts := utils.ToINT64Values(aggregationView[CountVolumeBytesPerSec])

	rawCounts := utils.ToINT64Values(rawTable[CountVolumeBytesPerSec])

	utils.SortINT64Values(aggregationCounts)

	utils.SortINT64Values(rawCounts)

	assertions.EqualValues(rawCounts, aggregationCounts)

}

func TestCreateAggregationContexts(t *testing.T) {

	shutdownQueryProcessors()

	defer cache.Clear()

	assertions := assert.New(t)

	horizontalAggregationsFile := utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.HorizontalAggregations

	err := os.Rename(horizontalAggregationsFile, utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.TempPatch+utils.HorizontalAggregations)

	assertions.Nil(err)

	keys, err := createAggregationContexts(testTokenizer)

	assertions.Nil(keys)

	assertions.NotNil(err)

	err = os.Rename(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.TempPatch+utils.HorizontalAggregations, horizontalAggregationsFile)

	assertions.Nil(err)

}

func shutdownQueryProcessors() {

	for _, worker := range workers {

		worker.ShutdownNotifications <- true
	}

	for _, executor := range executors {

		executor.ShutdownNotifications <- true
	}
}

func TestPatchPanicRecovery(t *testing.T) {

	defer cache.Clear()

	assertions := assert.New(t)

	err := Patch(nil, testEncoder, nil, testBatchIOEvents, testWaitGroup)

	assertions.NotNil(err)
}

func TestPatchAbort(t *testing.T) {

	defer cache.Clear()

	assertions := assert.New(t)

	utils.GlobalShutdown = true

	err := Patch(testValueBuffers, testEncoder, testTokenizer, testBatchIOEvents, testWaitGroup)

	assertions.NotNil(err)

	err = probeCompletion(nil)

	assertions.Nil(err)

	err = probeCompletion(map[string]struct{}{"key": {}})

	assertions.NotNil(err)

	utils.GlobalShutdown = false
}

func TestPatchReadInvalidAggregationContexts(t *testing.T) {

	defer cache.Clear()

	assertions := assert.New(t)

	err := os.RemoveAll(utils.JobDir)

	assertions.Nil(err)

	err = os.MkdirAll(utils.JobDir+utils.PathSeparator+utils.AggregationContexts, 0777)

	assertions.Nil(err)

	err = Patch(testValueBuffers, testEncoder, testTokenizer, testBatchIOEvents, testWaitGroup)

	assertions.NotNil(err)

	_ = os.RemoveAll(utils.JobDir)

	err = os.MkdirAll(utils.JobDir, 0777)

	assertions.Nil(err)

}

func TestInvalidAddIndexableColumnByDataStore(t *testing.T) {

	defer cache.Clear()

	horizontalAggregationsFile := utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.HorizontalAggregations

	assertions := assert.New(t)

	err := os.Rename(horizontalAggregationsFile, utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.TempPatch+utils.HorizontalAggregations)

	assertions.Nil(err)

	err = Patch(testValueBuffers, testEncoder, testTokenizer, testBatchIOEvents, testWaitGroup)

	assertions.NotNil(err)

	err = os.Rename(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.TempPatch+utils.HorizontalAggregations, horizontalAggregationsFile)

	assertions.Nil(err)
}

func calculateGranularity(fromTick, toTick int64, histogramQuery bool, engineType query.QueryEngineType) string {

	interval := chooseAggregationInterval(fromTick, toTick, false, engineType)

	if interval == 5 {

		return _5MinGranularity

	} else if interval == 15 {

		return _15MinGranularity

	} else if interval == 30 {

		return _30MinGranularity

	} else if interval == 60 {

		return _1HourGranularity

	} else if interval == 360 {

		return _6HourGranularity

	}

	return _6HourGranularity

}

func GetTimeline(timeline string) (result utils.MotadataMap) {

	result = make(utils.MotadataMap)

	currentTime := time.Now()

	currentHour := currentTime.Hour()

	currentSecond := 0

	currentMinute := currentTime.Minute()

	switch timeline {

	case Last5Minutes:

		result[RelativeTimeline] = "-5m"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute-5, currentSecond, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case Last15Minutes:

		result[RelativeTimeline] = "-15m"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute-15, currentSecond, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case Last30Minutes:

		result[RelativeTimeline] = "-30m"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute-30, currentSecond, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case Last1Hour:

		result[RelativeTimeline] = "-1h"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour-1, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case Last9Hours:

		result[RelativeTimeline] = "-9h"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour-9, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case Today:

		result[RelativeTimeline] = "today"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 23, 35, 0, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case Last24Hours:

		result[RelativeTimeline] = "-24H"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour-24, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case Tomorrow:

		result[RelativeTimeline] = "tomorrow"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()+1, 6, 0, 0, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()+1, 23, 59, 59, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case Last48Hours:

		result[RelativeTimeline] = "-48H"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour-48, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case Last2Days:

		result[RelativeTimeline] = "-2D"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-2, currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case Last7Days:

		result[RelativeTimeline] = "-7d"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-7, currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case Last1Month:

		result[RelativeTimeline] = "-30d"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-30, currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case LastQuarter:

		result[RelativeTimeline] = "-90d"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-90, currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case Last6Months:

		result[RelativeTimeline] = "-110d"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-110, currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case Custom:

		result[RelativeTimeline] = "-110d"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-3, currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-1, currentHour, currentMinute, currentSecond, 0, time.Local).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	}

	return
}

func chooseAggregationInterval(fromTick, toTick int64, filterQuery bool, queryEngineType query.QueryEngineType) int64 {

	difference := int((toTick - fromTick) / 1000) //converting millis to seconds

	if queryEngineType == query.Metric || queryEngineType == query.AIOps {

		return int64(chooseInterval(utils.AggregationIntervals, difference))
	}

	if filterQuery {

		if difference <= 3600 { // upto 1 hour 5 mins interval

			return int64(utils.FullTextViewAggregationIntervals[0])

		} else { // >1 hour 30 mins interval

			return int64(utils.FullTextViewAggregationIntervals[1])

		}
	}

	return int64(chooseInterval(utils.EventAggregationIntervals, difference))

}

func chooseInterval(intervals []int, difference int) int {

	probes := (difference / 60) / intervals[0]

	if len(intervals) == 3 {

		if probes <= 96 {

			return intervals[0]

		} else if probes <= 864 {

			return intervals[1]
		}

		return intervals[2]

	} else if len(intervals) == 2 {

		if probes <= 96 {

			return intervals[0]
		}

		return intervals[1]

	} else {

		return intervals[0]
	}
}

func enableHorizontalAggregation(plugin string) {

	switch plugin {

	case flowPlugin:

		datastore.AddHorizontalAggregation(flowPlugin, flowPlugin+utils.AggregationSeparator+"0", flowView1Table)
		datastore.AddHorizontalAggregation(flowPlugin, flowPlugin+utils.AggregationSeparator+"1", flowView2Table)
		datastore.AddHorizontalAggregation(flowPlugin, flowPlugin+utils.AggregationSeparator+"2", flowView3Table)

	case metricPolicyPlugin:

		datastore.AddHorizontalAggregation(metricPolicyPlugin, metricPolicyPlugin+utils.AggregationSeparator+"0", metricPolicyView1Table)

	case eventPolicyPlugin:

		datastore.AddHorizontalAggregation(eventPolicyPlugin, eventPolicyPlugin+utils.AggregationSeparator+"0", eventPolicyViewTable)

	case logPlugin:

		datastore.AddHorizontalAggregation(logPlugin, logPlugin+utils.AggregationSeparator+"0", utils.MotadataMap{

			utils.Type:                 int(utils.Log),
			FortinetTrafficBytesPerSec: float64(1),
			utils.IndexableColumns: map[string]interface{}{
				utils.EventSource: struct{}{},
			},
		})

	case datastore.EventSearchPlugin:

		datastore.AddHorizontalAggregation(datastore.EventSearchPlugin, datastore.EventSearchPlugin+utils.AggregationSeparator+"0", utils.MotadataMap{

			utils.Type:        int(utils.Log),
			utils.EventSource: float64(0),
			utils.IndexableColumns: map[string]interface{}{
				utils.EventSource: struct{}{},
			},
		})

	}

}

func executeQuery(request utils.MotadataMap) (map[string][]interface{}, string) {

	context := make(utils.MotadataMap)

	bytes, err := json.Marshal(request)

	if err != nil {

		panic(err)
	}

	context = utils.UnmarshalJson(bytes, context)

	context.GetMapValue(query.VisualizationDataSources)[utils.Type] = utils.ResolveQueryType(context.GetMapValue(query.VisualizationDataSources).GetStringValue(utils.Type))

	var result map[string][]interface{}

	var errs string

	executorId := 0

	queryEngineType := getQueryEngineType(context)

	switch queryEngineType {

	case query.Metric:

		for i := range executors {

			if executorAllocations[i] == query.Metric {

				executorId = i

				break
			}
		}

	case query.Flow:

		for i := range executors {

			if executorAllocations[i] == query.Flow {

				executorId = i

				break
			}
		}

	default:

		for i := range executors {

			if executorAllocations[i] == query.Log {

				executorId = i

				break
			}
		}
	}

	executors[executorId].Requests <- context

	for {

		select {

		case response := <-utils.PublisherResponses:

			if valid, buffer := utils.CheckProgress(response); valid {

				result, errs, _, _ = utils.UnpackResponse(buffer, false)

				<-utils.Responses

				return result, errs

			}

		}
	}

	return nil, ""
}

func getQueryEngineType(context utils.MotadataMap) (queryEngineType query.QueryEngineType) {

	datastoreType := utils.DatastoreType(context.GetMapValue(query.VisualizationDataSources).GetIntValue(utils.Type))

	category := context.GetStringValue(query.VisualizationCategory)

	if category == query.Anomaly || category == query.Forecast || category == query.Baseline {

		return query.AIOps

	} else if context.Contains(utils.DrillDown) && strings.EqualFold(context.GetStringValue(utils.DrillDown), utils.Yes) {

		if datastoreType == utils.PerformanceMetric || datastoreType == utils.ObjectStatusMetric || datastoreType == utils.TrapFlapHistory {

			return query.Metric
		}

		return query.DrillDown

	} else if datastoreType == utils.PerformanceMetric || datastoreType == utils.ObjectStatusMetric || datastoreType == utils.StatusFlapHistory {

		return query.Metric

	} else if datastoreType == utils.Flow {

		return query.Flow

	}

	return query.Log
}

func modifyAggregationTimeline(queryContext utils.MotadataMap, histogramQuery bool) {

	timeline := queryContext.GetMapValue(VisualizationTimeline)

	context := queryContext.DeepClone()

	context.GetMapValue(query.VisualizationDataSources)[utils.Type] = utils.ResolveQueryType(context.GetMapValue(query.VisualizationDataSources).GetStringValue(utils.Type))

	queryEngineType := getQueryEngineType(context)

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), false, queryEngineType)

	if interval == 5 {

		queryContext[VisualizationGranularity] = _5MinGranularity

	} else if interval == 15 {

		queryContext[VisualizationGranularity] = _15MinGranularity

	} else if interval == 30 {

		queryContext[VisualizationGranularity] = _30MinGranularity

	} else if interval == 60 {

		queryContext[VisualizationGranularity] = _1HourGranularity

	} else if interval == 360 {

		queryContext[VisualizationGranularity] = _6HourGranularity

	}
}

func removeDummyINT64ValueTable(table map[string][]interface{}) ([]int64, map[string][]int64) {

	minValue := math.MaxInt64

	maxValue := math.MinInt64

	if table == nil {

		return nil, map[string][]int64{}
	}

	startIndex := 0

	endIndex := 0

	groups := make(map[string][]int64)

	for key, values := range table {

		if key == "Timestamp" {

			continue
		}

		groups[key], _, startIndex, endIndex = removeDummyINT64ValueSlice(values, nil)

		if minValue > startIndex {

			minValue = startIndex
		}

		if maxValue < endIndex {

			maxValue = endIndex
		}
	}

	return utils.ToINT64Values(table["Timestamp"])[minValue : maxValue+1], groups
}

func removeDummyINT64ValueSlice(values []interface{}, ticks []interface{}) ([]int64, []int64, int, int) {

	var filteredValues []int64

	var filteredTicks []int64

	startIndex := 0

	endIndex := len(values)

	updated := false

	for index := range values {

		if values[index].(int64) != utils.DummyINT64Value {

			if !updated {

				startIndex = index

				updated = true
			}

			endIndex = index

			filteredValues = append(filteredValues, values[index].(int64))

			if ticks != nil {

				filteredTicks = append(filteredTicks, ticks[index].(int64))
			}
		}
	}

	return filteredValues, filteredTicks, startIndex, endIndex

}

func initQueryProcessors() {

	workers = make([]*query.Worker, utils.QueryWorkers)

	executors = make([]*query.Executor, utils.QueryExecutors)

	position := 0

	for id := 0; id < utils.MetricQueryWorkers; id++ {

		workers[id] = query.NewWorker(id, query.Metric)

		workers[id].Start(executors)

		workerAllocations[id] = query.Metric

	}

	position += utils.MetricQueryWorkers

	for id := position; id < position+utils.FlowQueryWorkers; id++ {

		workers[id] = query.NewWorker(id, query.Flow)

		workers[id].Start(executors)

		workerAllocations[id] = query.Flow

	}

	position += utils.FlowQueryWorkers

	for id := position; id < position+utils.LogQueryWorkers; id++ {

		workers[id] = query.NewWorker(id, query.Log)

		workers[id].Start(executors)

		workerAllocations[id] = query.Log

	}

	position += utils.LogQueryWorkers

	for id := position; id < position+utils.DrillDownQueryWorkers; id++ {

		workers[id] = query.NewWorker(id, query.DrillDown)

		workers[id].Start(executors)

		workerAllocations[id] = query.DrillDown

	}

	position += utils.DrillDownQueryWorkers

	if utils.AIOpsEngineQueryExecutors > 0 {

		for id := position; id < position+utils.AIOpsEngineQueryWorkers; id++ {

			workers[id] = query.NewWorker(id, query.AIOps)

			workers[id].Start(executors)

			workerAllocations[id] = query.AIOps

		}

		position += utils.AIOpsEngineQueryWorkers
	}

	position = 0

	availableWorkers := make([]atomic.Bool, utils.QueryWorkers)

	for id := 0; id < position+utils.MetricQueryExecutors; id++ {

		executors[id] = query.NewExecutor(id, query.Metric)

		executors[id].Start(workers, availableWorkers)

		executorAllocations[id] = query.Metric
	}

	position += utils.MetricQueryExecutors

	for id := position; id < position+utils.FlowQueryExecutors; id++ {

		executors[id] = query.NewExecutor(id, query.Flow)

		executors[id].Start(workers, availableWorkers)

		executorAllocations[id] = query.Flow
	}

	position += utils.FlowQueryExecutors

	for id := position; id < position+utils.LogQueryExecutors; id++ {

		executors[id] = query.NewExecutor(id, query.Log)

		executors[id].Start(workers, availableWorkers)

		executorAllocations[id] = query.Log
	}

	position += utils.LogQueryExecutors

	for id := position; id < position+utils.DrillDownQueryExecutors; id++ {

		executors[id] = query.NewExecutor(id, query.DrillDown)

		executors[id].Start(workers, availableWorkers)

		executorAllocations[id] = query.DrillDown
	}

	position += utils.DrillDownQueryExecutors

	if utils.AIOpsEngineQueryExecutors > 0 {

		for id := position; id < position+utils.AIOpsEngineQueryExecutors; id++ {

			executors[id] = query.NewExecutor(id, query.AIOps)

			executors[id].Start(workers, availableWorkers)

			executorAllocations[id] = query.AIOps
		}

		position += utils.AIOpsEngineQueryExecutors
	}

}

func loadTestEnv() {

	initConfigParams()

	utils.DiskIOWorkers = 300

	utils.IOCPWorkers = 16

	ioWorkers := make([]*storage.DiskIOWorker, utils.DiskIOWorkers)

	for id := range ioWorkers {

		ioWorkers[id] = storage.NewIOWorker(id)

		ioWorkers[id].Start()
	}

	datastore.Init()

	utils.StoreSyncTimerSeconds = 5

	syncJobManager = job.NewStoreSyncJobManager()

	syncJobManager.Start()

	writer.PopulateDefaultColumns()

	populateDefaultColumns()

	initQueryProcessors()

	writer.PopulateDefaultColumns()

}

func initConfigParams() {

	queryResponses = make(chan []byte, 10000)

	utils.PublisherResponses = queryResponses

	utils.Requests = make(chan []byte, 1000)

	utils.Responses = make(chan string, 1000)

	utils.AggregationJobs = 3

	utils.AggregationJobQueryAcks = make([]chan int, utils.AggregationJobs)

	utils.AggregationJobWriteNotifications = make([]chan utils.MotadataMap, utils.AggregationJobs)

	utils.StoreSyncJobAddNotifications = make(chan string, utils.StoreSyncJobs)

	utils.StoreSyncJobRemoveNotifications = make(chan string, utils.StoreSyncJobs)

}

func populateDefaultColumns() {

	datastore.AlterIndexableColumns(flowPlugin, map[string]interface{}{

		writer.DummyINT16Column:    struct{}{},
		writer.DummyINT32Column:    struct{}{},
		writer.DummyINT64Column:    struct{}{},
		writer.SourceIFIndex:       struct{}{},
		writer.DestinationIFIndex:  struct{}{},
		writer.TCPFlags:            struct{}{},
		writer.Protocol:            struct{}{},
		writer.ApplicationProtocol: struct{}{},
		writer.Application:         struct{}{},
		writer.SourceIP:            struct{}{},
		writer.DestinationIP:       struct{}{},
		writer.SourcePort:          struct{}{},
		writer.DestinationPort:     struct{}{},
		writer.DestinationCountry:  struct{}{},
		writer.SourceCity:          struct{}{},
		writer.DestinationCity:     struct{}{},
		writer.EventSource:         struct{}{},
	}, utils.Add)

	datastore.AlterIndexableColumns(metricPolicyPlugin, map[string]interface{}{

		utils.PolicySeverity: struct{}{},
		utils.ObjectId:       struct{}{},
	}, utils.Add)

	datastore.AlterIndexableColumns(logPlugin, map[string]interface{}{

		writer.EventSource: struct{}{},
	}, utils.Add)

	datastore.AlterIndexableColumns(datastore.EventSearchPlugin, map[string]interface{}{

		writer.EventSource: struct{}{},
	}, utils.Add)

}

func assertValueHavingErrorTolerance(expectedValue float64, value float64, assertions *assert.Assertions) {

	precision := float64(100000)

	if expectedValue > value {

		assertions.True(float64((value/precision)-(expectedValue/precision)) <= 0.3, fmt.Sprintf("clickhouse value :%v , motadataDbValue : %v , difference : %v", expectedValue, value, float32(value-expectedValue)))

	} else {

		assertions.True(float64((value/precision)-(expectedValue/precision)) <= 0.3, fmt.Sprintf("clickhouse value :%v , motadataDbValue : %v , difference : %v", expectedValue, value, float32(expectedValue-value)))

	}
}
