/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* 2025-06-04             <PERSON><PERSON><PERSON> Shah            MOTADATA-5780 Test Case Refactoring
 */

package patchv1_43

import (
	"github.com/stretchr/testify/assert"
	"motadatadatastore/utils"
	"os"
	"testing"
)

func TestMain(m *testing.M) {

	if utils.SkipBenchmarkTest() {

		return
	}

	m.Run()
}

func TestPatch(t *testing.T) {

	_ = os.RemoveAll(utils.JobDir)

	_ = os.Mkdir(utils.JobDir, 0777)

	assertions := assert.New(t)

	directory := utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.HyphenSeparator + utils.Aggregations + utils.PathSeparator

	_ = os.RemoveAll(directory)

	metric1 := "dummy.metric1"

	_ = os.MkdirAll(directory+metric1, os.ModePerm)

	metric2 := "dummy.metric2"

	_ = os.MkdirAll(directory+metric2, os.ModePerm)

	fileName1 := "dummy.metric1-153633600-30-181-windows-1"

	fileName2 := "dummy.metric1-153635400-30-181-windows-1"

	fileName3 := "dummy.metric2-153633600-30-182-windows-1"

	file, _ := os.Create(directory + metric1 + utils.PathSeparator + fileName1)

	file.Close()

	file, _ = os.Create(directory + metric1 + utils.PathSeparator + fileName2)

	file.Close()

	file, _ = os.Create(directory + metric2 + utils.PathSeparator + fileName3)

	file.Close()

	assertions.NoError(Patch())

	dir, err := os.ReadDir(directory)

	assertions.NoError(err)

	assertions.Len(dir, 0)

}
