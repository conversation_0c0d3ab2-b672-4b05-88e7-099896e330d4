/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:

* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization

 */

package patchv1_31

import (
	"github.com/goccy/go-json"
	"github.com/stretchr/testify/assert"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"sync"
	"testing"
)

var (
	tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	encoder codec.Encoder
)

func TestMain(m *testing.M) {

	if utils.SkipBenchmarkTest() {

		return
	}

	utils.CleanUpStores() //comment this for debugging

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	if err != nil {

		panic(err)
	}

	utils.SystemBootSequence = utils.Datastore

	utils.EnvironmentType = utils.DatastoreTestEnvironment

	if utils.InitConfigs(utils.UpdateConfigs(bytes, nil)) {

		ioWorkers := make([]*storage.DiskIOWorker, utils.DiskIOWorkers)

		for id := range ioWorkers {

			ioWorkers[id] = storage.NewIOWorker(id)

			ioWorkers[id].Start()
		}

		datastore.Init()

		encoder = codec.NewEncoder(utils.NewMemoryPool(10, utils.MaxPoolLength, true, utils.DefaultBlobPools))

		m.Run()

		for id := range ioWorkers {

			ioWorkers[id].ShutdownNotifications <- true
		}

	}

}

func TestPatchRemoveWALFilesAndTempAggregationStores(t *testing.T) {

	assertions := assert.New(t)

	utils.CleanUpStores()

	_ = os.MkdirAll(utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir, 0755)

	dummyStorePath := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + "dummy-store"

	_ = os.MkdirAll(dummyStorePath, 0755)

	_ = os.MkdirAll(dummyStorePath+utils.PathSeparator+"1", 0755)

	createFile(dummyStorePath+utils.PathSeparator+"1"+utils.PathSeparator+"0.wal", assertions)

	createFile(dummyStorePath+utils.PathSeparator+"1"+utils.PathSeparator+"1.wal", assertions)

	_ = os.MkdirAll(dummyStorePath+utils.PathSeparator+"2", 0755)

	createFile(dummyStorePath+utils.PathSeparator+"2"+utils.PathSeparator+"0.wal", assertions)

	createFile(dummyStorePath+utils.PathSeparator+"2"+utils.PathSeparator+"1.wal", assertions)

	_ = os.MkdirAll(utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+"system.cpu.percent-aggregations", 0755)

	assertions.Nil(Patch())

	_, err := os.Stat(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + "system.cpu.percent-aggregations")

	assertions.True(os.IsNotExist(err))

	_, err = os.Stat(dummyStorePath + utils.PathSeparator + "1" + utils.PathSeparator + "0.wal")

	assertions.True(os.IsNotExist(err))

	_, err = os.Stat(dummyStorePath + utils.PathSeparator + "1" + utils.PathSeparator + "1.wal")

	assertions.True(os.IsNotExist(err))

	_, err = os.Stat(dummyStorePath + utils.PathSeparator + "2" + utils.PathSeparator + "0.wal")

	assertions.True(os.IsNotExist(err))

	_, err = os.Stat(dummyStorePath + utils.PathSeparator + "2" + utils.PathSeparator + "1.wal")

	assertions.True(os.IsNotExist(err))

	_, err = os.Stat(dummyStorePath + utils.PathSeparator + "2")

	assertions.Nil(err)

	_, err = os.Stat(dummyStorePath + utils.PathSeparator + "1")

	assertions.Nil(err)

}

func TestPatch(t *testing.T) {

	assertions := assert.New(t)

	configStore := datastore.GetStore("config0", utils.StaticMetric, true, true, encoder, tokenizer)

	assertions.NotNil(configStore)

	bytes, _ := json.MarshalIndent(utils.MotadataMap{
		"datastore.type": 101,
		"pool.length":    10000,
		utils.Version:    "1.30",
	}, " ", " ")

	err := os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+configStore.GetName()+utils.PathSeparator+"metadata", bytes, 0777)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeStringValues([]string{"value"}, codec.None, utils.MaxValueBytes, "")

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	for index := 0; index < 100; index++ {

		err = configStore.Put([]byte("abc"+utils.KeySeparator+codec.ToString(index)), valueBytes, encoder, tokenizer)

		assertions.Nil(err)
	}

	err = Patch()

	assertions.Nil(err)

	configStore = datastore.GetStore("config0", utils.StaticMetric, false, true, encoder, tokenizer)

	keys, err := configStore.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 100)

	found, resultBytes, err := configStore.Get([]byte("abc"+utils.KeySeparator+codec.ToString(0)), make([]byte, utils.MaxValueBufferBytes), encoder, storage.DiskIOEvent{}, &sync.WaitGroup{}, tokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(valueBytes[8:], resultBytes)

}

// 100 different stores and each store has 1000 different keys
func TestPatchv1(t *testing.T) {

	assertions := assert.New(t)

	for index := 1; index < 100; index++ {

		configStore := datastore.GetStore("config"+utils.HyphenSeparator+codec.INTToStringValue(index), utils.StaticMetric, true, true, encoder, tokenizer)

		assertions.NotNil(configStore)

		bytes, _ := json.MarshalIndent(utils.MotadataMap{
			"datastore.type": 101,
			"pool.length":    10000,
			utils.Version:    "1.30",
		}, " ", " ")

		err := os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+configStore.GetName()+utils.PathSeparator+"metadata", bytes, 0777)

		assertions.Nil(err)

		for position := 0; position < 1000; position++ {

			err = configStore.Put([]byte("abc"+utils.KeySeparator+codec.ToString(position)), make([]byte, 10), encoder, tokenizer)

			assertions.Nil(err)
		}

	}

	err := Patch()

	assertions.Nil(err)

	for index := 1; index < 100; index++ {

		configStore := datastore.GetStore("config"+utils.HyphenSeparator+codec.INTToStringValue(index), utils.StaticMetric, true, true, encoder, tokenizer)

		assertions.NotNil(configStore)

		keys, err := configStore.GetKeys(nil, nil, false, codec.Invalid)

		assertions.Nil(err)

		assertions.Equal(len(keys), 1000)
	}

}

func TestPatchv2(t *testing.T) {

	utils.CleanUpStores()

	assertions := assert.New(t)

	utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"patchv1_31.zip", utils.CurrentDir)

	datastore.Init()

	err := Patch()

	assertions.Nil(err)

	for index := 1; index < 100; index++ {

		configStore := datastore.GetStore("config-zip"+utils.HyphenSeparator+codec.INTToStringValue(index), utils.StaticMetric, true, true, encoder, tokenizer)

		assertions.NotNil(configStore)

		keys, err := configStore.GetKeys(nil, nil, false, codec.Invalid)

		assertions.Nil(err)

		assertions.Equal(len(keys), 1000)
	}

}

func TestPatchV3(t *testing.T) {

	assertions := assert.New(t)

	datastoreDir := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir

	os.RemoveAll(datastoreDir)

	os.MkdirAll(datastoreDir, 0777)

	os.MkdirAll(datastoreDir+utils.PathSeparator+"dummy"+utils.PathSeparator+"metadata", os.ModePerm)

	assertions.Nil(Patch())

	utils.AssertLogMessage(assertions, "Patch 1.31", "patch", "failed to read the metadata file")

}

func createFile(path string, assertions *assert.Assertions) {

	file, err := os.Create(path)

	assertions.NotNil(file)

	assertions.Nil(err)

	_ = file.Close()
}
