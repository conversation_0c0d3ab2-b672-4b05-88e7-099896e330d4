/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs

* 2025-05-05			 Swapnil A<PERSON> Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-10             Dhaval Bera            MOTADATA-6174  Added Unmap function to release memory-mapped resources
* 2025-06-04             Aashil Shah            MOTADATA-5780 Called custom MmapAnonymous and Munmap functions
 */

package patchv1_31

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/fs"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/patch/patches"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
)

const variant = "1.31"

var logger = utils.NewLogger("Patch 1.31", "patch")

func Patch() error {

	var bytes []byte

	metadata := make(utils.MotadataMap)

	datastoreDir := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir

	dirs, _ := os.ReadDir(datastoreDir + utils.PathSeparator)

	stores := make([]string, 0)

	for _, dir := range dirs {

		if dir.IsDir() {

			files, err := os.ReadDir(datastoreDir + utils.PathSeparator + dir.Name())

			if err != nil {

				logger.Error(fmt.Sprintf("failed to read the store directory %v , reason %v", dir.Name(), err.Error()))

				continue
			}

			for file := range files {

				if strings.Contains(files[file].Name(), "metadata") {

					bytes, err = os.ReadFile(datastoreDir + utils.PathSeparator + dir.Name() + utils.PathSeparator + files[file].Name())

					if err != nil {

						logger.Error(fmt.Sprintf("failed to read the metadata file %v error %v", files[file].Name(), err.Error()))

					}

					err = json.Unmarshal(bytes, &metadata)

					if err != nil {

						logger.Error(fmt.Sprintf("failed to unmarshal the metadata %v reason %v", files[file].Name(), err.Error()))
					}

					if metadata.Contains(datastore.DatastoreType) && metadata.Contains(utils.Version) {

						if utils.DatastoreType(metadata.GetIntValue(datastore.DatastoreType)) == utils.StaticMetric && codec.StringToFloat64(metadata.GetStringValue(utils.Version)) < 1.31 {

							stores = append(stores, dir.Name())

						}

					}

				}

			}

		}
	}

	ioWorkers := make([]*storage.DiskIOWorker, runtime.NumCPU()*2)

	for id := range ioWorkers {

		ioWorkers[id] = storage.NewIOWorker(id)

		ioWorkers[id].Start()
	}

	defer func() {

		for id := range ioWorkers {

			ioWorkers[id].ShutdownNotifications <- true

		}

	}()

	//apply patch in the following stores

	if len(stores) > 0 {

		logger.Info(fmt.Sprintf("total stores qualified for patch are %v", len(stores)))

		if err := migrate(stores); err != nil {

			return err
		}
	}

	return filepath.WalkDir(utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir, func(path string, entry fs.DirEntry, err error) error {

		if (entry.IsDir() && strings.HasSuffix(entry.Name(), utils.HyphenSeparator+utils.Aggregations)) ||
			(!entry.IsDir() && strings.HasSuffix(entry.Name(), ".wal")) {

			_ = os.RemoveAll(path)
		}

		return nil

	})

}

func migrate(stores []string) (err error) {

	datastoreDir := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir

	encoder := codec.NewEncoder(utils.NewMemoryPool(10, utils.MaxPoolLength, true, utils.DefaultBlobPools))

	defer encoder.MemoryPool.Unmap()

	tokenizer := &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	var buffers [][]byte

	var errs []error

	keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	events := make([]storage.DiskIOEventBatch, utils.MaxStoreParts)

	for index := 0; index < len(events); index++ {

		events[index] = storage.DiskIOEventBatch{}
	}

	waitGroup := sync.WaitGroup{}

	defer func() {

		if r := recover(); r != nil {

			stackTraceBytes := make([]byte, 1<<20)

			logger.Error(fmt.Sprintf("error %v occurred in patch 1.31", r))

			logger.Error(fmt.Sprintf("!!!STACK TRACE for patch 1.11!!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			err = errors.New(fmt.Sprintf("err %v occurred while applying patch 1.31", r))

		}

		for index := 0; index < len(valueBuffers); index++ {

			_ = utils.Munmap(valueBuffers[index])

		}

	}()

	poolIndex, paddingBytes := encoder.MemoryPool.AcquireBytePool(8)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	for index := range valueBuffers {

		bytes, err := utils.MmapAnonymous(utils.MaxValueBufferBytes)

		if err != nil {

			logger.Error(fmt.Sprintf("error %v occurred while mapping annonymous buffer for patch %v and index %v", err, variant, index))

			bytes = make([]byte, utils.MaxValueBufferBytes)

		}

		valueBuffers[index] = bytes
	}

	for index := range stores {

		logger.Info(fmt.Sprintf("patch started for store %v", stores[index]))

		store := datastore.GetStore(stores[index], utils.None, true, true, encoder, tokenizer)

		newStore := datastore.GetStore(utils.TempPatch+stores[index], utils.StaticMetric, true, true, encoder, tokenizer)

		keys, err := store.GetKeys(nil, nil, false, codec.Invalid)

		if err != nil {

			logger.Error(fmt.Sprintf("failed to get the keys "))
		}

		runningIndex := 0

		for outstandingRequest := 0; outstandingRequest < len(keys); outstandingRequest++ {

			keyBuffers[runningIndex] = keys[outstandingRequest]

			runningIndex++

			if (runningIndex >= utils.MaxWorkerEventKeyGroupLength) || (outstandingRequest == len(keys)-1) {

				buffers, errs, err = store.GetMultiples(keyBuffers[:runningIndex], valueBuffers[:runningIndex], encoder, events, &waitGroup, tokenizer, false)

				if err != nil {

					runningIndex = 0

					logger.Error(fmt.Sprintf("error %v occurred while getting keys :%v", err, keys))

					continue
				}

				for position, err := range errs {

					if err == nil {

						bytePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

						copy(valueBytes, paddingBytes)

						copy(valueBytes[len(paddingBytes):], buffers[position])

						err = newStore.Put(keyBuffers[position], valueBytes[:len(buffers[position])+len(paddingBytes)], encoder, tokenizer)

						encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

						if err != nil {

							runningIndex = 0

							logger.Error(fmt.Sprintf(utils.ErrorWriteKey, keys[position], store.GetName(), err))

							continue
						}
					}
				}

				runningIndex = 0
			}

		}

		newStore.Close(encoder)

		store.Close(encoder)

		datastore.RemoveStore(store.GetName())

		datastore.RemoveStore(utils.TempPatch + stores[index])

		if err = patches.UpdateStoreVariant(stores[index], variant); err != nil {

			logger.Error(fmt.Sprintf("failed to update variant %v in store metadata, reason: %v", variant, err.Error()))

			continue
		}

		if err = os.Rename(datastoreDir+utils.PathSeparator+utils.TempPatch+stores[index], datastoreDir+utils.PathSeparator+utils.Patch+stores[index]); err != nil {

			logger.Error(fmt.Sprintf("failed to migrate config keys for store %v, reason: %v", stores[index], err.Error()))

			_ = os.RemoveAll(datastoreDir + utils.PathSeparator + utils.TempPatch + stores[index])

			continue
		}

		_ = os.RemoveAll(datastoreDir + utils.PathSeparator + stores[index])

		if err = os.Rename(datastoreDir+utils.PathSeparator+utils.Patch+stores[index], datastoreDir+utils.PathSeparator+stores[index]); err != nil {

			logger.Error(fmt.Sprintf("failed to migrate count keys for store %v, reason: %v", stores[index], err.Error()))

			_ = os.RemoveAll(datastoreDir + utils.PathSeparator + utils.TempPatch + stores[index])
		}

		if utils.DebugEnabled() {

			logger.Debug(fmt.Sprintf("keys for store %v migrated successfully", stores[index]))
		}

		logger.Info(fmt.Sprintf("patch finished for store %v", stores[index]))
	}

	return err

}
