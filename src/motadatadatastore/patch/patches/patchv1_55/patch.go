/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package patchv1_55

import (
	"encoding/json"
	"errors"
	"fmt"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"strings"
)

const (
	variant = "1.55"

	DummyNumericOrdinal = 0
)

var logger = utils.NewLogger("Patch 1.55", "patch")

func Patch(tokenizer *utils.Tokenizer, encoder codec.Encoder) error {

	entries, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	if err != nil {

		return errors.New("Could not read datastore directory: " + err.Error())
	}

	//mapping store

	mappingStore := datastore.GetStore(datastore.Object+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, false, true, encoder, tokenizer)

	if mappingStore == nil {

		logger.Error(fmt.Sprintf(utils.ErrorAcquireStore, datastore.Object+utils.HyphenSeparator+datastore.Mappings))

		return nil
	}

	stores := make(map[string]struct{})

	for _, entry := range entries {

		utils.Split(entry.Name(), utils.HyphenSeparator, tokenizer)

		if strings.Contains(entry.Name(), "corrupted") || tokenizer.Counts > 2 || strings.HasSuffix(entry.Name(), datastore.Duration) || strings.HasSuffix(entry.Name(), datastore.Mappings) {

			continue
		}

		configStore := datastore.GetStore(entry.Name(), utils.None, false, true, encoder, tokenizer)

		if configStore == nil {

			continue
		}

		if err = reconstructConfigStores(configStore, mappingStore, tokenizer, encoder, stores); err != nil {

			logger.Error(fmt.Sprintf("failed to reconstruct config stores: %s", err.Error()))
		}

		logger.Info(fmt.Sprintf("patch finished for store %v", entry.Name()))

	}

	logger.Info(fmt.Sprintf("started patch for motadata-datastore.json"))

	if len(stores) > 0 {

		//read motadata-datastore config
		bytes, err := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

		if err != nil {

			logger.Error(fmt.Sprintf("failed to read config file: %s", err.Error()))

			return nil
		}

		err = reconstructConfigs(stores, bytes)

		if err != nil {

			logger.Error(fmt.Sprintf("failed to reconstruct config stores: %s", err.Error()))

			//in case of error we need to write the orginal configBytes once again

			_ = os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.MotadataDatastoreConfigFile, bytes, 0777)

		}

	}

	return nil
}

func reconstructConfigStores(configStore, mappingStore *storage.Store, tokenizer *utils.Tokenizer, encoder codec.Encoder, stores map[string]struct{}) error {

	defer func() {

		if r := recover(); r != nil {

			logger.Error(fmt.Sprintf("failed to reconstruct config store %v , reason %v", configStore.GetName(), r))
		}

		configStore.Close(encoder)
	}()

	keys := make(map[string]uint64)

	storeName := configStore.GetName()

	if codec.StringToFloat64(configStore.GetVariant()) >= codec.StringToFloat64(variant) {

		logger.Info(fmt.Sprintf("current store %v variant is %v greater than variant hence no need to apply patch", configStore.GetName(), configStore.GetVariant()))

		return nil
	}

	logger.Info(fmt.Sprintf("patch started for store %v", configStore.GetName()))

	configKeys, err := configStore.GetKeys(nil, nil, false, codec.Invalid)

	if err != nil {

		logger.Error(fmt.Sprintf("failed to get config keys: %v", err.Error()))

		return err
	}

	instanceType := utils.Empty

	for _, keyBytes := range configKeys {

		utils.Split(string(keyBytes), utils.KeySeparator, tokenizer)

		//cannot check here for aggregation since there is a scenario in which string counter is not qualified in aggregation but numeric counter is
		if strings.Contains(string(keyBytes), utils.InstanceSeparator) {

			key := tokenizer.Tokens[0] + utils.GroupSeparator + tokenizer.Tokens[1]

			keys[key] = DummyNumericOrdinal

			// Set new ordinals from the mapping store
			if found, ordinal, _ := mappingStore.GetStringMapping(key); found {

				keys[key] = uint64(ordinal)

			}

			if instanceType == utils.Empty {

				utils.Split(tokenizer.Tokens[2], utils.InstanceSeparator, tokenizer)

				instanceType = tokenizer.Tokens[0]

			}
		}
	}

	//there might be a case where we don't have instance metric in a plugin
	if len(keys) == 0 || instanceType == utils.Empty {

		logger.Info(fmt.Sprintf("no keys found for patching for store %v", storeName))

		return nil
	}

	return rebuildMappingStore(keys, instanceType, tokenizer, encoder, stores)

}

func rebuildMappingStore(keys map[string]uint64, instanceType string, tokenizer *utils.Tokenizer, encoder codec.Encoder, stores map[string]struct{}) error {

	keyPoolIndex, sortedKeys := encoder.MemoryPool.AcquireStringPool(len(keys))

	defer encoder.MemoryPool.ReleaseStringPool(keyPoolIndex)

	keyElementSize := 0

	for key := range keys {

		sortedKeys[keyElementSize] = key

		keyElementSize++
	}

	utils.SortStringValues(sortedKeys)

	storeName := instanceType + utils.HyphenSeparator + datastore.Mappings

	//remove previous store if present
	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + utils.Patch + storeName)

	mappingStore := datastore.GetStore(storeName, utils.None, false, true, encoder, tokenizer)

	if mappingStore == nil {

		store := datastore.GetStore(utils.Patch+storeName, utils.Mapping, true, true, encoder, tokenizer)

		for _, key := range sortedKeys {

			err := storage.WriteStringMapping(key, int32(keys[key]), store, encoder)

			if err != nil {

				logger.Error(fmt.Sprintf("failed to write mapping %v to store %v", key, err.Error()))

				continue

			}
		}

		store.Close(encoder)

		datastore.RemoveStore(utils.Patch + storeName)

		err := os.Rename(utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+utils.Patch+storeName, utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+storeName)

		if err != nil {

			logger.Error(fmt.Sprintf("failed to rename store %v", err.Error()))

		}

		//opening store once again so that store can be there in stores map, as renaming store doesn't add the store in the stores map
		mappingStore = datastore.GetStore(storeName, utils.None, true, true, encoder, tokenizer)

		if mappingStore != nil {

			mappingStore.Close(encoder)

			datastore.RemoveStore(storeName)
		}

	} else {

		// Same instance type comes in multiple stores , need to avoid writing duplicate ordinals
		for _, key := range sortedKeys {

			if found, _, _ := mappingStore.GetStringMapping(key); !found {

				err := storage.WriteStringMapping(key, int32(keys[key]), mappingStore, encoder)

				if err != nil {

					logger.Error(fmt.Sprintf("failed to write mapping %v to store %v", key, err.Error()))

					continue

				}

			}

		}

		mappingStore.Close(encoder)

		datastore.RemoveStore(storeName)
	}

	stores[storeName] = struct{}{}

	logger.Info(fmt.Sprintf("created new store %v", storeName))

	return nil

}

func reconstructConfigs(stores map[string]struct{}, bytes []byte) (err error) {

	// need to update the existing motadata config

	defer func() {

		if r := recover(); r != nil {

			logger.Error(fmt.Sprintf("Error occurred while reconstructing config error :%v", r))

			err = r.(error)
		}
	}()

	configs := make(utils.MotadataMap)

	_ = json.Unmarshal(bytes, &configs)

	if len(configs) > 0 && configs.Contains(utils.MappingStoreCacheConfigs) {

		if cacheConfigs := configs.GetMapValue(utils.MappingStoreCacheConfigs); cacheConfigs != nil && cacheConfigs.Contains(datastore.Object+utils.HyphenSeparator+datastore.Mappings) {

			maxValue := cacheConfigs.GetIntValue(datastore.Object + utils.HyphenSeparator + datastore.Mappings)

			for store := range stores {

				cacheConfigs[store] = maxValue
			}

			configs[utils.MappingStoreCacheConfigs] = cacheConfigs

			bytes, _ := json.MarshalIndent(&configs, "", " ")

			err = os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.MotadataDatastoreConfigFile, bytes, 0777)

			if err != nil {

				logger.Error(fmt.Sprintf("failed to write config file: %s", err.Error()))
			}

			logger.Info("config file updated successfully")

		}

	}

	return nil

}
