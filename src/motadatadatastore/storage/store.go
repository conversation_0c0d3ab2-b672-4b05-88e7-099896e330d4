/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-01             Vedant Dokania       	MOTADATA-5194 temp mapping reset lock changes
* 2025-03-05			 <PERSON><PERSON><PERSON>			Mo<PERSON>-5190 Migrated constants from datastore to utils to match SonarQube Standard
* 2025-03-05             Vedant Dokania          Motadata-5451  Status Flap vertical new datastore type
* 2025-03-21			 <PERSON><PERSON>val <PERSON>-5452 Introduced NetRouteMetric DatastoreType
* 2025-02-25             Vedant <PERSON>kan<PERSON>	        Motad<PERSON>-5646 Removing shadow variables
* 2025-04-02			 <PERSON><PERSON>val <PERSON>ra			<PERSON>-4859  Added NetRoute Status Metric Datastore Type
* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 introduced new put multiples method for writing in batch over single lock hold.
* 2025-06-03			 Dhaval Bera			Motadata-6393  Updated With Master Branch
* 2025-06-23             Dhaval Bera            MOTADATA-6642  Added comments and memory aligned the struct
* 2025-06-24			 Aashil Shah		    MOTADATA-6543  Refactored Logs

* 2025-06-23             Vedant Dokania         Motadata-6370 Mapping operand changes to get the instance type store

 */

// Package storage implements the physical storage layer of the Motadata Database system.
// It handles disk I/O operations, memory-mapped file management, data partitioning,
// and provides efficient access to stored data.
//
// The storage module is the heart of the DATASTORE, providing an abstraction for storing key-value pairs.
// It is responsible for all data persistence operations including:
// - Writing and reading data throughout the system
// - Managing data partitioning for scalability
// - Providing transaction support for data consistency
// - Handling efficient mapping between string/numeric values and their ordinals
// - Supporting various query patterns through specialized indexes
// - Managing blob storage for large values
//
// Key architectural components:
// - Store: The main storage unit that manages a collection of key-value pairs
// - Partition: A subdivision of a store for better performance and scalability
// - Mapping: A system for efficiently storing and retrieving string and numeric values
// - Transaction: Support for atomic operations across multiple keys
//
// Data organization:
// - Data is divided into partitions based on key characteristics
// - Each partition manages its own segment of the keyspace
// - Values are limited to 1.5 MB by default; larger values are stored as blob files
// - Special mapping stores provide efficient ordinal-based representation of values
//
// Performance optimizations:
// - Memory-mapped files for high-performance access
// - Bitmap indexes for efficient filtering
// - Caching of frequently accessed mappings
// - Batch operations for reduced lock contention
//
// NOTE:
//   - Mapping stores do not have partitions; they are only used for ordinals
//   - The storage system is designed to handle high write throughput while maintaining
//     fast read access for time-series data
package storage

import (
	"archive/zip"
	bytes2 "bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/blevesearch/vellum"
	"github.com/dolthub/swiss"
	"github.com/kamstrup/intmap"
	"github.com/kelindar/bitmap"
	cp "github.com/otiai10/copy"
	"io"
	"io/fs"
	"motadatadatastore/cache"
	"motadatadatastore/codec"
	. "motadatadatastore/utils"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

var (
	storeLogger = NewLogger("Store", "storage")
)

// MappingRecoveryMode defines the level of recovery to be performed for mapping stores.
type MappingRecoveryMode int

const (
	// Normal indicates no special recovery is needed
	Normal MappingRecoveryMode = -1

	// Partial indicates that partial recovery of mappings is needed
	Partial MappingRecoveryMode = 0

	// Full indicates that complete recovery of mappings is needed
	Full MappingRecoveryMode = 1

	// MultipartRepaired is a flag used to indicate that multipart keys have been repaired
	// Do not change the variable name as patching is applied on client side
	MultipartRepaired = "multipart.repaired"

	// version2 is used for version compatibility checks
	// It will be based on current version so check will always be less than not less than equal to
	version2 = 1.42
)

// Store is the primary storage unit in the Motadata Database system.
// It manages a collection of key-value pairs with support for efficient
// string and numeric value mapping, partitioning, and transaction handling.
//
// A Store can be of different types (DatastoreType) which determines how
// the data is organized and accessed. Each store type has specific
// optimizations for its intended use case.
//
// The Store provides operations for:
// - Reading and writing key-value pairs
// - Mapping string and numeric values to ordinals for efficient storage
// - Managing partitions for scalability
// - Supporting transactions for data consistency
// - Handling large values through blob storage
// - Performing various query operations (prefix, suffix, range queries)
// - Cleanup, archiving, and backup operations
//
// Memory Layout Optimization:
// The struct fields are arranged to minimize memory usage through proper alignment:
// - 8-byte aligned fields (int64, uint64, float64, atomic types) are placed first
// - Pointer-sized fields (8 bytes on 64-bit systems) are grouped together
// - 4-byte fields (int32, int) are grouped together
// - Smaller fields (bool, byte) are placed at the end
// - This arrangement reduces memory padding and improves cache efficiency
//
// Performance Considerations:
// - Hot path fields (locks, atomic counters) are placed early for better cache locality
// - Related fields are grouped together to improve spatial locality
// - Memory-mapped fields are grouped for efficient memory management
// - File handles are grouped for resource management efficiency
type Store struct {
	// === ATOMIC FIELDS (8-byte aligned, must be first for atomic operations) ===

	// lastUsedTimestampSeconds tracks the last access time for LRU-based cleanup
	// Used by cleanup jobs to identify inactive stores for potential closure
	// CRITICAL: Must be 8-byte aligned for atomic operations on 32-bit systems
	lastUsedTimestampSeconds atomic.Int64

	// containMultipartKeys indicates if the store contains multipart key entries
	// Used for optimization - stores without multipart keys can skip related processing
	// Atomic to allow lock-free reads during high-frequency operations
	containMultipartKeys atomic.Bool

	// === 8-BYTE NUMERIC FIELDS (grouped for optimal alignment) ===

	// Performance and monitoring metrics (grouped for cache efficiency)
	// reads tracks the total number of read operations performed on this store
	reads int64

	// puts tracks the total number of write operations performed on this store
	puts int64

	// storeBytes tracks the total size in bytes of data stored in this store
	storeBytes int64

	// readLatency accumulates read operation latencies for performance monitoring
	readLatency uint64

	// putLatency accumulates write operation latencies for performance monitoring
	putLatency uint64

	// parts specifies the number of partitions in this store for load distribution
	parts uint64

	// variant stores the store format version for backward compatibility handling
	// Used during store opening to determine format migration requirements
	variant float64

	// === POINTER FIELDS (8 bytes on 64-bit systems, grouped for alignment) ===

	// Mapping data structures for value-to-ordinal conversion
	// tempStringMappings provides in-memory string-to-ordinal mapping for active session data
	// Used for fast lookups during write operations before FST persistence
	tempStringMappings *swiss.Map[string, int32]

	// tempNumericMappings provides in-memory numeric-to-ordinal mapping for active session data
	// Optimized for numeric data types with efficient integer key handling
	tempNumericMappings *intmap.Map[int64, int32]

	// Cache structures for frequently accessed mappings (performance optimization)
	// cacheStringMappings caches frequently accessed string mappings across sessions
	// Reduces FST lookups for hot data, significantly improving read performance
	cacheStringMappings *swiss.Map[int32, string]

	// cacheNumericMappings caches frequently accessed numeric mappings across sessions
	// Provides O(1) access to commonly used numeric values
	cacheNumericMappings *intmap.Map[int32, int64]

	// File handles for mapping operations (grouped for resource management)
	// numericMappingTempFile handles temporary numeric mapping writes during active sessions
	numericMappingTempFile *os.File

	// stringMappingTempFile handles temporary string mapping writes during active sessions
	stringMappingTempFile *os.File

	// File handles for mapping operations (grouped for resource management)
	// numericMappingFile handles numeric mapping writes during active sessions
	numericMappingFile *os.File

	// stringMappingFile handles string mapping writes during active sessions
	stringMappingFile *os.File

	// multipartKeyFile manages multipart key metadata for complex key structures
	multipartKeyFile *os.File

	// lockFile provides exclusive access control to prevent concurrent store access
	lockFile *os.File

	// numericMappingBackupFile maintains backup of numeric mappings for crash recovery
	numericMappingBackupFile *os.File

	// stringMappingBackupFile maintains backup of string mappings for crash recovery
	stringMappingBackupFile *os.File

	// Synchronization and concurrency control
	// lock provides thread-safe access to store operations with reader-writer semantics
	// Read operations can proceed concurrently, writes require exclusive access
	lock *sync.RWMutex

	// Persistent mapping storage using FST (Finite State Transducer) for efficient queries
	// stringMapping provides persistent string-to-ordinal mapping with range query support
	// Supports prefix searches, pattern matching, and sorted iteration
	stringMapping *vellum.FST

	// numericMapping provides persistent numeric-to-ordinal mapping with range query support
	// Optimized for numeric range queries and efficient big-endian key ordering
	numericMapping *vellum.FST

	// Memory-mapped file interfaces for high-performance I/O operations
	// tempStringMappingMMapBytes provides memory-mapped access to string mapping temp files
	// Enables high-performance writes with OS-level caching and lazy persistence
	tempStringMappingMMapBytes MMap

	// tempNumericMappingMMapBytes provides memory-mapped access to numeric mapping temp files
	// Optimized for fixed-size numeric data with efficient sequential writes
	tempNumericMappingMMapBytes MMap

	// Memory-mapped file interfaces for high-performance I/O operations
	// stringMappingMMapBytes provides memory-mapped access to string mapping temp files
	// Enables high-performance writes with OS-level caching and lazy persistence
	stringMappingMMapBytes MMap

	// numericMappingMMapBytes provides memory-mapped access to numeric mapping temp files
	// Optimized for fixed-size numeric data with efficient sequential writes
	numericMappingMMapBytes MMap

	// Bitmap structures for ordinal management and cleanup operations
	// availableStringMappingOrdinals tracks reusable string ordinals after cleanup operations
	// Used to reclaim ordinals from deleted mappings, preventing ordinal space exhaustion
	availableStringMappingOrdinals *bitmap.Bitmap

	// availableNumericMappingOrdinals tracks reusable numeric ordinals after cleanup operations
	// Maintains efficient ordinal space utilization for numeric mappings
	availableNumericMappingOrdinals *bitmap.Bitmap

	// tempMappingOrdinals tracks ordinals created during cleanup operations
	// Used to discard ordinals that were created between cleanup start and completion
	// Ensures consistency during concurrent cleanup and write operations
	tempMappingOrdinals *bitmap.Bitmap

	// Store metadata and configuration
	// metadata contains store configuration, version information, and operational parameters
	// Persisted to disk for store recovery and configuration management
	metadata MotadataMap

	// partitions contains the array of partition instances for data distribution
	// Each partition manages a subset of the keyspace for improved performance and scalability
	partitions []*Partition

	// multipartKeys maps multipart key hashes to their partition assignments
	// Used for efficient routing of complex keys that span multiple segments
	multipartKeys map[uint64]uint16

	// === STRING FIELDS (pointer + length, grouped together) ===

	// name is the unique identifier for this store instance
	name string

	// path is the filesystem directory path where store data is persisted
	path string

	// === 4-BYTE INTEGER FIELDS (grouped for optimal packing) ===

	// Cache and mapping configuration parameters
	// maxMappingCacheRecords defines the maximum number of mappings to cache in memory
	// Balances memory usage with lookup performance for frequently accessed data
	maxMappingCacheRecords int

	// stringMappingSize tracks the current size of the string mapping temporary file
	// Used for memory-mapped file expansion and space management
	stringMappingSize int

	// numericMappingSize tracks the current size of the numeric mapping temporary file
	// Enables efficient file growth and memory mapping operations
	numericMappingSize int

	// cacheTTL defines the time-to-live for cached mapping entries in seconds
	// Prevents stale cache entries and manages memory usage over time
	cacheTTL int

	// Ordinal generation and management
	// currentStringMappingOrdinal tracks the next available ordinal for string mappings
	// Ensures unique ordinal assignment and prevents collisions
	currentStringMappingOrdinal int32

	// currentNumericMappingOrdinal tracks the next available ordinal for numeric mappings
	// Maintains separate ordinal space for numeric values with even-number encoding
	currentNumericMappingOrdinal int32

	// storeType defines the type of data store and its optimization characteristics
	// Determines partitioning strategy, indexing behavior, and query optimizations
	storeType DatastoreType

	// === BOOLEAN FIELDS (1 byte each, grouped at end to minimize padding) ===

	// State management flags
	// closed indicates whether the store has been closed and is no longer accepting operations
	// Used to prevent operations on inactive stores and enable proper cleanup
	closed bool

	// dirty indicates whether the store has uncommitted changes that need persistence
	// Used by sync operations to determine which stores require disk writes
	dirty bool

	// cleanup indicates whether a cleanup operation is currently in progress
	// Prevents concurrent cleanup operations and coordinates ordinal management
	cleanup bool
}

/////////////////////////////////////////// Init Code //////////////////////////////////////////////

// GetDatastoreType returns the type of this store instance.
//
// The datastore type determines:
// - Partitioning strategy and number of partitions
// - Indexing behavior and optimization patterns
// - Query processing optimizations
// - Memory allocation strategies
// - Cleanup and maintenance procedures
//
// Returns:
//   - DatastoreType: The type identifier for this store
func (store *Store) GetDatastoreType() DatastoreType {

	return store.storeType
}

// GetPartitions returns the array of partition instances for this store.
//
// Partitions provide:
// - Data distribution across multiple segments
// - Parallel processing capabilities
// - Improved scalability for large datasets
// - Isolation of different key ranges
//
// Returns:
//   - []*Partition: Array of partition instances
func (store *Store) GetPartitions() []*Partition {

	return store.partitions
}

// GetLastUsedTimestamps returns the last access timestamp for LRU-based cleanup.
//
// This timestamp is used by:
// - Cleanup jobs to identify inactive stores
// - Memory management for store closure decisions
// - Performance monitoring and usage analytics
// - Resource optimization strategies
//
// Returns:
//   - int64: Unix timestamp of last store access
func (store *Store) GetLastUsedTimestamps() int64 {

	return store.lastUsedTimestampSeconds.Load()
}

// IsClosed returns whether the store has been closed and is no longer accepting operations.
//
// A closed store:
// - Rejects all new read and write operations
// - Has released all file handles and memory mappings
// - Cannot be reopened without going through full initialization
// - May still have background cleanup operations in progress
//
// Returns:
//   - bool: true if the store is closed, false if active
func (store *Store) IsClosed() bool {

	return store.closed
}

// getPartitionLength determines the optimal number of partitions for different store types.
//
// Partition count is carefully tuned based on:
// - Expected data volume and write patterns
// - Query performance requirements
// - Memory usage considerations
// - Concurrency and lock contention patterns
// - I/O distribution across storage devices
//
// Partitioning Strategy:
// - Mapping stores: No partitions (0) - single namespace for ordinal consistency
// - Low-volume stores: 2 partitions - minimal overhead with basic parallelism
// - Medium-volume stores: 4 partitions - balanced performance and resource usage
// - High-volume stores: 6 partitions - optimized for heavy write workloads
// - Ultra-high-volume stores: 8 partitions - maximum parallelism for flow data
//
// IMPORTANT: Always update MaxStoreParts in utils when modifying partition counts
// to ensure proper memory allocation and resource management.
//
// Parameters:
//   - dataStoreType: The type of store to determine partitioning for
//
// Returns:
//   - byte: Number of partitions for the specified store type
func getPartitionLength(dataStoreType DatastoreType) byte {

	// Mapping stores require no partitions for ordinal consistency
	// All mappings must be in a single namespace to prevent ordinal conflicts
	if dataStoreType == Mapping {

		return 0

	} else if dataStoreType == HealthMetric || dataStoreType == ConfigHistory || dataStoreType == Compliance {

		// Low-volume stores: 2 partitions for basic parallelism
		// These stores typically have infrequent writes and small data volumes
		return 2

	} else if dataStoreType == MetricPolicy || dataStoreType == MetricAggregation || dataStoreType == StaticMetric || dataStoreType == MetricIndex ||
		dataStoreType == TrapAggregation || dataStoreType == FlowIndex || dataStoreType == Trap || dataStoreType == EventPolicy ||
		dataStoreType == PolicyAggregation || dataStoreType == PolicyFlapHistory || dataStoreType == StatusFlapHistory ||
		dataStoreType == LogAggregation || dataStoreType == ObjectStatusFlapMetric || dataStoreType == NetRouteStatusMetricAggregation || dataStoreType == NetRouteStatusMetric {

		// Medium-volume stores: 4 partitions for balanced performance
		// These stores have moderate write rates and benefit from parallel processing
		return 4

	} else if dataStoreType == PerformanceMetric || dataStoreType == ObjectStatusMetric || dataStoreType == Log || dataStoreType == FlowAggregation {

		// High-volume stores: 6 partitions for heavy workloads
		// These stores handle high-frequency writes and require significant parallelism
		return 6

	} else if dataStoreType == Flow {

		// Ultra-high-volume stores: 8 partitions for maximum throughput
		// Flow data represents the highest volume workload requiring maximum parallelism
		return 8
	}

	// Default fallback: 2 partitions for unknown store types
	// Provides basic functionality while maintaining conservative resource usage
	return 2
}

// GetPartitionKey extracts the partition key from a full key based on store type.
//
// Different store types use different partitioning strategies to optimize:
// - Data distribution across partitions
// - Query performance for common access patterns
// - Load balancing for write operations
// - Cache locality for related data
//
// Partitioning Strategies:
//
// 1. Full Key Partitioning (StaticMetric, Index stores):
//   - Uses the entire key for partitioning
//   - Provides maximum distribution granularity
//   - Optimal for stores with diverse key patterns
//
// 2. Prefix Partitioning (Performance/Status metrics):
//   - Uses the first component before KeySeparator
//   - Groups related metrics by entity/device
//   - Improves cache locality for entity-based queries
//
// 3. Format-Aware Partitioning (EventPolicy):
//   - Adapts strategy based on data format (vertical/horizontal)
//   - Handles time-based suffixes for temporal data
//   - Optimizes for different query patterns per format
//
// 4. Temporal Partitioning (TrapFlapHistory):
//   - Removes time-based suffixes for grouping
//   - Enables efficient time-range queries
//   - Balances temporal and entity-based access patterns
//
// 5. Tick-Based Partitioning (Default):
//   - Uses first 9 bytes (tick timestamp)
//   - Groups data by time periods
//   - Optimal for time-series data access patterns
//
// Parameters:
//   - dataStoreType: Store type determining partitioning strategy
//   - bytes: Full key bytes to extract partition key from
//   - storeName: Store name for format detection
//   - tokenizer: Tokenizer for string parsing operations
//
// Returns:
//   - []byte: Partition key for hash-based partition selection
func GetPartitionKey(dataStoreType DatastoreType, bytes []byte, storeName string, tokenizer *Tokenizer) []byte {

	// Strategy 1: Full key partitioning for maximum distribution
	if dataStoreType == StaticMetric || IsIndexStore(dataStoreType) {

		return bytes
	} else if dataStoreType == PerformanceMetric || dataStoreType == ObjectStatusMetric ||
		dataStoreType == ObjectStatusFlapMetric || dataStoreType == NetRouteMetric || dataStoreType == NetRouteStatusMetric {

		// Strategy 2: Prefix partitioning - use entity/device identifier
		// Split by KeySeparator and use first component for entity-based grouping
		return bytes2.Split(bytes, []byte(KeySeparator))[0]

	} else if dataStoreType == EventPolicy {

		// Strategy 3: Format-aware partitioning based on store format
		Split(storeName, HyphenSeparator, tokenizer)

		if strings.EqualFold(tokenizer.Tokens[1], VerticalFormat) {

			// Vertical format: Remove last component and handle time suffixes
			bytes = bytes[:bytes2.LastIndexByte(bytes, KeySeparatorByte)] //part bytes

			// Remove metric time suffix if present for temporal grouping
			if bytes2.HasSuffix(bytes, MetricTimeKeySuffixBytes) {

				return bytes[:len(bytes)-len(MetricTimeKeySuffixBytes)]
			}

			return bytes
		} else {

			// Horizontal format: Use tick-based partitioning (first 9 bytes)
			return bytes[:9]
		}

	} else if dataStoreType == TrapFlapHistory {

		// Strategy 4: Temporal partitioning with time suffix handling
		bytes = bytes[:bytes2.LastIndexByte(bytes, KeySeparatorByte)] //part bytes

		// Remove metric time suffix for temporal grouping
		if bytes2.HasSuffix(bytes, MetricTimeKeySuffixBytes) {

			return bytes[:len(bytes)-len(MetricTimeKeySuffixBytes)]
		}

		return bytes
	}

	// Strategy 5: Default tick-based partitioning for time-series data
	// Use first 9 bytes which typically contain the tick timestamp
	return bytes[:9] // means our tick occupies 9 bytes at most...

}

// OpenOrCreateStore opens an existing store or creates a new one with the specified configuration.
//
// This function implements the complete store initialization process:
// 1. Validates input parameters and store type
// 2. Creates or loads store directory and metadata
// 3. Initializes file locking for exclusive access
// 4. Sets up mapping structures for ordinal management
// 5. Opens or recovers partition data
// 6. Performs version migration and repair operations
// 7. Loads caches and optimizes for expected workload
//
// Store Creation Process:
// - Creates directory structure with proper permissions
// - Initializes metadata file with version and configuration
// - Sets up partition structure based on store type
// - Configures mapping ordinals and cache parameters
//
// Store Opening Process:
// - Validates existing metadata and version compatibility
// - Performs recovery operations if needed
// - Loads existing mappings and partition data
// - Rebuilds indices and caches for optimal performance
//
// Error Recovery:
// - Detects corrupted files and initiates recovery procedures
// - Rebuilds mappings from backup files when necessary
// - Repairs partition data and indices
// - Handles version migration for backward compatibility
//
// Parameters:
//   - name: Unique identifier for the store
//   - dataStoreType: Type determining store behavior and optimizations
//   - encoder: Encoder instance for data serialization operations
//   - tokenizer: Tokenizer for key parsing and partitioning
//   - repair: Whether to attempt repair of corrupted partitions
//
// Returns:
//   - *Store: Initialized store instance ready for operations
//   - error: Any error encountered during initialization
//
// Error handling:
// - Validates all input parameters before processing
// - Cleans up partially created resources on failure
// - Provides detailed error messages for debugging
// - Handles concurrent access attempts gracefully
func OpenOrCreateStore(name string, dataStoreType DatastoreType, encoder codec.Encoder, tokenizer *Tokenizer, repair bool) (*Store, error) {

	// Validate store name is provided
	if len(name) == 0 {

		return nil, errors.New("store name can not be blank")
	}

	// Start timing the store opening operation for performance monitoring
	t := time.Now()

	// Log event trace for store opening
	storeLogger.Info(fmt.Sprintf("opening store %v", name))

	// Validate store type is within acceptable range
	if dataStoreType < Index || dataStoreType > NetRouteStatusMetricAggregation {

		return nil, errors.New("invalid store type")
	}

	// Construct store directory path within the datastore root
	storeDir := CurrentDir + PathSeparator + DatastoreDir + PathSeparator + name

	// Check if store directory already exists
	_, err := os.Stat(storeDir)

	// Initialize metadata map with reasonable initial capacity
	metadata := make(MotadataMap, 10)

	create := false

	// Store Creation Path: Initialize new store with default configuration
	if os.IsNotExist(err) {

		create = true

		// Create store directory with appropriate permissions
		// 0755 allows owner full access, group/others read+execute
		err = os.MkdirAll(storeDir, 0755)

		if err != nil {

			return nil, err
		}

		// Initialize metadata with essential store configuration
		metadata["datastore.type"] = dataStoreType

		metadata["pool.length"] = MaxPoolLength

		metadata[Version] = StoreVariant

		// Special configuration for MetricAggregation stores
		// Mark multipart keys as repaired to skip legacy repair operations
		if dataStoreType == MetricAggregation {

			metadata[MultipartRepaired] = Yes

		}

		// Serialize metadata to JSON with proper formatting
		bytes, _ := json.MarshalIndent(metadata, "", "  ")

		// Write metadata file with full permissions for configuration access
		err = os.WriteFile(storeDir+PathSeparator+MetadataFile, bytes, 0777)

		if err != nil {

			return nil, errors.New(fmt.Sprintf(ErrorFailedMetadataFile, name, err.Error()))
		}

	} else {

		updated := false

		if _, err = os.Stat(storeDir + PathSeparator + TempPatch + MetadataFile); err == nil {

			updated = true

			bytes, _ := os.ReadFile(storeDir + PathSeparator + TempPatch + MetadataFile)

			if err = json.Unmarshal(bytes, &metadata); err != nil {

				updated = false

				_ = os.Remove(storeDir + PathSeparator + TempPatch + MetadataFile)

			} else {

				_ = os.Rename(storeDir+PathSeparator+TempPatch+MetadataFile, storeDir+PathSeparator+MetadataFile)
			}
		}

		if !updated {

			bytes, err := os.ReadFile(storeDir + PathSeparator + MetadataFile)

			if err != nil || len(bytes) == 0 {

				return nil, errors.New(fmt.Sprintf("failed to open the store %v, reason: metadata file does not exist.", name))
			}

			err = json.Unmarshal(bytes, &metadata)

			if err != nil {

				return nil, errors.New(fmt.Sprintf("failed to open the store %v, reason: %s", name, err.Error()))
			}
		}

		dataStoreType = DatastoreType(metadata.GetIntValue("datastore.type"))

		if dataStoreType != Mapping && dataStoreType != StaticMetric {

			poolLength := metadata.GetIntValue("pool.length")

			if MaxPoolLength < poolLength {

				err := fmt.Sprintf("failed to open the store %v, reason: store pool length %v is less than system pool length %v", name, poolLength, MaxPoolLength)

				stackTraceBytes := make([]byte, 1<<20)

				ShutdownNotifications <- "Store" + GroupSeparator + err + GroupSeparator + fmt.Sprintf("%v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

				return nil, errors.New(err)
			}
		}
	}

	lockFile, err := lockStore(name)

	if err != nil {

		if errors.Is(err, os.ErrExist) {

			//start recovery...

			return nil, errors.New(fmt.Sprintf("failed to open the store %v, reason: store is already open by some other process.", name))
		}

	}

	store := Store{}

	/*if this store is opened for the first time then the unix timestamp must not be zero
	* if cleanup job goroutine checks the store before any io method is called it will be closed and the method
	* might behave unexpectedly*/

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.storeType = dataStoreType

	store.metadata = metadata

	store.variant = StringToFloat64(store.metadata.GetStringValue(Version))

	store.cacheTTL = 90

	store.path = storeDir

	store.lockFile = lockFile

	store.name = name

	store.lock = &sync.RWMutex{}

	if dataStoreType == Mapping {

		store.currentStringMappingOrdinal = DummyStringMappingOrdinal + 2 // 1 is reserved for empty

		store.currentNumericMappingOrdinal = 0

	} else {

		// in case of change in the store partition we will open store with previous number of partitions exists in the store
		if !create {

			var dirs []os.DirEntry

			dirs, err = os.ReadDir(store.path)

			if err != nil {

				return nil, errors.New(fmt.Sprintf("failed to load store partitions, reason:%s", err))
			} else {

				parts := 0

				for _, dir := range dirs {

					if dir.IsDir() {

						parts++
					}
				}

				store.partitions = make([]*Partition, parts)
			}
		} else {

			store.partitions = make([]*Partition, getPartitionLength(dataStoreType))

		}

		store.parts = uint64(len(store.partitions))

		if err = store.loadMultipartKeys(); err != nil {

			return nil, errors.New(fmt.Sprintf("failed to load multipart keys, reason:%s", err))
		}
	}

	Split(store.name, HyphenSeparator, tokenizer)

	if strings.EqualFold(tokenizer.Tokens[1], VerticalFormat) && (dataStoreType == ObjectStatusMetric || strings.Contains(name, "availability")) {

		store.cacheTTL = 60
	}

	if dataStoreType == Mapping {

		//in case of abnormal shutdown during the mapping cleanupjob we need to clear the bkp file

		err = os.RemoveAll(store.path + PathSeparator + FileBKPTemp176)

		if err != nil {

			storeLogger.Error(fmt.Sprintf("error %v occurred while removing the string temp mapping backup file store : %s", store.name, err.Error()))

		}

		err = os.RemoveAll(store.path + PathSeparator + FileBKPTemp112)

		if err != nil {

			storeLogger.Error(fmt.Sprintf("error %v occurred while removing the numeric temp mapping backup file store : %s", store.name, err.Error()))

		}

		if _, err := os.Stat(store.path + PathSeparator + FileBKP112); err == nil {

			store.numericMappingBackupFile, err = os.OpenFile(store.path+PathSeparator+FileBKP112, os.O_RDWR|os.O_APPEND, 0666)

			if err != nil {

				return nil, errors.New(fmt.Sprintf("failed to open numeric mapping bkp file, reason: %v", err.Error()))
			}

		} else {

			store.numericMappingBackupFile, err = os.Create(store.path + PathSeparator + FileBKP112)

			if err != nil {

				return nil, errors.New(fmt.Sprintf("failed to create numeric mapping bkp file, reason: %v", err.Error()))
			}
		}

		store.numericMappingFile, err = os.OpenFile(storeDir+PathSeparator+FileMapping112, os.O_RDWR, 0666)

		if err != nil {

			if !errors.Is(err, ErrorFileNotFound) {

				storeLogger.Info(fmt.Sprintf("starting numeric mapping recovery for store: %v, reason: err: %v occurred while opening numeric mapping file", name, err.Error()))

				if err = rebuildNumericMapping(&store, encoder, Full); err != nil {

					return nil, errors.New(fmt.Sprintf(ErrorCreateStore, name, err))
				}

			}

		}

		if store.numericMappingFile != nil {

			store.numericMappingMMapBytes, err = Map(store.numericMappingFile, ReadWrite)

			if err != nil {

				_ = store.numericMappingFile.Close()

				storeLogger.Info(fmt.Sprintf("starting numeric mapping recovery for store: %v, reason: err: %v occurred while opening numeric mapping file", name, err.Error()))

				if err = rebuildNumericMapping(&store, encoder, Full); err != nil {

					return nil, errors.New(fmt.Sprintf(ErrorCreateStore, name, err))
				}
			}

			store.numericMapping, err = vellum.Load(store.numericMappingMMapBytes)

			if err != nil {

				_ = store.numericMappingMMapBytes.Unmap()

				_ = store.numericMappingFile.Close()

				storeLogger.Info(fmt.Sprintf("starting numeric mapping recovery for store: %v, reason: err: %v occurred while opening numeric mapping file", name, err.Error()))

				if err = rebuildNumericMapping(&store, encoder, Full); err != nil {

					return nil, errors.New(fmt.Sprintf(ErrorCreateStore, name, err))
				}
			}
		}

		_, err = os.Stat(storeDir + PathSeparator + "temp.112")

		if err == nil {

			if err = rebuildNumericMapping(&store, encoder, Partial); err != nil {

				storeLogger.Info(fmt.Sprintf("starting numeric mapping recovery for store: %v, reason: err: %v occurred while rebuilding temp numeric mapping file", name, err.Error()))

				if err = rebuildNumericMapping(&store, encoder, Full); err != nil {

					return nil, errors.New(fmt.Sprintf(ErrorCreateStore, name, err))
				}
			}
		}

		store.maxMappingCacheRecords = GetMappingCacheRecords(name)

		store.cacheNumericMappings = intmap.New[int32, int64](store.maxMappingCacheRecords)

		store.cacheStringMappings = swiss.NewMap[int32, string](uint32(store.maxMappingCacheRecords))

		store.availableStringMappingOrdinals = &bitmap.Bitmap{}

		store.availableNumericMappingOrdinals = &bitmap.Bitmap{}

		store.tempMappingOrdinals = &bitmap.Bitmap{}

		cleanup := false

		for column := range MappingCleanupColumns {

			if strings.EqualFold(column+HyphenSeparator+"mappings", store.name) {

				cleanup = true

				break
			}
		}

		if cleanup {

			numericOrdinals := &bitmap.Bitmap{}

			if store.numericMapping != nil {

				records := 0

				iterator, err := store.numericMapping.Iterator(nil, nil)

				for err == nil {

					bytes, ordinal := iterator.Current()

					if store.currentNumericMappingOrdinal < int32(ordinal) {

						store.currentNumericMappingOrdinal = int32(ordinal)
					}

					//load ordinals in cache while store reloads
					if records < store.maxMappingCacheRecords {

						store.cacheNumericMappings.Put(int32(ordinal), codec.ReadBigEndianINT64Value(bytes))

					}

					numericOrdinals.Set(uint32(ordinal))

					records++

					err = iterator.Next()
				}

				store.currentNumericMappingOrdinal = store.currentNumericMappingOrdinal + 2
			}

			for index := uint32(0); index < uint32(store.currentNumericMappingOrdinal); index += 2 {

				if !numericOrdinals.Contains(index) {

					store.availableNumericMappingOrdinals.Set(index)
				}

			}

		} else {

			if store.numericMapping != nil {

				records := 0

				iterator, err := store.numericMapping.Iterator(nil, nil)

				for err == nil {

					bytes, ordinal := iterator.Current()

					if store.currentNumericMappingOrdinal < int32(ordinal) {

						store.currentNumericMappingOrdinal = int32(ordinal)
					}

					//load ordinals in cache while store reloads
					if records < store.maxMappingCacheRecords {

						store.cacheNumericMappings.Put(int32(ordinal), codec.ReadBigEndianINT64Value(bytes))

					}

					records++

					err = iterator.Next()
				}

				store.currentNumericMappingOrdinal = store.currentNumericMappingOrdinal + 2
			}
		}

		if _, err := os.Stat(store.path + PathSeparator + FileBKP176); err == nil {

			store.stringMappingBackupFile, err = os.OpenFile(store.path+PathSeparator+FileBKP176, os.O_RDWR|os.O_APPEND, 0666)

			if err != nil {

				return nil, errors.New(fmt.Sprintf("failed to open string mapping bkp file, reason: %v", err.Error()))
			}

		} else {

			store.stringMappingBackupFile, err = os.Create(store.path + PathSeparator + FileBKP176)

			if err != nil {

				return nil, errors.New(fmt.Sprintf("failed to create string mapping bkp file, reason: %v", err.Error()))
			}
		}

		store.stringMappingFile, err = os.OpenFile(storeDir+PathSeparator+FileMapping176, os.O_RDWR, 0666)

		if err != nil {

			if !errors.Is(err, ErrorFileNotFound) {

				storeLogger.Info(fmt.Sprintf("starting string mapping recovery for store: %v, reason: err: %v occurred while opening string mapping file", name, err.Error()))

				if err = rebuildStringMapping(&store, encoder, Full); err != nil {

					return nil, errors.New(fmt.Sprintf(ErrorCreateStore, name, err))
				}
			}

		}

		if store.stringMappingFile != nil {

			store.stringMappingMMapBytes, err = Map(store.stringMappingFile, ReadWrite)

			if err != nil {

				_ = store.stringMappingFile.Close()

				storeLogger.Info(fmt.Sprintf("starting string mapping recovery for store: %v, reason: err: %v occurred while mmaping string mapping file", name, err.Error()))

				if err = rebuildStringMapping(&store, encoder, Full); err != nil {

					return nil, errors.New(fmt.Sprintf(ErrorCreateStore, name, err))
				}
			}

			store.stringMapping, err = vellum.Load(store.stringMappingMMapBytes)

			if err != nil {

				_ = store.stringMappingMMapBytes.Unmap()

				_ = store.stringMappingFile.Close()

				storeLogger.Info(fmt.Sprintf("starting string mapping recovery for store: %v, reason: err: %v occurred while loading string mapping file fst", name, err.Error()))

				if err = rebuildStringMapping(&store, encoder, Full); err != nil {

					return nil, errors.New(fmt.Sprintf(ErrorCreateStore, name, err))
				}
			}
		}

		_, err = os.Stat(storeDir + PathSeparator + FileTemp176)

		if err == nil {

			if err = rebuildStringMapping(&store, encoder, Partial); err != nil {

				storeLogger.Info(fmt.Sprintf("starting string mapping recovery for store: %v, reason: err: %v occurred while rebuilding temp string mapping file", name, err.Error()))

				if err = rebuildStringMapping(&store, encoder, Full); err != nil {

					return nil, errors.New(fmt.Sprintf(ErrorCreateStore, name, err))
				}
			}
		}

		if cleanup {

			stringOrdinals := &bitmap.Bitmap{}

			if store.stringMapping != nil {

				records := 0

				iterator, err := store.stringMapping.Iterator(nil, nil)

				for err == nil {

					value, ordinal := iterator.Current()

					if store.currentStringMappingOrdinal < int32(ordinal) {

						store.currentStringMappingOrdinal = int32(ordinal)
					}

					//load ordinals in cache while store reloads
					if records < store.maxMappingCacheRecords {

						store.cacheStringMappings.Put(int32(ordinal), string(value))

					}

					stringOrdinals.Set(uint32(ordinal))

					records++

					err = iterator.Next()
				}

				store.currentStringMappingOrdinal = store.currentStringMappingOrdinal + 2

			}

			for index := uint32(3); index < uint32(store.currentStringMappingOrdinal); index += 2 {

				if !stringOrdinals.Contains(index) {

					store.availableStringMappingOrdinals.Set(index)
				}

			}

		} else {

			if store.stringMapping != nil {

				records := 0

				iterator, err := store.stringMapping.Iterator(nil, nil)

				for err == nil {

					value, ordinal := iterator.Current()

					if store.currentStringMappingOrdinal < int32(ordinal) {

						store.currentStringMappingOrdinal = int32(ordinal)
					}

					//load ordinals in cache while store reloads
					if records < store.maxMappingCacheRecords {

						store.cacheStringMappings.Put(int32(ordinal), string(value))

					}

					records++

					err = iterator.Next()
				}

				store.currentStringMappingOrdinal = store.currentStringMappingOrdinal + 2

			}
		}

		if TraceEnabled() {

			bytes, _ := json.Marshal(store.GetMetrics())

			storeLogger.Trace(fmt.Sprintf("mapping store %v metrics %v", store.name, string(bytes)))
		}

		if store.variant < version2 && store.stringMapping != nil && store.stringMapping.Len() > 0 {

			bkpFile, err := os.OpenFile(storeDir+PathSeparator+FileBKPTemp176, os.O_CREATE|os.O_RDWR|os.O_APPEND, 0666)

			if err != nil {

				return nil, errors.New(fmt.Sprintf("failed to open temp bkp file reason %v", err.Error()))
			}

			store.ReconcileStringMappingsBackupFile(encoder, bkpFile)

			_ = store.stringMappingBackupFile.Close()

			_ = os.Rename(storeDir+PathSeparator+FileBKPTemp176, storeDir+PathSeparator+FileBKP176)

			_ = bkpFile.Close()

			_ = os.Remove(storeDir + PathSeparator + FileBKPTemp176)

			//Need to update the store variant and also put the new variant in the store metadata

			store.variant = StringToFloat64(StoreVariant)

			metadata[Version] = StoreVariant

			bytes, _ := json.MarshalIndent(metadata, "", "  ")

			err = os.WriteFile(storeDir+PathSeparator+MetadataFile, bytes, 0777)

			if err != nil {

				return nil, errors.New(fmt.Sprintf(ErrorFailedMetadataFile, name, err.Error()))
			}

			//reopen string file again
			if store.stringMappingBackupFile, err = os.OpenFile(storeDir+PathSeparator+FileBKP176, os.O_RDWR|os.O_APPEND, 0666); err != nil {

				mappingLogger.Error(fmt.Sprintf("failed to open bkp.176 file store : %s , reason : %s", store.name, err.Error()))
			}

		}

	} else {

		err = store.openPartitions(encoder, tokenizer, repair)

		if err != nil {

			if store.multipartKeyFile != nil {

				_ = store.multipartKeyFile.Close()

			}

			if store.lockFile != nil {

				_ = store.lockFile.Close()
			}

			return nil, errors.New(fmt.Sprintf("failed to open partition reason %s", err.Error()))
		}
	}

	if multipartRepair := store.GetDatastoreType() == MetricAggregation && store.metadata.GetStringValue(MultipartRepaired) != Yes; multipartRepair {

		store.metadata[MultipartRepaired] = Yes

		bytes, _ := json.MarshalIndent(store.metadata, "", "  ")

		err = os.WriteFile(storeDir+PathSeparator+MetadataFile, bytes, 0777)

		if err != nil {

			return nil, errors.New(fmt.Sprintf(ErrorFailedMetadataFile, name, err.Error()))
		}
	}

	store.storeBytes = int64(store.GetSize())

	store.puts = int64(store.Count())

	storeLogger.Info(fmt.Sprintf("store %v open took %v", name, time.Since(t)))

	return &store, nil

}

// GetName returns the unique identifier for this store instance.
//
// The store name is used for:
// - File system directory naming and organization
// - Logging and monitoring identification
// - Configuration and metadata management
// - Inter-process communication and coordination
//
// Returns:
//   - string: The unique store name
func (store *Store) GetName() string {

	return store.name
}

// lockStore creates an exclusive file lock to prevent concurrent store access.
//
// This function implements process-level mutual exclusion by:
// 1. Creating a lock file in the store directory
// 2. Acquiring an exclusive, non-blocking file lock
// 3. Maintaining the lock for the lifetime of the store instance
// 4. Preventing data corruption from concurrent access
//
// Lock Mechanism:
// - Uses POSIX file locking (flock) for cross-process coordination
// - Non-blocking lock acquisition prevents deadlocks
// - Exclusive lock ensures only one process can access the store
// - Lock is automatically released when the process exits
//
// Error Scenarios:
// - Lock file creation failure (permissions, disk space)
// - Lock acquisition failure (store already open)
// - File system errors or network storage issues
//
// Parameters:
//   - name: Store name for lock file path construction
//
// Returns:
//   - *os.File: Lock file handle (must be kept open)
//   - error: Any error encountered during lock acquisition
//
// CRITICAL: The returned file handle must remain open for the entire
// store lifetime to maintain the exclusive lock.
func lockStore(name string) (*os.File, error) {

	// Construct lock file path within store directory
	name = CurrentDir + PathSeparator + DatastoreDir + PathSeparator + name + PathSeparator + "lock"

	// Create or open lock file with read-write permissions
	// 0644 allows owner read-write, group/others read-only
	file, err := os.OpenFile(name, os.O_RDWR|os.O_CREATE, os.FileMode(0644))

	if err != nil {

		return nil, err
	}

	// Acquire exclusive, non-blocking file lock
	// LOCK_EX: Exclusive lock (no other process can acquire)
	// LOCK_NB: Non-blocking (fail immediately if lock unavailable)
	if err := Flock(int(file.Fd())); err != nil {

		return nil, err
	}

	return file, nil
}

// openPartitions initializes all partition instances for the store.
//
// This function implements comprehensive partition initialization by:
// 1. Opening existing partitions or creating new ones as needed
// 2. Handling partition corruption through repair mechanisms
// 3. Preserving WAL files during corruption recovery
// 4. Ensuring all partitions are properly initialized before use
// 5. Maintaining partition numbering consistency
//
// Partition Management:
// - Partitions are numbered starting from 1 (not 0) for user clarity
// - Each partition manages a subset of the store's keyspace
// - Partition count is determined by store type and expected load
// - Failed partitions can be repaired without affecting others
//
// Repair Process:
// - Detects corrupted partition files during opening
// - Preserves WAL files for transaction recovery
// - Removes corrupted data files while keeping transaction logs
// - Recreates partition structure from WAL replay
// - Continues with remaining partitions if repair fails
//
// Error Handling:
// - Non-repair mode: Fails fast on any partition error
// - Repair mode: Attempts recovery and continues operation
// - Logs detailed error information for debugging
// - Maintains store consistency even with partial failures
//
// Parameters:
//   - encoder: Encoder instance for partition data operations
//   - tokenizer: Tokenizer for key parsing and processing
//   - repair: Whether to attempt repair of corrupted partitions
//
// Returns:
//   - error: Any unrecoverable error encountered during initialization
func (store *Store) openPartitions(encoder codec.Encoder, tokenizer *Tokenizer, repair bool) error {

	// Mark store as active (not closed) for partition operations
	store.closed = false

	// Initialize each partition sequentially to ensure proper setup
	for partition := 0; partition < int(store.parts); partition++ {

		// Create new partition instance (partitions numbered from 1)
		storePartition, err := newPartition(store, partition+1, encoder, tokenizer)

		if err != nil {

			// Handle partition opening failure based on repair mode
			if !repair {

				return errors.New(fmt.Sprintf("failed to open the store %v, reason: error %v occurred while opening or creating a store partition %v", store.GetName(), err, partition))
			}

			// Repair mode: Attempt to recover corrupted partition
			storeLogger.Fatal(fmt.Sprintf("partition : %d is corrupted in store : %s  reason : %s", partition+1, store.name, err.Error()))

			// Read partition directory to identify files for cleanup
			if files, err := os.ReadDir(store.path + PathSeparator + codec.INTToStringValue(partition+1) + PathSeparator); err != nil {

				// Log error trace for directory read failure
				storeLogger.Error(fmt.Sprintf("failed to cleanup corrupted partition,partition : %d in store : %s  reason : %s", partition+1, store.name, err.Error()))

				return err

			} else {

				// Remove corrupted files while preserving WAL files for recovery
				for _, file := range files {

					// Preserve WAL files as they contain transaction data for recovery
					if !strings.HasSuffix(file.Name(), ".wal") {

						_ = os.Remove(store.path + PathSeparator + codec.INTToStringValue(partition+1) + PathSeparator + file.Name())
					}

				}

			}

			// Attempt to recreate partition after cleanup
			storePartition, err = newPartition(store, partition+1, encoder, tokenizer)

			if err != nil {

				// Log fatal error for partition recreation failure
				storeLogger.Fatal(fmt.Sprintf("failed to reopen partition, partition : %d in store : %s  reason : %s", partition+1, store.name, err.Error()))

				return err
			}

		}

		// Store successfully initialized partition in the array
		store.partitions[partition] = storePartition

	}

	return nil
}

/////////////////////////////////////////// Transaction Put Code //////////////////////////////////////////////

// GetPartition determines the target partition for a given key using consistent hashing.
//
// This function implements the core partitioning logic by:
// 1. Extracting the partition key based on store type and key structure
// 2. Computing a 64-bit hash of the partition key for distribution
// 3. Using modulo operation to map hash to partition index
// 4. Ensuring consistent routing for the same key across operations
//
// Partitioning Benefits:
// - Distributes load evenly across all partitions
// - Enables parallel processing of different key ranges
// - Reduces lock contention by isolating operations
// - Improves cache locality for related keys
// - Supports horizontal scaling through partition management
//
// Hash Distribution:
// - Uses 64-bit hash for excellent distribution properties
// - Modulo operation ensures uniform partition assignment
// - Consistent hashing maintains key-to-partition mapping
// - Handles partition count changes gracefully
//
// Parameters:
//   - keyBytes: Full key bytes for partition determination
//   - tokenizer: Tokenizer for key parsing operations
//
// Returns:
//   - int: Zero-based partition index for the key
func (store *Store) GetPartition(keyBytes []byte, tokenizer *Tokenizer) int {

	return int(GetHash64(GetPartitionKey(store.storeType, keyBytes, store.name, tokenizer)) % store.parts)

}

// CommitTxn commits a transaction containing multiple key-value operations to a specific partition.
//
// This function implements atomic transaction processing by:
// 1. Updating store access timestamp for LRU management
// 2. Marking store as dirty for sync operations
// 3. Delegating transaction commit to the target partition
// 4. Updating store-level metrics for monitoring
// 5. Tracking operation latency for performance analysis
//
// Transaction Guarantees:
// - Atomicity: All operations in the transaction succeed or fail together
// - Consistency: Store state remains valid after transaction completion
// - Isolation: Concurrent transactions don't interfere with each other
// - Durability: Committed data survives system failures through WAL
//
// Performance Tracking:
// - Updates store byte count for capacity monitoring
// - Increments operation counter for throughput analysis
// - Measures and accumulates latency for performance optimization
// - Provides metrics for system health monitoring
//
// Parameters:
//   - buffers: Serialized transaction data for persistence
//   - entries: Map of transaction entries with operation details
//   - encoder: Encoder instance for data serialization
//   - partition: Target partition index for the transaction
//
// Returns:
//   - error: Any error encountered during transaction commit
func (store *Store) CommitTxn(buffers []byte, entries map[uint64]TxnEntry, encoder codec.Encoder, partition int) error {

	// Update last used timestamp for LRU-based cleanup
	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	// Mark store as dirty to indicate uncommitted changes
	store.dirty = true

	// Start latency measurement for performance monitoring
	latency := time.Now().UnixNano()

	// Delegate transaction commit to the target partition
	err := store.partitions[partition].commitTxn(buffers, entries, encoder)

	// Update store metrics atomically for thread safety
	atomic.AddInt64(&store.storeBytes, int64(len(buffers)))

	atomic.AddInt64(&store.puts, 1)

	// Calculate and accumulate operation latency
	latency = time.Now().UnixNano() - latency

	atomic.AddUint64(&store.putLatency, uint64(latency))

	return err
}

/////////////////////////////////////////// Normal Put Code //////////////////////////////////////////////

// Put stores a single key-value pair in the appropriate partition of the store.
//
// This method is the primary way to write data to the store. It:
// 1. Determines the target partition using a hash of the key
// 2. Delegates the actual storage operation to the partition
// 3. Updates store metrics for monitoring and performance analysis
//
// Parameters:
//   - keyBytes: The key to store
//   - valueBytes: The value to associate with the key
//   - encoder: The encoder to use for any necessary encoding operations
//   - tokenizer: Used for key partitioning and processing
//
// Returns:
//   - error: Any error that occurred during the operation
func (store *Store) Put(keyBytes, valueBytes []byte, encoder codec.Encoder, tokenizer *Tokenizer) error {
	// Update the last used timestamp for LRU-based cleanup
	// This helps identify inactive stores for potential cleanup
	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	// Mark the store as dirty (modified since last sync)
	// This flag is used to determine if the store needs to be synced to disk
	store.dirty = true

	// Start timing the operation for performance metrics
	latency := time.Now().UnixNano()

	// Determine the target partition using a hash of the key
	// This distributes data evenly across partitions for better performance
	// The partitioning strategy is critical for load balancing and scalability
	partitionIndex := int(GetHash64(GetPartitionKey(store.storeType, keyBytes, store.name, tokenizer)) % store.parts)

	// Delegate the actual storage operation to the selected partition
	// The partition handles the details of storing the data on disk
	err := store.partitions[partitionIndex].put(keyBytes, valueBytes, encoder, true)

	// Update store size metrics (used for monitoring and capacity planning)
	// This is done atomically to ensure thread safety
	atomic.AddInt64(&store.storeBytes, int64(len(valueBytes)))

	// Increment the put operation counter (used for monitoring)
	atomic.AddInt64(&store.puts, 1)

	// Calculate and record the operation latency
	// This helps identify performance issues and trends
	latency = time.Now().UnixNano() - latency
	atomic.AddUint64(&store.putLatency, uint64(latency))

	return err
}

// PutMultiples stores multiple key-value pairs in a batch operation.
//
// This method is optimized for bulk insertions, reducing overhead by:
// 1. Acquiring partition locks only once for the entire batch
// 2. Reducing the number of disk operations
// 3. Amortizing the cost of partition selection and other operations
//
// Note: All keys must belong to the same partition for this optimization to work.
// The method uses the first key to determine the partition.
//
// Parameters:
//   - keyBuffers: Array of keys to store
//   - valueBuffers: Array of values corresponding to the keys
//   - encoder: The encoder to use for any necessary encoding operations
//   - tokenizer: Used for key partitioning and processing
//
// Returns:
//   - error: Any error that occurred during the operation
func (store *Store) PutMultiples(keyBuffers [][]byte, valueBuffers [][]byte, encoder codec.Encoder, tokenizer *Tokenizer) error {
	// Update the last used timestamp for LRU-based cleanup
	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	// Mark the store as dirty (modified since last sync)
	store.dirty = true

	// Start timing the operation for performance metrics
	latency := time.Now().UnixNano()

	// Determine the target partition using a hash of the first key
	// This assumes all keys in the batch belong to the same partition
	// This is a critical optimization for batch operations
	partitionIndex := int(GetHash64(GetPartitionKey(store.storeType, keyBuffers[0], store.name, tokenizer)) % store.parts)

	// Delegate the batch storage operation to the selected partition
	// The partition handles the details of storing multiple key-value pairs efficiently
	err := store.partitions[partitionIndex].putMultiples(keyBuffers, valueBuffers, encoder)

	// Calculate and record the operation latency
	latency = time.Now().UnixNano() - latency
	atomic.AddUint64(&store.putLatency, uint64(latency))

	// Update metrics for each value in the batch
	// This is done in a loop to account for each key-value pair individually
	for i := range valueBuffers {
		// Update store size metrics
		atomic.AddInt64(&store.storeBytes, int64(len(valueBuffers[i])))

		// Increment the put operation counter
		atomic.AddInt64(&store.puts, 1)
	}

	return err
}

// PutBlob stores a large binary object (blob) in the store.
//
// This method is specialized for handling large values that exceed
// the normal value size limits. It:
// 1. Stores the blob in a separate file
// 2. Returns an offset that can be used to retrieve the blob later
// 3. Updates store metrics to account for the large data size
//
// Parameters:
//   - keyBytes: The key to associate with the blob
//   - valueBytes: The blob data to store
//   - tokenizer: Used for key partitioning and processing
//
// Returns:
//   - int: The offset where the blob was stored (used for retrieval)
//   - error: Any error that occurred during the operation
func (store *Store) PutBlob(keyBytes, valueBytes []byte, tokenizer *Tokenizer) (int, error) {
	// Update the last used timestamp for LRU-based cleanup
	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	// Mark the store as dirty (modified since last sync)
	store.dirty = true

	// Start timing the operation for performance metrics
	latency := time.Now().UnixNano()

	// Determine the target partition using a hash of the key
	// The same partitioning strategy is used for blobs to ensure
	// consistent distribution across partitions
	partitionIndex := int(GetHash64(GetPartitionKey(store.storeType, keyBytes, store.name, tokenizer)) % store.parts)

	// Delegate the blob storage operation to the selected partition
	// The partition handles the details of storing the blob in a separate file
	// and returns an offset for later retrieval
	offset, err := store.partitions[partitionIndex].putBlob(valueBytes)

	// Update store size metrics
	// Blobs can be very large, so it's important to account for their size
	atomic.AddInt64(&store.storeBytes, int64(len(valueBytes)))

	// Increment the put operation counter
	atomic.AddInt64(&store.puts, 1)

	// Calculate and record the operation latency
	// Blob operations can be slower due to the larger data size
	latency = time.Now().UnixNano() - latency
	atomic.AddUint64(&store.putLatency, uint64(latency))

	return offset, err
}

/////////////////////////////////////////// GET Code //////////////////////////////////////////////

// GetMultiples retrieves multiple key-value pairs using parallel partition processing.
//
// This function implements high-performance batch retrieval by:
// 1. Distributing keys across partitions for parallel processing
// 2. Using worker pools to handle I/O operations asynchronously
// 3. Minimizing lock contention through partition-level isolation
// 4. Supporting WAL lookup for uncommitted data access
// 5. Providing detailed error reporting per key
//
// Performance Optimizations:
// - Parallel processing across multiple partitions
// - Asynchronous I/O through worker event system
// - Memory pool usage for efficient buffer management
// - Bitmap-based partition tracking for minimal overhead
// - Batch processing to amortize operation costs
//
// WAL Integration:
// - Optional WAL lookup for reading uncommitted transactions
// - Ensures read-after-write consistency within transactions
// - Handles concurrent read/write scenarios gracefully
//
// Parameters:
//   - keyBuffers: Array of keys to retrieve
//   - valueBuffers: Pre-allocated buffers for returned values
//   - encoder: Encoder instance for memory pool management
//   - events: Pre-allocated event structures for worker communication
//   - waitGroup: Synchronization primitive for parallel operations
//   - tokenizer: Tokenizer for key parsing and partitioning
//   - lookupWAL: Whether to include WAL data in search
//
// Returns:
//   - [][]byte: Array of retrieved values (nil for missing keys)
//   - []error: Array of errors per key (nil for successful retrievals)
//   - error: Overall operation error (rare, indicates system issues)
func (store *Store) GetMultiples(keyBuffers, valueBuffers [][]byte, encoder codec.Encoder, events []DiskIOEventBatch, waitGroup *sync.WaitGroup, tokenizer *Tokenizer, lookupWAL bool) ([][]byte, []error, error) {

	// Update last used timestamp for LRU-based cleanup
	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	// Start latency measurement for performance monitoring
	latency := time.Now().UnixNano()

	// Update read operation counter for monitoring
	atomic.AddInt64(&store.reads, int64(len(keyBuffers)))

	// Initialize error array for per-key error reporting
	errs := make([]error, len(valueBuffers))

	partition := 0

	// Track which partitions are involved in this batch operation
	partitions := bitmap.Bitmap{}

	// Initialize result buffer array
	buffers := make([][]byte, len(keyBuffers))

	// Clear checksum bytes in value buffers for clean state
	// This ensures consistent checksum validation across operations
	for j := 0; j < len(CheckSumV1Bytes); j++ {

		for i := range valueBuffers {

			valueBuffers[i][j] = 0
		}
	}

	for key := range keyBuffers {

		partition = int(GetHash64(GetPartitionKey(store.storeType, keyBuffers[key], store.name, tokenizer)) % store.parts)

		if !partitions.Contains(uint32(partition)) {

			partitions.Set(uint32(partition))

			events[partition].keyElementSize = 0

			events[partition].errs = errs

			events[partition].lookUpWAL = lookupWAL

			events[partition].partition = store.partitions[partition]

			events[partition].buffers = buffers

			events[partition].bytePoolIndex, _ = encoder.MemoryPool.AcquireBytePool(8)

			defer encoder.MemoryPool.ReleaseBytePool(events[partition].bytePoolIndex)

			events[partition].keyBuffers = keyBuffers

			events[partition].valueBuffers = valueBuffers

			events[partition].memoryPool = encoder.MemoryPool

			events[partition].positionPoolIndex, _ = encoder.MemoryPool.AcquireINTPool(MaxWorkerEventKeyGroupLength)

			defer encoder.MemoryPool.ReleaseINTPool(events[partition].positionPoolIndex)

			waitGroup.Add(1)
		}

		encoder.MemoryPool.GetINTPool(events[partition].positionPoolIndex)[events[partition].keyElementSize] = key

		events[partition].keyElementSize++
	}

	partitions.Range(func(partition uint32) {

		events[partition].waitGroup = waitGroup

		diskIOBatchEvents <- &events[partition]
	})

	waitGroup.Wait()

	latency = time.Now().UnixNano() - latency

	atomic.AddUint64(&store.readLatency, uint64(latency))

	return buffers, errs, nil
}

func (store *Store) GetCacheMultiples(keyBuffers, valueBuffers [][]byte, encoder codec.Encoder, events []DiskIOEventBatch, waitGroup *sync.WaitGroup, tokenizer *Tokenizer) ([][]byte, []error, error) {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	var err error

	latency := time.Now().UnixNano()

	atomic.AddInt64(&store.reads, int64(len(keyBuffers)))

	errs := make([]error, len(valueBuffers))

	buffers := make([][]byte, len(keyBuffers))

	misses := make([]int, 0, len(keyBuffers))

	for j := 0; j < len(CheckSumV1Bytes); j++ {

		for i := range valueBuffers {

			valueBuffers[i][j] = 0
		}
	}

	for key := range keyBuffers {

		if buffers[key], err = cache.Get([]byte(store.name+KeySeparator+string(keyBuffers[key])), valueBuffers[key]); len(buffers[key]) > 0 && err == nil {

			continue
		}

		misses = append(misses, key)
	}

	err = nil

	if len(misses) > 0 {

		partition := 0

		partitions := bitmap.Bitmap{}

		for _, key := range misses {

			partition = int(GetHash64(GetPartitionKey(store.storeType, keyBuffers[key], store.name, tokenizer)) % store.parts)

			if !partitions.Contains(uint32(partition)) {

				partitions.Set(uint32(partition))

				events[partition].keyElementSize = 0

				events[partition].errs = errs

				events[partition].lookUpWAL = false

				events[partition].partition = store.partitions[partition]

				events[partition].buffers = buffers

				events[partition].bytePoolIndex, _ = encoder.MemoryPool.AcquireBytePool(8)

				defer encoder.MemoryPool.ReleaseBytePool(events[partition].bytePoolIndex)

				events[partition].keyBuffers = keyBuffers

				events[partition].valueBuffers = valueBuffers

				events[partition].memoryPool = encoder.MemoryPool

				events[partition].positionPoolIndex, _ = encoder.MemoryPool.AcquireINTPool(MaxWorkerEventKeyGroupLength)

				defer encoder.MemoryPool.ReleaseINTPool(events[partition].positionPoolIndex)

				waitGroup.Add(1)
			}

			encoder.MemoryPool.GetINTPool(events[partition].positionPoolIndex)[events[partition].keyElementSize] = key

			events[partition].keyElementSize++
		}

		partitions.Range(func(partition uint32) {

			events[partition].waitGroup = waitGroup

			diskIOBatchEvents <- &events[partition]
		})

		waitGroup.Wait()

		for _, key := range misses {

			if buffers[key] != nil && errs[key] == nil {

				if err = cache.PutTTL([]byte(store.name+KeySeparator+string(keyBuffers[key])), buffers[key], store.cacheTTL); err != nil {

					storeLogger.Error(fmt.Sprintf(ErrorWriteCache, store.name+KeySeparator+string(keyBuffers[key]), err))

					err = nil
				}
			}
		}
	}

	latency = time.Now().UnixNano() - latency

	atomic.AddUint64(&store.readLatency, uint64(latency))

	return buffers, errs, err
}

// Has checks if a key exists in the store without retrieving its value.
//
// This function provides efficient key existence checking by:
// 1. Determining the target partition using consistent hashing
// 2. Delegating the existence check to the appropriate partition
// 3. Avoiding value retrieval for better performance
// 4. Supporting both persistent and WAL data sources
//
// Performance Benefits:
// - No value copying or memory allocation for large values
// - Faster than Get operations when only existence matters
// - Useful for conditional operations and data validation
// - Supports efficient duplicate detection workflows
//
// Use Cases:
// - Conditional inserts (insert only if key doesn't exist)
// - Data validation and integrity checks
// - Duplicate detection in data processing pipelines
// - Cache warming and preloading decisions
//
// Parameters:
//   - keyBytes: Key to check for existence
//   - tokenizer: Tokenizer for key parsing and partitioning
//
// Returns:
//   - bool: true if key exists, false otherwise
//   - error: Any error encountered during the check
func (store *Store) Has(keyBytes []byte, tokenizer *Tokenizer) (bool, error) {

	return store.partitions[int(GetHash64(GetPartitionKey(store.storeType, keyBytes, store.name, tokenizer))%store.parts)].has(keyBytes)
}

func (store *Store) Get(keyBytes, valueBytes []byte, encoder codec.Encoder, event DiskIOEvent, waitGroup *sync.WaitGroup, tokenizer *Tokenizer, lookupWAL bool) (bool, []byte, error) {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	latency := time.Now().UnixNano()

	atomic.AddInt64(&store.reads, 1)

	var err error

	for i := 0; i < len(CheckSumV1Bytes); i++ {

		valueBytes[i] = 0
	}

	partition := int(GetHash64(GetPartitionKey(store.storeType, keyBytes, store.name, tokenizer)) % store.parts)

	event.err = err

	event.lookUpWAL = lookupWAL

	event.partition = store.partitions[partition]

	event.keyBytes = keyBytes

	event.valueBytes = valueBytes

	event.memoryPool = encoder.MemoryPool

	event.waitGroup = waitGroup

	event.blob = false

	waitGroup.Add(1)

	DiskIOEvents <- &event

	waitGroup.Wait()

	latency = time.Now().UnixNano() - latency

	atomic.AddUint64(&store.readLatency, uint64(latency))

	return event.err == nil && len(event.bufferBytes) > 0, event.bufferBytes, event.err

}

func (store *Store) GetBlob(keyBytes, valueBytes []byte, offset int, tokenizer *Tokenizer, event DiskIOEvent, waitGroup *sync.WaitGroup) ([]byte, error) {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	latency := time.Now().UnixNano()

	atomic.AddInt64(&store.reads, 1)

	valueBytes, err := store.partitions[int(GetHash64(GetPartitionKey(store.storeType, keyBytes, store.name, tokenizer))%store.parts)].getBlob(valueBytes, offset, event, waitGroup)

	latency = time.Now().UnixNano() - latency

	atomic.AddUint64(&store.readLatency, uint64(latency))

	return valueBytes, err

}

/////////////////////////////////////////// Index Scanning Code //////////////////////////////////////////////

// GetGreaterThanKeys retrieves all keys with values greater than the specified threshold.
//
// This function implements efficient range query processing by:
// 1. Scanning all partitions for matching keys
// 2. Using partition-level indices for optimized lookups
// 3. Supporting inclusive/exclusive range boundaries
// 4. Aggregating results across all partitions
// 5. Providing consistent ordering and deduplication
//
// Range Query Optimization:
// - Leverages FST indices for efficient range scanning
// - Supports both inclusive and exclusive boundaries
// - Handles numeric comparisons with proper ordering
// - Minimizes memory allocation through result aggregation
//
// Use Cases:
// - Time-based queries (keys after timestamp)
// - Numeric range filtering for metrics
// - Data archival and cleanup operations
// - Analytics and reporting queries
//
// Parameters:
//   - value: Threshold value for comparison
//   - inclusive: Whether to include keys equal to the threshold
//   - excluded: Whether to exclude certain key patterns
//
// Returns:
//   - [][]byte: Array of keys matching the criteria
//   - error: Any error encountered during scanning
func (store *Store) GetGreaterThanKeys(value uint64, inclusive, excluded bool) ([][]byte, error) {

	// Update last used timestamp for LRU-based cleanup
	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	// Initialize result buffer with reasonable initial capacity
	buffers := make([][]byte, 0)

	// Scan all partitions for matching keys
	for partition := range store.partitions {

		// Delegate range query to partition-level implementation
		byteBuffers, err := store.partitions[partition].getGreaterThanKeys(value, inclusive, excluded)

		if err != nil {

			return nil, err
		}

		// Aggregate non-empty results from partition
		if byteBuffers != nil && len(byteBuffers) > 0 {

			buffers = append(buffers, byteBuffers...)
		}
	}

	return buffers, nil
}

// GetPrefixKeys retrieves all keys that start with the specified prefix.
//
// This function implements efficient prefix matching by:
// 1. Scanning all partitions for keys with matching prefixes
// 2. Using FST indices for optimized prefix traversal
// 3. Supporting inclusion/exclusion filtering
// 4. Aggregating results across partitions
// 5. Maintaining lexicographic ordering
//
// Prefix Query Optimization:
// - Leverages FST prefix iteration capabilities
// - Avoids full key scanning through index optimization
// - Supports pattern-based filtering for complex queries
// - Minimizes memory usage through streaming results
//
// Use Cases:
// - Hierarchical key queries (e.g., all metrics for a device)
// - Namespace-based data retrieval
// - Auto-completion and suggestion systems
// - Data organization and categorization
//
// Parameters:
//   - bytes: Prefix pattern to match against keys
//   - exclude: Whether to exclude keys matching the prefix
//
// Returns:
//   - [][]byte: Array of keys matching the prefix criteria
//   - error: Any error encountered during scanning
func (store *Store) GetPrefixKeys(bytes []byte, exclude bool) ([][]byte, error) {

	// Update last used timestamp for LRU-based cleanup
	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	// Initialize result buffer with reasonable initial capacity
	buffers := make([][]byte, 0)

	// Scan all partitions for prefix-matching keys
	for partition := range store.partitions {

		// Delegate prefix query to partition-level implementation
		byteBuffers, err := store.partitions[partition].getPrefixKeys(bytes, exclude)

		if err != nil {

			return nil, err
		}

		// Aggregate non-empty results from partition
		if byteBuffers != nil && len(byteBuffers) > 0 {

			buffers = append(buffers, byteBuffers...)
		}
	}

	return buffers, nil
}

// GetSuffixKeys retrieves all keys that end with the specified suffix.
//
// This function implements efficient suffix matching by:
// 1. Scanning all partitions for keys with matching suffixes
// 2. Using partition-level indices for optimized traversal
// 3. Supporting inclusion/exclusion filtering
// 4. Aggregating results across partitions
// 5. Handling reverse pattern matching efficiently
//
// Suffix Query Implementation:
// - Performs full key scanning with suffix comparison
// - Optimizes through partition-level parallelization
// - Supports pattern-based filtering for complex queries
// - Maintains consistent result ordering across partitions
//
// Use Cases:
// - File extension-based queries
// - Time-based suffix filtering (e.g., keys ending with timestamp)
// - Data type categorization by suffix patterns
// - Cleanup operations based on key patterns
//
// Performance Considerations:
// - Suffix matching requires more computation than prefix matching
// - Results are aggregated across all partitions
// - Memory usage scales with result set size
// - Consider using prefix queries when possible for better performance
//
// Parameters:
//   - bytes: Suffix pattern to match against keys
//   - exclude: Whether to exclude keys matching the suffix
//
// Returns:
//   - [][]byte: Array of keys matching the suffix criteria
//   - error: Any error encountered during scanning
func (store *Store) GetSuffixKeys(bytes []byte, exclude bool) ([][]byte, error) {

	// Update last used timestamp for LRU-based cleanup
	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	// Initialize result buffer with reasonable initial capacity
	buffers := make([][]byte, 0)

	// Scan all partitions for suffix-matching keys
	for partition := range store.partitions {

		// Delegate suffix query to partition-level implementation
		byteBuffers, err := store.partitions[partition].getSuffixKeys(bytes, exclude)

		if err != nil {

			return nil, err
		}

		// Aggregate non-empty results from partition
		if byteBuffers != nil && len(byteBuffers) > 0 {

			buffers = append(buffers, byteBuffers...)
		}
	}

	return buffers, nil
}

// GetKeys
// 1. for include filter string values and numeric values is always nil as it's not required as we have to return all the keys
// 2. for exclude filter we have to return keys that string values and numeric values not contains
func (store *Store) GetKeys(stringValues *swiss.Map[string, int32], numericValues *swiss.Map[int64, int32], excluded bool, dataType codec.DataType) ([][]byte, error) {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	keyBuffers := make([][]byte, 0)

	for partition := range store.partitions {

		buffers, err := store.partitions[partition].listKeys(stringValues, numericValues, excluded, dataType)

		if err != nil {

			return nil, err
		}

		if buffers != nil && len(buffers) > 0 {

			keyBuffers = append(keyBuffers, buffers...)
		}
	}

	return keyBuffers, nil
}

func (store *Store) GetContainKeys(bytes []byte, exclude bool) ([][]byte, error) {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	keyBuffers := make([][]byte, 0)

	for partition := range store.partitions {

		buffers, err := store.partitions[partition].getContainKeys(bytes, exclude)

		if err != nil {

			return nil, err
		}

		if buffers != nil && len(buffers) > 0 {

			keyBuffers = append(keyBuffers, buffers...)
		}
	}

	return keyBuffers, nil
}

func (store *Store) GetLessThanKeys(value uint64, inclusive, excluded bool) ([][]byte, error) {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	buffers := make([][]byte, 0)

	for partition := range store.partitions {

		byteBuffers, err := store.partitions[partition].getLessThanKeys(value, inclusive, excluded)

		if err != nil {

			return nil, err
		}

		if byteBuffers != nil && len(byteBuffers) > 0 {

			buffers = append(buffers, byteBuffers...)
		}
	}

	return buffers, nil
}

/////////////////////////////////////////// Store Metrics Code //////////////////////////////////////////////

//func (store *Store) GetAverageValueSize() int64 {
//
//	if store.closed || store.puts == 0 {
//
//		return 0
//	}
//
//	return atomic.LoadInt64(&store.storeBytes) / atomic.LoadInt64(&store.puts)
//}

func (store *Store) Count() int {

	if store.closed {

		return -1
	}

	count := 0

	if store.storeType == Mapping {

		store.lock.RLock()

		defer store.lock.RUnlock()

		if store.tempStringMappings != nil {

			count += store.tempStringMappings.Count()
		}

		if store.tempNumericMappings != nil {

			count += store.tempNumericMappings.Len()
		}

		if store.stringMapping != nil {

			count += store.stringMapping.Len()
		}

		if store.numericMapping != nil {

			count += store.numericMapping.Len()
		}
	} else {

		for partition := range store.partitions {

			count += store.partitions[partition].count()
		}
	}

	return count
}

func (store *Store) GetSize() int {

	if store.closed {

		return -1
	}

	size := 0

	if store.storeType == Mapping {

		store.lock.RLock()

		defer store.lock.RUnlock()

		size += store.stringMappingSize

		size += store.numericMappingSize

		if store.stringMapping != nil {

			stats, _ := os.Stat(store.path + PathSeparator + FileMapping176)

			size += int(stats.Size())
		}

		if store.numericMapping != nil {

			stats, _ := os.Stat(store.path + PathSeparator + FileMapping112)

			size += int(stats.Size())
		}

	} else {

		for partition := range store.partitions {

			size += store.partitions[partition].getSize()
		}

	}

	return size
}

func (store *Store) GetMetrics() map[string]int64 {

	if store.closed {

		return nil
	}

	metrics := map[string]int64{}

	metrics[Reads] = atomic.LoadInt64(&store.reads)

	metrics[Puts] = store.puts

	if metrics[Reads] > 0 {

		metrics[ReadLatencyNanos] = int64(atomic.LoadUint64(&store.readLatency) / uint64(metrics[Reads]))
	}

	if metrics[Puts] > 0 {

		metrics[PutLatencyNanos] = int64(store.putLatency / uint64(metrics[Puts]))
	}

	metrics[Updates] = 0

	metrics[Writes] = 0

	metrics[Moves] = 0

	metrics[MemoryIndexEntries] = 0

	metrics[IndexEntries] = 0

	metrics[SegmentFiles] = 0

	metrics[DataSizeBytes] = 0

	metrics[IndexSizeBytes] = 0

	metrics[MappingCacheMaxRecords] = 0

	metrics[CacheStringMappings] = 0

	metrics[CacheNumericMappings] = 0

	if store.storeType == Mapping {

		store.lock.RLock()

		defer store.lock.RUnlock()

		size := 0

		size += store.stringMappingSize

		size += store.numericMappingSize

		entries := 0

		if store.stringMapping != nil {

			stats, _ := os.Stat(store.path + PathSeparator + FileMapping176)

			entries += store.stringMapping.Len()

			size += int(stats.Size())
		}

		if store.numericMapping != nil {

			stats, _ := os.Stat(store.path + PathSeparator + FileMapping112)

			entries += store.numericMapping.Len()

			size += int(stats.Size())
		}

		if store.tempStringMappings != nil {

			entries += store.tempStringMappings.Count()
		}

		if store.tempNumericMappings != nil {

			entries += store.tempNumericMappings.Len()
		}

		metrics[IndexEntries] = int64(entries)

		metrics[DataSizeBytes] = int64(size)

		metrics[MappingCacheMaxRecords] = int64(store.maxMappingCacheRecords)

		if store.cacheStringMappings != nil && store.cacheStringMappings.Count() > 0 {

			metrics[CacheStringMappings] += int64(store.cacheStringMappings.Count())
		}

		if store.cacheNumericMappings != nil && store.cacheNumericMappings.Len() > 0 {

			metrics[CacheNumericMappings] += int64(store.cacheNumericMappings.Len())
		}

	} else {

		for partition := range store.partitions {

			partitionMetrics := store.partitions[partition].getMetrics()

			metrics[Updates] += partitionMetrics[Updates]

			metrics[Writes] += partitionMetrics[Writes]

			metrics[Moves] += partitionMetrics[Moves]

			metrics[MemoryIndexEntries] += partitionMetrics[MemoryIndexEntries]

			metrics[IndexEntries] += partitionMetrics[IndexEntries]

			metrics[SegmentFiles] += partitionMetrics[SegmentFiles]

			metrics[DataSizeBytes] += partitionMetrics[DataSizeBytes]

			metrics[IndexSizeBytes] += partitionMetrics[IndexSizeBytes]
		}
	}

	return metrics
}

/////////////////////////////////////////// Cleanup Code //////////////////////////////////////////////

func (store *Store) Cleanup(encoder codec.Encoder, keyBuffers [][]byte, valueBuffers [][]byte, batchEvent DiskIOEventBatch, event DiskIOEvent, waitGroup *sync.WaitGroup) {

	if !store.closed {

		for partition := range store.partitions {

			//to avoid store from recompacting
			if store.partitions[partition].lastWriteOpsTimestampSeconds.Load() == 0 || len(store.partitions[partition].segmentFiles) == 0 {

				continue
			}

			if time.Now().Unix()-store.partitions[partition].lastWriteOpsTimestampSeconds.Load() < CleanupThresholdSeconds {

				continue
			}

			dirty := store.partitions[partition].unmapActiveSegments()

			if !dirty { // possible chance if motadatadatastore restarts and after partition is not dirty

				_ = filepath.WalkDir(store.partitions[partition].path, func(name string, entry fs.DirEntry, err error) error {

					if err != nil {

						return err
					}

					if filepath.Ext(entry.Name()) == ".segment" || filepath.Ext(entry.Name()) == ".wal" {

						dirty = true
					}

					return nil
				})
			}

			if dirty {

				err := store.partitions[partition].compact(encoder, keyBuffers, valueBuffers, batchEvent, event, waitGroup)

				if err != nil {

					if !strings.Contains(err.Error(), "dirty") {

						storeLogger.Error(fmt.Sprintf("error occurred while performing compaction for partition -%v , error -%v ", store.partitions[partition].name, err.Error()))
					}

					_ = os.Remove(store.partitions[partition].path + PathSeparator + Temp255)

					_ = os.Remove(store.partitions[partition].path + PathSeparator + Idx255)

				}
			}

		}

		if time.Now().Unix()-store.lastUsedTimestampSeconds.Load() >= IdleStoreDetectionThresholdSeconds {

			store.Close(encoder)
		}
	}
}

func (store *Store) Delete(keyBytes []byte, encoder codec.Encoder, tokenizer *Tokenizer) {

	store.dirty = true

	cache.Delete([]byte(store.name + KeySeparator + string(keyBytes)))

	err := store.partitions[int(GetHash64(GetPartitionKey(store.storeType, keyBytes, store.name, tokenizer))%store.parts)].delete(keyBytes, encoder)

	if err != nil {

		storeLogger.Error(fmt.Sprintf("error occurrred while deleting key - %v from store - %v", string(keyBytes), store.name))
	}

	return
}

func (store *Store) Close(encoder codec.Encoder) {

	if store.closed {

		return

	}

	if store.storeType != Mapping {

		for partition := range store.partitions {

			store.partitions[partition].close(encoder)
		}
	}

	store.lock.Lock()

	defer store.lock.Unlock()

	if store.storeType == Mapping {

		mergeNumericMapping(store, encoder, Normal)

		mergeStringMapping(store, encoder, Normal)

		if store.numericMappingBackupFile != nil {

			_ = store.numericMappingBackupFile.Close()

			store.numericMappingBackupFile = nil

		}

		if store.stringMappingBackupFile != nil {

			_ = store.stringMappingBackupFile.Close()

			store.stringMappingBackupFile = nil

		}

		if store.stringMapping != nil {

			_ = store.stringMappingMMapBytes.Unmap()

			_ = store.stringMappingFile.Close()

			_ = store.stringMapping.Close()
		}

		if store.cacheStringMappings != nil {

			store.cacheStringMappings.Clear()
		}

		if store.numericMapping != nil {

			_ = store.numericMappingMMapBytes.Unmap()

			_ = store.numericMappingFile.Close()

			_ = store.numericMapping.Close()
		}

		if store.cacheNumericMappings != nil {

			store.cacheNumericMappings.Clear()
		}

		store.stringMapping = nil

		store.numericMapping = nil

		store.cacheStringMappings = nil

		store.cacheNumericMappings = nil

		store.availableNumericMappingOrdinals.Clear()

		store.availableNumericMappingOrdinals = nil

		store.availableStringMappingOrdinals.Clear()

		store.availableStringMappingOrdinals = nil

		store.tempMappingOrdinals.Clear()

		store.tempMappingOrdinals = nil

	} else {

		store.containMultipartKeys.Store(false)

		store.multipartKeys = nil

		_ = store.multipartKeyFile.Close()

		store.multipartKeyFile = nil
	}

	store.metadata = nil

	if store.lockFile != nil {

		_ = store.lockFile.Close()

		_ = os.Remove(store.lockFile.Name())
	}

	store.closed = true

	store.dirty = false

	if StoreSyncJobRemoveNotifications != nil {

		StoreSyncJobRemoveNotifications <- store.GetName()
	}

	storeLogger.Info(fmt.Sprintf("store %v closed", store.name))
}

func (store *Store) Sync(encoder codec.Encoder) error {

	if store.dirty {

		if store.storeType != Mapping {

			for partition := range store.partitions {

				if store.partitions[partition].dirty {

					err := store.partitions[partition].sync(encoder, true)

					if err != nil {

						storeLogger.Error(fmt.Sprintf("error %v occurred while syncing partition %v", err, store.partitions[partition].name))
					}

				}

			}

		} else {

			store.lock.Lock()

			mergeNumericMapping(store, encoder, Normal)

			mergeStringMapping(store, encoder, Normal)

			store.lock.Unlock()

			if TraceEnabled() {

				bytes, _ := json.Marshal(store.GetMetrics())

				storeLogger.Trace(fmt.Sprintf("mapping store %v metrics %v", store.name, string(bytes)))
			}
		}

		store.dirty = false
	}

	return nil

}

/////////////////////////////////////////// Mapping Code //////////////////////////////////////////////

func (store *Store) ExistStringMapping(value string) bool {

	if value == Empty {

		return false
	}

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.RLock()

	defer store.lock.RUnlock()

	if store.closed {

		return false

	}

	found, _ := existStringMapping(store, value)

	return found
}

func (store *Store) PutStringMapping(value string, encoder codec.Encoder) error {

	if value == Empty {

		return nil
	}

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	var err error

	store.lock.Lock()

	defer store.lock.Unlock()

	if store.closed {

		return errors.New(fmt.Sprintf(ErrorStoreClosed, store.name))

	}

	found, ordinal := existStringMapping(store, value)

	if !found {

		store.dirty = true

		if store.availableStringMappingOrdinals != nil && store.availableStringMappingOrdinals.Count() > 0 {

			stringOrdinal, _ := store.availableStringMappingOrdinals.Min()

			err = WriteStringMapping(value, int32(stringOrdinal), store, encoder)

			if err != nil {

				return err
			}

			store.availableStringMappingOrdinals.Remove(stringOrdinal)

			ordinal = int32(stringOrdinal)

		} else {

			ordinal = store.currentStringMappingOrdinal

			err = WriteStringMapping(value, ordinal, store, encoder)

			if err != nil {

				return err
			}

			store.currentStringMappingOrdinal += 2

		}

	}

	if store.cleanup {

		store.tempMappingOrdinals.Set(uint32(ordinal))
	}

	return err
}

func (store *Store) PutNumericMapping(value int64, encoder codec.Encoder) error {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	var err error

	store.lock.Lock()

	defer store.lock.Unlock()

	if store.closed {

		return errors.New(fmt.Sprintf(ErrorStoreClosed, store.name))

	}

	found, ordinal := existNumericMapping(store, value, encoder)

	if !found {

		store.dirty = true

		if store.availableNumericMappingOrdinals != nil && store.availableNumericMappingOrdinals.Count() > 0 {

			numericOrdinal, _ := store.availableNumericMappingOrdinals.Min()

			err = writeNumericMapping(value, int32(numericOrdinal), store, encoder)

			if err != nil {

				return err
			}

			store.availableNumericMappingOrdinals.Remove(numericOrdinal)

			ordinal = int32(numericOrdinal)

		} else {

			ordinal = store.currentNumericMappingOrdinal

			err = writeNumericMapping(value, store.currentNumericMappingOrdinal, store, encoder)

			if err != nil {

				return err
			}

			store.currentNumericMappingOrdinal += 2

		}

	}

	if store.cleanup {

		store.tempMappingOrdinals.Set(uint32(ordinal))
	}

	return err
}

func (store *Store) GetStringMapping(value string) (bool, int32, error) {

	if value == Empty {

		return true, DummyStringMappingOrdinal, nil
	}

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.RLock()

	defer store.lock.RUnlock()

	if store.closed {

		return false, -1, errors.New(fmt.Sprintf(ErrorStoreClosed, store.name))

	}

	return getStringMapping(value, store)

}

func (store *Store) GetMaxStringMappingOrdinal() uint64 {

	store.lock.RLock()

	defer store.lock.RUnlock()

	if store.closed {

		return 0
	}

	return uint64(store.currentStringMappingOrdinal)
}

func (store *Store) GetMaxNumericMappingOrdinal() uint64 {

	store.lock.RLock()

	defer store.lock.RUnlock()

	if store.closed {

		return 0
	}

	return uint64(store.currentNumericMappingOrdinal)
}

func (store *Store) GetAvailableStringMappingOrdinals() *bitmap.Bitmap {

	if store.closed {

		return nil
	}

	store.lock.RLock()

	defer store.lock.RUnlock()

	return store.availableStringMappingOrdinals

}

func (store *Store) GetAvailableNumericMappingOrdinals() *bitmap.Bitmap {

	if store.closed {

		return nil
	}

	store.lock.RLock()

	defer store.lock.RUnlock()

	return store.availableNumericMappingOrdinals

}

func (store *Store) GetNumericMapping(value int64, encoder codec.Encoder) (bool, int32, error) {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.RLock()

	defer store.lock.RUnlock()

	if store.closed {

		return false, -1, errors.New(fmt.Sprintf(ErrorStoreClosed, store.name))

	}

	return getNumericMapping(value, store, encoder)

}

func (store *Store) ResolveMapping(poolIndex int, encoder codec.Encoder, stringMappings, numericMappings *swiss.Map[int32, []int], size int) (error, codec.DataType, int) {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.RLock()

	defer store.lock.RUnlock()

	if store.closed {

		return errors.New(fmt.Sprintf(ErrorStoreClosed, store.name)), codec.Invalid, -1
	}

	return resolveMapping(store, poolIndex, encoder, stringMappings, numericMappings, size)
}

func (store *Store) MapStringValues(poolIndex int, encoder codec.Encoder, size int) (error, int, []int32) {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.RLock()

	if store.closed {

		store.lock.RUnlock()

		return errors.New(fmt.Sprintf(ErrorStoreClosed, store.name)), -1, nil
	}

	ordinalPoolIndex, unmappedItemPoolIndex, ordinals, unmappedItems := mapStringValues(store, poolIndex, encoder, size)

	if unmappedItemPoolIndex != -1 {

		values := encoder.MemoryPool.GetStringPool(poolIndex)

		values = values[:size]

		store.lock.RUnlock()

		store.lock.Lock()

		defer store.lock.Unlock()

		defer encoder.MemoryPool.ReleaseINTPool(unmappedItemPoolIndex)

		store.dirty = true

		for _, index := range unmappedItems {

			if values[index] != Empty {

				if found, ordinal := existStringMapping(store, values[index]); found {

					ordinals[index] = ordinal

				} else {

					err := WriteStringMapping(values[index], store.currentStringMappingOrdinal, store, encoder)

					if err != nil {

						return err, -1, nil
					}

					ordinals[index] = store.currentStringMappingOrdinal

					store.currentStringMappingOrdinal += 2
				}
			} else {

				ordinals[index] = DummyStringMappingOrdinal
			}

		}

	} else {

		store.lock.RUnlock()

	}

	return nil, ordinalPoolIndex, ordinals
}

func (store *Store) ReconcileStringMappingsBackupFile(encoder codec.Encoder, bkpFile *os.File) {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.Lock()

	defer store.lock.Unlock()

	if store.stringMapping != nil {

		iterator, err := store.stringMapping.Iterator(nil, nil)

		for err == nil {

			bytes, ordinal := iterator.Current()

			index, encodedBytes := writeStringMappingItem(string(bytes), int32(ordinal), encoder)

			_, _ = bkpFile.Write(encodedBytes)

			encoder.MemoryPool.ReleaseBytePool(index)

			err = iterator.Next()
		}
	}

}

// ListStringMappings Note - if mappings exceeded 10k don't use these methods.
func (store *Store) ListStringMappings(stringMappings MotadataMap) {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.RLock()

	defer store.lock.RUnlock()

	listStringMappings(store, stringMappings)
}

func (store *Store) MapNumericValues(poolIndex int, encoder codec.Encoder, size int) (error, int, []int32) {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.RLock()

	if store.closed {

		store.lock.RUnlock()

		return errors.New(fmt.Sprintf(ErrorStoreClosed, store.name)), -1, nil
	}

	ordinalPoolIndex, unmappedItemPoolIndex, ordinals, unmappedItems := mapNumericValues(store, poolIndex, encoder, size)

	if unmappedItemPoolIndex != -1 {

		values := encoder.MemoryPool.GetINT64Pool(poolIndex)

		values = values[:size]

		store.lock.RUnlock()

		store.lock.Lock()

		defer store.lock.Unlock()

		defer encoder.MemoryPool.ReleaseINTPool(unmappedItemPoolIndex)

		store.dirty = true

		for _, index := range unmappedItems {

			if found, ordinal := existNumericMapping(store, values[index], encoder); found {

				ordinals[index] = ordinal

			} else {

				err := writeNumericMapping(values[index], store.currentNumericMappingOrdinal, store, encoder)

				if err != nil {

					return err, -1, nil
				}

				ordinals[index] = store.currentNumericMappingOrdinal

				store.currentNumericMappingOrdinal += 2
			}
		}

	} else {

		store.lock.RUnlock()

	}

	return nil, ordinalPoolIndex, ordinals
}

func (store *Store) GetGreaterThanMappings(value int64, inclusive bool, mappings *swiss.Map[int64, int32], encoder codec.Encoder) error {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.RLock()

	defer store.lock.RUnlock()

	if store.closed {

		return errors.New(fmt.Sprintf(ErrorStoreClosed, store.name))
	}

	doGreaterThanMapping(store, value, inclusive, mappings, encoder)

	return nil

}

func (store *Store) GetLessThanMappings(value int64, inclusive bool, mappings *swiss.Map[int64, int32], encoder codec.Encoder) error {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.RLock()

	defer store.lock.RUnlock()

	if store.closed {

		return errors.New(fmt.Sprintf(ErrorStoreClosed, store.name))
	}

	doLessThanMapping(store, value, inclusive, mappings, encoder)

	return nil

}

func (store *Store) GetPrefixMappings(value string, mappings *swiss.Map[string, int32]) error {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.RLock()

	defer store.lock.RUnlock()

	if store.closed {

		return errors.New(fmt.Sprintf(ErrorStoreClosed, store.name))
	}

	doPrefixMapping(store, value, mappings)

	return nil

}

func (store *Store) GetAllPrefixMappings(values MotadataMap, mappings *swiss.Map[string, int32], tokenizer *Tokenizer, separator string) error {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.RLock()

	defer store.lock.RUnlock()

	if store.closed {

		return errors.New(fmt.Sprintf(ErrorStoreClosed, store.name))
	}

	doAllPrefixMapping(store, values, mappings, tokenizer, separator)

	return nil

}

func (store *Store) GetStringEqualMappings(value string, mappings *swiss.Map[string, int32]) error {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.RLock()

	defer store.lock.RUnlock()

	if store.closed {

		return errors.New(fmt.Sprintf(ErrorStoreClosed, store.name))
	}

	doStringEqualMapping(store, value, mappings)

	return nil

}

func (store *Store) GetSuffixMappings(value string, mappings *swiss.Map[string, int32]) error {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.RLock()

	defer store.lock.RUnlock()

	if store.closed {

		return errors.New(fmt.Sprintf(ErrorStoreClosed, store.name))
	}

	doSuffixMapping(store, value, mappings)

	return nil

}

func (store *Store) GetContainMappings(value string, mappings *swiss.Map[string, int32]) error {

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	store.lock.RLock()

	defer store.lock.RUnlock()

	if store.closed {

		return errors.New(fmt.Sprintf(ErrorStoreClosed, store.name))
	}

	doContainMapping(store, value, mappings)

	return nil

}

func (store *Store) UpdateCacheMapping(records int, lock bool) {

	var err error

	store.lastUsedTimestampSeconds.Store(time.Now().Unix())

	if lock {

		store.lock.Lock()

		defer store.lock.Unlock()

	}

	if store.closed {

		return
	}

	if TraceEnabled() {

		storeLogger.Trace(fmt.Sprintf("updating cache size for store : %s , current size : %d updated size : %d", store.name, store.maxMappingCacheRecords, records))

	}

	store.maxMappingCacheRecords = records

	if err = updateCacheNumericMapping(store); err != nil {

		storeLogger.Error(fmt.Sprintf("failed to update numeric mapping cache entries for store : %s , size : %d, reason : %s", store.name, records, err.Error()))
	}

	if err = updateCacheStringMapping(store); err != nil {

		storeLogger.Error(fmt.Sprintf("failed update string mapping cache entries for store : %s , size : %d, reason : %s", store.name, records, err.Error()))
	}

}

// repair mappings

func (store *Store) RepairMappings(usedMappingOrdinals *bitmap.Bitmap, encoder codec.Encoder, maxStringMappingOrdinal, maxNumericMappingOrdinal uint64) (err error) {

	store.lock.Lock()

	defer store.lock.Unlock()

	if store.closed {

		return
	}

	if err = repairStringMappings(store, usedMappingOrdinals, encoder, maxStringMappingOrdinal); err != nil {

		return err
	}

	if err = repairNumericMappings(store, usedMappingOrdinals, encoder, maxNumericMappingOrdinal); err != nil {

		return err
	}

	store.UpdateCacheMapping(GetMappingCacheRecords(store.GetName()), false)

	store.cleanup = false

	store.tempMappingOrdinals.Clear()

	return err

}

////////////////////////////////////////////// Multipart keys Code //////////////////////////////////////////////

/*

	While storing key value pair data, we have a length limit for values that cannot exceed the pool length,
	so for values greater than the pool length, we divide data into parts where we store data in multiple parts
	and append part in key while also maintaining the status of parts of the key.

	if more than one part or parts other than default part is considered as multipart
*/

/*func (store *Store) GetMultipartKeys() map[uint64]uint16 {

	store.lock.RLock()

	defer store.lock.RUnlock()

	keys := make(map[uint64]uint16, len(store.multipartKeys))

	for key, parts := range store.multipartKeys {

		keys[key] = parts
	}

	return keys
}

func (store *Store) IsMultipartKey(key uint64) (bool, uint16) {

	store.lock.RLock()

	defer store.lock.RUnlock()

	parts, ok := store.multipartKeys[key]

	return ok, parts
}*/

func (store *Store) AddMultipartKey(key uint64, parts uint16) error {

	store.lock.Lock()

	defer store.lock.Unlock()

	if !store.ContainsMultipartKey() {

		store.containMultipartKeys.Store(true)
	}

	if _, ok := store.multipartKeys[key]; ok {

		store.multipartKeys[key] += parts
	} else {
		store.multipartKeys[key] = 1 + parts
	}

	bytes := make([]byte, 10)

	bytes[0] = byte(key)

	bytes[1] = byte(key >> 8)

	bytes[2] = byte(key >> 16)

	bytes[3] = byte(key >> 24)

	bytes[4] = byte(key >> 32)

	bytes[5] = byte(key >> 40)

	bytes[6] = byte(key >> 48)

	bytes[7] = byte(key >> 56)

	bytes[8] = byte(store.multipartKeys[key])

	bytes[9] = byte(store.multipartKeys[key] >> 8)

	_, err := store.multipartKeyFile.Write(bytes)

	if err != nil {

		return errors.New(fmt.Sprintf("failed to write the multipart index for the store %v, reason:%v", err.Error(), store.name))
	}

	return nil

}

func (store *Store) SetMultipartKey(key uint64, parts uint16) error {

	if !store.ContainsMultipartKey() {

		store.containMultipartKeys.Store(true)
	}

	store.multipartKeys[key] = 1 + parts

	bytes := make([]byte, 10)

	bytes[0] = byte(key)

	bytes[1] = byte(key >> 8)

	bytes[2] = byte(key >> 16)

	bytes[3] = byte(key >> 24)

	bytes[4] = byte(key >> 32)

	bytes[5] = byte(key >> 40)

	bytes[6] = byte(key >> 48)

	bytes[7] = byte(key >> 56)

	bytes[8] = byte(store.multipartKeys[key])

	bytes[9] = byte(store.multipartKeys[key] >> 8)

	_, err := store.multipartKeyFile.Write(bytes)

	if err != nil {

		return errors.New(fmt.Sprintf("failed to set the multipart key for the store %v, reason:%v", err.Error(), store.name))
	}

	return nil

}

func (store *Store) ContainsMultipartKey() bool {

	return store.containMultipartKeys.Load()
}

func (store *Store) loadMultipartKeys() error {

	_, err := os.Stat(store.path + PathSeparator + MultipartIdxFile)

	if err == nil {

		store.multipartKeyFile, err = os.OpenFile(store.path+PathSeparator+MultipartIdxFile, os.O_RDWR|os.O_APPEND, 0666)

		bytes, err := os.ReadFile(store.path + PathSeparator + MultipartIdxFile)

		if err != nil {

			return errors.New(fmt.Sprintf("failed to open the multipart index for the store %v, reason:%v", err.Error(), store.name))

		}

		if len(bytes) > 0 {

			if len(bytes)%10 != 0 {

				return errors.New("failed to read multipart index, reason: invalid buffer length")
			}

			store.containMultipartKeys.Store(true)

			store.multipartKeys = make(map[uint64]uint16, len(bytes)/10)

			for i := 0; i < len(bytes)/10; i++ {

				valueBytes := bytes[i*10 : (i*10)+10]

				store.multipartKeys[uint64(valueBytes[0])|
					uint64(valueBytes[1])<<8|
					uint64(valueBytes[2])<<16|
					uint64(valueBytes[3])<<24|
					uint64(valueBytes[4])<<32|
					uint64(valueBytes[5])<<40|
					uint64(valueBytes[6])<<48|
					uint64(valueBytes[7])<<56] = uint16(valueBytes[8]) | uint16(valueBytes[9])<<8
			}

		} else {

			store.multipartKeys = make(map[uint64]uint16)

		}

	} else {

		store.multipartKeyFile, err = os.Create(store.path + PathSeparator + MultipartIdxFile)

		store.multipartKeys = make(map[uint64]uint16)

		if err != nil {

			return errors.New(fmt.Sprintf("failed to create the multipart index for the store %v, reason:%v", err.Error(), store.name))

		}

	}

	return nil
}

func (store *Store) GetMaxPart(key uint64) uint16 {

	store.lock.RLock()

	defer store.lock.RUnlock()

	if _, found := store.multipartKeys[key]; !found {

		return 0
	}

	return store.multipartKeys[key] - 1
}

// When we close store, partitions  are not closed. To close partitions, we must call this method.
func (store *Store) MarkClosed(encoder codec.Encoder) {

	for partition := range store.partitions {

		store.partitions[partition].lock.Lock()

		store.partitions[partition].txnLock.Lock()

		store.partitions[partition].closed = true

		store.partitions[partition].txnLock.Unlock()

		store.partitions[partition].lock.Unlock()

	}

	store.Close(encoder)
}

func (store *Store) MarkCleanup() {

	store.lock.RLock()

	defer store.lock.RUnlock()

	store.cleanup = true

}

func (store *Store) ResetTempMapping() {

	store.lock.Lock()

	defer store.lock.Unlock()

	store.cleanup = false

	if store.tempMappingOrdinals != nil {

		store.tempMappingOrdinals.Clear()
	}

}

func (store *Store) IsDirty() bool {

	return store.dirty
}

// Archive, Here, we create a zip for store.
func (store *Store) Archive(encoder codec.Encoder, profile string) (err error) {

	defer func() {

		if r := recover(); r != nil {

			err = errors.New(fmt.Sprintf("%v", r))
		}

	}()

	zipFile, err := os.Create(BackUpDir + PathSeparator + profile + PathSeparator + store.name + ZipExtension)

	if err != nil {

		return err
	}

	writer := zip.NewWriter(zipFile)

	defer func(zipFile *os.File, writer *zip.Writer) {

		_ = writer.Close()

		_ = zipFile.Close()

	}(zipFile, writer)

	for partition := range store.partitions {

		info, _ := os.Stat(store.partitions[partition].path)

		header, err := zip.FileInfoHeader(info)

		if err != nil {

			return err
		}

		partitionId := codec.INTToStringValue(partition + 1)

		header.Name = partitionId + "/"

		_, _ = writer.CreateHeader(header)

		err = store.partitions[partition].archive(writer, encoder, partitionId)

		if err != nil {

			storeLogger.Error(fmt.Sprintf("failed to backup partition: %v of store: %v", partition+1, store.name))

			_ = os.Remove(zipFile.Name())

			return err
		}
	}

	files, err := os.ReadDir(store.path)

	if err != nil { //in-case of closed we might fail to read directory

		return err
	}

	store.lock.RLock()

	defer store.lock.RUnlock()

	for index := range files {

		if files[index].IsDir() || files[index].Name() == "lock" {

			continue
		}

		info, _ := os.Stat(store.path + PathSeparator + files[index].Name())

		header, err := zip.FileInfoHeader(info)

		if err != nil {

			return err
		}

		header.Name = files[index].Name()

		zipWriter, err := writer.CreateHeader(header)

		if err != nil {

			return err
		}

		file, err := os.Open(store.path + PathSeparator + files[index].Name())

		if err != nil {

			return err
		}

		_, err = io.Copy(zipWriter, file)

		_ = file.Close()

		if err != nil {

			return err
		}
	}

	return err
}

/////////////////////////////////// datastore copy //////////////////////////////////////

// Clone is used to copy store in temporary directory
func (store *Store) Clone(encoder codec.Encoder, tokenizer *Tokenizer, destination string) (err error) {

	defer func() {

		if r := recover(); r != nil {

			err = errors.New(fmt.Sprintf("unable to copy datastore  store : %s reason : %v", store.name, r.(error).Error()))

		}

	}()

	if err = os.MkdirAll(destination+PathSeparator+store.name, 0755); err != nil {

		return err
	}

	for _, partition := range store.partitions {

		Split(partition.path, PathSeparator, tokenizer)

		if err = partition.Clone(encoder, destination+PathSeparator+store.name+PathSeparator+tokenizer.Tokens[tokenizer.Counts-1]); err != nil {

			return err
		}
	}

	files, err := os.ReadDir(store.path)

	if err != nil {

		return err
	}

	store.lock.RLock()

	defer store.lock.RUnlock()

	for _, file := range files {

		if !file.IsDir() {

			if err = cp.Copy(store.path+PathSeparator+file.Name(), destination+PathSeparator+store.name+PathSeparator+file.Name()); err != nil {

				return errors.New(fmt.Sprintf("error occurred while copying store meta files store : %s , reason : %s", store.name, err.Error()))
			}

		}

	}

	return nil
}

// version of store
func (store *Store) GetVariant() string {

	store.lock.RLock()

	defer store.lock.RUnlock()

	return store.metadata.GetStringValue(Version)

}
