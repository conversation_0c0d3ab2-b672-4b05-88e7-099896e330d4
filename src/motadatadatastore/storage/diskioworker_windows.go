/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-06-04             A<PERSON>l Shah            MOTADATA-5780 Introduced New DiskIOWorker for Windows that implements IOCP
 */

/*
	For reading IO worker is in charge of reading; read-related requests arrive here, and the requests are then executed.
*/

package storage

import (
	"errors"
	"fmt"
	"golang.org/x/sys/windows"
	"motadatadatastore/codec"
	"motadatadatastore/utils"
	"os"
	"runtime"
	"strings"
	"sync"
	"syscall"
)

var diskIOWorkerLogger = utils.NewLogger("Disk IO Worker", "storage")

var workers []*IOCPWorker

const errorIOWorker = "error %v occurred in io-worker %v"

type DiskIOWorker struct {
	workerId int

	ShutdownNotifications chan bool

	ReadRequests []*ReadRequest

	// WaitGroup for synchronizing IOCP operations
	waitGroup *sync.WaitGroup
}

func NewIOWorker(id int) *DiskIOWorker {

	waitGroup := &sync.WaitGroup{}

	readRequests := make([]*ReadRequest, utils.MaxWorkerEventKeyGroupLength)

	for i := range readRequests {

		readRequests[i] = &ReadRequest{

			workerId: int32(id),
		}
	}

	return &DiskIOWorker{

		workerId: id,

		ShutdownNotifications: make(chan bool, 5),

		ReadRequests: readRequests,

		waitGroup: waitGroup,
	}
}

type DiskIOEventBatch struct {
	memoryPool *utils.MemoryPool

	partition *Partition

	waitGroup *sync.WaitGroup

	keyBuffers, valueBuffers, buffers [][]byte

	errs []error

	bytePoolIndex, positionPoolIndex int

	keyElementSize int8

	lookUpWAL bool
}

type DiskIOEvent struct {
	memoryPool *utils.MemoryPool

	partition *Partition

	waitGroup *sync.WaitGroup

	keyBytes, valueBytes, bufferBytes []byte

	err error

	offset uint64

	length int

	lookUpWAL, blob bool
}

type ReadRequest struct {
	overlapped windows.Overlapped

	index, workerId int32

	waitGroup *sync.WaitGroup

	err error
}

func (worker *DiskIOWorker) Start() {

	if worker.workerId == 0 {

		workers = make([]*IOCPWorker, utils.IOCPWorkers)

		for id := range workers {

			workers[id] = NewIOCPWorker(id)

			workers[id].Start()
		}
	}

	go func() {

		for {

			select {

			case event := <-diskIOBatchEvents:

				worker.processIOEventBatch(event)

			case event := <-DiskIOEvents:

				worker.processIOEvent(event)

			case <-worker.ShutdownNotifications:

				diskIOWorkerLogger.Info("shutting down...")

				return
			}
		}

	}()

}

// batch of read request is served here
func (worker *DiskIOWorker) processIOEventBatch(event *DiskIOEventBatch) {

	event.partition.lock.RLock()

	defer event.partition.lock.RUnlock()

	if event.lookUpWAL {

		event.partition.txnLock.RLock()

		defer event.partition.txnLock.RUnlock()
	}

	defer event.waitGroup.Done()

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			for _, index := range event.memoryPool.GetINTPool(event.positionPoolIndex)[:event.keyElementSize] {

				event.errs[index] = errors.New(fmt.Sprintf("error %v occurred while reading in partition %v", err, event.partition.name))
			}

			diskIOWorkerLogger.Error(fmt.Sprintf(errorIOWorker, err, worker.workerId))

			diskIOWorkerLogger.Error(fmt.Sprintf("!!!STACK TRACE for io-worker %v!!! \n %v", worker.workerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	if event.partition.closed {

		for _, index := range event.memoryPool.GetINTPool(event.positionPoolIndex)[:event.keyElementSize] {

			event.errs[index] = errors.New(fmt.Sprintf(utils.ErrorPartitionDeleted, event.partition.name))
		}
		return
	}

	var err error

	requestElementSize, fd, segmentLength := 0, 0, 0

	bytes := event.memoryPool.GetBytePool(event.bytePoolIndex)

	for _, index := range event.memoryPool.GetINTPool(event.positionPoolIndex)[:event.keyElementSize] {

		event.buffers[index], err, fd, segmentLength = event.partition.buildIORequests(event.keyBuffers[index], event.valueBuffers[index], bytes, event.lookUpWAL)

		if err != nil {

			event.errs[index] = err

			continue
		}

		if fd == 0 || segmentLength == 0 { // err from build requests

			event.errs[index] = errors.New(fmt.Sprintf(utils.ErrorIOURing, "prep failed"))

			continue
		}

		if fd == utils.NotAvailable || segmentLength == utils.NotAvailable { // data read from mmap file

			continue
		}

		var context = worker.ReadRequests[requestElementSize]

		requestElementSize++

		context.index = int32(index)

		context.waitGroup = worker.waitGroup

		offset := uint64(codec.ReadINTValue(bytes[2:]))

		context.overlapped.Offset = uint32(offset)

		context.overlapped.OffsetHigh = uint32(offset >> 32)

		// Add to wait group before submitting
		worker.waitGroup.Add(1)

		event.buffers[index] = event.valueBuffers[index]

		event.errs[index] = errors.New(fmt.Sprintf(utils.ErrorIOURing, "internal"))

		err = windows.ReadFile(windows.Handle(fd), event.valueBuffers[index][:segmentLength], nil, &context.overlapped)

		if err != nil && !errors.Is(err, syscall.ERROR_IO_PENDING) {

			event.errs[index] = fmt.Errorf(utils.ErrorIOURing, err)

			worker.waitGroup.Done()

			continue
		}
	}

	err = nil

	if requestElementSize > 0 {

		err = ExecuteIOCPRequests(worker.ReadRequests[:requestElementSize], event.buffers, event.errs, worker.waitGroup)

		if err != nil {

			diskIOWorkerLogger.Error(fmt.Sprintf("error %v occurred while doing io for partition %v", err, event.partition.name))
		}
	}
}

// here one read request is served
func (worker *DiskIOWorker) processIOEvent(event *DiskIOEvent) {

	event.partition.lock.RLock()

	defer event.partition.lock.RUnlock()

	//in case of blob file this block will be executed
	if event.blob {

		defer event.waitGroup.Done()

		event.bufferBytes, event.err = worker.readBlob(event)

		event.length = 0

		event.blob = false

		event.valueBytes = nil

		event.offset = 0

		return
	}

	if event.lookUpWAL {

		event.partition.txnLock.RLock()

		defer event.partition.txnLock.RUnlock()
	}

	defer event.waitGroup.Done()

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			event.err = errors.New(fmt.Sprintf(errorIOWorker, err, worker.workerId))

			diskIOWorkerLogger.Error(fmt.Sprintf(errorIOWorker, err, worker.workerId))

			diskIOWorkerLogger.Error(fmt.Sprintf("!!!STACK TRACE for io-worker %v!!! \n %v", worker.workerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	if event.partition.closed {

		event.err = errors.New(fmt.Sprintf(utils.ErrorPartitionDeleted, event.partition.name))

		return
	}

	_, event.bufferBytes, event.err = get(event, worker)

}

func (worker *DiskIOWorker) readBlob(event *DiskIOEvent) (bytes []byte, err error) {

	defer func() {

		if r := recover(); r != nil {

			stackTraceBytes := make([]byte, 1<<20)

			err = errors.New(fmt.Sprintf("error %v occurred while reading in io worker %v", r, worker.workerId))

			diskIOWorkerLogger.Error(fmt.Sprintf("error %v occurred while reading in io-worker %v", r, worker.workerId))

			diskIOWorkerLogger.Error(fmt.Sprintf("!!!STACK TRACE for reading io-worker %v!!! \n %v", worker.workerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	_, err = event.partition.blobFile.ReadAt(event.valueBytes[:event.length], int64(event.offset))

	return event.valueBytes[:event.length], err
}

func get(event *DiskIOEvent, worker *DiskIOWorker) (bool, []byte, error) {

	var err error

	if event.lookUpWAL {

		if entry, ok := event.partition.entries[utils.GetHash64(event.keyBytes)]; ok {

			if entry.length > len(event.valueBytes) {

				return false, nil, errors.New(utils.ErrorTooLarge)
			}

			copy(event.valueBytes, event.partition.walBytes[entry.offset:entry.offset+entry.length])

			bytes := getValueBytes(event.valueBytes)

			if bytes == nil {

				return false, nil, errors.New(utils.ErrorCorrupted)
			}

			return true, bytes, nil

		}

	}

	offset, found, err := searchIndex(event.keyBytes, event.partition)

	if err != nil {

		return found, nil, err
	}

	if !found {

		return found, nil, err

	}

	index, bytes := event.memoryPool.AcquireBytePool(8)

	defer event.memoryPool.ReleaseBytePool(index)

	writeINT64Value(int64(offset), bytes)

	event.valueBytes, err = read(bytes, event.valueBytes, event.partition, worker)

	if err != nil {

		return false, nil, err
	}

	return found, event.valueBytes, err

}

func read(offsetBytes, bytes []byte, partition *Partition, worker *DiskIOWorker) ([]byte, error) {

	// if the target segment is mapped into the memory then use Memory mapped I/O rather than Disk I/O
	segmentHeader := offsetBytes[1]

	offset := codec.ReadINTValue(offsetBytes[2:])

	segmentType := getSegmentType(segmentHeader)

	segmentLength := getSegmentBufferLength(segmentType)

	if segmentLength > len(bytes) {

		return nil, errors.New(utils.ErrorTooLarge)
	}

	// If the segment is in memory, read from memory
	if segment, ok := partition.writingSegments[segmentType]; ok && offsetBytes[0] == NotCompacted && segment == getSegment(segmentHeader) {

		segmentBuffer := partition.segmentBuffers[segmentHeader]

		if len(segmentBuffer) < offset+getSegmentBufferLength(segmentType) {

			return nil, errors.New(utils.ErrorCorrupted)
		}

		copy(bytes, segmentBuffer[offset:offset+segmentLength])

	} else {

		var fileHandle windows.Handle

		if offsetBytes[0] == NotCompacted {

			fileHandle = windows.Handle(partition.segmentFiles[segmentHeader].Fd())

		} else {

			fileHandle = windows.Handle(partition.segment255.Fd())
		}

		var context = worker.ReadRequests[0]

		context.index = 0

		context.waitGroup = worker.waitGroup

		context.overlapped.Offset = uint32(offset)

		context.overlapped.OffsetHigh = uint32(offset >> 32)

		worker.waitGroup.Add(1)

		err := windows.ReadFile(fileHandle, bytes[:segmentLength], nil, &context.overlapped)

		if err != nil && !errors.Is(err, syscall.ERROR_IO_PENDING) {

			return nil, err
		}

		worker.waitGroup.Wait()

		if context.err != nil {

			return nil, context.err
		}
	}

	valueBytes := getValueBytes(bytes)

	if valueBytes == nil {

		return nil, errors.New(utils.ErrorCorrupted)
	}

	return valueBytes, nil
}

func OpenFile(name string, createMode int, flags int) (*os.File, error) {

	handle, err := windows.CreateFile(
		windows.StringToUTF16Ptr(name),
		syscall.GENERIC_READ|syscall.GENERIC_WRITE,
		syscall.FILE_SHARE_READ|syscall.FILE_SHARE_WRITE|syscall.FILE_SHARE_DELETE,
		nil,
		uint32(createMode),
		uint32(flags),
		0,
	)

	if err != nil {

		return nil, err
	}

	file := os.NewFile(uintptr(handle), name)

	if flags&syscall.FILE_FLAG_OVERLAPPED != 0 {

		_, err = windows.CreateIoCompletionPort(handle, workers[utils.GetFastModN(utils.GetHash64([]byte(codec.INTToStringValue(int(file.Fd())))), utils.IOCPWorkers)].handle, 1, 0)

		// If the file is already associated with this IOCP, it will return an error
		// We can ignore this specific error
		if err != nil && !strings.Contains(err.Error(), "The parameter is incorrect.") {

			return nil, fmt.Errorf(utils.ErrorIOURing, "prep failed")

		}
	}

	return file, err
}
