/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-05-08  			 <PERSON><PERSON><PERSON> Shah			MOTADATA-6073 Added Flush and FlushAsync methods
* 2025-06-04             <PERSON><PERSON><PERSON> Shah            MOTADATA-5780 Refactored mmap to enable support on windows
 */

package storage

import (
	"errors"
	"motadatadatastore/utils"
	"os"
	"reflect"
	"unsafe"
)

const (
	ReadOnly = 0

	ReadWrite = 1 << iota

	Copy

	Execute
)

const (
	ANON = 1 << iota
)

type MMap []byte

// Map maps an entire file into memory.
func Map(file *os.File, protection int) (MMap, error) {

	return MapRegion(file, -1, protection, 0, 0)
}

// MapRegion maps part of a file into memory.
// The offset parameter must be a multiple of the system's page size.
// If length < 0, the entire file will be mapped.
// If ANON is set in flags, f is ignored.
func MapRegion(file *os.File, length int, protection, flags int, offset int64) (MMap, error) {

	if offset%int64(os.Getpagesize()) != 0 {

		return nil, errors.New(utils.ErrorOffsetNotPageAligned)
	}

	var fileDescriptor uintptr

	if flags&ANON == 0 {

		fileDescriptor = file.Fd()

		if length < 0 {

			fileInfo, err := file.Stat()

			/*in some cases if the datastore is shutdown while the segment file was empty it causes error while mapping*/

			if err != nil {

				return nil, err

			} else if fileInfo.Size() == 0 {

				return nil, errors.New(utils.ErrorEmptyFile)
			}

			length = int(fileInfo.Size())
		}

	} else {

		if length <= 0 {
			return nil, errors.New(utils.ErrorAnonymousMappingLength)
		}

		fileDescriptor = ^uintptr(0)
	}

	return mmap(length, uintptr(protection), fileDescriptor, offset)
}

func (m *MMap) header() *reflect.SliceHeader {

	return (*reflect.SliceHeader)(unsafe.Pointer(m))
}

func (m *MMap) addrLen() (uintptr, uintptr) {

	if len(*m) == 0 {

		return 0, 0
	}

	return uintptr(unsafe.Pointer(&(*m)[0])), uintptr(len(*m))
}

func (m *MMap) Unmap() error {

	err := m.unmap()

	*m = nil

	return err
}

func Remap(bytes MMap, size int, file *os.File) (MMap, error) {

	err := bytes.Unmap()

	if err != nil {

		return nil, err
	}

	err = file.Truncate(int64(size))

	if err != nil {

		return nil, err
	}

	return MapRegion(file, size, ReadWrite, 0, 0)
}
