/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-06-04             <PERSON><PERSON><PERSON> Shah            MOTADATA-5780 Implemented mmap logic for windows
 */

package storage

import (
	"errors"
	"golang.org/x/sys/windows"
	"os"
	"sync"
)

// mmap on Windows is a two-step process.
// First, we call CreateFileMapping to get a handle.
// Then, we call MapviewToFile to get an actual pointer into memory.
// Because we want to emulate a POSIX-style mmap, we don't want to expose
// the handle -- only the pointer. We also want to return only a byte slice,
// not a struct, so it's convenient to manipulate.

type addrinfo struct {
	file windows.Handle

	mapview windows.Handle

	writable bool
}

var handleLock sync.Mutex

var handles = map[uintptr]*addrinfo{}

func mmap(length int, protection, fileHandle uintptr, offset int64) ([]byte, error) {

	fileProtection := uint32(windows.PAGE_READONLY)

	fileAccess := uint32(windows.FILE_MAP_READ)

	writable := false

	switch {

	case protection&Copy != 0:

		fileProtection = windows.PAGE_WRITECOPY

		fileAccess = windows.FILE_MAP_COPY

		writable = true

	case protection&ReadWrite != 0:

		fileProtection = windows.PAGE_READWRITE

		fileAccess = windows.FILE_MAP_WRITE

		writable = true

	}

	if protection&Execute != 0 {

		fileProtection <<= 4

		fileAccess |= windows.FILE_MAP_EXECUTE

	}

	maxSizeHigh := uint32((offset + int64(length)) >> 32)

	maxSizeLow := uint32(offset + int64(length))

	mappingHandle, err := windows.CreateFileMapping(windows.Handle(fileHandle), nil, fileProtection, maxSizeHigh, maxSizeLow, nil)

	if mappingHandle == 0 {

		return nil, os.NewSyscallError("CreateFileMapping", err)
	}

	fileOffsetHigh := uint32(offset >> 32)

	fileOffsetLow := uint32(offset)

	memoryAddress, err := windows.MapViewOfFile(mappingHandle, fileAccess, fileOffsetHigh, fileOffsetLow, uintptr(length))

	if memoryAddress == 0 {

		windows.CloseHandle(mappingHandle)

		return nil, os.NewSyscallError("MapViewOfFile", err)
	}

	handleLock.Lock()

	handles[memoryAddress] = &addrinfo{

		file: windows.Handle(fileHandle),

		mapview: mappingHandle,

		writable: writable,
	}

	handleLock.Unlock()

	memoryMappedBytes := MMap{}

	dataHeader := memoryMappedBytes.header()

	dataHeader.Data = memoryAddress

	dataHeader.Len = length

	dataHeader.Cap = dataHeader.Len

	return memoryMappedBytes, nil
}

func (m MMap) flush() error {

	memoryAddress, length := m.addrLen()

	err := windows.FlushViewOfFile(memoryAddress, length)

	if err != nil {
		return os.NewSyscallError("FlushViewOfFile", err)
	}

	handleLock.Lock()

	defer handleLock.Unlock()

	handle, ok := handles[memoryAddress]

	if !ok {
		// should be impossible; we would've errored above
		return errors.New("unknown base address")
	}

	if handle.writable && handle.file != windows.Handle(^uintptr(0)) {

		if err := windows.FlushFileBuffers(handle.file); err != nil {

			return os.NewSyscallError("FlushFileBuffers", err)
		}
	}

	return nil
}

func (m MMap) unmap() error {

	err := m.flush()

	if err != nil {
		return err
	}

	addr := m.header().Data

	handleLock.Lock()

	defer handleLock.Unlock()

	err = windows.UnmapViewOfFile(addr)

	if err != nil {
		return err
	}

	handle, ok := handles[addr]

	if !ok {
		// should be impossible; we would've errored above
		return errors.New("unknown base address")
	}

	delete(handles, addr)

	err = windows.CloseHandle(handle.mapview)

	return os.NewSyscallError("CloseHandle", err)
}

func AdviceRandom(bytes []byte) error {
	// In theory PrefetchVirtualMemory is for prefetching — there's no real way to advise random
	// Instead, we avoid using read-ahead patterns and hope Windows doesn't aggressively prefetch

	// Optionally, we can use MEM_WRITE_WATCH or MEM_RESET on VirtualAlloc regions,
	// but mmap-go doesn't give us access to raw handles, so we're limited

	// So for now, this is a no-op
	return nil
}

func (m MMap) FlushAsync() error {

	return m.flush()
}

func (m MMap) Flush() error {

	return m.flush()
}
