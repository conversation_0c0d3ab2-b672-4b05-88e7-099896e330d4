/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
	Mapping provides efficient value-to-ordinal mapping for data deduplication and indexing optimization.

	The mapping system is a critical component that handles:
	1. String-to-ordinal mapping for deduplicating repetitive string values
	2. Numeric-to-ordinal mapping for optimizing numeric data storage
	3. FST-based persistent storage for fast lookups and range queries
	4. Memory-mapped temporary files for high-performance writes
	5. Cache layers for frequently accessed mappings
	6. Recovery mechanisms for data integrity and crash resilience

	Storage Architecture:

		Value Deduplication:
		- Maps repetitive values to unique ordinal identifiers
		- Stores actual values once in mapping files, references everywhere else
		- Reduces storage space significantly for datasets with repeated values
		- Enables efficient compression and faster I/O operations

		Dual Mapping System:
		- String mappings: Handle text data with variable-length encoding
		- Numeric mappings: Handle integer data with fixed-length encoding
		- Separate FST indices for optimal performance per data type
		- Independent caching and recovery mechanisms

		FST Index Structure:
		- Uses vellum.FST for efficient key-to-ordinal mapping
		- Supports range queries, prefix searches, and pattern matching
		- Maintains sorted order for optimal query performance
		- Provides atomic updates through merge operations

		Memory Management:
		- Memory-mapped temporary files for write operations
		- In-memory maps for active session data
		- LRU cache for frequently accessed mappings
		- Automatic cleanup and resource management

		Recovery and Backup:
		- Backup files (.bkp) for crash recovery
		- Temporary files (.temp) for atomic operations
		- Version-aware format handling for backward compatibility
		- Panic recovery with detailed error logging

	Performance Optimizations:

		Multi-tier Lookup Strategy:
		- Cache lookup: Fastest access for hot data
		- Memory lookup: Fast access for session data
		- FST lookup: Persistent storage with good performance
		- Automatic promotion to higher tiers based on access patterns

		Write Optimization:
		- Batch operations to reduce I/O overhead
		- Memory-mapped files for efficient writes
		- Atomic file operations for consistency
		- Background merging to maintain performance

		Query Optimization:
		- Range queries using FST iterator capabilities
		- Pattern matching with regular expression support
		- Prefix and suffix searches for text data
		- Efficient greater-than/less-than operations for numeric data

	Use Cases:

		Data Deduplication:
		- Repetitive string values in large datasets
		- Common numeric values across multiple records
		- Categorical data with limited unique values
		- Time series data with repeated patterns

		Query Optimization:
		- Indexable columns for fast query processing
		- Range queries on numeric data
		- Text search operations on string data
		- Aggregation operations on mapped values

		Storage Efficiency:
		- Reduces storage footprint for repetitive data
		- Enables better compression ratios
		- Improves cache locality for frequently accessed values
		- Optimizes network transfer for distributed systems
*/

/* Change Logs:
* Date                   Author          		Notes
* 2025-04-09			 Aashil Shah			Motadata-4859  Refactoring "fmt.Sprintf" to update go version to 1.24
* 2025-05-01			 Dhaval Bera			Motadata-6077  Added Panic Recover In Case Of Rebuild Mapping
* 2025-05-15			 Vedant D. Dokania		Motadata-6251 Sonar Error Fixing
* 2025-06-04             Aashil Shah            MOTADATA-5780 Handled File Open/Close logic for fst manually instead of vellum
* 2025-06-23             Dhaval Bera            MOTADATA-6642  Added comments and memory aligned the struct
* 2025-06-24			 Aashil Shah		    MOTADATA-6543  Refactored Logs
* 2025-06-23             Vedant Dokania         Motadata-6370 Mapping operand changes to get the instance type store

 */

package storage

import (
	bytes2 "bytes"
	"errors"
	"fmt"
	"github.com/blevesearch/vellum"
	"github.com/blevesearch/vellum/regexp"
	"github.com/dolthub/swiss"
	"github.com/kamstrup/intmap"
	"github.com/kelindar/bitmap"
	"motadatadatastore/codec"
	"motadatadatastore/utils"
	"os"
	"runtime"
	"sort"
	"strings"
)

const (
	// Dummy ordinal constants for handling missing or empty values

	// DummyStringMappingOrdinal is the reserved ordinal for empty string values
	// This ordinal (1) is always odd, following the string mapping convention
	// Used when a string column has missing data or empty values
	DummyStringMappingOrdinal = 1

	// DummyString is the placeholder string value for missing data
	// Used in scenarios where a string representation is needed for the dummy ordinal
	DummyString = "1"

	// File naming constants for mapping storage and operations

	// FileMapping112 is the main numeric mapping file (112 = 8 bytes value + 4 bytes ordinal)
	// Contains the persistent FST index for numeric-to-ordinal mappings
	FileMapping112 = "mapping.112"

	// FileMapping176 is the main string mapping file (176 = variable length strings + ordinals)
	// Contains the persistent FST index for string-to-ordinal mappings
	FileMapping176 = "mapping.176"

	// FileMerged112 is the temporary file created during numeric mapping merge operations
	// Used to atomically replace the main numeric mapping file
	FileMerged112 = "merged.112"

	// FileMerged176 is the temporary file created during string mapping merge operations
	// Used to atomically replace the main string mapping file
	FileMerged176 = "merged.176"

	// FileTemp176 is the temporary file for active string mapping writes
	// Memory-mapped for high-performance write operations during active sessions
	FileTemp176 = "temp.176"

	// FileTemp112 is the temporary file for active numeric mapping writes
	// Memory-mapped for high-performance write operations during active sessions
	FileTemp112 = "temp.112"

	// FileBKP112 is the backup file for numeric mappings
	// Used for crash recovery and data integrity verification
	FileBKP112 = "bkp.112"

	// FileBKP176 is the backup file for string mappings
	// Used for crash recovery and data integrity verification
	FileBKP176 = "bkp.176"

	// FileBKPTemp176 is the temporary backup file for string mappings during repair operations
	// Used to ensure atomic backup file updates
	FileBKPTemp176 = "temp.bkp.176"

	// FileBKPTemp112 is the temporary backup file for numeric mappings during repair operations
	// Used to ensure atomic backup file updates
	FileBKPTemp112 = "temp.bkp.112"
)

// mappingLogger provides logging functionality for mapping operations
// Used throughout the mapping system to record events, errors, and performance metrics
var mappingLogger = utils.NewLogger("Mapping", "storage")

// resolveMapping resolves ordinal values to their actual data values using a multi-tier lookup strategy.
//
// This function implements the core mapping resolution logic by:
// 1. Categorizing ordinals into string and numeric types based on odd/even values
// 2. Performing multi-tier lookups: cache -> memory -> persistent storage
// 3. Optimizing memory pool usage based on data types discovered
// 4. Handling mixed data types within a single resolution operation
//
// The resolution process follows a performance-optimized hierarchy:
// - Cache lookup: Fastest access for frequently used mappings
// - Memory lookup: Fast access for session-active mappings
// - FST lookup: Persistent storage with good performance characteristics
//
// Ordinal Encoding Convention:
// - Even ordinals (0, 2, 4, ...) represent numeric values
// - Odd ordinals (1, 3, 5, ...) represent string values
// - DummyStringMappingOrdinal (1) is reserved for missing string data
//
// Parameters:
//   - store: Store instance containing mapping data and cache structures
//   - poolIndex: Memory pool index for the ordinal array
//   - encoder: Encoder instance for memory pool management and data operations
//   - tempStringMappings: Map to collect string ordinals and their array positions
//   - tempNumericMappings: Map to collect numeric ordinals and their array positions
//   - size: Number of ordinals to process from the pool
//
// Returns:
//   - error: Any error encountered during mapping resolution
//   - codec.DataType: Determined data type (String or Int64) based on resolved mappings
//   - int: Memory pool index for the resolved values array
//
// Error handling:
// - Continues processing even if individual lookups fail
// - Logs resolution errors for monitoring and debugging
// - Returns appropriate data type even for partial resolution failures
func resolveMapping(store *Store, poolIndex int, encoder codec.Encoder, stringMappings, numericMappings *swiss.Map[int32, []int], size int) (error, codec.DataType, int) {

	// Get the ordinal array from memory pool and slice to required size
	// This array contains the ordinals that need to be resolved to actual values
	ordinals := encoder.MemoryPool.GetINT32Pool(poolIndex)[:size]

	// Default to numeric data type - will be changed to String if string mappings are found
	// This optimization assumes numeric data is more common in most use cases
	dataType := codec.Int64

	var err error

	var iterator vellum.Iterator

	// Phase 1: Categorize ordinals into string and numeric types
	// This phase builds maps of ordinals to their positions in the result array
	// for efficient batch processing in subsequent lookup phases
	for i, ordinal := range ordinals {

		// Even ordinals represent numeric values (0, 2, 4, ...)
		// This encoding allows quick type determination without additional metadata
		if ordinal%2 == 0 {

			// Get existing position list for this numeric ordinal
			values, _ := numericMappings.Get(ordinal)

			// Add current array position to the list
			// Multiple positions may map to the same ordinal (duplicate values)
			values = append(values, i)

			// Store updated position list for batch processing
			numericMappings.Put(ordinal, values)

		} else {

			// Odd ordinals represent string values (1, 3, 5, ...)
			// Skip the reserved dummy ordinal as it represents missing data
			if ordinal != DummyStringMappingOrdinal {

				// Get existing position list for this string ordinal
				values, _ := stringMappings.Get(ordinal)

				// Add current array position to the list
				values = append(values, i)

				// Store updated position list for batch processing
				stringMappings.Put(ordinal, values)
			}

		}

	}

	// Track whether any mappings were found to determine return behavior
	found := false

	// Phase 2: Process string mappings if any exist
	// String mappings take precedence and determine the final data type
	if stringMappings.Count() > 0 {

		// Mark that we found mappings to resolve
		found = true

		// Set data type to String since we have string mappings
		// Mixed data will be converted to string representation
		dataType = codec.String

		// Acquire string pool for resolved values
		// Pool size matches original ordinal array for direct indexing
		valuePoolIndex, values := encoder.MemoryPool.AcquireStringPool(len(ordinals))

		// Update pool index to point to the string pool
		poolIndex = valuePoolIndex

		// Tier 1: Lookup in cache entries (fastest access)
		// Cache contains frequently accessed string mappings for performance
		if store.cacheStringMappings != nil && store.cacheStringMappings.Count() > 0 {

			// Iterate through string mappings that need resolution
			stringMappings.Iter(func(ordinal int32, indices []int) (stop bool) {

				// Check if this ordinal exists in the cache
				if bytes, ok := store.cacheStringMappings.Get(ordinal); ok {

					// Set the resolved value at all positions that reference this ordinal
					for _, index := range indices {

						values[index] = bytes
					}

					// Remove from pending mappings since it's resolved
					stringMappings.Delete(ordinal)

				}

				return stop

			})

		}

		// Tier 2: Lookup in memory FST (fast access for session data)
		// Memory mappings contain active session data, becomes nil after store sync
		if stringMappings.Count() > 0 && store.tempStringMappings != nil {

			// Iterate through in-memory string mappings
			store.tempStringMappings.Iter(func(value string, ordinal int32) (stop bool) {

				// Check if this ordinal is in our pending resolution list
				if indices, ok := stringMappings.Get(ordinal); ok {

					// Set the resolved value at all positions that reference this ordinal
					for _, index := range indices {

						values[index] = value
					}

					// Remove from pending mappings since it's resolved
					stringMappings.Delete(ordinal)
				}

				return stop
			})
		}

		// Tier 3: Lookup in persistent FST storage (good performance for all data)
		// This is the fallback for mappings not found in cache or memory
		if stringMappings.Count() > 0 && store.stringMapping != nil {

			// Create iterator for the persistent string mapping FST
			iterator, err = store.stringMapping.Iterator(nil, nil)

			// Iterate through all entries in the persistent mapping
			for err == nil {

				// Get current key-value pair from FST
				bytes, ordinal := iterator.Current()

				// Check if this ordinal is in our pending resolution list
				if indices, ok := stringMappings.Get(int32(ordinal)); ok {

					// Set the resolved value at all positions that reference this ordinal
					for _, index := range indices {

						values[index] = string(bytes)
					}

					// Remove from pending mappings since it's resolved
					stringMappings.Delete(int32(ordinal))
				}

				// Early termination if all mappings are resolved
				// This optimization avoids unnecessary FST iteration
				if stringMappings.Count() == 0 {

					err = nil

					break
				}

				// Move to next entry in the FST
				err = iterator.Next()
			}
		}
	}

	// Phase 3: Process numeric mappings if any exist
	// Numeric mappings are processed after string mappings to handle mixed data types
	if numericMappings.Count() > 0 {

		// Mark that we found mappings to resolve
		found = true

		// Handle mixed data type scenario (both string and numeric mappings exist)
		if dataType == codec.String {

			// Use existing string pool and convert numeric values to strings
			// This maintains consistency when both string and numeric data are present
			values := encoder.MemoryPool.GetStringPool(poolIndex)

			// Tier 1: Lookup numeric mappings in cache entries (fastest access)
			// Cache contains frequently accessed numeric mappings for performance
			if store.cacheNumericMappings != nil && store.cacheNumericMappings.Len() > 0 {

				// Iterate through numeric mappings that need resolution
				numericMappings.Iter(func(ordinal int32, indices []int) (stop bool) {

					// Check if this ordinal exists in the numeric cache
					if value, ok := store.cacheNumericMappings.Get(ordinal); ok {

						// Convert numeric value to string representation for mixed data type
						value := codec.INT64ToStringValue(value)

						// Set the resolved string value at all positions that reference this ordinal
						for _, index := range indices {

							values[index] = value
						}

						// Remove from pending mappings since it's resolved
						numericMappings.Delete(ordinal)

					}

					return stop
				})

			}

			// Tier 2: Lookup in memory FST for numeric mappings (fast access for session data)
			// Memory mappings contain active session data, becomes nil after store sync
			if numericMappings.Count() > 0 && store.tempNumericMappings != nil {

				// Iterate through all in-memory numeric mappings
				for value, ordinal := range store.tempNumericMappings.All() {

					// Check if this ordinal is in our pending resolution list
					if indices, ok := numericMappings.Get(ordinal); ok {

						// Convert numeric value to string for mixed data type consistency
						numericValue := codec.INT64ToStringValue(value)

						// Set the resolved string value at all positions that reference this ordinal
						for _, index := range indices {

							values[index] = numericValue
						}

						// Remove from pending mappings since it's resolved
						numericMappings.Delete(ordinal)
					}
				}
			}

			// Tier 3: Lookup in persistent FST storage for numeric mappings
			// This is the fallback for numeric mappings not found in cache or memory
			if numericMappings.Count() > 0 && store.numericMapping != nil {

				// Create iterator for the persistent numeric mapping FST
				iterator, err = store.numericMapping.Iterator(nil, nil)

				// Iterate through all entries in the persistent numeric mapping
				for err == nil {

					// Get current key-value pair from FST
					bytes, ordinal := iterator.Current()

					// Check if this ordinal is in our pending resolution list
					if indices, ok := numericMappings.Get(int32(ordinal)); ok {

						// Convert bytes to numeric value, then to string for consistency
						for _, index := range indices {

							values[index] = codec.INT64ToStringValue(codec.ReadBigEndianINT64Value(bytes))
						}

						// Remove from pending mappings since it's resolved
						numericMappings.Delete(int32(ordinal))
					}

					// Early termination if all mappings are resolved
					// This optimization avoids unnecessary FST iteration
					if numericMappings.Count() == 0 {

						err = nil

						break
					}

					// Move to next entry in the FST
					err = iterator.Next()
				}
			}
		} else {

			// Handle pure numeric data type scenario (no string mappings found)
			// Use numeric pool for optimal performance and memory usage
			valuePoolIndex, values := encoder.MemoryPool.AcquireINT64Pool(len(ordinals))

			// Critical: Reset the pool to zero values to avoid stale data
			// Scenario: Store has only numeric ordinals and we have set dummy string ordinal
			// in case of missing column. If we don't reset the pool, we will get previous
			// values set in the pool, leading to incorrect data resolution
			encoder.MemoryPool.ResetINT64Pool(valuePoolIndex, len(ordinals), 0)

			// Update pool index to point to the numeric pool
			poolIndex = valuePoolIndex

			// Tier 1: Lookup numeric mappings in cache entries (fastest access)
			// Cache contains frequently accessed numeric mappings for performance
			if store.cacheNumericMappings != nil && store.cacheNumericMappings.Len() > 0 {

				// Iterate through numeric mappings that need resolution
				numericMappings.Iter(func(ordinal int32, indices []int) (stop bool) {

					// Check if this ordinal exists in the numeric cache
					if value, ok := store.cacheNumericMappings.Get(ordinal); ok {

						// Set the resolved numeric value at all positions that reference this ordinal
						for _, index := range indices {

							values[index] = value
						}

						// Remove from pending mappings since it's resolved
						numericMappings.Delete(ordinal)

					}

					return stop
				})

			}

			// Tier 2: Lookup in memory FST for numeric mappings (fast access for session data)
			// Memory mappings contain active session data, becomes nil after store sync
			if numericMappings.Count() > 0 && store.tempNumericMappings != nil {

				// Iterate through all in-memory numeric mappings
				for value, ordinal := range store.tempNumericMappings.All() {

					// Check if this ordinal is in our pending resolution list
					if indices, ok := numericMappings.Get(ordinal); ok {

						// Set the resolved numeric value at all positions that reference this ordinal
						for _, index := range indices {

							values[index] = value
						}

						// Remove from pending mappings since it's resolved
						numericMappings.Delete(ordinal)
					}
				}
			}

			// Tier 3: Lookup in persistent FST storage for numeric mappings
			// This is the fallback for numeric mappings not found in cache or memory
			if numericMappings.Count() > 0 && store.numericMapping != nil {

				// Create iterator for the persistent numeric mapping FST
				iterator, err = store.numericMapping.Iterator(nil, nil)

				// Iterate through all entries in the persistent numeric mapping
				for err == nil {

					// Get current key-value pair from FST
					bytes, ordinal := iterator.Current()

					// Check if this ordinal is in our pending resolution list
					if indices, ok := numericMappings.Get(int32(ordinal)); ok {

						// Convert bytes to numeric value and set at all referencing positions
						for _, index := range indices {

							values[index] = codec.ReadBigEndianINT64Value(bytes)
						}

						// Remove from pending mappings since it's resolved
						numericMappings.Delete(int32(ordinal))
					}

					// Early termination if all mappings are resolved
					// This optimization avoids unnecessary FST iteration
					if numericMappings.Count() == 0 {

						err = nil

						break
					}

					// Move to next entry in the FST
					err = iterator.Next()
				}
			}
		}

	}

	// Handle case where all ordinals were reserved string mapping ordinals (dummy values)
	// This means the data contains only missing/empty string values
	if !found {

		// Acquire string pool for empty string values
		// Default to string type for missing data scenarios
		valuePoolIndex, _ := encoder.MemoryPool.AcquireStringPool(size)

		poolIndex = valuePoolIndex

		// Return string data type with empty pool for missing data handling
		return nil, codec.String, poolIndex
	}

	// Return the resolution results with determined data type and pool index
	return err, dataType, poolIndex
}

////////////////////////////////// String Mapping //////////////////////////////////////////

// writeStringMapping writes a string-to-ordinal mapping to the temporary mapping file and backup.
//
// This function implements the core string mapping write logic by:
// 1. Initializing memory-mapped temporary files for high-performance writes
// 2. Managing file size expansion through remapping operations
// 3. Writing to both temporary and backup files for data durability
// 4. Updating in-memory caches for immediate access
// 5. Handling memory pool management for efficient buffer usage
//
// The write process uses memory-mapped I/O for optimal performance and ensures
// data durability through dual-write operations to temporary and backup files.
//
// File Format (Version 2):
// - 4 bytes: Total record length (key length + key + ordinal + EOT)
// - 2 bytes: Key length
// - N bytes: Key data
// - 4 bytes: Ordinal value
// - 3 bytes: EOT marker
//
// Parameters:
//   - value: String value to map to the ordinal
//   - ordinal: Unique ordinal identifier for the string value
//   - store: Store instance containing mapping files and cache structures
//   - encoder: Encoder instance for memory pool management and data operations
//
// Returns:
//   - error: Any error encountered during the write operation
//
// Error handling:
// - Cleans up partially created files on initialization failure
// - Handles file expansion through remapping operations
// - Logs write errors for monitoring and debugging
func WriteStringMapping(value string, ordinal int32, store *Store, encoder codec.Encoder) error {

	var err error

	// Initialize temporary mapping file if this is the first write operation
	if store.stringMappingTempFile == nil {

		// Create temporary file for memory-mapped string mapping writes
		store.stringMappingTempFile, err = os.Create(store.path + utils.PathSeparator + FileTemp176)

		if err != nil {

			return errors.New(fmt.Sprintf("failed to create temp string mapping file for the store %v, reason:%v", store.name, err.Error()))

		}

		// Pre-allocate file space for better performance
		// Initial size: (header + value + ordinal) * growth factor
		_ = store.stringMappingTempFile.Truncate(int64(2+len(value)+4) * 4)

		// Memory-map the file for high-performance write operations
		store.tempStringMappingMMapBytes, err = Map(store.stringMappingTempFile, ReadWrite)

		if err != nil {

			// Clean up partially created resources on failure
			_ = store.stringMappingTempFile.Close()

			_ = os.Remove(store.path + utils.PathSeparator + FileTemp176)

			// Attempt to unmap if mapping was partially successful
			if store.tempStringMappingMMapBytes != nil {

				err1 := store.tempStringMappingMMapBytes.Unmap()

				if err1 != nil {

					err = errors.Join(err, err1)
				}

			}

			mappingLogger.Error(fmt.Sprintf("failed to create string mapping for the store %v, reason:%v", store.name, err.Error()))

			return errors.New(fmt.Sprintf("failed to create string mapping for the store %v, reason:%v", store.name, err.Error()))

		}

		// Initialize in-memory string mappings with reasonable initial capacity
		// Capacity of 500 balances memory usage with resize frequency
		store.tempStringMappings = swiss.NewMap[string, int32](500)
	} else {

		// Check if current memory-mapped file has sufficient space for new mapping
		// Required space: current size + header + value length + ordinal size
		if len(store.tempStringMappingMMapBytes) < store.stringMappingSize+(2+len(value)+4) {

			// Remap to larger size with growth factor for future writes
			// Growth factor of 4 reduces frequency of remapping operations
			store.tempStringMappingMMapBytes, err = Remap(store.tempStringMappingMMapBytes, (len(store.tempStringMappingMMapBytes)+2+len(value)+4)*4, store.stringMappingTempFile)

			if err != nil {

				// Clean up resources on remapping failure
				_ = store.stringMappingTempFile.Close()

				if store.tempStringMappingMMapBytes != nil {

					_ = store.tempStringMappingMMapBytes.Unmap()
				}

				mappingLogger.Error(fmt.Sprintf("failed to extend the string mappping for the store %v, reason:%v", store.name, err.Error()))

				return errors.New(fmt.Sprintf("failed to extend the string mappping for the store %v, reason:%v", store.name, err.Error()))

			}
		}
	}

	// Encode the string mapping item into the proper binary format
	index, encodedBytes := writeStringMappingItem(value, ordinal, encoder)

	// Write to backup file for durability and crash recovery
	// Continue operation even if backup write fails (logged for monitoring)
	if _, err = store.stringMappingBackupFile.Write(encodedBytes); err != nil {

		// Log error trace for backup write failure
		mappingLogger.Error(fmt.Sprintf("failed to write string mapping to backup file for store %v, reason: %v", store.name, err.Error()))
	}

	// Write to memory-mapped temporary file for immediate access
	// This provides high-performance writes for active session data
	copy(store.tempStringMappingMMapBytes[store.stringMappingSize:], encodedBytes)

	// Update the current size tracker for the memory-mapped file
	store.stringMappingSize += len(encodedBytes)

	// Add to in-memory mappings for fast session access
	// This provides the fastest lookup tier for recently added mappings
	store.tempStringMappings.Put(value, ordinal)

	// Add to cache if there's space available
	// Cache provides fast access for frequently used mappings across sessions
	if store.cacheStringMappings.Count()+1 <= store.maxMappingCacheRecords {

		store.cacheStringMappings.Put(ordinal, value)

	}

	// Release the memory pool buffer used for encoding
	// This prevents memory leaks and maintains pool efficiency
	encoder.MemoryPool.ReleaseBytePool(index)

	return err
}

// writeStringMappingItem encodes a string mapping entry into binary format for storage.
//
// This function implements the Version 2 string mapping format which provides:
// 1. Improved data integrity through length prefixing
// 2. Efficient parsing without string splitting operations
// 3. Better error detection and recovery capabilities
// 4. Forward compatibility for future format changes
//
// Format Evolution:
//
// Legacy Format (Version 1):
// - 2 bytes: Key length
// - N bytes: Key data
// - 8 bytes: Offset/value
// - 3 bytes: EOT marker
// - Parsing: Split by EOT bytes (inefficient and error-prone)
//
// Current Format (Version 2):
// - 4 bytes: Total record length (key length + key + ordinal)
// - 2 bytes: Key length
// - N bytes: Key data
// - 4 bytes: Ordinal value
// - 3 bytes: EOT marker
// - Parsing: Read length prefix, validate EOT (efficient and robust)
//
// The new format provides several advantages:
// - Length prefixing enables efficient sequential parsing
// - No string splitting required, improving performance
// - Better error detection through length validation
// - Atomic record validation through EOT checking
//
// Parameters:
//   - value: String value to encode
//   - ordinal: Ordinal identifier for the string value
//   - encoder: Encoder instance for memory pool management
//
// Returns:
//   - int: Memory pool index for the allocated buffer (must be released)
//   - []byte: Encoded binary data ready for storage
//
// Memory Management:
// - Acquires buffer from memory pool for efficiency
// - Caller must release the returned pool index
// - Buffer size calculated to fit exact record requirements
func writeStringMappingItem(value string, ordinal int32, encoder codec.Encoder) (int, []byte) {

	// Acquire buffer from memory pool with exact size requirements
	// Buffer layout: 4 (total length) + 2 (key length) + N (key) + 4 (ordinal) + 3 (EOT)
	index, bytes := encoder.MemoryPool.AcquireBytePool(4 + 2 + len(value) + 4 + 3)

	// Write total record length (excluding the length field itself)
	// This enables efficient parsing without scanning for delimiters
	bufferIndex, bufferBytes := encoder.WriteINT32Value(int32(2+len(value)+4), 0)

	copy(bytes, bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	// Write key length as 16-bit value (supports keys up to 65KB)
	// Using 16-bit length provides good balance between size and capacity
	bufferIndex, bufferBytes = encoder.WriteUINT16Value(uint16(len(value)), 0)

	copy(bytes[4:], bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	// Write the actual key data directly to the buffer
	// No encoding needed for string data
	copy(bytes[4+2:], value)

	// Write the ordinal value as 32-bit integer
	// This provides the mapping from string to ordinal identifier
	bufferIndex, bufferBytes = encoder.WriteINT32Value(ordinal, 0)

	copy(bytes[4+2+len(value):], bufferBytes)

	// Write End-of-Transaction marker for record validation
	// This enables integrity checking during parsing
	copy(bytes[len(bytes)-3:], utils.EOTBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	return index, bytes

}

// mergeStringMapping merges in-memory string mappings with persistent storage using FST.
//
// This function implements the core merge logic for string mappings by:
// 1. Converting in-memory mappings to a sorted FST structure
// 2. Merging with existing persistent mappings when appropriate
// 3. Atomically replacing the persistent mapping file
// 4. Cleaning up temporary files and memory structures
// 5. Handling different recovery modes for various scenarios
//
// The merge process ensures data consistency and optimal query performance
// by maintaining sorted FST structures for efficient range queries and lookups.
//
// Recovery Modes:
// - Normal: Standard merge operation with full cleanup
// - Partial: Merge from temporary files during recovery
// - Full: Complete rebuild from backup files
//
// Parameters:
//   - store: Store instance containing mapping data and file handles
//   - encoder: Encoder instance for memory pool management
//   - recoveryMode: Mode determining merge behavior and cleanup strategy
//
// Error handling:
// - Recovers from panics to prevent system crashes
// - Logs detailed error information for debugging
// - Continues operation even if individual steps fail
func mergeStringMapping(store *Store, encoder codec.Encoder, recoveryMode MappingRecoveryMode) {

	// Set up panic recovery for robust error handling
	// Merge operations are critical and failures need detailed logging
	defer func() {

		if err := recover(); err != nil {

			// Log error trace for merge failure with stack trace
			mappingLogger.Error(fmt.Sprintf("error %v occurred while merge string mapping for store %v", err, store.name))
		}
	}()

	// Only proceed if there are in-memory string mappings to merge
	if store.tempStringMappings != nil && store.tempStringMappings.Count() > 0 {

		// Clean up memory-mapped resources for normal operations
		// Skip cleanup in benchmark environments to avoid affecting performance tests
		if recoveryMode == Normal && utils.EnvironmentType != utils.DatastoreBenchUnitEnvironment {

			// Unmap memory-mapped file to release system resources
			err := store.tempStringMappingMMapBytes.Unmap()

			if err != nil {

				// Log fatal error for unmapping failure as it indicates serious system issues
				mappingLogger.Fatal(fmt.Sprintf("failed to unmap string mapping memory mapped file for store %v, reason : %v", store.name, err.Error()))
			}

			// Close temporary file handle
			_ = store.stringMappingTempFile.Close()

			// Reset file-related state variables
			store.tempStringMappingMMapBytes = nil

			store.stringMappingTempFile = nil

			store.stringMappingSize = 0
		}

		var buffer bytes2.Buffer

		tempMappingWriter, err := vellum.New(&buffer, nil)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to create the temp string mapping writer for the store %v, reason:%v", store.name, err.Error()))

			return
		}

		poolIndex := utils.NotAvailable

		var values []string

		if store.tempStringMappings.Count() > encoder.MemoryPool.GetPoolLength() {

			values = make([]string, store.tempStringMappings.Count(), store.tempStringMappings.Count())

		} else {

			poolIndex, values = encoder.MemoryPool.AcquireStringPool(store.tempStringMappings.Count())
		}

		i := 0

		store.tempStringMappings.Iter(func(value string, _ int32) (stop bool) {

			values[i] = value

			i++

			return stop
		})

		sort.Strings(values)

		for j := range values {

			value, _ := store.tempStringMappings.Get(values[j])

			_ = tempMappingWriter.Insert([]byte(values[j]), uint64(value))
		}

		if poolIndex != utils.NotAvailable {

			encoder.MemoryPool.ReleaseStringPool(poolIndex)
		}

		_ = tempMappingWriter.Close()

		store.tempStringMappings = nil

		if recoveryMode != Full && store.stringMapping != nil && store.stringMapping.Len() > 0 {

			iterators := make([]vellum.Iterator, 2)

			iterator, _ := store.stringMapping.Iterator(nil, nil)

			iterators[0] = iterator

			tempMapping, err := vellum.Load(buffer.Bytes())

			if err != nil {

				mappingLogger.Error(fmt.Sprintf("failed to load temp string mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}

			iterator, _ = tempMapping.Iterator(nil, nil)

			iterators[1] = iterator

			file, err := os.Create(store.path + utils.PathSeparator + FileMerged176)

			if err != nil {

				mappingLogger.Error(fmt.Sprintf("failed to create the merged string mapping file for the store %v, reason:%v", store.name, err.Error()))

				_ = tempMapping.Close()

				return
			}

			err = vellum.Merge(file, nil, iterators, func(values []uint64) uint64 {

				return values[0]
			})

			if err != nil {

				mappingLogger.Error(fmt.Sprintf(utils.ErrorMergeStringMapping, store.name, err.Error()))

				_ = file.Close()

				_ = os.Remove(store.path + utils.PathSeparator + FileMerged176)

				_ = tempMapping.Close()

				return
			}

			_ = file.Close()

			_ = tempMapping.Close()

			_ = store.stringMappingMMapBytes.Unmap()

			_ = store.stringMappingFile.Close()

			_ = store.stringMapping.Close()

			err = os.Rename(store.path+utils.PathSeparator+FileMerged176,
				store.path+utils.PathSeparator+FileMapping176)

			if err != nil {

				err = os.Remove(store.path + utils.PathSeparator + FileMerged176)

				mappingLogger.Error(fmt.Sprintf("failed to rename the merged string mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}

		} else {

			//closed previously opened file if any
			if store.stringMapping != nil {

				_ = store.stringMappingMMapBytes.Unmap()

				_ = store.stringMappingFile.Close()

				_ = store.stringMapping.Close()

			}

			err = os.WriteFile(store.path+utils.PathSeparator+FileMapping176, buffer.Bytes(), 0666)

			if err != nil {

				mappingLogger.Error(fmt.Sprintf("failed to write the string mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}
		}

		store.stringMappingFile, err = os.OpenFile(store.path+utils.PathSeparator+FileMapping176, os.O_RDWR, 0666)

		if err != nil {

			if !errors.Is(err, utils.ErrorFileNotFound) {

				mappingLogger.Error(fmt.Sprintf("failed to open the string mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}

		}

		if store.stringMappingFile != nil {

			store.stringMappingMMapBytes, err = Map(store.stringMappingFile, ReadWrite)

			if err != nil {

				_ = store.stringMappingFile.Close()

				mappingLogger.Error(fmt.Sprintf("failed to open the string mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}

			store.stringMapping, err = vellum.Load(store.stringMappingMMapBytes)

			if err != nil {

				_ = store.stringMappingMMapBytes.Unmap()

				_ = store.stringMappingFile.Close()

				mappingLogger.Error(fmt.Sprintf("failed to open the string mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}
		}

		_ = os.Remove(store.path + utils.PathSeparator + FileTemp176)

	}

}

func readStringMappingItem(bytes []byte) (string, int32) {

	index := 0

	if len(bytes) > 2 {

		length := codec.ReadUINT16Value(bytes[:2], &index)

		if len(bytes[index:]) == length+4 {

			return string(bytes[index : index+length]), codec.ReadINT32Value(bytes[index+length : index+length+4])

		}

	}

	return "", DummyStringMappingOrdinal

}

// rebuildStringMapping rebuilds string mappings from backup or temporary files during recovery.
//
// This function implements comprehensive string mapping recovery by:
// 1. Reading mapping data from appropriate recovery files
// 2. Parsing data using version-aware format handling
// 3. Reconstructing in-memory mapping structures
// 4. Merging recovered data with persistent storage
// 5. Handling format evolution between different versions
//
// The recovery process supports multiple scenarios:
// - Partial recovery: From temporary files after incomplete operations
// - Full recovery: From backup files after system crashes
// - Version migration: Handling legacy format data
//
// Recovery Modes:
// - Partial: Recover from temp.176 file (incomplete merge operation)
// - Full: Recover from bkp.176 file (system crash scenario)
//
// Parameters:
//   - store: Store instance containing mapping files and structures
//   - encoder: Encoder instance for memory pool management
//   - recoveryMode: Mode determining which files to use for recovery
//
// Returns:
//   - error: Any error encountered during the recovery process
//
// Error handling:
// - Recovers from panics to prevent system crashes during recovery
// - Cleans up partially recovered state on failure
// - Logs detailed error information with stack traces
// - Attempts backup reconstruction on full recovery failures
func rebuildStringMapping(store *Store, encoder codec.Encoder, recoveryMode MappingRecoveryMode) error {

	// Set up comprehensive panic recovery for critical recovery operations
	defer func() {

		if err := recover(); err != nil {

			// Handle cleanup based on recovery mode
			if recoveryMode == Partial {

				// Clean up partial recovery state
				if store.stringMappingTempFile != nil {

					_ = store.stringMappingTempFile.Close()

					store.stringMappingTempFile = nil
				}

				// Remove corrupted temporary file
				_ = os.Remove(store.path + utils.PathSeparator + FileTemp176)

			} else {

				// Clean up full recovery state
				if store.stringMappingBackupFile != nil {

					_ = store.stringMappingBackupFile.Close()

					store.stringMappingBackupFile = nil

				}

				// Remove corrupted backup file
				_ = os.Remove(store.path + utils.PathSeparator + FileBKP176)

				// Attempt to reconstruct backup from persistent storage
				constructStringMappingBackup(store, encoder)

			}

			// Log error trace for recovery failure
			mappingLogger.Error(fmt.Sprintf("error %v occurred while rebuilding string mapping for store %v", err, store.name))

			// Allocate buffer for stack trace capture (1MB should be sufficient)
			stackTraceBytes := make([]byte, 1<<20)

			// Log detailed stack trace for debugging the recovery failure
			mappingLogger.Error(fmt.Sprintf("!!!STACK TRACE for store %v while rebuilding string mapping!!! \n %v", store.name, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	var bytes []byte

	var err error

	if recoveryMode == Partial {

		bytes, err = os.ReadFile(store.path + utils.PathSeparator + FileTemp176)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open the temp string mapping for the store %v, reason:%v", store.name, err.Error()))

			return errors.New(fmt.Sprintf("failed to open the temp string mapping for the store %v, reason:%v", store.name, err.Error()))

		}

	} else {

		bytes, err = os.ReadFile(store.path + utils.PathSeparator + FileBKP176)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open the bkp string mapping for the store %v, reason:%v", store.name, err.Error()))

			return errors.New(fmt.Sprintf("failed to open the bkp string mapping for the store %v, reason:%v", store.name, err.Error()))

		}
	}

	store.tempStringMappings = swiss.NewMap[string, int32](500)

	// initially if store is opened and variant is not updated as we are not doing patch, we need to read the TEMP file with old manner
	if store.variant < version2 {

		bufferBytes := bytes2.Split(bytes, utils.EOTBytes)

		// if last buffers has eot then we get last tokens as an empty so avoid it or if buffers not end with eot means corrupted record so skip that last record

		for i := 0; i < len(bufferBytes)-1; i++ {

			value, ordinal := readStringMappingItem(bufferBytes[i])

			if len(value) > 0 {

				store.tempStringMappings.Put(value, ordinal)

			} else {

				break
			}
		}

	} else {

		position := int32(0)

		for position < int32(len(bytes)) && len(bytes[position:]) > 4 {

			length := codec.ReadINT32Value(bytes[position : position+4])

			position += 4

			if int32(len(bytes[position:])) >= length+3 && bytes2.Equal(bytes[position+length:position+length+3], utils.EOTBytes) {

				value, ordinal := readStringMappingItem(bytes[position : position+length])

				if len(value) > 0 {

					store.tempStringMappings.Put(value, ordinal)

					position += length + 3

				} else {

					break

				}
			} else {

				break

			}
		}
	}

	if store.tempStringMappings.Count() == 0 {

		return errors.New("failed to recover, reason: recovery file is empty.")
	}

	mergeStringMapping(store, encoder, recoveryMode)

	return nil

}

func getStringMapping(value string, store *Store) (bool, int32, error) {

	if store.tempStringMappings != nil && store.tempStringMappings.Count() > 0 {

		ordinal, found := store.tempStringMappings.Get(value)

		if found {

			return found, ordinal, nil

		}
	}

	if store.stringMapping != nil {

		ordinal, found, err := store.stringMapping.Get([]byte(value))

		if err != nil {

			return found, -1, nil
		}

		return found, int32(ordinal), nil
	}

	return false, -1, nil

}

func existStringMapping(store *Store, value string) (bool, int32) {

	found := false

	ordinal := int32(-1)

	if store.tempStringMappings != nil && store.tempStringMappings.Count() > 0 {

		ordinal, found = store.tempStringMappings.Get(value)
	}

	if !found && store.stringMapping != nil {

		var tempValue uint64

		tempValue, found, _ = store.stringMapping.Get([]byte(value))

		if found {

			ordinal = int32(tempValue)
		}

	}

	return found, ordinal

}

// mapStringValues maps an array of string values to their corresponding ordinal identifiers.
//
// This function implements efficient batch mapping of string values by:
// 1. Processing values through multi-tier lookup strategy
// 2. Identifying unmapped values that need new ordinal assignments
// 3. Handling empty/missing values with reserved dummy ordinals
// 4. Optimizing memory pool usage for large value arrays
// 5. Providing detailed tracking of mapping success and failures
//
// The mapping process follows a performance-optimized hierarchy:
// - Memory lookup: Fast access for session-active mappings
// - FST lookup: Persistent storage with good performance
// - Returns unmapped values for ordinal assignment by caller
//
// Parameters:
//   - store: Store instance containing string mapping data
//   - poolIndex: Memory pool index for the string value array
//   - encoder: Encoder instance for memory pool management
//   - size: Number of values to process (NotAvailable = use full array)
//
// Returns:
//   - int: Pool index for the ordinal array (must be released)
//   - int: Pool index for unmapped value indices (must be released, -1 if none)
//   - []int32: Array of ordinals corresponding to input values
//   - []int: Array of indices for values that need new ordinal assignments
//
// Error handling:
// - Recovers from panics to prevent system crashes
// - Logs detailed error information with stack traces
// - Continues processing even if individual lookups fail
func mapStringValues(store *Store, poolIndex int, encoder codec.Encoder, size int) (int, int, []int32, []int) {

	// Set up panic recovery for robust error handling
	// Mapping operations are critical and failures need detailed logging
	defer func() {

		if err := recover(); err != nil {

			// Allocate buffer for stack trace capture (1MB should be sufficient)
			stackTraceBytes := make([]byte, 1<<20)

			// Log error trace for mapping failure
			mappingLogger.Error(fmt.Sprintf("error %v occurred while mapping string values for store %v", err, store.name))

			// Log detailed stack trace for debugging the panic cause
			mappingLogger.Error(fmt.Sprintf("!!!STACK TRACE for map string values !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	// Get the string value array from memory pool
	values := encoder.MemoryPool.GetStringPool(poolIndex)

	// Slice to required size if specified, otherwise use full array
	if size != utils.NotAvailable {

		values = values[:size]
	}

	// Acquire pool for tracking indices of values that need mapping
	// This will contain positions of values not found in existing mappings
	missingItemPoolIndex, missingItems := encoder.MemoryPool.AcquireINTPool(len(values))

	defer encoder.MemoryPool.ReleaseINTPool(missingItemPoolIndex)

	// Track current position in missing items array
	index := 0

	// Acquire pool for ordinal results array
	// This will contain the final ordinal mappings for all input values
	ordinalPoolIndex, ordinals := encoder.MemoryPool.AcquireINT32Pool(len(values))

	if store.tempStringMappings != nil && store.tempStringMappings.Count() > 0 {

		for i := range values {

			if values[i] != utils.Empty {

				if ordinal, ok := store.tempStringMappings.Get(values[i]); ok {

					ordinals[i] = ordinal

					size--
				} else {

					missingItems[index] = i

					index++
				}
			} else {

				ordinals[i] = DummyStringMappingOrdinal // reserved

				size--
			}

		}
	}

	if size > 0 {

		if store.stringMapping != nil && store.stringMapping.Len() > 0 {

			if size == len(values) {

				unmappedPoolIndex, unmappedItems := encoder.MemoryPool.AcquireINTPool(size)

				index = 0

				for i := range values {

					if values[i] != utils.Empty {

						if ordinal, found, _ := store.stringMapping.Get([]byte(values[i])); found {

							ordinals[i] = int32(ordinal)

							size--
						} else {

							unmappedItems[index] = i

							index++
						}
					} else {

						ordinals[i] = DummyStringMappingOrdinal

						size--
					}

				}

				if index > 0 {

					return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems[:index]
				}

				encoder.MemoryPool.ReleaseINTPool(unmappedPoolIndex)

				return ordinalPoolIndex, -1, ordinals, nil

			} else {

				unmappedPoolIndex, unmappedItems := encoder.MemoryPool.AcquireINTPool(index)

				missingItems = missingItems[:index]

				index = 0

				for _, i := range missingItems {

					if ordinal, found, _ := store.stringMapping.Get([]byte(values[i])); found {

						ordinals[i] = int32(ordinal)

						size--
					} else {

						unmappedItems[index] = i

						index++
					}
				}

				if index > 0 {

					return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems[:index]
				}

				encoder.MemoryPool.ReleaseINTPool(unmappedPoolIndex)

				return ordinalPoolIndex, -1, ordinals, nil

			}

		} else {

			unmappedPoolIndex, unmappedItems := encoder.MemoryPool.AcquireINTPool(size)

			missingItems = missingItems[:size]

			index = 0

			if size == len(values) {

				for i := range values {

					unmappedItems[i] = i
				}

				return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems

			} else {

				for _, i := range missingItems {

					unmappedItems[index] = i

					index++
				}

				return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems[:index]

			}

		}

	}

	return ordinalPoolIndex, -1, ordinals, nil

}

func doPrefixMapping(store *Store, conditionValue string, mappings *swiss.Map[string, int32]) {

	if store.tempStringMappings != nil && store.tempStringMappings.Count() > 0 {

		store.tempStringMappings.Iter(func(value string, ordinal int32) (stop bool) {

			if strings.HasPrefix(strings.ToLower(value), conditionValue) {

				mappings.Put(value, ordinal)
			}

			return stop
		})
	}

	if store.stringMapping != nil {

		query, _ := regexp.New("(?i)" + conditionValue + ".*")

		iterator, err := store.stringMapping.Search(query, nil, nil)

		for err == nil {

			bytes, ordinal := iterator.Current()

			mappings.Put(string(bytes), int32(ordinal))

			err = iterator.Next()
		}

	}
}

func doAllPrefixMapping(store *Store, values utils.MotadataMap, mappings *swiss.Map[string, int32], tokenizer *utils.Tokenizer, separator string) {

	if store.tempStringMappings != nil && store.tempStringMappings.Count() > 0 {

		store.tempStringMappings.Iter(func(value string, ordinal int32) (stop bool) {

			utils.Split(value, separator, tokenizer)

			if _, ok := values[tokenizer.Tokens[0]]; ok {

				mappings.Put(value, ordinal)
			}

			return stop
		})
	}

	if store.stringMapping != nil {

		iterator, err := store.stringMapping.Iterator(nil, nil)

		for err == nil {

			bytes, ordinal := iterator.Current()

			value := string(bytes)

			utils.Split(value, separator, tokenizer)

			if _, ok := values[tokenizer.Tokens[0]]; ok {

				mappings.Put(value, int32(ordinal))
			}

			err = iterator.Next()
		}

	}
}

func doStringEqualMapping(store *Store, conditionValue string, mappings *swiss.Map[string, int32]) {

	if store.tempStringMappings != nil && store.tempStringMappings.Count() > 0 {

		store.tempStringMappings.Iter(func(value string, ordinal int32) (stop bool) {

			if strings.EqualFold(value, conditionValue) {

				mappings.Put(value, ordinal)
			}

			return stop
		})
	}

	if store.stringMapping != nil {

		query, _ := regexp.New("(?i)" + conditionValue)

		iterator, err := store.stringMapping.Search(query, nil, nil)

		for err == nil {

			bytes, ordinal := iterator.Current()

			mappings.Put(string(bytes), int32(ordinal))

			err = iterator.Next()
		}

	}
}

func doSuffixMapping(store *Store, conditionValue string, mappings *swiss.Map[string, int32]) {

	if store.tempStringMappings != nil && store.tempStringMappings.Count() > 0 {

		store.tempStringMappings.Iter(func(value string, ordinal int32) (stop bool) {

			if strings.HasSuffix(strings.ToLower(value), conditionValue) {

				mappings.Put(value, ordinal)
			}

			return stop
		})
	}

	if store.stringMapping != nil {

		query, _ := regexp.New("(?i).*" + conditionValue)

		iterator, err := store.stringMapping.Search(query, nil, nil)

		for err == nil {

			bytes, ordinal := iterator.Current()

			mappings.Put(string(bytes), int32(ordinal))

			err = iterator.Next()
		}
	}
}

func doContainMapping(store *Store, conditionValue string, mappings *swiss.Map[string, int32]) {

	if store.tempStringMappings != nil && store.tempStringMappings.Count() > 0 {

		store.tempStringMappings.Iter(func(value string, ordinal int32) (stop bool) {

			if strings.Contains(strings.ToLower(value), conditionValue) {

				mappings.Put(value, ordinal)
			}

			return stop
		})
	}

	if store.stringMapping != nil {

		query, _ := regexp.New("(?i).*" + conditionValue + ".*")

		iterator, err := store.stringMapping.Search(query, nil, nil)

		for err == nil {

			bytes, ordinal := iterator.Current()

			mappings.Put(string(bytes), int32(ordinal))

			err = iterator.Next()
		}

	}
}

func repairStringMappings(store *Store, usedMappingOrdinals *bitmap.Bitmap, encoder codec.Encoder, maxStringMappingOrdinal uint64) error {

	if store.stringMapping != nil {

		tempBackupFile, err := os.Create(store.path + utils.PathSeparator + FileBKPTemp176)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorCreateTempMappingBackup, store.name, err.Error()))

		}

		iterator, err := store.stringMapping.Iterator(nil, nil)

		if err != nil {

			return err
		}

		availableStringMappingOrdinals := &bitmap.Bitmap{}

		for err == nil {

			bytes, ordinal := iterator.Current()

			//ordinal >= maxStringMappingOrdinal means new ordinal is there

			if usedMappingOrdinals.Contains(uint32(ordinal)) || ordinal >= maxStringMappingOrdinal || (store.tempMappingOrdinals != nil && store.tempMappingOrdinals.Contains(uint32(ordinal))) {

				index, encodedBytes := writeStringMappingItem(string(bytes), int32(ordinal), encoder)

				if _, err = tempBackupFile.Write(encodedBytes); err != nil {

					mappingLogger.Error(fmt.Sprintf(utils.ErrorWriteStringMapping, err.Error()))

				}

				encoder.MemoryPool.ReleaseBytePool(index)
			} else {

				availableStringMappingOrdinals.Set(uint32(int32(ordinal)))
			}

			err = iterator.Next()
		}

		_ = tempBackupFile.Close()

		if store.stringMappingBackupFile != nil {

			_ = store.stringMappingBackupFile.Close()

		}

		if err = os.Rename(store.path+utils.PathSeparator+FileBKPTemp176, store.path+utils.PathSeparator+FileBKP176); err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorRenameTempMappingBackup, store.name, err.Error()))
		}

		if store.stringMappingBackupFile, err = os.OpenFile(store.path+utils.PathSeparator+FileBKP176, os.O_RDWR|os.O_APPEND, 0666); err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open bkp.176 file store : %s , reason : %s", store.name, err.Error()))
		}

		if err = rebuildStringMapping(store, encoder, Full); err != nil {

			return err
		}

		//if there is no error anywhere than resetting availableStringMappingOrdinals

		availableStringMappingOrdinals.Range(func(ordinal uint32) {

			store.availableStringMappingOrdinals.Set(ordinal)
		})

		store.cacheStringMappings.Clear()

	}

	return nil
}

////////////////////////////////// Numeric Mapping //////////////////////////////////////////

// existNumericMapping checks if a numeric value already has an ordinal mapping.
//
// This function implements a multi-tier lookup strategy for numeric mappings:
// 1. Fast lookup in in-memory mappings for session data
// 2. Persistent lookup in FST storage for historical data
// 3. Efficient big-endian encoding for FST key compatibility
//
// The lookup process is optimized for performance by checking the fastest
// access tier first and falling back to persistent storage only when needed.
//
// Parameters:
//   - store: Store instance containing numeric mapping data
//   - value: Numeric value to search for
//   - encoder: Encoder instance for memory pool management and data operations
//
// Returns:
//   - bool: Whether the numeric value was found in mappings
//   - int32: Ordinal identifier for the value (if found)
//
// Performance considerations:
// - Memory lookup: O(1) hash table access
// - FST lookup: O(log n) tree traversal
// - Big-endian encoding ensures proper FST key ordering
func existNumericMapping(store *Store, value int64, encoder codec.Encoder) (bool, int32) {

	// Initialize return values
	found := false

	ordinal := int32(-1)

	// Tier 1: Check in-memory numeric mappings (fastest access)
	// This contains active session data and recently accessed mappings
	if store.tempNumericMappings != nil && store.tempNumericMappings.Len() > 0 {

		// Direct hash table lookup for O(1) performance
		ordinal, found = store.tempNumericMappings.Get(value)
	}

	// Tier 2: Check persistent FST storage if not found in memory
	// This provides access to all historical numeric mappings
	if !found && store.numericMapping != nil {

		// Acquire buffer for big-endian encoding of the numeric value
		// Big-endian encoding ensures proper lexicographic ordering in FST
		index, bytes := encoder.MemoryPool.AcquireBytePool(8)

		// Encode value in big-endian format for FST key compatibility
		codec.WriteBigEndianINT64Value(value, 0, bytes)

		var tempValue uint64

		// Perform FST lookup using encoded key
		tempValue, found, _ = store.numericMapping.Get(bytes)

		// Release the buffer back to memory pool
		encoder.MemoryPool.ReleaseBytePool(index)

		// Convert FST value to ordinal if found
		if found {

			ordinal = int32(tempValue)
		}
	}

	return found, ordinal

}

// writeNumericMapping writes a numeric-to-ordinal mapping to temporary and backup files.
//
// This function implements the core numeric mapping write logic by:
// 1. Initializing memory-mapped temporary files for high-performance writes
// 2. Managing file size expansion through remapping operations
// 3. Writing to both temporary and backup files for data durability
// 4. Updating in-memory caches and mappings for immediate access
// 5. Using fixed-size encoding for optimal FST performance
//
// The write process uses memory-mapped I/O for optimal performance and ensures
// data durability through dual-write operations to temporary and backup files.
//
// Numeric Mapping Format:
// - 8 bytes: Big-endian encoded numeric value (for FST key ordering)
// - 4 bytes: Ordinal identifier
// - 3 bytes: EOT marker for record validation
//
// Parameters:
//   - value: Numeric value to map to the ordinal
//   - ordinal: Unique ordinal identifier for the numeric value
//   - store: Store instance containing mapping files and cache structures
//   - encoder: Encoder instance for memory pool management and data operations
//
// Returns:
//   - error: Any error encountered during the write operation
//
// Error handling:
// - Cleans up partially created files on initialization failure
// - Handles file expansion through remapping operations
// - Logs write errors for monitoring and debugging
func writeNumericMapping(value int64, ordinal int32, store *Store, encoder codec.Encoder) error {

	var err error

	if store.numericMappingTempFile == nil {

		store.numericMappingTempFile, err = os.Create(store.path + utils.PathSeparator + FileTemp112)

		if err != nil {

			return errors.New(fmt.Sprintf("failed to create temp numeric mapping file for the store %v, reason:%v", store.name, err.Error()))

		}

		_ = store.numericMappingTempFile.Truncate(int64(12 * 4))

		store.tempNumericMappingMMapBytes, err = Map(store.numericMappingTempFile, ReadWrite)

		if err != nil {

			_ = store.numericMappingTempFile.Close()

			_ = os.Remove(store.path + utils.PathSeparator + FileTemp112)

			if store.tempNumericMappingMMapBytes != nil {

				_ = store.tempNumericMappingMMapBytes.Unmap()
			}

			mappingLogger.Error(fmt.Sprintf("failed to create numeric mapping for the store %v, reason:%v", store.name, err.Error()))

			return errors.New(fmt.Sprintf("failed to create numeric mapping for the store %v, reason:%v", store.name, err.Error()))

		}

		store.tempNumericMappings = intmap.New[int64, int32](5000)
	} else {

		if len(store.tempNumericMappingMMapBytes) < store.numericMappingSize+12 {

			store.tempNumericMappingMMapBytes, err = Remap(store.tempNumericMappingMMapBytes, (len(store.tempNumericMappingMMapBytes)+12)*4, store.numericMappingTempFile)

			if err != nil {

				_ = store.numericMappingTempFile.Close()

				if store.tempNumericMappingMMapBytes != nil {

					_ = store.tempNumericMappingMMapBytes.Unmap()
				}

				mappingLogger.Error(fmt.Sprintf("failed to extend the numeric mappping for the store %v, reason:%v", store.name, err.Error()))

				return errors.New(fmt.Sprintf("failed to extend the numeric mappping for the store %v, reason:%v", store.name, err.Error()))

			}
		}
	}

	index, encodedBytes := writeNumericMappingItem(value, ordinal, encoder)

	if _, err = store.numericMappingBackupFile.Write(encodedBytes); err != nil {

		mappingLogger.Error(fmt.Sprintf(utils.ErrorWriteNumericMapping, err.Error()))
	}

	copy(store.tempNumericMappingMMapBytes[store.numericMappingSize:], encodedBytes)

	store.numericMappingSize += len(encodedBytes)

	store.tempNumericMappings.Put(value, ordinal)

	encoder.MemoryPool.ReleaseBytePool(index)

	if store.cacheNumericMappings.Len()+1 <= store.maxMappingCacheRecords {

		store.cacheNumericMappings.Put(ordinal, value)

	}

	return err
}

func writeNumericMappingItem(value int64, ordinal int32, encoder codec.Encoder) (int, []byte) {

	index, bytes := encoder.MemoryPool.AcquireBytePool(8 + 4 + 3)

	codec.WriteINT64Value(value, 0, bytes)

	codec.WriteINT32Value(ordinal, 8, bytes)

	copy(bytes[4+8:], utils.EOTBytes)

	return index, bytes

}

func mergeNumericMapping(store *Store, encoder codec.Encoder, recoveryMode MappingRecoveryMode) {

	defer func() {

		if err := recover(); err != nil {

			mappingLogger.Error(fmt.Sprintf("error %v occurred while merge numeric mapping for store %v", err, store.name))
		}
	}()

	if store.tempNumericMappings != nil && store.tempNumericMappings.Len() > 0 {

		if recoveryMode == Normal {

			err := store.tempNumericMappingMMapBytes.Unmap()

			if err != nil {

				mappingLogger.Fatal(fmt.Sprintf("failed to unmap numeric mapping memory mapped file for store %v, reason : %v", store.name, err.Error()))
			}

			err = store.numericMappingTempFile.Close()

			store.tempNumericMappingMMapBytes = nil

			store.numericMappingTempFile = nil

			store.numericMappingSize = 0
		}

		var buffer bytes2.Buffer

		tempMappingWriter, err := vellum.New(&buffer, nil)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to create the temp numeric mapping writer for the store %v, reason:%v", store.name, err.Error()))

			return
		}

		poolIndex := utils.NotAvailable

		var values []int64

		if store.tempNumericMappings.Len() > encoder.MemoryPool.GetPoolLength() {

			values = make([]int64, store.tempNumericMappings.Len(), store.tempNumericMappings.Len())

		} else {

			poolIndex, values = encoder.MemoryPool.AcquireINT64Pool(store.tempNumericMappings.Len())
		}

		i := 0

		for value := range store.tempNumericMappings.Keys() {

			values[i] = value

			i++
		}

		utils.SortINT64Values(values)

		index, valueBytes := encoder.MemoryPool.AcquireBytePool(8)

		defer encoder.MemoryPool.ReleaseBytePool(index)

		for j := range values {

			codec.WriteBigEndianINT64Value(values[j], 0, valueBytes)

			value, _ := store.tempNumericMappings.Get(values[j])

			_ = tempMappingWriter.Insert(valueBytes, uint64(value))
		}

		if poolIndex != utils.NotAvailable {

			encoder.MemoryPool.ReleaseINT64Pool(poolIndex)
		}

		_ = tempMappingWriter.Close()

		store.tempNumericMappings = nil

		if recoveryMode != Full && store.numericMapping != nil && store.numericMapping.Len() > 0 {

			iterators := make([]vellum.Iterator, 2)

			iterator, _ := store.numericMapping.Iterator(nil, nil)

			iterators[0] = iterator

			tempMapping, err := vellum.Load(buffer.Bytes())

			if err != nil {

				mappingLogger.Error(fmt.Sprintf("failed to load temp numeric mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}

			iterator, _ = tempMapping.Iterator(nil, nil)

			iterators[1] = iterator

			file, err := os.Create(store.path + utils.PathSeparator + FileMerged112)

			if err != nil {

				mappingLogger.Error(fmt.Sprintf("failed to create the merged numeric mapping file for the store %v, reason:%v", store.name, err.Error()))

				_ = tempMapping.Close()

				return
			}

			err = vellum.Merge(file, nil, iterators, func(values []uint64) uint64 {

				return values[0]
			})

			if err != nil {

				mappingLogger.Error(fmt.Sprintf(utils.ErrorMergeNumericMapping, store.name, err.Error()))

				_ = file.Close()

				_ = os.Remove(store.path + utils.PathSeparator + FileMerged112)

				_ = tempMapping.Close()

				return
			}

			_ = file.Close()

			_ = tempMapping.Close()

			_ = store.numericMappingMMapBytes.Unmap()

			_ = store.numericMappingFile.Close()

			_ = store.numericMapping.Close()

			err = os.Rename(store.path+utils.PathSeparator+FileMerged112,
				store.path+utils.PathSeparator+FileMapping112)

			if err != nil {

				err = os.Remove(store.path + utils.PathSeparator + FileMerged112)

				mappingLogger.Error(fmt.Sprintf("failed to rename the merged numeric mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}

		} else {

			//closed previously opened file if any
			if store.numericMapping != nil {

				_ = store.numericMappingMMapBytes.Unmap()

				_ = store.numericMappingFile.Close()

				_ = store.numericMapping.Close()

			}

			err = os.WriteFile(store.path+utils.PathSeparator+FileMapping112, buffer.Bytes(), 0666)

			if err != nil {

				mappingLogger.Error(fmt.Sprintf("failed to write the numeric mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}
		}

		store.numericMappingFile, err = os.OpenFile(store.path+utils.PathSeparator+FileMapping112, os.O_RDWR, 0666)

		if err != nil {

			if !errors.Is(err, utils.ErrorFileNotFound) {

				mappingLogger.Error(fmt.Sprintf("failed to open the numeric mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}

		}

		if store.numericMappingFile != nil {

			store.numericMappingMMapBytes, err = Map(store.numericMappingFile, ReadWrite)

			if err != nil {

				_ = store.numericMappingFile.Close()

				mappingLogger.Error(fmt.Sprintf("failed to open the numeric mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}

			store.numericMapping, err = vellum.Load(store.numericMappingMMapBytes)

			if err != nil {

				_ = store.numericMappingMMapBytes.Unmap()

				_ = store.numericMappingFile.Close()

				mappingLogger.Error(fmt.Sprintf("failed to open the numeric mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}
		}

		_ = os.Remove(store.path + utils.PathSeparator + FileTemp112)

	}

}

func readNumericMappingItem(bytes []byte) (int64, int32) {

	return codec.ReadINT64Value(bytes[:8]), codec.ReadINT32Value(bytes[8:12])

}

func rebuildNumericMapping(store *Store, encoder codec.Encoder, recoveryMode MappingRecoveryMode) error {

	defer func() {

		if err := recover(); err != nil {

			if recoveryMode == Partial {

				if store.numericMappingTempFile != nil {

					_ = store.numericMappingTempFile.Close()

					store.numericMappingTempFile = nil
				}

				_ = os.Remove(store.path + utils.PathSeparator + FileTemp112)

			} else {

				if store.numericMappingBackupFile != nil {

					_ = store.numericMappingBackupFile.Close()

					store.numericMappingBackupFile = nil

				}

				_ = os.Remove(store.path + utils.PathSeparator + FileBKP112)

				constructNumericMappingBackup(store, encoder)

			}

			mappingLogger.Warn(fmt.Sprintf("error %v occurred while rebuilding numeric mapping for store %v", err, store.name))

			stackTraceBytes := make([]byte, 1<<20)

			mappingLogger.Error(fmt.Sprintf("!!!STACK TRACE for store %v while rebuilding numeric mapping!!! \n %v", store.name, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	var bytes []byte

	var err error

	if recoveryMode == Partial {

		bytes, err = os.ReadFile(store.path + utils.PathSeparator + FileTemp112)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open the temp numeric mapping for the store %v, reason:%v", store.name, err.Error()))

			return errors.New(fmt.Sprintf("failed to open the temp numeric mapping for the store %v, reason:%v", store.name, err.Error()))

		}
	} else {

		bytes, err = os.ReadFile(store.path + utils.PathSeparator + FileBKP112)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open the bkp numeric mapping for the store %v, reason:%v", store.name, err.Error()))

			return errors.New(fmt.Sprintf("failed to open the bkp numeric mapping for the store %v, reason:%v", store.name, err.Error()))

		}
	}

	store.tempNumericMappings = intmap.New[int64, int32](5000)

	//We have not changed writing format in case of numeric mappings so no version check is needed
	//for numeric mappings at every 12 bytes there should be EOT bytes if it is not there than record is corrupted

	position := int32(0)

	for position < int32(len(bytes)) {

		if int32(len(bytes[position:])) >= 12+3 && bytes2.Equal(bytes[position+12:position+12+3], utils.EOTBytes) {

			value, ordinal := readNumericMappingItem(bytes[position : position+12])

			store.tempNumericMappings.Put(value, ordinal)

			position += 12 + 3

		} else {

			break
		}

	}

	if store.tempNumericMappings.Len() == 0 {

		return errors.New("failed to recover, reason: recovery file is empty.")
	}

	mergeNumericMapping(store, encoder, recoveryMode)

	return nil

}

func getNumericMapping(value int64, store *Store, encoder codec.Encoder) (bool, int32, error) {

	if store.tempNumericMappings != nil && store.tempNumericMappings.Len() > 0 {

		ordinal, found := store.tempNumericMappings.Get(value)

		if found {

			return found, ordinal, nil

		}
	}

	if store.numericMapping != nil {

		index, bytes := encoder.MemoryPool.AcquireBytePool(8)

		codec.WriteBigEndianINT64Value(value, 0, bytes)

		ordinal, found, err := store.numericMapping.Get(bytes)

		encoder.MemoryPool.ReleaseBytePool(index)

		if err != nil {

			return found, -1, err
		}

		return found, int32(ordinal), nil
	}

	return false, -1, nil

}

func mapNumericValues(store *Store, poolIndex int, encoder codec.Encoder, size int) (int, int, []int32, []int) {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			mappingLogger.Error(fmt.Sprintf("error %v occurred while mapping numeric values for store %v", err, store.name))

			mappingLogger.Error(fmt.Sprintf("!!!STACK TRACE for map numeric values !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	values := encoder.MemoryPool.GetINT64Pool(poolIndex)

	values = values[:size]

	missingPoolIndex, missingItems := encoder.MemoryPool.AcquireINTPool(len(values))

	defer encoder.MemoryPool.ReleaseINTPool(missingPoolIndex)

	index := 0

	ordinalPoolIndex, ordinals := encoder.MemoryPool.AcquireINT32Pool(len(values))

	if store.tempNumericMappings != nil && store.tempNumericMappings.Len() > 0 {

		for i := range values {

			if ordinal, ok := store.tempNumericMappings.Get(values[i]); ok {

				ordinals[i] = ordinal

				size--
			} else {

				missingItems[index] = i

				index++
			}
		}
	}

	if size > 0 {

		if store.numericMapping != nil && store.numericMapping.Len() > 0 {

			if size == len(values) {

				unmappedPoolIndex, unmappedItems := encoder.MemoryPool.AcquireINTPool(size)

				index = 0

				bufferPoolIndex, bytes := encoder.MemoryPool.AcquireBytePool(8)

				defer encoder.MemoryPool.ReleaseBytePool(bufferPoolIndex)

				for i := range values {

					codec.WriteBigEndianINT64Value(values[i], 0, bytes)

					if ordinal, found, _ := store.numericMapping.Get(bytes); found {

						ordinals[i] = int32(ordinal)

						size--
					} else {

						unmappedItems[index] = i

						index++
					}
				}

				if index > 0 {

					return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems[:index]
				}

				encoder.MemoryPool.ReleaseINTPool(unmappedPoolIndex)

				return ordinalPoolIndex, -1, ordinals, nil

			} else {

				unmappedPoolIndex, unmappedItems := encoder.MemoryPool.AcquireINTPool(index)

				missingItems = missingItems[:index]

				index = 0

				bufferPoolIndex, bytes := encoder.MemoryPool.AcquireBytePool(8)

				defer encoder.MemoryPool.ReleaseBytePool(bufferPoolIndex)

				for _, i := range missingItems {

					codec.WriteBigEndianINT64Value(values[i], 0, bytes)

					if ordinal, found, _ := store.numericMapping.Get(bytes); found {

						ordinals[i] = int32(ordinal)

						size--
					} else {

						unmappedItems[index] = i

						index++
					}
				}

				if index > 0 {

					return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems[:index]
				}

				encoder.MemoryPool.ReleaseINTPool(unmappedPoolIndex)

				return ordinalPoolIndex, -1, ordinals, nil
			}
		} else {

			unmappedPoolIndex, unmappedItems := encoder.MemoryPool.AcquireINTPool(size)

			missingItems = missingItems[:size]

			index = 0

			if size == len(values) {

				for i := range values {

					unmappedItems[i] = i
				}

				return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems

			} else {

				for _, i := range missingItems {

					unmappedItems[index] = i

					index++
				}

				return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems[:index]

			}

		}

	}

	return ordinalPoolIndex, -1, ordinals, nil

}

func repairNumericMappings(store *Store, usedMappingOrdinals *bitmap.Bitmap, encoder codec.Encoder, maxNumericMappingsOrdinal uint64) error {

	if store.numericMapping != nil {

		tempBackupFile, err := os.Create(store.path + utils.PathSeparator + FileBKPTemp112)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorCreateTempMappingBackup, store.name, err.Error()))

		}

		iterator, err := store.numericMapping.Iterator(nil, nil)

		if err != nil {

			return err
		}

		availableNumericMappingOrdinals := &bitmap.Bitmap{}

		for err == nil {

			bytes, ordinal := iterator.Current()

			if usedMappingOrdinals.Contains(uint32(ordinal)) || ordinal >= maxNumericMappingsOrdinal || (store.tempMappingOrdinals != nil && store.tempMappingOrdinals.Contains(uint32(ordinal))) {

				index, encodedBytes := writeNumericMappingItem(codec.ReadBigEndianINT64Value(bytes), int32(ordinal), encoder)

				if _, err = tempBackupFile.Write(encodedBytes); err != nil {

					mappingLogger.Error(fmt.Sprintf(utils.ErrorWriteNumericMapping, err.Error()))
				}

				encoder.MemoryPool.ReleaseBytePool(index)
			} else {

				availableNumericMappingOrdinals.Set(uint32(ordinal))
			}

			err = iterator.Next()
		}

		_ = tempBackupFile.Close()

		if store.numericMappingBackupFile != nil {

			_ = store.numericMappingBackupFile.Close()

		}

		if err = os.Rename(store.path+utils.PathSeparator+FileBKPTemp112, store.path+utils.PathSeparator+FileBKP112); err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorRenameTempMappingBackup, store.name, err.Error()))
		}

		if store.numericMappingBackupFile, err = os.OpenFile(store.path+utils.PathSeparator+FileBKP112, os.O_RDWR|os.O_APPEND, 0666); err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open bkp.112 file store : %s , reason : %s", store.name, err.Error()))
		}

		if err = rebuildNumericMapping(store, encoder, Full); err != nil {

			return err
		}

		availableNumericMappingOrdinals.Range(func(ordinal uint32) {

			store.availableNumericMappingOrdinals.Set(ordinal)

		})

		store.cacheNumericMappings.Clear()

	}

	return nil
}

func listStringMappings(store *Store, stringMappings utils.MotadataMap) {

	if store.tempStringMappings != nil {

		store.tempStringMappings.Iter(func(key string, _ int32) (stop bool) {

			stringMappings[key] = struct{}{}

			return stop
		})
	}

	if store.stringMapping != nil {

		iterator, err := store.stringMapping.Iterator(nil, nil)

		for err == nil {

			bytes, _ := iterator.Current()

			stringMappings[string(bytes)] = struct{}{}

			err = iterator.Next()
		}
	}
}

func doGreaterThanMapping(store *Store, conditionValue int64, inclusive bool, mappings *swiss.Map[int64, int32], encoder codec.Encoder) {

	if store.tempNumericMappings != nil && store.tempNumericMappings.Len() > 0 {

		if inclusive {

			for value, ordinal := range store.tempNumericMappings.All() {

				if value >= conditionValue {

					mappings.Put(value, ordinal)

				}
			}
		} else {

			for value, ordinal := range store.tempNumericMappings.All() {

				if value > conditionValue {

					mappings.Put(value, ordinal)

				}
			}
		}
	}

	if store.numericMapping != nil {

		poolIndex, bytes := encoder.MemoryPool.AcquireBytePool(8)

		defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

		if inclusive {

			codec.WriteBigEndianINT64Value(conditionValue, 0, bytes)

			iterator, err := store.numericMapping.Iterator(bytes, nil)

			for err == nil {

				bytes, ordinal := iterator.Current()

				mappings.Put(codec.ReadBigEndianINT64Value(bytes), int32(ordinal))

				err = iterator.Next()
			}

		} else if !inclusive {

			codec.WriteBigEndianINT64Value(conditionValue+1, 0, bytes)

			iterator, err := store.numericMapping.Iterator(bytes, nil)

			for err == nil {

				bytes, ordinal := iterator.Current()

				mappings.Put(codec.ReadBigEndianINT64Value(bytes), int32(ordinal))

				err = iterator.Next()
			}
		}

	}
}

func doLessThanMapping(store *Store, conditionValue int64, inclusive bool, mappings *swiss.Map[int64, int32], encoder codec.Encoder) {

	if store.tempNumericMappings != nil && store.tempNumericMappings.Len() > 0 {

		if inclusive {

			for value, ordinal := range store.tempNumericMappings.All() {

				if value <= conditionValue {

					mappings.Put(value, ordinal)

				}
			}

		} else {

			for value, ordinal := range store.tempNumericMappings.All() {

				if value < conditionValue {

					mappings.Put(value, ordinal)

				}
			}
		}
	}

	if store.numericMapping != nil {

		poolIndex, bytes := encoder.MemoryPool.AcquireBytePool(8)

		defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

		if inclusive {

			codec.WriteBigEndianINT64Value(conditionValue+1, 0, bytes)

			iterator, err := store.numericMapping.Iterator(nil, bytes)

			for err == nil {

				bytes, ordinal := iterator.Current()

				mappings.Put(codec.ReadBigEndianINT64Value(bytes), int32(ordinal))

				err = iterator.Next()
			}

		} else if !inclusive {

			codec.WriteBigEndianINT64Value(conditionValue, 0, bytes)

			iterator, err := store.numericMapping.Iterator(nil, bytes)

			for err == nil {

				bytes, ordinal := iterator.Current()

				mappings.Put(codec.ReadBigEndianINT64Value(bytes), int32(ordinal))

				err = iterator.Next()
			}
		}

	}
}

func updateCacheNumericMapping(store *Store) error {

	if store.numericMapping != nil {

		store.cacheNumericMappings.Clear()

		iterator, err := store.numericMapping.Iterator(nil, nil)

		if err != nil {

			return err
		}

		for err == nil {

			key, ordinal := iterator.Current()

			if store.cacheNumericMappings.Len()+1 <= store.maxMappingCacheRecords {

				store.cacheNumericMappings.Put(int32(ordinal), codec.ReadBigEndianINT64Value(key))
			}

			err = iterator.Next()
		}

	}

	return nil
}

func updateCacheStringMapping(store *Store) error {

	if store.stringMapping != nil {

		store.cacheStringMappings.Clear()

		iterator, err := store.stringMapping.Iterator(nil, nil)

		if err != nil {

			return err
		}

		for err == nil {

			key, ordinal := iterator.Current()

			if store.cacheStringMappings.Count()+1 <= store.maxMappingCacheRecords {

				store.cacheStringMappings.Put(int32(ordinal), string(key))
			}

			err = iterator.Next()
		}

	}

	return nil
}

// constructStringMappingBackup creates a backup file from persistent string mappings.
//
// This function implements backup file construction by:
// 1. Reading all entries from the persistent string mapping FST
// 2. Encoding each entry in the current backup format
// 3. Writing to a temporary backup file for atomic updates
// 4. Atomically replacing the existing backup file
// 5. Clearing caches to ensure consistency
//
// The backup construction process ensures data durability and provides
// a recovery mechanism for system crashes or data corruption scenarios.
//
// Backup File Purpose:
// - Crash recovery: Restore mappings after unexpected shutdowns
// - Data integrity: Verify mapping consistency during startup
// - Migration support: Handle format changes between versions
//
// Parameters:
//   - store: Store instance containing persistent mapping data
//   - encoder: Encoder instance for memory pool management and data operations
//
// Error handling:
// - Recovers from panics to prevent system crashes during backup
// - Logs detailed error information with stack traces
// - Continues operation even if backup creation fails
// - Cleans up temporary files on failure
func constructStringMappingBackup(store *Store, encoder codec.Encoder) {

	// Set up panic recovery for robust error handling
	// Backup operations should not crash the system
	defer func() {

		if r := recover(); r != nil {

			// Allocate buffer for stack trace capture (1MB should be sufficient)
			stackTraceBytes := make([]byte, 1<<20)

			// Log error trace for backup construction failure
			mappingLogger.Error(fmt.Sprintf("error %v occurred while rebuilding string mapping for store %v", r, store.name))

			// Log detailed stack trace for debugging the panic cause
			mappingLogger.Error(fmt.Sprintf("!!!STACK TRACE for store %v while rebuilding string mapping !!! \n %v", store.name, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

	}()

	// Only proceed if persistent string mapping exists
	if store.stringMapping != nil {

		// Clean up any existing temporary backup file
		if err := os.RemoveAll(store.path + utils.PathSeparator + FileBKPTemp176); err != nil {

			// Log error trace for cleanup failure
			mappingLogger.Error(fmt.Sprintf("failed to remove the temp mapping backup file store : %s , reason : %s", store.name, err.Error()))

		}

		// Create temporary backup file for atomic backup updates
		tempBackupFile, err := os.Create(store.path + utils.PathSeparator + FileBKPTemp176)

		if err != nil {

			// Log error trace for backup file creation failure
			mappingLogger.Error(fmt.Sprintf(utils.ErrorCreateTempMappingBackup, store.name, err.Error()))

		}

		iterator, err := store.stringMapping.Iterator(nil, nil)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to create iterator of FST for store %v while building backup file , reason : %s", store.name, err.Error()))
		}

		for err == nil {

			bytes, ordinal := iterator.Current()

			index, encodedBytes := writeStringMappingItem(string(bytes), int32(ordinal), encoder)

			if _, err = tempBackupFile.Write(encodedBytes); err != nil {

				mappingLogger.Error(fmt.Sprintf(utils.ErrorWriteStringMapping, err.Error()))

			}

			encoder.MemoryPool.ReleaseBytePool(index)

			err = iterator.Next()
		}

		_ = tempBackupFile.Close()

		if store.stringMappingBackupFile != nil {

			_ = store.stringMappingBackupFile.Close()

		}

		if err = os.Rename(store.path+utils.PathSeparator+FileBKPTemp176, store.path+utils.PathSeparator+FileBKP176); err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorRenameTempMappingBackup, store.name, err.Error()))
		}

		if store.stringMappingBackupFile, err = os.OpenFile(store.path+utils.PathSeparator+FileBKP176, os.O_RDWR|os.O_APPEND, 0666); err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open bkp.176 file store : %s , reason : %s", store.name, err.Error()))
		}

		store.cacheStringMappings.Clear()
	}
}

func constructNumericMappingBackup(store *Store, encoder codec.Encoder) {

	defer func() {

		if r := recover(); r != nil {

			stackTraceBytes := make([]byte, 1<<20)

			mappingLogger.Error(fmt.Sprintf("error %v occurred while rebuilding numeric mapping for store %v", r, store.name))

			mappingLogger.Error(fmt.Sprintf("!!!STACK TRACE for store %v while rebuilding numeric mapping !!! \n %v", store.name, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

	}()

	if store.numericMapping != nil {

		if err := os.RemoveAll(store.path + utils.PathSeparator + FileBKPTemp112); err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to remove the temp mapping backup file store : %s , reason : %s", store.name, err.Error()))

		}

		tempBackupFile, err := os.Create(store.path + utils.PathSeparator + FileBKPTemp112)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorCreateTempMappingBackup, store.name, err.Error()))

		}

		iterator, err := store.numericMapping.Iterator(nil, nil)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to create iterator of FST for store %v while building backup file , reason : %s", store.name, err.Error()))
		}

		for err == nil {

			bytes, ordinal := iterator.Current()

			index, encodedBytes := writeNumericMappingItem(codec.ReadBigEndianINT64Value(bytes), int32(ordinal), encoder)

			if _, err = tempBackupFile.Write(encodedBytes); err != nil {

				mappingLogger.Error(fmt.Sprintf(utils.ErrorWriteNumericMapping, err.Error()))
			}

			encoder.MemoryPool.ReleaseBytePool(index)

			err = iterator.Next()
		}

		_ = tempBackupFile.Close()

		if store.numericMappingBackupFile != nil {

			_ = store.numericMappingBackupFile.Close()

		}

		if err = os.Rename(store.path+utils.PathSeparator+FileBKPTemp112, store.path+utils.PathSeparator+FileBKP112); err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorRenameTempMappingBackup, store.name, err.Error()))
		}

		if store.numericMappingBackupFile, err = os.OpenFile(store.path+utils.PathSeparator+FileBKP112, os.O_RDWR|os.O_APPEND, 0666); err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open bkp.112 file store : %s , reason : %s", store.name, err.Error()))
		}

		store.cacheNumericMappings.Clear()

	}
}
