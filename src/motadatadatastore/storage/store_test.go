/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-05-01			 Dhaval <PERSON>ra			Motadata-6077  Added Test Case For Rebuild Mapping
* 2025-05-05			 Swapnil A. Dave		M<PERSON>ADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
												written valid and invalid scenario tests for put multiples.
* 2025-06-23             Dhaval Bera            MOTADATA-6642  Added test case for Coonfigurable Cache stores feature added for memory optimisation
* 2025-06-23             Vedant <PERSON><PERSON><PERSON>tadata-6370 Mapping operand changes to get the instance type store
* 2025-06-04             <PERSON><PERSON><PERSON>            MOTADATA-5780  Refactored TestCases
*/

package storage

import (
	bytes2 "bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"github.com/dolthub/swiss"
	"github.com/kamstrup/intmap"
	"github.com/kelindar/bitmap"
	cp "github.com/otiai10/copy"
	"github.com/stretchr/testify/assert"
	"math"
	"math/rand"
	"motadatadatastore/cache"
	"motadatadatastore/codec"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"
	"time"
	"unsafe"
)

var (
	memoryPool = utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools)

	encoder = codec.NewEncoder(memoryPool)

	decoder = codec.NewDecoder(memoryPool)

	valueBuffers [][]byte

	batches []DiskIOEventBatch

	event DiskIOEvent

	waitGroup *sync.WaitGroup

	tokenizer *utils.Tokenizer

	txnBuffers [][]byte

	txnEntries []map[uint64]utils.TxnEntry

	txnOffsets []*int

	ioWorkers []*DiskIOWorker
)

func TestMain(m *testing.M) {

	for _, arg := range os.Args {

		if strings.Contains(arg, "bench") {

			utils.EnvironmentType = utils.DatastoreBenchUnitEnvironment

			populateSortedKeys()

			initNumericKeys()

			populateMappingTokens()

			tmpBuffer, _ = utils.MmapAnonymous(1024 * 1024 * 20)

			defer func() {

				utils.Munmap(tmpBuffer)
			}()

			break
		}
	}

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	if err != nil {

		panic(err)
	}

	utils.SystemBootSequence = utils.Datastore

	if utils.InitConfigs(utils.UpdateConfigs(bytes, utils.MotadataMap{

		"datastore.max.pool.length": 500000,
		"datastore.store.wal.size.mb": utils.MotadataMap{
			"11": 1,
		},
	})) {

		tokenizer = &utils.Tokenizer{

			Tokens: make([]string, utils.TokenizerLength),
		}

		utils.CleanUpStores()

		cache.InitCacheEngine()

		valueBuffers = make([][]byte, 4)

		for i := 0; i < 4; i++ {

			valueBuffers[i] = make([]byte, utils.MaxValueBufferBytes+1000)
		}

		utils.DiskIOWorkers = 30

		ioWorkers = make([]*DiskIOWorker, utils.DiskIOWorkers)

		for id := range ioWorkers {

			ioWorkers[id] = NewIOWorker(id)

			ioWorkers[id].Start()
		}

		batches = make([]DiskIOEventBatch, utils.MaxStoreParts)

		event = DiskIOEvent{}

		for i := range batches {

			batches[i] = DiskIOEventBatch{}
		}

		waitGroup = &sync.WaitGroup{}

		txnBuffers = make([][]byte, utils.MaxStoreParts)

		for index := 0; index < len(txnBuffers); index++ {

			txnBuffers[index], err = utils.MmapAnonymous(10 * 1024 * 1024)

			if err != nil {

				txnBuffers[index] = make([]byte, 10*1024*1024)
			}

		}

		txnEntries = make([]map[uint64]utils.TxnEntry, utils.MaxStoreParts)

		for index := 0; index < len(txnEntries); index++ {

			txnEntries[index] = make(map[uint64]utils.TxnEntry, 10)
		}

		txnOffsets = make([]*int, utils.MaxStoreParts)

		for index := 0; index < len(txnOffsets); index++ {

			txnOffsets[index] = new(int)

			*txnOffsets[index] = 4

		}

		m.Run()

		utils.CleanUpStores()
	}
}

func TestConfigureStoreWALSize(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("configure-store-wal-size", utils.Log, encoder, tokenizer, false)

	assertions.Nil(err)

	txnBufferBytes := make([]byte, 1024*1024)

	copy(txnBufferBytes[len(txnBufferBytes)-3:], utils.EOTBytes)

	partition := store.partitions[0]

	previousWALBytes := len(partition.walBytes)

	previousWALSizeBytes := int64(partition.walSizeBytes)

	assertions.Equal(0, partition.walFilePosition)

	err = store.CommitTxn(txnBufferBytes, map[uint64]utils.TxnEntry{}, encoder, 0)

	assertions.Nil(err)

	currentWALBytes := len(partition.walBytes)

	stat, _ := partition.walFile.Stat()

	currentWALSizeBytes := stat.Size()

	assertions.Greater(currentWALBytes, previousWALBytes) //remapping the txn file

	assertions.Greater(currentWALSizeBytes, previousWALSizeBytes)

	assertions.Equal(utils.EOTBytes, []byte(partition.walBytes[partition.walFilePosition-3:partition.walFilePosition]))

}

func TestStoreMultipartKey(t *testing.T) {

	configStore, err := OpenOrCreateStore("test-store-multipart-key-part0", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(configStore)

	err = configStore.AddMultipartKey(1, 1)

	assertions.Nil(err)

	assertions.Equal(uint16(2), configStore.multipartKeys[1])

	assertions.NotNil(configStore.multipartKeyFile)

	assertions.True(configStore.ContainsMultipartKey())

	assertions.Equal(uint16(1), configStore.GetMaxPart(1))
}

func TestStoreCleanup(t *testing.T) {

	configStore, err := OpenOrCreateStore("test-store-cleanup-dataStoretype-config", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(configStore)

	err = configStore.Put([]byte("key1"), []byte{0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 3, 4, 5}, encoder, tokenizer)

	configStore.Cleanup(encoder, nil, nil, batches[0], event, waitGroup)

	found, valueBytes, err := configStore.Get([]byte("key1"), valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.NotNil(valueBytes)

	assertions.Nil(err)

	assertions.Equal([]byte{1, 2, 3, 4, 5}, valueBytes)

	metricStore, err := OpenOrCreateStore("test-store-cleanup-dataStoretype-metric", utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(metricStore)

	err = metricStore.Put([]byte("key1^"), []byte{0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 3, 4, 5}, encoder, tokenizer)

	metricStore.Cleanup(encoder, nil, nil, batches[0], event, waitGroup)

	found, valueBytes, err = metricStore.Get([]byte("key1^"), valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.NotNil(valueBytes)

	assertions.Nil(err)

	assertions.Equal([]byte{1, 2, 3, 4, 5}, valueBytes)

}

func TestFindWritableSegmentType1(t *testing.T) {

	store, err := OpenOrCreateStore("test-find-writable-segment-1", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	expectedValues := []int32{10, 11, 12, 13, 14, 15, 16}

	poolIndex, valueBytes, _ := encoder.EncodeINT32Values(expectedValues, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	testKey := "1^test.instance.1^test.metric"

	err = store.Put([]byte(testKey), valueBytes, encoder, tokenizer)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore("test-find-writable-segment-1", utils.StaticMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		As the store is closed and reopened the memory map will be nil
		This get should be served from io-uring
	*/

	assertions.Len(store.partitions[1].writingSegments, 0)

	assertions.Len(store.partitions[1].segmentBuffers, 0)

	found, valueBytes, err := store.Get([]byte(testKey), valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.NotNil(valueBytes)

	assertions.Nil(err)

	/*
		The below assertions prove that the segment was read through io-uring
	*/

	assertions.Len(store.partitions[1].writingSegments, 0)

	assertions.Len(store.partitions[1].segmentBuffers, 0)

	poolIndex, values, _ := decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], "", "", 0)

	assertions.EqualValues(expectedValues, values)

	values = append(values, 17, 18, 19, 20, 21)

	poolIndex, valueBytes, _ = encoder.EncodeINT32Values(expectedValues, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	defer encoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	segmentType := calculateWritingSegmentType(len(valueBytes))

	/*
		If we now put new value bytes into this key the segment will be memory mapped
	*/

	err = store.Put([]byte(testKey), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	/*
		This proves that the segment is now memory mapped
	*/

	partition := int(utils.GetHash64(GetPartitionKey(store.storeType, []byte(testKey), store.name, tokenizer)) % store.parts)

	assertions.NotNil(store.partitions[partition].segmentBuffers[byte(segmentType)|byte(Segment0)])

	/*
		Still the segment is memory mapped but not included in the writing segments,
		hence the read from this segment will still be through io-uring
	*/

	/*
		For reading it through memory map we need to fill up the writing segments,

		let's move the values to different segment
	*/

	valueBytes = append(valueBytes, make([]byte, 10000)...)

	segmentType = calculateWritingSegmentType(len(valueBytes))

	err = store.Put([]byte(testKey), valueBytes, encoder, tokenizer)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(store.partitions[partition].writingSegments[segmentType], Segment0)

	assertions.Len(store.partitions[partition].writingSegments, 1)

}

func TestFindWritableSegmentType2(t *testing.T) {

	store, err := OpenOrCreateStore("test-find-writable-segment-2", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	testValues := []int32{10, 11, 12, 13, 14, 15, 16}

	poolIndex, valueBytes, _ := encoder.EncodeINT32Values(testValues, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	testKey1 := "1^test.instance.1^test.metric"

	err = store.Put([]byte(testKey1), valueBytes, encoder, tokenizer)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore("test-find-writable-segment-2", utils.StaticMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		As the store is closed and reopened the memory map will be nil
		This get should be served from io-uring
	*/

	assertions.Len(store.partitions[1].writingSegments, 0)

	assertions.Len(store.partitions[1].segmentBuffers, 0)

	found, valueBytes, err := store.Get([]byte(testKey1), valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.NotNil(valueBytes)

	assertions.Nil(err)

	/*
		The below assertions prove that the segment was read through io-uring
	*/

	assertions.Len(store.partitions[1].writingSegments, 0)

	assertions.Len(store.partitions[1].segmentBuffers, 0)

	poolIndex, values, _ := decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], "", "", 0)

	assertions.EqualValues(testValues, values)

	values = append(values, 17, 18, 19, 20, 21)

	poolIndex, valueBytes, _ = encoder.EncodeINT32Values(testValues, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	defer encoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	segmentType := calculateWritingSegmentType(len(valueBytes))

	/*
		If we now put new value bytes into this key the segment will be memory mapped
	*/

	err = store.Put([]byte(testKey1), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	/*
		This proves that the segment is now memory mapped
	*/

	partition := int(utils.GetHash64(GetPartitionKey(store.storeType, []byte(testKey1), store.name, tokenizer)) % store.parts)

	assertions.NotNil(store.partitions[partition].segmentBuffers[byte(segmentType)|byte(Segment0)])

	/*
		Still the segment is memory mapped but not included in the writing segments,
		hence the read from this segment will still be through io-uring
	*/

	/*
		For reading it through memory map we need to fill up the writing segments,

		let's move the values to different segment
	*/

	err = store.Put([]byte("already.occupied.segment"), make([]byte, 43), encoder, tokenizer) //This will occupy the 50 byte segment

	assertions.Nil(err)

	valueBytes = append(valueBytes, make([]byte, 10)...)

	segmentType = calculateWritingSegmentType(len(valueBytes))

	err = store.Put([]byte(testKey1), valueBytes, encoder, tokenizer)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(store.partitions[0].writingSegments[segmentType], Segment0)

	assertions.Len(store.partitions[2].writingSegments, 1)

}

/*----segment corner test cases-------------------*/

func TestDeleteEmptySegment(t *testing.T) {

	store, err := OpenOrCreateStore("test-delete-empty-segment", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	segment, err := createSegment(SegmentType15Bytes, store.partitions[0])

	store.dirty = true

	store.partitions[0].dirty = true

	store.partitions[0].segmentDirtyFlags[0] = true

	assertions.Nil(err)

	assertions.NotEqual(255, segment)

	err = store.Sync(encoder)

	assertions.Nil(err)

	dir := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir

	stores, err := os.ReadDir(dir)

	assertions.Nil(err)

	for _, entry := range stores {

		if entry.Name() != "test-delete-empty-segment" {

			continue
		}

		partition, err := os.ReadDir(dir + utils.PathSeparator + entry.Name())

		assertions.Nil(err)

		files, err := os.ReadDir(dir + utils.PathSeparator + entry.Name() + utils.PathSeparator + partition[0].Name())

		assertions.Nil(err)

		for index := 0; index < len(files); index++ {

			if files[index].Name() != txnWALFile && files[index].Name() != SegmentMeta {

				assertions.False(true, "empty segments are there")
			}
		}

	}

}

func TestReadByMemoryMappedIO(t *testing.T) {

	store, err := OpenOrCreateStore("test-read-memory-map", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	err = store.Put([]byte("key"), make([]byte, 9), encoder, tokenizer)

	assertions.Nil(err)

	partition := int(utils.GetHash64(GetPartitionKey(store.storeType, []byte("key"), store.name, tokenizer)) % store.parts)

	offset, found, err := searchIndex([]byte("key"), store.partitions[partition])

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotEqual(-1, offset)

	offsetBytes := make([]byte, 8)

	codec.WriteINT64Value(int64(offset), 0, offsetBytes)

	store.partitions[partition].lock.RLock()

	valueBytes, err := readByMemoryMappedIO(offsetBytes[1], getSegmentBufferLength(getSegmentType(offsetBytes[1])), codec.ReadINTValue(offsetBytes[2:]), make([]byte, 10000), store.partitions[partition])

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal([]byte{0}, valueBytes)

	store.partitions[partition].lock.RUnlock()

	found, valueBytes, err = store.Get([]byte("key"), make([]byte, 90000), encoder, event, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal([]byte{0}, valueBytes)
}

func TestCalculateWritingSegmentType(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(SegmentType15Bytes+4, SegmentType20Bytes)
	assertions.Equal(SegmentType20Bytes+4, SegmentType30Bytes)
	assertions.Equal(SegmentType30Bytes+4, SegmentType40Bytes)
	assertions.Equal(SegmentType40Bytes+4, SegmentType50Bytes)
	assertions.Equal(SegmentType50Bytes+4, SegmentType60Bytes)
	assertions.Equal(SegmentType60Bytes+4, SegmentType70Bytes)
	assertions.Equal(SegmentType70Bytes+4, SegmentType80Bytes)
	assertions.Equal(SegmentType80Bytes+4, SegmentType90Bytes)
	assertions.Equal(SegmentType90Bytes+4, SegmentType100Bytes)
	assertions.Equal(SegmentType100Bytes+4, SegmentType150Bytes)
	assertions.Equal(SegmentType150Bytes+4, SegmentType200Bytes)
	assertions.Equal(SegmentType200Bytes+4, SegmentType250Bytes)
	assertions.Equal(SegmentType250Bytes+4, SegmentType300Bytes)
	assertions.Equal(SegmentType300Bytes+4, SegmentType350Bytes)
	assertions.Equal(SegmentType350Bytes+4, SegmentType400Bytes)
	assertions.Equal(SegmentType400Bytes+4, SegmentType450Bytes)
	assertions.Equal(SegmentType450Bytes+4, SegmentType500Bytes)
	assertions.Equal(SegmentType500Bytes+4, SegmentType600Bytes)
	assertions.Equal(SegmentType600Bytes+4, SegmentType700Bytes)
	assertions.Equal(SegmentType700Bytes+4, SegmentType800Bytes)
	assertions.Equal(SegmentType800Bytes+4, SegmentType900Bytes)
	assertions.Equal(SegmentType900Bytes+4, SegmentType1000Bytes)
	assertions.Equal(SegmentType1000Bytes+4, SegmentType1200Bytes)
	assertions.Equal(SegmentType1200Bytes+4, SegmentType1500Bytes)
	assertions.Equal(SegmentType1500Bytes+4, SegmentType1700Bytes)
	assertions.Equal(SegmentType1700Bytes+4, SegmentType2000Bytes)
	assertions.Equal(SegmentType2000Bytes+4, SegmentType2200Bytes)
	assertions.Equal(SegmentType2200Bytes+4, SegmentType2400Bytes)
	assertions.Equal(SegmentType2400Bytes+4, SegmentType2600Bytes)
	assertions.Equal(SegmentType2600Bytes+4, SegmentType2800Bytes)
	assertions.Equal(SegmentType2800Bytes+4, SegmentType3000Bytes)
	assertions.Equal(SegmentType3000Bytes+4, SegmentType3200Bytes)
	assertions.Equal(SegmentType3200Bytes+4, SegmentType3500Bytes)
	assertions.Equal(SegmentType3500Bytes+4, SegmentType4000Bytes)
	assertions.Equal(SegmentType4000Bytes+4, SegmentType4500Bytes)
	assertions.Equal(SegmentType4500Bytes+4, SegmentType5000Bytes)
	assertions.Equal(SegmentType5000Bytes+4, SegmentType5500Bytes)
	assertions.Equal(SegmentType5500Bytes+4, SegmentType6000Bytes)
	assertions.Equal(SegmentType6000Bytes+4, SegmentType7000Bytes)
	assertions.Equal(SegmentType7000Bytes+4, SegmentType8000Bytes)
	assertions.Equal(SegmentType8000Bytes+4, SegmentType10000Bytes)
	assertions.Equal(SegmentType10000Bytes+4, SegmentType13000Bytes)
	assertions.Equal(SegmentType13000Bytes+4, SegmentType15000Bytes)
	assertions.Equal(SegmentType15000Bytes+4, SegmentType20000Bytes)
	assertions.Equal(SegmentType20000Bytes+4, SegmentType25000Bytes)
	assertions.Equal(SegmentType25000Bytes+4, SegmentType30000Bytes)
	assertions.Equal(SegmentType30000Bytes+4, SegmentType40000Bytes)
	assertions.Equal(SegmentType40000Bytes+4, SegmentType50000Bytes)
	assertions.Equal(SegmentType50000Bytes+4, SegmentType60000Bytes)
	assertions.Equal(SegmentType60000Bytes+4, SegmentType70000Bytes)
	assertions.Equal(SegmentType70000Bytes+4, SegmentType80000Bytes)
	assertions.Equal(SegmentType100000Bytes+4, SegmentType160000Bytes)
	assertions.Equal(SegmentType160000Bytes+4, SegmentType240000Bytes)
	assertions.Equal(SegmentType240000Bytes+4, SegmentType320000Bytes)
	assertions.Equal(SegmentType320000Bytes+4, SegmentType400000Bytes)
	assertions.Equal(SegmentType400000Bytes+4, SegmentType480000Bytes)
	assertions.Equal(SegmentType480000Bytes+4, SegmentType600000Bytes)
	assertions.Equal(SegmentType600000Bytes+4, SegmentType700000Bytes)
	assertions.Equal(SegmentType700000Bytes+4, SegmentType800000Bytes)
	assertions.Equal(SegmentType800000Bytes+4, SegmentType900000Bytes)
	assertions.Equal(SegmentType900000Bytes+4, SegmentType1000000Bytes)
	assertions.Equal(SegmentType1000000Bytes+4, SegmentType1500000Bytes)

	assertions.Equal(SegmentType15Bytes, calculateWritingSegmentType(11))
	assertions.Equal(SegmentType20Bytes, calculateWritingSegmentType(20))
	assertions.Equal(SegmentType70Bytes, calculateWritingSegmentType(30))
	assertions.Equal(SegmentType70Bytes, calculateWritingSegmentType(40))
	assertions.Equal(SegmentType70Bytes, calculateWritingSegmentType(50))
	assertions.Equal(SegmentType70Bytes, calculateWritingSegmentType(60))
	assertions.Equal(SegmentType70Bytes, calculateWritingSegmentType(70))
	assertions.Equal(SegmentType100Bytes, calculateWritingSegmentType(80))
	assertions.Equal(SegmentType100Bytes, calculateWritingSegmentType(90))
	assertions.Equal(SegmentType100Bytes, calculateWritingSegmentType(100))
	assertions.Equal(SegmentType200Bytes, calculateWritingSegmentType(150))
	assertions.Equal(SegmentType200Bytes, calculateWritingSegmentType(200))
	assertions.Equal(SegmentType350Bytes, calculateWritingSegmentType(250))
	assertions.Equal(SegmentType350Bytes, calculateWritingSegmentType(300))
	assertions.Equal(SegmentType350Bytes, calculateWritingSegmentType(350))
	assertions.Equal(SegmentType500Bytes, calculateWritingSegmentType(400))
	assertions.Equal(SegmentType500Bytes, calculateWritingSegmentType(450))
	assertions.Equal(SegmentType500Bytes, calculateWritingSegmentType(500))
	assertions.Equal(SegmentType800Bytes, calculateWritingSegmentType(600))
	assertions.Equal(SegmentType800Bytes, calculateWritingSegmentType(700))
	assertions.Equal(SegmentType800Bytes, calculateWritingSegmentType(800))
	assertions.Equal(SegmentType1500Bytes, calculateWritingSegmentType(900))
	assertions.Equal(SegmentType1500Bytes, calculateWritingSegmentType(1000))
	assertions.Equal(SegmentType1500Bytes, calculateWritingSegmentType(1200))
	assertions.Equal(SegmentType1500Bytes, calculateWritingSegmentType(1500))
	assertions.Equal(SegmentType2400Bytes, calculateWritingSegmentType(1700))
	assertions.Equal(SegmentType2400Bytes, calculateWritingSegmentType(2000))
	assertions.Equal(SegmentType2400Bytes, calculateWritingSegmentType(2200))
	assertions.Equal(SegmentType2400Bytes, calculateWritingSegmentType(2400))
	assertions.Equal(SegmentType3000Bytes, calculateWritingSegmentType(2600))
	assertions.Equal(SegmentType3000Bytes, calculateWritingSegmentType(2800))
	assertions.Equal(SegmentType3000Bytes, calculateWritingSegmentType(3000))
	assertions.Equal(SegmentType4000Bytes, calculateWritingSegmentType(3500))
	assertions.Equal(SegmentType4000Bytes, calculateWritingSegmentType(4000))
	assertions.Equal(SegmentType5500Bytes, calculateWritingSegmentType(4500))
	assertions.Equal(SegmentType5500Bytes, calculateWritingSegmentType(5000))
	assertions.Equal(SegmentType5500Bytes, calculateWritingSegmentType(5500))
	assertions.Equal(SegmentType8000Bytes, calculateWritingSegmentType(6000))
	assertions.Equal(SegmentType8000Bytes, calculateWritingSegmentType(7000))
	assertions.Equal(SegmentType8000Bytes, calculateWritingSegmentType(8000))
	assertions.Equal(SegmentType15000Bytes, calculateWritingSegmentType(10000))
	assertions.Equal(SegmentType15000Bytes, calculateWritingSegmentType(13000))
	assertions.Equal(SegmentType15000Bytes, calculateWritingSegmentType(15000))
	assertions.Equal(SegmentType25000Bytes, calculateWritingSegmentType(20000))
	assertions.Equal(SegmentType25000Bytes, calculateWritingSegmentType(25000))
	assertions.Equal(SegmentType40000Bytes, calculateWritingSegmentType(30000))
	assertions.Equal(SegmentType40000Bytes, calculateWritingSegmentType(40000))
	assertions.Equal(SegmentType80000Bytes, calculateWritingSegmentType(50000))
	assertions.Equal(SegmentType80000Bytes, calculateWritingSegmentType(60000))
	assertions.Equal(SegmentType80000Bytes, calculateWritingSegmentType(70000))
	assertions.Equal(SegmentType80000Bytes, calculateWritingSegmentType(80000))
	assertions.Equal(SegmentType100000Bytes, calculateWritingSegmentType(100000))
	assertions.Equal(SegmentType160000Bytes, calculateWritingSegmentType(150000))
	assertions.Equal(SegmentType240000Bytes, calculateWritingSegmentType(240000))
	assertions.Equal(SegmentType320000Bytes, calculateWritingSegmentType(320000))
	assertions.Equal(SegmentType400000Bytes, calculateWritingSegmentType(400000))
	assertions.Equal(SegmentType480000Bytes, calculateWritingSegmentType(480000))
	assertions.Equal(SegmentType600000Bytes, calculateWritingSegmentType(600000))
	assertions.Equal(SegmentType900000Bytes, calculateWritingSegmentType(700000))
	assertions.Equal(SegmentType900000Bytes, calculateWritingSegmentType(800000))
	assertions.Equal(SegmentType900000Bytes, calculateWritingSegmentType(900000))
	assertions.Equal(SegmentType1500000Bytes, calculateWritingSegmentType(1000000))
	assertions.Equal(SegmentType1500000Bytes, calculateWritingSegmentType(1500000))

}

func TestGetSegmentBufferLength(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(15, getSegmentBufferLength(SegmentType15Bytes))
	assertions.Equal(20, getSegmentBufferLength(SegmentType20Bytes))
	assertions.Equal(30, getSegmentBufferLength(SegmentType30Bytes))
	assertions.Equal(40, getSegmentBufferLength(SegmentType40Bytes))
	assertions.Equal(50, getSegmentBufferLength(SegmentType50Bytes))
	assertions.Equal(60, getSegmentBufferLength(SegmentType60Bytes))
	assertions.Equal(70, getSegmentBufferLength(SegmentType70Bytes))
	assertions.Equal(80, getSegmentBufferLength(SegmentType80Bytes))
	assertions.Equal(90, getSegmentBufferLength(SegmentType90Bytes))
	assertions.Equal(100, getSegmentBufferLength(SegmentType100Bytes))
	assertions.Equal(150, getSegmentBufferLength(SegmentType150Bytes))
	assertions.Equal(200, getSegmentBufferLength(SegmentType200Bytes))
	assertions.Equal(250, getSegmentBufferLength(SegmentType250Bytes))
	assertions.Equal(300, getSegmentBufferLength(SegmentType300Bytes))
	assertions.Equal(350, getSegmentBufferLength(SegmentType350Bytes))
	assertions.Equal(400, getSegmentBufferLength(SegmentType400Bytes))
	assertions.Equal(450, getSegmentBufferLength(SegmentType450Bytes))
	assertions.Equal(500, getSegmentBufferLength(SegmentType500Bytes))
	assertions.Equal(600, getSegmentBufferLength(SegmentType600Bytes))
	assertions.Equal(700, getSegmentBufferLength(SegmentType700Bytes))
	assertions.Equal(800, getSegmentBufferLength(SegmentType800Bytes))
	assertions.Equal(900, getSegmentBufferLength(SegmentType900Bytes))
	assertions.Equal(1000, getSegmentBufferLength(SegmentType1000Bytes))
	assertions.Equal(1200, getSegmentBufferLength(SegmentType1200Bytes))
	assertions.Equal(1500, getSegmentBufferLength(SegmentType1500Bytes))
	assertions.Equal(1700, getSegmentBufferLength(SegmentType1700Bytes))
	assertions.Equal(2000, getSegmentBufferLength(SegmentType2000Bytes))
	assertions.Equal(2200, getSegmentBufferLength(SegmentType2200Bytes))
	assertions.Equal(2400, getSegmentBufferLength(SegmentType2400Bytes))
	assertions.Equal(2800, getSegmentBufferLength(SegmentType2800Bytes))
	assertions.Equal(3000, getSegmentBufferLength(SegmentType3000Bytes))
	assertions.Equal(3500, getSegmentBufferLength(SegmentType3500Bytes))
	assertions.Equal(4000, getSegmentBufferLength(SegmentType4000Bytes))
	assertions.Equal(4500, getSegmentBufferLength(SegmentType4500Bytes))
	assertions.Equal(5000, getSegmentBufferLength(SegmentType5000Bytes))
	assertions.Equal(5500, getSegmentBufferLength(SegmentType5500Bytes))
	assertions.Equal(6000, getSegmentBufferLength(SegmentType6000Bytes))
	assertions.Equal(7000, getSegmentBufferLength(SegmentType7000Bytes))
	assertions.Equal(8000, getSegmentBufferLength(SegmentType8000Bytes))
	assertions.Equal(10000, getSegmentBufferLength(SegmentType10000Bytes))
	assertions.Equal(13000, getSegmentBufferLength(SegmentType13000Bytes))
	assertions.Equal(15000, getSegmentBufferLength(SegmentType15000Bytes))
	assertions.Equal(20000, getSegmentBufferLength(SegmentType20000Bytes))
	assertions.Equal(25000, getSegmentBufferLength(SegmentType25000Bytes))
	assertions.Equal(30000, getSegmentBufferLength(SegmentType30000Bytes))
	assertions.Equal(40000, getSegmentBufferLength(SegmentType40000Bytes))
	assertions.Equal(50000, getSegmentBufferLength(SegmentType50000Bytes))
	assertions.Equal(60000, getSegmentBufferLength(SegmentType60000Bytes))
	assertions.Equal(70000, getSegmentBufferLength(SegmentType70000Bytes))
	assertions.Equal(80000, getSegmentBufferLength(SegmentType80000Bytes))
	assertions.Equal(100000, getSegmentBufferLength(SegmentType100000Bytes))
	assertions.Equal(160000, getSegmentBufferLength(SegmentType160000Bytes))
	assertions.Equal(240000, getSegmentBufferLength(SegmentType240000Bytes))
	assertions.Equal(320000, getSegmentBufferLength(SegmentType320000Bytes))
	assertions.Equal(400000, getSegmentBufferLength(SegmentType400000Bytes))
	assertions.Equal(480000, getSegmentBufferLength(SegmentType480000Bytes))
	assertions.Equal(600000, getSegmentBufferLength(SegmentType600000Bytes))
	assertions.Equal(700000, getSegmentBufferLength(SegmentType700000Bytes))
	assertions.Equal(800000, getSegmentBufferLength(SegmentType800000Bytes))
	assertions.Equal(900000, getSegmentBufferLength(SegmentType900000Bytes))
	assertions.Equal(1000000, getSegmentBufferLength(SegmentType1000000Bytes))
	assertions.Equal(1500000, getSegmentBufferLength(SegmentType1500000Bytes))

}

/*-----------------------store corner test cases----------------------*/

func TestGetStoreType(t *testing.T) {

	store, err := OpenOrCreateStore("test-get-store-type-config", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.Equal(utils.StaticMetric, store.GetDatastoreType())

	assertions.Equal("test-get-store-type-config", store.GetName())

	store, err = OpenOrCreateStore("test-get-store-type-posting-list", utils.Index, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.Equal(utils.Index, store.GetDatastoreType())

	assertions.Equal("test-get-store-type-posting-list", store.GetName())

}

func TestDeleteStore(t *testing.T) {

	store, err := OpenOrCreateStore("test-store-delete-key", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	err = store.Put([]byte("key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	found, valueBytes, err := store.Get([]byte("key"), make([]byte, 100), encoder, event, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	assertions.NotNil(valueBytes)

	assertions.Equal(make([]byte, 1), valueBytes)

	store.Delete([]byte("key"), encoder, tokenizer)

	found, valueBytes, err = store.Get([]byte("key"), make([]byte, 100), encoder, event, waitGroup, tokenizer, false)

	assertions.NotNil(err)

	assertions.False(found)

	assertions.Nil(valueBytes)

}

/*
	Partition corner test cases
*/

func TestOpenStore(t *testing.T) {

	store, err := OpenOrCreateStore("test-store-reload", utils.PerformanceMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := []byte("1^system.cpu.percent^0")

	err = store.Put(keyBytes, make([]byte, decoder.MemoryPool.GetPoolLength()*8), encoder, tokenizer)

	assertions.Nil(err)

	assertStoreTestResult(store, assertions, keyBytes)

	store.Close(encoder)

	assertions.False(store.dirty)

	assertions.True(store.closed)

	assertions.Len(store.partitions, int(getPartitionLength(utils.PerformanceMetric)))

	assertions.Equal(uint64(getPartitionLength(utils.PerformanceMetric)), store.parts)

	partition := store.partitions[int(utils.GetHash64(GetPartitionKey(store.storeType, keyBytes, store.name, tokenizer))%store.parts)]

	assertions.Nil(partition.segmentFiles)

	assertions.Nil(partition.segmentBuffers)

	assertions.Nil(partition.segmentStats)

	assertions.Nil(partition.segmentDirtyFlags)

	assertions.Nil(partition.segmentMetadataBitmaps)

	assertions.Nil(partition.writingSegments)

	assertions.False(partition.dirty)

	store, err = OpenOrCreateStore("test-store-reload", utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Equal(store.name, "test-store-reload")

	assertions.Equal(store.reads, int64(0))

	assertions.Equal(store.puts, int64(1))

	assertions.False(store.closed)

	assertions.Equal(store.readLatency, uint64(0))

	assertions.Equal(store.putLatency, uint64(0))

	assertions.Equal(store.parts, uint64(getPartitionLength(utils.PerformanceMetric)))

}

func TestOpenStoreWithSmallPoolLength(t *testing.T) {

	utils.MaxPoolLength = 20000

	pool := utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools)

	encoder := codec.NewEncoder(pool)

	store, err := OpenOrCreateStore("test-store-pool-length", utils.PerformanceMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	store.Close(encoder)

	assertions.False(store.dirty)

	assertions.True(store.closed)

	utils.MaxPoolLength = 10000

	store, err = OpenOrCreateStore("test-store-pool-length", utils.PerformanceMetric, codec.NewEncoder(utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools)), tokenizer, false)

	assertions.NotNil(err)

	assertions.Equal(err.Error(), "failed to open the store test-store-pool-length, reason: store pool length 20000 is less than system pool length 10000")

}

func assertStoreTestResult(store *Store, assertions *assert.Assertions, keyBytes []byte) {

	assertions.True(store.dirty)

	assertions.False(store.closed)

	assertions.Len(store.partitions, int(getPartitionLength(utils.PerformanceMetric)))

	assertions.Equal(uint64(getPartitionLength(utils.PerformanceMetric)), store.parts)

	partition := store.partitions[int(utils.GetHash64(GetPartitionKey(store.storeType, keyBytes, store.name, tokenizer))%store.parts)]

	assertions.Len(partition.segmentFiles, 1)

	assertions.NotNil(partition.segmentFiles[204])

	assertions.Len(partition.segmentBuffers, 1)

	assertions.NotNil(partition.segmentBuffers[204])

	assertions.True(partition.segmentDirtyFlags[204])

	assertions.Equal(1, partition.segmentMetadataBitmaps[204].Count())

	assertions.True(partition.memoryIndices.Count() == 1)

	assertions.NotNil(partition.tempIndexFile)

	assertions.Contains(partition.writingSegments, SegmentType80000Bytes)

	assertions.Equal(Segment0, partition.writingSegments[SegmentType80000Bytes])

	assertions.True(partition.dirty)

	assertions.Equal(int64(1), partition.writes)

	assertions.Equal(int64(0), partition.moves)

	assertions.Equal(int64(0), partition.updates)

}

/*
	index corner test cases
*/

func TestRebuildIndex(t *testing.T) {

	store, err := OpenOrCreateStore("test-rebuild-index", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	/*
		syncing the store will make datastore.idx file which is the main index file
	*/

	err = store.Sync(encoder)

	assertions.Nil(err)

	err = store.Put([]byte("key2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	partition := int(utils.GetHash64(GetPartitionKey(store.storeType, []byte("key2"), store.name, tokenizer)) % store.parts)

	err = rebuildIndex(store.partitions[partition], encoder)

	assertions.Nil(err)

}

func TestRebuildIndexMultipleKeys(t *testing.T) {

	store, err := OpenOrCreateStore("test-rebuild-index2", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put([]byte("key2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put([]byte("key3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	for partition := range store.partitions {

		if store.partitions[partition].dirty {

			err = rebuildIndex(store.partitions[partition], encoder)

			assertions.Nil(err)

		}

	}

	keys, err = store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	//we have inserted 3 keys key1,key2,key3
	assertions.Equal(len(keys), 3)

}

func TestRebuildIndexMultipleKeysV1(t *testing.T) {

	store, err := OpenOrCreateStore("test-rebuild-index1", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	store.variant = 1.41

	for partition := range store.partitions {

		store.partitions[partition].variant = 1.41
	}

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put([]byte("key2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put([]byte("key3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	for partition := range store.partitions {

		store.partitions[partition].variant = 1.41

		if store.partitions[partition].dirty {

			err = rebuildIndex(store.partitions[partition], encoder)

			assertions.Nil(err)

		}

	}

	keys, err = store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	//we have inserted 3 keys key1,key2,key3
	assertions.Equal(len(keys), 3)

}

func TestMergeIndex(t *testing.T) {

	store, err := OpenOrCreateStore("test-merge-index", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	/*
		syncing the store will make datastore.idx file which is the main index file
	*/

	err = store.Sync(encoder)

	assertions.Nil(err)

	err = store.Put([]byte("key2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	mergeIndex(store.partitions[1], encoder)

}

func TestDoGreaterThanLookupTempIndexExclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-greaterThanLookUp-temp-memory-index-exclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := make([]byte, 8)

	startIndex := uint64(0)

	endIndex := uint64(10)

	for i := startIndex; i < endIndex; i++ {

		binary.BigEndian.PutUint64(keyBytes, i)

		err = store.Put(keyBytes, make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

		assertions.Nil(err)
	}

	conditionValue := uint64(5)

	for _, partition := range store.partitions {

		partition.cacheIndices = nil
	}

	actualKeys := doGreaterThanLookup(store.partitions[1], conditionValue, false, false)

	for _, bytes := range actualKeys {

		assertions.Greater(binary.BigEndian.Uint64(bytes), conditionValue)

	}

}

func TestDoGreaterThanLookupTempIndexInclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-greaterThanLookUp-temp-memory-index-inclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := make([]byte, 8)

	startIndex := uint64(0)

	endIndex := uint64(10)

	for i := startIndex; i < endIndex; i++ {

		binary.BigEndian.PutUint64(keyBytes, i)

		err = store.Put(keyBytes, make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

		assertions.Nil(err)
	}

	conditionValue := uint64(5)

	actualKeys := doGreaterThanLookup(store.partitions[1], conditionValue, true, false)

	for _, bytes := range actualKeys {

		assertions.GreaterOrEqual(binary.BigEndian.Uint64(bytes), conditionValue)

	}

}

func TestDoGreaterThanLookupTempIndexInclusiveType1(t *testing.T) {

	store, err := OpenOrCreateStore("test-greaterThanLookUp-temp-memory-index-inclusive-0", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := make([]byte, 8)

	startIndex := uint64(0)

	endIndex := uint64(10)

	for i := startIndex; i < endIndex; i++ {

		binary.BigEndian.PutUint64(keyBytes, i)

		err = store.Put(keyBytes, make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

		assertions.Nil(err)
	}

	conditionValue := uint64(5)

	actualKeys := doGreaterThanLookup(store.partitions[1], conditionValue, true, true)

	for _, bytes := range actualKeys {

		assertions.LessOrEqual(binary.BigEndian.Uint64(bytes), conditionValue)

	}

}

func TestDoGreaterThanLookupIndexExclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-greaterThanLookUp-index-exclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := make([]byte, 8)

	startIndex := uint64(0)

	endIndex := uint64(10)

	for i := startIndex; i < endIndex; i++ {

		binary.BigEndian.PutUint64(keyBytes, i)

		err = store.Put(keyBytes, make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

		assertions.Nil(err)
	}

	err = store.Sync(encoder)

	assertions.Nil(err)

	conditionValue := uint64(5)

	actualKeys := doGreaterThanLookup(store.partitions[1], conditionValue, false, false)

	for _, bytes := range actualKeys {

		assertions.Greater(binary.BigEndian.Uint64(bytes), conditionValue)

	}

}

func TestDoGreaterThanLookupIndexInclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-greaterThanLookUp-index-inclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := make([]byte, 8)

	startIndex := uint64(0)

	endIndex := uint64(10)

	for i := startIndex; i < endIndex; i++ {

		binary.BigEndian.PutUint64(keyBytes, i)

		err = store.Put(keyBytes, make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

		assertions.Nil(err)
	}

	err = store.Sync(encoder)

	assertions.Nil(err)

	conditionValue := uint64(5)

	actualKeys := doGreaterThanLookup(store.partitions[1], conditionValue, true, false)

	for _, bytes := range actualKeys {

		assertions.GreaterOrEqual(binary.BigEndian.Uint64(bytes), conditionValue)

	}

}

func TestDoGreaterThanLookupIndexInclusiveType1(t *testing.T) {

	utils.CleanUpStores()

	store, err := OpenOrCreateStore("test-greaterThanLookUp-index-inclusive-1", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := make([]byte, 8)

	startIndex := uint64(0)

	endIndex := uint64(10)

	for i := startIndex; i < endIndex; i++ {

		binary.BigEndian.PutUint64(keyBytes, i)

		err = store.Put(keyBytes, make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

		assertions.Nil(err)
	}

	conditionValue := uint64(5)

	actualKeys := doGreaterThanLookup(store.partitions[1], conditionValue, false, true)

	for _, bytes := range actualKeys {

		assertions.LessOrEqual(binary.BigEndian.Uint64(bytes), conditionValue)

	}

}

func TestDoLessThanLookupTempIndexExclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-lessThanLookUp-temp-memory-index-exclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := make([]byte, 8)

	startIndex := uint64(0)

	endIndex := uint64(10)

	for i := startIndex; i < endIndex; i++ {

		binary.BigEndian.PutUint64(keyBytes, i)

		err = store.Put(keyBytes, make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

		assertions.Nil(err)
	}

	conditionValue := uint64(5)

	for _, partition := range store.partitions {

		partition.cacheIndices = nil
	}

	actualKeys := doLessThanLookup(store.partitions[1], conditionValue, false, false)

	for _, bytes := range actualKeys {

		assertions.Less(binary.BigEndian.Uint64(bytes), conditionValue)

	}

}

func TestDoLessThanLookupTempIndexInclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-lessThanLookUp-temp-memory-index-inclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := make([]byte, 8)

	startIndex := uint64(0)

	endIndex := uint64(10)

	for i := startIndex; i < endIndex; i++ {

		binary.BigEndian.PutUint64(keyBytes, i)

		err = store.Put(keyBytes, make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

		assertions.Nil(err)
	}

	conditionValue := uint64(5)

	actualKeys := doLessThanLookup(store.partitions[1], conditionValue, true, false)

	for _, bytes := range actualKeys {

		assertions.LessOrEqual(binary.BigEndian.Uint64(bytes), conditionValue)

	}

}

func TestDoLessThanLookupTempIndexInclusiveType1(t *testing.T) {

	store, err := OpenOrCreateStore("test-lessThanLookUp-temp-memory-index-inclusive-2", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := make([]byte, 8)

	startIndex := uint64(0)

	endIndex := uint64(10)

	for i := startIndex; i < endIndex; i++ {

		binary.BigEndian.PutUint64(keyBytes, i)

		err = store.Put(keyBytes, make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

		assertions.Nil(err)
	}

	conditionValue := uint64(5)

	actualKeys := doLessThanLookup(store.partitions[1], conditionValue, true, true)

	for _, bytes := range actualKeys {

		assertions.GreaterOrEqual(binary.BigEndian.Uint64(bytes), conditionValue)

	}

}

func TestDoLessThanLookupTempIndexInclusiveType2(t *testing.T) {

	store, err := OpenOrCreateStore("test-lessThanLookUp-temp-memory-index-inclusive-3", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := make([]byte, 8)

	startIndex := uint64(0)

	endIndex := uint64(10)

	for i := startIndex; i < endIndex; i++ {

		binary.BigEndian.PutUint64(keyBytes, i)

		err = store.Put(keyBytes, make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

		assertions.Nil(err)
	}

	conditionValue := uint64(5)

	actualKeys := doLessThanLookup(store.partitions[1], conditionValue, false, true)

	for _, bytes := range actualKeys {

		assertions.GreaterOrEqual(binary.BigEndian.Uint64(bytes), conditionValue)

	}

}

func TestDoLessThanLookupIndexExclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-lessThanLookUp-index-exclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := make([]byte, 8)

	startIndex := uint64(0)

	endIndex := uint64(10)

	for i := startIndex; i < endIndex; i++ {

		binary.BigEndian.PutUint64(keyBytes, i)

		err = store.Put(keyBytes, make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

		assertions.Nil(err)
	}

	err = store.Sync(encoder)

	assertions.Nil(err)

	conditionValue := uint64(5)

	actualKeys := doLessThanLookup(store.partitions[1], conditionValue, false, false)

	for _, bytes := range actualKeys {

		assertions.Less(binary.BigEndian.Uint64(bytes), conditionValue)

	}

}

func TestDoLessThanLookupIndexInclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-lessThanLookUp-index-inclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := make([]byte, 8)

	startIndex := uint64(0)

	endIndex := uint64(10)

	for i := startIndex; i < endIndex; i++ {

		binary.BigEndian.PutUint64(keyBytes, i)

		err = store.Put(keyBytes, make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

		assertions.Nil(err)
	}

	err = store.Sync(encoder)

	assertions.Nil(err)

	conditionValue := uint64(5)

	actualKeys := doLessThanLookup(store.partitions[1], conditionValue, true, false)

	for _, bytes := range actualKeys {

		assertions.LessOrEqual(binary.BigEndian.Uint64(bytes), conditionValue)

	}

}

func TestDoSuffixLookupTempIndexExclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-suffixLookUp-temp-memory-index-exclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	valid := true

	for index := 0; index < int(store.parts); index++ {

		keys := doSuffixLookup(store.partitions[index], []byte("key"), true)

		for i := range keys {

			if strings.HasSuffix(string(keys[i]), "key") {

				valid = false
			}
		}

	}

	assertions.True(valid)

}

func TestDoSuffixLookupTempIndexInclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-suffixLookUp-temp-memory-index-inclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	valid := true

	for index := 0; index < int(store.parts); index++ {

		keys := doSuffixLookup(store.partitions[index], []byte("key"), false)

		for i := range keys {

			if !strings.HasSuffix(string(keys[i]), "key") {

				valid = false
			}
		}

	}

	assertions.True(valid)

}

func TestDoPrefixLookupTempIndexExclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-prefixLookUp-temp-memory-index-exclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	valid := true

	for index := 0; index < int(store.parts); index++ {

		keys := doPrefixLookup(store.partitions[index], []byte("test"), false)

		for i := range keys {

			if strings.HasSuffix(string(keys[i]), "key") {

				valid = false
			}
		}

	}

	assertions.True(valid)

}

func TestDoPrefixLookupTempIndexInclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-prefixLookUp-temp-memory-index-inclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1-test"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2-test"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3-test"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4-test"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	valid := true

	for index := 0; index < int(store.parts); index++ {

		keys := doPrefixLookup(store.partitions[index], []byte("key"), false)

		for i := range keys {

			if strings.HasSuffix(string(keys[i]), "key") {

				valid = false
			}
		}

	}

	assertions.True(valid)

}

func TestDoPrefixLookupIndexExclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-prefixLookUp-index-exclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	valid := true

	for index := 0; index < int(store.parts); index++ {

		keys := doPrefixLookup(store.partitions[index], []byte("test"), false)

		for i := range keys {

			if strings.HasSuffix(string(keys[i]), "key") {

				valid = false
			}
		}

	}

	assertions.True(valid)

}

func TestDoPrefixLookupIndexInclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-prefixLookUp-index-inclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1-test"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2-test"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3-test"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4-test"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	valid := true

	for index := 0; index < int(store.parts); index++ {

		keys := doPrefixLookup(store.partitions[index], []byte("key"), true)

		for i := range keys {

			if strings.HasSuffix(string(keys[i]), "key") {

				valid = false
			}
		}

	}

	assertions.True(valid)

}

func TestDoContainLookupTempIndexExclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-containLookUp-temp-memory-index-Exclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	valid := true

	for index := 0; index < int(store.parts); index++ {

		keys := doContainLookup(store.partitions[index], []byte("key"), true)

		for i := range keys {

			if strings.HasSuffix(string(keys[i]), "key") {

				valid = false
			}
		}

	}

	assertions.True(valid)

}

func TestDoContainLookupTempIndexInclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-containLookUp-temp-memory-index-Inclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	valid := true

	for index := 0; index < int(store.parts); index++ {

		keys := doContainLookup(store.partitions[index], []byte("test"), false)

		for i := range keys {

			if strings.HasSuffix(string(keys[i]), "key") {

				valid = false
			}
		}

	}

	assertions.True(valid)

}

func TestDoContainLookupMemoryIndexExclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-containLookUp-main-memory-index-Exclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	valid := true

	for index := 0; index < int(store.parts); index++ {

		keys := doContainLookup(store.partitions[index], []byte("key"), true)

		for i := range keys {

			if strings.HasSuffix(string(keys[i]), "key") {

				valid = false
			}
		}

	}

	assertions.True(valid)

}

func TestDoContainLookupMemoryIndexInclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-containLookUp-main-memory-index-Inclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	valid := true

	for index := 0; index < int(store.parts); index++ {

		keys := doContainLookup(store.partitions[index], []byte("test"), false)

		for i := range keys {

			if strings.HasSuffix(string(keys[i]), "key") {

				valid = false
			}
		}

	}

	assertions.True(valid)

}

func TestDoSuffixLookupIndexExclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-suffixLookUp-main-index-Exclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test4"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test5"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	/*
		max memory index entries are 7 so adding two more and syncing the store hence the index won't be loaded
	*/

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	valid := true

	for index := 0; index < int(store.parts); index++ {

		keys := doContainLookup(store.partitions[index], []byte("test"), true)

		for i := range keys {

			if !strings.HasSuffix(string(keys[i]), "key") {

				valid = false
			}
		}

	}

	assertions.True(valid)

}

func TestDoSuffixLookupIndexInclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-suffixLookUp-main-index-inclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4-key"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	valid := true

	for index := 0; index < int(store.parts); index++ {

		keys := doSuffixLookup(store.partitions[index], []byte("key"), false)

		for i := range keys {

			if !strings.HasSuffix(string(keys[i]), "key") {

				valid = false
			}
		}

	}

	assertions.True(valid)

}

func TestDoContainLookupIndexExclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-containLookUp-main-index-Exclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test4"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test5"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	/*
		max memory index entries are 7 so adding two more and syncing the store hence the index won't be loaded
	*/

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	valid := true

	for index := 0; index < int(store.parts); index++ {

		keys := doContainLookup(store.partitions[index], []byte("key"), true)

		for i := range keys {

			if strings.HasSuffix(string(keys[i]), "key") {

				valid = false
			}
		}

	}

	assertions.True(valid)

}

func TestDoContainLookupIndexInclusive(t *testing.T) {

	store, err := OpenOrCreateStore("test-containLookUp-main-index-Inclusive", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test4"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test5"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	valid := true

	for index := 0; index < int(store.parts); index++ {

		keys := doContainLookup(store.partitions[1], []byte("key"), false)

		for i := range keys {

			if strings.HasSuffix(string(keys[i]), "key") {

				valid = false
			}
		}

	}

	assertions.True(valid)

}

func TestDoListingTempIndex(t *testing.T) {

	store, err := OpenOrCreateStore("test-doListing-temp-memory-index", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	valid := 0

	for index := 0; index < int(store.parts); index++ {

		keys := doListing(store.partitions[index], nil, nil, false, codec.Invalid)

		valid += len(keys)

	}

	assertions.Equal(5, valid)

}

func TestDoListingMemoryIndex(t *testing.T) {

	store, err := OpenOrCreateStore("test-doListing-main-memory-index", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("test1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	valid := 0

	for index := 0; index < int(store.parts); index++ {

		keys := doListing(store.partitions[index], nil, nil, false, codec.Invalid)

		valid += len(keys)

	}

	assertions.Equal(5, valid)

}

func TestDoListingIndex(t *testing.T) {

	store, err := OpenOrCreateStore("test-doListing-main-index", utils.StaticMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		writing a key which will be first written into temp memory index and memory map of temp index file
	*/

	err = store.Put([]byte("key1"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key2"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key3"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key4"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key5"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key6"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key7"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key8"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	err = store.Put([]byte("key9"), make([]byte, utils.MaxValueBytes+1), encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	valid := 0

	for index := 0; index < int(store.parts); index++ {

		keys := doListing(store.partitions[index], nil, nil, false, codec.Invalid)

		valid += len(keys)

	}

	assertions.Equal(valid, 9)

}

func TestStoreGetMetrics(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("info-Store", utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		putting a key into the store
	*/

	key := "1^system.cpu.percent^"

	err = store.Put([]byte(key+"0"), []byte{0, 0, 0, 0, 0, 0, 0, 0, 1}, encoder, tokenizer)

	assertions.Nil(err)

	bufferBytes := make([]byte, decoder.MemoryPool.GetPoolLength()*8)

	/*
		checking whether the key exists or not
	*/

	found, valueBytes, err := store.Get([]byte(key+"0"), bufferBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal([]byte{1}, valueBytes) //the key exists

	var int64Values []int64

	for i := 0; i < encoder.MemoryPool.GetPoolLength()-utils.MaxValueBytes; i++ {

		int64Values = append(int64Values, rand.Int63())
	}

	poolIndex, encodedBytes, err := encoder.EncodeINT64Values(int64Values, codec.None, codec.Int64, utils.MaxValueBytes)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(encodedBytes)

	/*

		key1 was in the segmentType0 which is 10 bytes now
		the new values cannot be stored in segmentType0 hence this key in the current segment needs to be moved to
		higher segmentType184
	*/

	err = store.Put([]byte(key+"0"), encodedBytes, encoder, tokenizer) //now key 1 with segment type 0 will be moved to segment type 204

	assertions.Nil(err)

	/*
		Scenario :- max out the segment with a certain segmentType so that new segment of same segmentType will be
					rolled over

		Now putting new keys into a segment with 5_00_000 bytes but the segment when maxed out will be
		then written into the new segment with same segmentType
	*/

	for i := 2; i < 5000; i++ {

		err = store.Put([]byte(key+codec.INTToStringValue(i)), make([]byte, encoder.MemoryPool.GetPoolLength()*8), encoder, tokenizer)

		assertions.Nil(err)

	}

	keyBuffers := make([][]byte, 4)

	/*

		key1 is still in writing segments
		key2 is not in the writing segments as the segmentType was maxed out and rolled over with new segment
		so, when made a read request the key1 will be read from MemoryMap whereas the key2 will be read from file system
		using iouring
	*/

	keyBuffers[0] = []byte(key + "0")

	keyBuffers[1] = []byte(key + "2")

	buffers, errs, err := store.GetMultiples(keyBuffers[:2], valueBuffers[:2], encoder, batches, waitGroup, tokenizer, false)

	assertions.Nil(errs[0])

	assertions.Nil(errs[1])

	assertions.Equal(encodedBytes[utils.MaxValueBytes:], buffers[0])

	assertions.Equal(make([]byte, encoder.MemoryPool.GetPoolLength()*8)[utils.MaxValueBytes:], buffers[1])

	/*
		validating the read values of the keys
	*/

	found, valueBytes, err = store.Get([]byte(key+"2"), valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(make([]byte, encoder.MemoryPool.GetPoolLength()*8)[utils.MaxValueBytes:], valueBytes)

	metrics := store.GetMetrics()

	if metrics != nil {

		assertions.Equal(int64(1), metrics[Moves])

		assertions.Equal(int64(0), metrics[IndexEntries])

		assertions.Equal(int64(2), metrics[SegmentFiles])

		assertions.Equal(int64(1310781440), metrics[DataSizeBytes])

		assertions.Equal(int64(0), metrics[IndexSizeBytes])

		assertions.Equal(int64(4), metrics[Reads])

		assertions.Equal(int64(5000), metrics[Puts])

		assertions.Equal(int64(0), metrics[Updates])

		assertions.Equal(int64(4999), metrics[Writes])

		assertions.Equal(int64(4999), metrics[MemoryIndexEntries])
	}

}

func TestStoreTTL(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("24012022-0-performace-store", utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	assertions.Equal(store.cacheTTL, 90)

	store, err = OpenOrCreateStore("24012022-0-status-store", utils.ObjectStatusMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	assertions.Equal(store.cacheTTL, 60)

	store, err = OpenOrCreateStore("24012022-1-log-store", utils.Log, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	assertions.Equal(store.cacheTTL, 90)

}

func TestStoreGetMetricsV1(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("info-Store1", utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	/*
		putting a key into the store
	*/

	key := "1^system.cpu.percent^"

	err = store.Put([]byte(key+"0"), []byte{0, 0, 0, 0, 0, 0, 0, 0, 1}, encoder, tokenizer)

	assertions.Nil(err)

	bufferBytes := make([]byte, decoder.MemoryPool.GetPoolLength()*8)

	/*
		checking whether the key exists or not
	*/

	found, valueBytes, err := store.Get([]byte(key+"0"), bufferBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal([]byte{1}, valueBytes) //the key exists

	var int64Values []int64

	for i := 0; i < encoder.MemoryPool.GetPoolLength()-utils.MaxValueBytes; i++ {

		int64Values = append(int64Values, rand.Int63())
	}

	poolIndex, encodedBytes, err := encoder.EncodeINT64Values(int64Values, codec.None, codec.Int64, utils.MaxValueBytes)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(encodedBytes)

	/*

		key1 was in the segmentType0 which is 10 bytes now
		the new values cannot be stored in segmentType0 hence this key in the current segment needs to be moved to
		higher segmentType184
	*/

	err = store.Put([]byte(key+"0"), encodedBytes, encoder, tokenizer) //now key 1 with segment type 0 will be moved to segment type 204

	assertions.Nil(err)

	/*
		Scenario :- max out the segment with a certain segmentType so that new segment of same segmentType will be
					rolled over

		Now putting new keys into a segment with 5_00_000 bytes but the segment when maxed out will be
		then written into the new segment with same segmentType
	*/

	for i := 2; i < 5000; i++ {

		err = store.Put([]byte(key+codec.INTToStringValue(i)), make([]byte, encoder.MemoryPool.GetPoolLength()*8), encoder, tokenizer)

		assertions.Nil(err)

	}

	keyBuffers := make([][]byte, 4)

	/*

		key1 is still in writing segments
		key2 is not in the writing segments as the segmentType was maxed out and rolled over with new segment
		so, when made a read request the key1 will be read from MemoryMap whereas the key2 will be read from file system
		using iouring
	*/

	keyBuffers[0] = []byte(key + "0")

	keyBuffers[1] = []byte(key + "2")

	buffers, errs, err := store.GetMultiples(keyBuffers[:2], valueBuffers[:2], encoder, batches, waitGroup, tokenizer, false)

	assertions.Nil(errs[0])

	assertions.Nil(errs[1])

	assertions.Equal(encodedBytes[utils.MaxValueBytes:], buffers[0])

	assertions.Equal(make([]byte, encoder.MemoryPool.GetPoolLength()*8)[utils.MaxValueBytes:], buffers[1])

	/*
		validating the read values of the keys
	*/

	found, valueBytes, err = store.Get([]byte(key+"2"), valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(make([]byte, encoder.MemoryPool.GetPoolLength()*8)[utils.MaxValueBytes:], valueBytes)

	err = store.Sync(encoder)

	assertions.Nil(err)

	metrics := store.GetMetrics()

	if metrics != nil {

		assertions.Equal(int64(1), metrics[Moves])

		assertions.Equal(int64(4999), metrics[IndexEntries])

		assertions.Equal(int64(1), metrics[SegmentFiles])

		assertions.Equal(int64(1310720000), metrics[DataSizeBytes])

		assertions.Equal(int64(44296), metrics[IndexSizeBytes])

		assertions.Equal(int64(4), metrics[Reads])

		assertions.Equal(int64(5000), metrics[Puts])

		assertions.Equal(int64(0), metrics[Updates])

		assertions.Equal(int64(4999), metrics[Writes])

		assertions.Equal(int64(0), metrics[MemoryIndexEntries])
	}
}

func TestStoreGetMetricsV2(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("mapping-Store", utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	err = store.PutStringMapping("abc", encoder)

	assertions.Nil(err)

	err = store.PutStringMapping(utils.Empty, encoder)

	assertions.Nil(err)

	found, ordinal, err := store.GetStringMapping(utils.Empty)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(int32(DummyStringMappingOrdinal), ordinal)

	err = store.PutNumericMapping(int64(10), encoder)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore("mapping-Store", utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	metrics := store.GetMetrics()

	if metrics != nil {

		assertions.Equal(int64(0), metrics[Moves])

		assertions.Equal(int64(2), metrics[IndexEntries])

		assertions.Equal(int64(0), metrics[SegmentFiles])

		assertions.Equal(int64(90), metrics[DataSizeBytes])

		assertions.Equal(int64(0), metrics[IndexSizeBytes])

		assertions.Equal(int64(0), metrics[Reads])

		assertions.Equal(int64(2), metrics[Puts])

		assertions.Equal(int64(0), metrics[Updates])

		assertions.Equal(int64(0), metrics[Writes])

		assertions.Equal(int64(0), metrics[MemoryIndexEntries])
	}

	assertions.True(store.GetLastUsedTimestamps() >= 0)

}

func TestStoreTransactionVersion1(t *testing.T) {

	defer cleanup()

	store, err := OpenOrCreateStore("transaction-store-test-version1", utils.PerformanceMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	key := "1^system.cpu.percent"

	keyBuffers := make([][]byte, 2)

	valueBuffers := make([][]byte, 2)

	for index := range valueBuffers {

		valueBuffers[index] = make([]byte, 80000)
	}

	keyBuffers[0] = []byte(key + utils.KeySeparator + "0")

	keyBuffers[1] = []byte(key + utils.KeySeparator + "time" + utils.KeySeparator + "0")

	tick := utils.UnixToSeconds(time.Now().Unix())

	values := []int32{1, 2, 3, 4, 5}

	ticks := []int32{tick, tick + 1, tick + 2, tick + 3, tick + 4}

	bufferIndex, bufferBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	assertions.Nil(err)

	assertions.NotNil(bufferBytes)

	assertions.NotEqual(utils.NotAvailable, bufferIndex)

	store.writeTxn(keyBuffers[0], bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	assertions.Nil(err)

	bufferIndex, bufferBytes, err = encoder.EncodeINT32Values(append(values, 6, 7, 8), codec.None, codec.Int32, 32, utils.MaxValueBytes)

	assertions.Nil(err)

	assertions.NotNil(bufferBytes)

	assertions.NotEqual(utils.NotAvailable, bufferIndex)

	store.writeTxn(keyBuffers[0], bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	assertions.Nil(err)

	bufferIndex, bufferBytes, err = encoder.EncodeINT32Values(ticks, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	defer assertions.Nil(err)

	assertions.NotNil(bufferBytes)

	assertions.NotEqual(utils.NotAvailable, bufferIndex)

	store.writeTxn(keyBuffers[1], bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	assertions.Nil(err)

	id := store.GetPartition(keyBuffers[1], tokenizer)

	err = store.putTxn(id)

	assertions.Nil(err)

	id = store.GetPartition(keyBuffers[0], tokenizer)

	err = store.putTxn(id)

	assertions.Nil(err)

	buffers, errs, err := store.GetMultiples(keyBuffers, valueBuffers, encoder, batches, waitGroup, tokenizer, true)

	assertions.NotNil(buffers[0])

	assertions.NotNil(buffers[1])

	assertions.Nil(errs[0])

	assertions.Nil(errs[1])

	poolIndex, decodedValues, err := decoder.DecodeINT32Values(codec.GetEncoding(buffers[0][0]), codec.Int32, buffers[0][1:], key, store.name, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.NotEqual(utils.NotAvailable, poolIndex)

	assertions.Nil(err)

	assertions.Equal(append(values, 6, 7, 8), decodedValues)

	poolIndex, decodedValues, err = decoder.DecodeINT32Values(codec.GetEncoding(buffers[1][0]), codec.Int32, buffers[1][1:], key, store.name, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.NotEqual(utils.NotAvailable, poolIndex)

	assertions.Nil(err)

	assertions.Equal(ticks, decodedValues)

}

func TestStoreTransactionVersion2(t *testing.T) {

	defer cleanup()

	storeName := "transaction-store-test-version2"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	key := "1^system.cpu.percent"

	keyBuffers := make([][]byte, 2)

	valueBuffers := make([][]byte, 2)

	for index := range valueBuffers {

		valueBuffers[index] = make([]byte, 80000)
	}

	keyBuffers[0] = []byte(key + utils.KeySeparator + "0")

	keyBuffers[1] = []byte(key + utils.KeySeparator + "time" + utils.KeySeparator + "0")

	tick := utils.UnixToSeconds(time.Now().Unix())

	values := []int32{1, 2, 3, 4, 5}

	ticks := []int32{tick, tick + 1, tick + 2, tick + 3, tick + 4}

	bufferIndex, bufferBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	assertions.Nil(err)

	assertions.NotNil(bufferBytes)

	assertions.NotEqual(utils.NotAvailable, bufferIndex)

	store.writeTxn(keyBuffers[0], bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	assertions.Nil(err)

	bufferIndex, bufferBytes, err = encoder.EncodeINT32Values(append(values, 6, 7, 8), codec.None, codec.Int32, 32, utils.MaxValueBytes)

	assertions.Nil(err)

	assertions.NotNil(bufferBytes)

	assertions.NotEqual(utils.NotAvailable, bufferIndex)

	store.writeTxn(keyBuffers[0], bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	assertions.Nil(err)

	bufferIndex, bufferBytes, err = encoder.EncodeINT32Values(ticks, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	assertions.Nil(err)

	assertions.NotNil(bufferBytes)

	assertions.NotEqual(utils.NotAvailable, bufferIndex)

	store.writeTxn(keyBuffers[1], bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	assertions.Nil(err)

	id := store.GetPartition(keyBuffers[1], tokenizer)

	err = store.putTxn(id)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	buffers, errs, err := store.GetMultiples(keyBuffers, valueBuffers, encoder, batches, waitGroup, tokenizer, true)

	assertions.NotNil(buffers[0])

	assertions.NotNil(buffers[1])

	assertions.Nil(errs[0])

	assertions.Nil(errs[1])

	poolIndex, decodedValues, err := decoder.DecodeINT32Values(codec.GetEncoding(buffers[0][0]), codec.Int32, buffers[0][1:], key, store.name, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.NotEqual(utils.NotAvailable, poolIndex)

	assertions.Nil(err)

	assertions.Equal(append(values, 6, 7, 8), decodedValues)

	poolIndex, decodedValues, err = decoder.DecodeINT32Values(codec.GetEncoding(buffers[1][0]), codec.Int32, buffers[1][1:], key, store.name, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.NotEqual(utils.NotAvailable, poolIndex)

	assertions.Nil(err)

	assertions.Equal(ticks, decodedValues)
}

func TestStoreTransactionVersion3(t *testing.T) {

	defer cleanup()

	encoder := codec.NewEncoder(utils.NewMemoryPool(10, 10_000, false, utils.DefaultBlobPools))

	decoder := codec.NewDecoder(utils.NewMemoryPool(10, 10_000, false, utils.DefaultBlobPools))

	storeName := "transaction-store-test-version3"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	go func() {

		pool := utils.NewMemoryPool(4, 10000, false, utils.DefaultBlobPools)

		testEncoder := codec.NewEncoder(pool)

		for {

			_ = store.Sync(testEncoder)

		}

	}()

	assertions := assert.New(t)

	assertions.Nil(err)

	key := "1^system.cpu.percent"

	keyBuffers := make([][]byte, 2)

	valueBuffers := make([][]byte, 2)

	for index := range valueBuffers {

		valueBuffers[index] = make([]byte, 80000)
	}

	keyBuffers[0] = []byte(key + utils.KeySeparator + "0")

	keyBuffers[1] = []byte(key + utils.KeySeparator + "time" + utils.KeySeparator + "0")

	tick := utils.UnixToSeconds(time.Now().Unix())

	values := []int32{1, 2, 3, 4, 5}

	ticks := []int32{tick, tick + 1, tick + 2, tick + 3, tick + 4}

	bytePoolIndex := utils.NotAvailable

	var bufferBytes []byte

	for i := 5; i < 100; i++ {

		values = append(values, int32(i))

		ticks = append(ticks, int32(i))

		bytePoolIndex, bufferBytes, err = encoder.EncodeINT32Values(values, codec.None, codec.Int32, 32, utils.MaxValueBytes)

		assertions.Nil(err)

		assertions.NotNil(bufferBytes)

		assertions.NotEqual(utils.NotAvailable, bytePoolIndex)

		store.writeTxn(keyBuffers[0], bufferBytes)

		encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

		assertions.Nil(err)

		bytePoolIndex, bufferBytes, err = encoder.EncodeINT32Values(append(ticks, int32(i)), codec.None, codec.Int32, 32, utils.MaxValueBytes)

		assertions.Nil(err)

		assertions.NotNil(bufferBytes)

		assertions.NotEqual(utils.NotAvailable, bytePoolIndex)

		store.writeTxn(keyBuffers[1], bufferBytes)

		encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

		assertions.Nil(err)

		time.Sleep(time.Millisecond * 100)

	}

	id := store.GetPartition(keyBuffers[0], tokenizer)

	err = store.putTxn(id)

	assertions.Nil(err)

	buffers, errs, err := store.GetMultiples(keyBuffers, valueBuffers, encoder, batches, waitGroup, tokenizer, true)

	assertions.NotNil(buffers[0])

	assertions.NotNil(buffers[1])

	assertions.Nil(errs[0])

	assertions.Nil(errs[1])

	poolIndex, decodedValues, err := decoder.DecodeINT32Values(codec.GetEncoding(buffers[0][0]), codec.Int32, buffers[0][1:], key, store.name, 0)

	assertions.NotEqual(utils.NotAvailable, poolIndex)

	assertions.Nil(err)

	for i := int32(0); i < 100; i++ {

		assertions.Equal(values[i], decodedValues[i])

	}

	poolIndex, decodedValues, err = decoder.DecodeINT32Values(codec.GetEncoding(buffers[1][0]), codec.Int32, buffers[1][1:], key, store.name, 0)

	assertions.NotEqual(utils.NotAvailable, poolIndex)

	assertions.Nil(err)

	for i := int32(0); i < 100; i++ {

		assertions.Equal(ticks[i], decodedValues[i])

	}

}

func TestStoreTransactionVersion5(t *testing.T) {

	defer cleanup()

	storeName := "transaction-store-test-version5"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := []byte("1^system.cpu.percent^0")

	store.writeTxn(keyBytes, append(make([]byte, utils.MaxValueBytes), []byte{1, 2, 3}...))

	partition := store.GetPartition(keyBytes, tokenizer)

	err = store.putTxn(partition)

	assertions.Nil(err)

	store.partitions[partition].syncWAL(encoder, store.partitions[partition].walBytes[:store.partitions[partition].walFilePosition], store.partitions[partition].walRecords)

	found, valueBytes, err := store.Get(keyBytes, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	assertions.NotNil(valueBytes)

	assertions.Equal([]byte{1, 2, 3}, valueBytes)

}

func TestStoreTransactionVersion7(t *testing.T) {

	defer cleanup()

	storeName := "transaction-store-test-version7"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := []byte("1^system.cpu.percent^0")

	partition := store.GetPartition(keyBytes, tokenizer)

	store.writeTxn(keyBytes, append(make([]byte, utils.MaxValueBytes)))

	err = store.putTxn(partition)

	assertions.Nil(err)

	cleanup()
}

func TestStoreTransactionVersion12(t *testing.T) {

	storeName := "transaction-store-test-version12"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	walFile := store.partitions[0].path + utils.PathSeparator + txnWALFile

	err = os.WriteFile(walFile, append([]byte{51, 55, 56, 57}, utils.EOTBytes...), 0644)

	assertions.Nil(err)

	err = store.partitions[0].syncWAL(encoder, append([]byte{51, 55, 56, 57}, utils.EOTBytes...), 1)

	assertions.NotNil(err) // error occurred while reading the wal file

	err = os.RemoveAll(walFile)

	assertions.Nil(err)

	err = store.partitions[0].syncWAL(encoder, append([]byte{51, 55, 56, 57}, utils.EOTBytes...), 1)

	assertions.NotNil(err) // failed to find the wal file
}

func TestStoreTransactionVersion13(t *testing.T) {

	storeName := "transaction-store-test-version13"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	walFile := store.partitions[0].path + utils.PathSeparator + txnWALFile

	err = os.WriteFile(walFile, []byte{51, 55, 56, 57}, 0644)

	assertions.Nil(err)

	err = store.partitions[0].syncWAL(encoder, []byte{51, 55, 56, 57}, 1)

	assertions.NotNil(err) // invalid wal file
}

//Compaction job testcases

func TestStoreCompactionVersion1(t *testing.T) {

	assertions := assert.New(t)

	storeName := "compaction-test-version1"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	for index := range valueBuffers {

		valueBuffers[index] = make([]byte, utils.MaxValueBufferBytes)

	}

	values := []int32{1, 2, 3, 4, 5, 6, 78}

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	keyBytes := []byte("1^system.cpu.percent^0")

	err = store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	for partition := range store.partitions {

		store.partitions[partition].SetLastWriteTimestamp(time.Now().Unix() - 86400)
	}

	store.Cleanup(encoder, keyBuffers, valueBuffers, batches[0], event, waitGroup)

	found, valueBytes, err := store.Get(keyBytes, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	assertions.True(found)

	poolIndex, decodedValues, err := decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], string(keyBytes), store.name, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(values, decodedValues)

}

// Scenario - Using Put to move segment after the compaction is done
func TestStoreCompactionVersion2(t *testing.T) {

	assertions := assert.New(t)

	storeName := "compaction-test-version2"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	for index := range valueBuffers {

		valueBuffers[index] = make([]byte, utils.MaxValueBufferBytes)

	}

	values := []int32{1, 2, 3, 4, 5, 6, 78}

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	keyBytes := []byte("1^system.cpu.percent^0")

	err = store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	for partition := range store.partitions {

		store.partitions[partition].SetLastWriteTimestamp(time.Now().Unix() - 86400)
	}

	store.Cleanup(encoder, keyBuffers, valueBuffers, batches[0], event, waitGroup)

	found, valueBytes, err := store.Get(keyBytes, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	assertions.True(found)

	poolIndex, decodedValues, err := decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], string(keyBytes), store.name, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(values, decodedValues)

	//again putting same key in the store , to move the segment

	values = []int32{1, 2, 3, 4, 5, 6, math.MaxInt32}

	poolIndex, valueBytes, err = encoder.EncodeINT32Values(values, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Put(keyBytes, valueBytes, encoder, tokenizer)

	found, valueBytes, err = store.Get(keyBytes, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	assertions.True(found)

	poolIndex, decodedValues, err = decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], string(keyBytes), store.name, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(values, decodedValues)

}

// Scenario - Using PutTxn to update segment after the compaction is done
func TestStoreCompactionVersion3(t *testing.T) {

	assertions := assert.New(t)

	storeName := "compaction-test-version3"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	valueBuffers = make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	for index := range valueBuffers {

		valueBuffers[index] = make([]byte, utils.MaxValueBufferBytes)

	}

	values := []int32{1, 2, 3, 4, 5, 6, 78}

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	keyBytes := []byte("1^system.cpu.percent^0")

	err = store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	for partition := range store.partitions {

		store.partitions[partition].SetLastWriteTimestamp(time.Now().Unix() - 86400)
	}

	store.Cleanup(encoder, keyBuffers, valueBuffers, batches[0], event, waitGroup)

	found, valueBytes, err := store.Get(keyBytes, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	assertions.True(found)

	poolIndex, decodedValues, err := decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], string(keyBytes), store.name, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(values, decodedValues)

	//again putting same key in the store , to update the segment

	values = []int32{1, 2, 3, 4, 5, 6, 7}

	poolIndex, valueBytes, err = encoder.EncodeINT32Values(values, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	found, valueBytes, err = store.Get(keyBytes, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	assertions.True(found)

	poolIndex, decodedValues, err = decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], string(keyBytes), store.name, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(values, decodedValues)

}

// Scenario - Using PutTxn to move segment after the compaction is done
func TestStoreCompactionVersion4(t *testing.T) {

	assertions := assert.New(t)

	storeName := "compaction-test-version4"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	for index := range valueBuffers {

		valueBuffers[index] = make([]byte, utils.MaxValueBufferBytes)

	}

	values := []int32{1}

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	keyBytes := []byte("1^system.cpu.percent^0")

	err = store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	for partition := range store.partitions {

		store.partitions[partition].SetLastWriteTimestamp(time.Now().Unix() - 86400)
	}

	store.Cleanup(encoder, keyBuffers, valueBuffers, batches[0], event, waitGroup)

	found, valueBytes, err := store.Get(keyBytes, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	assertions.True(found)

	poolIndex, decodedValues, err := decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], string(keyBytes), store.name, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(values, decodedValues)

	//again putting same key in the store , to move the segment

	values = []int32{1, 2, 3, 4, 5, 6, math.MaxInt32}

	poolIndex, valueBytes, err = encoder.EncodeINT32Values(values, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	found, valueBytes, err = store.Get(keyBytes, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	assertions.True(found)

	poolIndex, decodedValues, err = decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], string(keyBytes), store.name, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(values, decodedValues)

}

// Scenario - Adding too large column for compaction
func TestStoreCompactionVersion5(t *testing.T) {

	assertions := assert.New(t)

	storeName := "compaction-test-version5"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBuffers := make([][]byte, 1)

	valueBuffers := make([][]byte, 1)

	for index := range valueBuffers {

		valueBuffers[index] = make([]byte, 100)

	}

	var value string

	valus := []string{"a", "b", "c", "d", "e", "f", "g"}

	for index := 0; index < 10000; index++ {

		value = value + valus[rand.Intn(len(valus)-1)]
	}

	keyBytes := []byte("large.column")

	poolIndex, valueBytes, err := encoder.EncodeStringValues([]string{value}, codec.None, utils.MaxValueBytes, string(keyBytes))

	err = store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	for partition := range store.partitions {

		store.partitions[partition].SetLastWriteTimestamp(time.Now().Unix() - 86400)
	}

	store.Cleanup(encoder, keyBuffers, valueBuffers, batches[0], event, waitGroup)

	found, _, err := store.Get(keyBytes, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.False(found)

	assertions.True(strings.Contains(err.Error(), utils.ErrorTooLarge))

	// Now give blobBytes as a valueBUffer

	blobPoolIndex, blobBytes := encoder.MemoryPool.AcquireBlobPool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

	found, bufferBytes, err := store.Get(keyBytes, blobBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeStringValues(codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], string(keyBytes), store.name, 0)

	defer decoder.MemoryPool.ReleaseStringPool(poolIndex)

	assertions.Nil(err)

	assertions.Equal([]string{value}, values)

}

// Scenario - deleted key found during compaction
func TestStoreCompactionVersion6(t *testing.T) {

	assertions := assert.New(t)

	storeName := "compaction-test-version6"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	for index := range valueBuffers {

		valueBuffers[index] = make([]byte, utils.MaxValueBufferBytes)

	}

	values := []int32{1, 2, 3, 4, 5, 6, 78}

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	keyBytes := []byte("1^system.cpu.percent^0")

	err = store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	//deleting the key

	store.Delete(keyBytes, encoder, tokenizer)

	for partition := range store.partitions {

		store.partitions[partition].SetLastWriteTimestamp(time.Now().Unix() - 86400)
	}

	store.Cleanup(encoder, keyBuffers, valueBuffers, batches[0], event, waitGroup)

	found, valueBytes, err := store.Get(keyBytes, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.False(found)

	assertions.Nil(err)

	assertions.Nil(err)

	bytes, err := utils.ReadLogFile("Partition", "storage")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "error deleted/corrupted entry 1^system.cpu.percent^0 found in the partition"))

}

// Scenario - getting key again from the writing segment
func TestStoreCompactionVersion7(t *testing.T) {

	assertions := assert.New(t)

	storeName := "compaction-test-version7"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	values := []string{"value1", "value2", "value3"}

	keyBytes1 := []byte("key1")

	keyBytes2 := []byte("key2")

	poolIndex, valueBytes, err := encoder.EncodeStringValues(values, codec.None, utils.MaxValueBytes, storeName)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	err = store.Put(keyBytes1, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put(keyBytes2, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	valueBuffers = make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	for index := 0; index < len(valueBuffers); index++ {

		keyBuffers[index] = make([]byte, utils.MaxValueBufferBytes)

		valueBuffers[index] = make([]byte, utils.MaxValueBufferBytes)
	}

	store.lastUsedTimestampSeconds.Store(0)

	for partition := range store.partitions {

		store.partitions[partition].SetLastWriteTimestamp(time.Now().Unix() - 86400)
	}

	store.Cleanup(encoder, keyBuffers, valueBuffers, batches[0], event, waitGroup)

	store, _ = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	err = store.Put(keyBytes1, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	found, valueBytes, err := store.Get(keyBytes1, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	found, valueBytes, err = store.Get(keyBytes2, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

}

func TestStoreClose(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("close", utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	for index := range valueBuffers {

		valueBuffers[index] = make([]byte, utils.MaxValueBufferBytes)

	}

	values := []int32{1, 2, 3, 4, 5, 6, 78}

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	keyBytes := []byte("1^system.cpu.percent^0")

	err = store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	for partition := range store.partitions {

		store.partitions[partition].SetLastWriteTimestamp(time.Now().Unix() - 86400)
	}

	found, valueBytes, err := store.Get(keyBytes, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	utils.IdleStoreDetectionThresholdSeconds = 1

	time.Sleep(time.Second * 1)

	store.Cleanup(encoder, keyBuffers, valueBuffers, batches[0], event, waitGroup)

	store, err = OpenOrCreateStore("close", utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	found, valueBytes, err = store.Get(keyBytes, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	assertions.True(found)

	poolIndex, decodedValues, err := decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], string(keyBytes), store.name, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(values, decodedValues)

}

func TestStoreCloseV1(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("close1", utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	store.Close(encoder)

	assertions.Equal(-1, store.Count())

	assertions.Equal(-1, store.GetSize())

	assertions.Nil(store.GetMetrics())

	store.Close(encoder)

	err = store.PutStringMapping("abc", encoder)

	assertions.NotNil(err)

	err = store.PutNumericMapping(int64(1), encoder)

	assertions.NotNil(err)

	_, _, err = store.GetStringMapping("abc")

	assertions.NotNil(err)

	_, _, err = store.GetNumericMapping(int64(1), encoder)

	assertions.NotNil(err)

	err, _, _ = store.ResolveMapping(1, encoder, nil, nil, 100)

	assertions.NotNil(err)

	err, _, _ = store.MapStringValues(1, encoder, 100)

	assertions.NotNil(err)

	err, _, _ = store.MapNumericValues(1, encoder, 100)

	assertions.NotNil(err)

	err = store.GetGreaterThanMappings(int64(1), true, nil, encoder)

	assertions.NotNil(err)

	err = store.GetLessThanMappings(int64(1), true, nil, encoder)

	assertions.NotNil(err)

	err = store.GetPrefixMappings("abc", nil)

	assertions.NotNil(err)

	err = store.GetStringEqualMappings("abc", nil)

	assertions.NotNil(err)

	err = store.GetSuffixMappings("abc", nil)

	assertions.NotNil(err)

	err = store.GetContainMappings("abc", nil)

	assertions.NotNil(err)

}

func TestStoreIndexVersion1(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("index-test-typ1", utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := []byte("key")

	value1 := append(make([]byte, utils.MaxValueBytes), []byte{1, 2, 3}...)

	err = store.Put(keyBytes, value1, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	value2 := []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22}

	err = store.Put(keyBytes, append(make([]byte, utils.MaxValueBytes), value2...), encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	found, valueBytes, err := store.Get(keyBytes, make([]byte, utils.MaxValueBufferBytes), encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	assertions.Equal(value2, valueBytes)
}

func TestStoreIndexVersion2(t *testing.T) {

	goroutine1AbnormalExit := false

	store, err := OpenOrCreateStore("index-test-type2", utils.PerformanceMetric, encoder, tokenizer, false)

	var finalValue []byte

	waitGroup1 := &sync.WaitGroup{}

	waitGroup1.Add(3)

	keyBytes := []byte("key")

	go func() {

		defer waitGroup1.Done()

		value1 := []byte{1, 2, 3}

		value2 := []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22}

		value3 := []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35}

		encoder := codec.NewEncoder(utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools))

		tokenizer := &utils.Tokenizer{
			Tokens: make([]string, 30),
		}

		for i := 0; i < 1000; i++ {

			index := rand.Intn(3)

			if index == 0 {

				err = store.Put(keyBytes, append(make([]byte, utils.MaxValueBytes), value1...), encoder, tokenizer)

				if err != nil {

					goroutine1AbnormalExit = true

					return
				}

				finalValue = value1

			} else if index == 1 {

				err = store.Put(keyBytes, append(make([]byte, utils.MaxValueBytes), value2...), encoder, tokenizer)

				if err != nil {

					goroutine1AbnormalExit = true

					return
				}

				finalValue = value2

			} else if index == 2 {

				err = store.Put(keyBytes, append(make([]byte, utils.MaxValueBytes), value3...), encoder, tokenizer)

				if err != nil {

					goroutine1AbnormalExit = true

					return
				}

				finalValue = value3
			}

			time.Sleep(time.Millisecond * 20)

		}

	}()

	go func() {

		defer waitGroup1.Done()

		value1 := []byte{1, 2, 3}

		value2 := []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22}

		encoder := codec.NewEncoder(utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools))

		tokenizer := &utils.Tokenizer{
			Tokens: make([]string, 30),
		}

		for i := 0; i < 1000; i++ {

			if i%2 == 0 {

				err = store.Put(nil, append(make([]byte, utils.MaxValueBytes), value1...), encoder, tokenizer)

			} else {

				err = store.Put(nil, append(make([]byte, utils.MaxValueBytes), value2...), encoder, tokenizer)

			}

			time.Sleep(time.Millisecond * 10)
		}

	}()

	goroutine2AbnormalExit := false

	go func() {

		defer waitGroup1.Done()

		encoder := codec.NewEncoder(utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools))

		for i := 0; i < 1000; i++ {

			err = store.Sync(encoder)

			if err != nil {

				goroutine2AbnormalExit = true

				return
			}

			time.Sleep(time.Millisecond * 10)

		}
	}()

	waitGroup1.Wait()

	assertions := assert.New(t)

	err = store.Sync(encoder)

	assertions.Nil(err)

	assertions.False(goroutine1AbnormalExit)

	assertions.False(goroutine2AbnormalExit)

	found, valueBytes, err := store.Get(keyBytes, make([]byte, utils.MaxValueBufferBytes), encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	assertions.Equal(finalValue, valueBytes)
}

/*
	------------------------------------------utils.Mapping Test Cases-----------------------------------------------------
*/
/*
	Bug :- store after reopening was starting with the wrong max ordinal than it was supposed to...
	previously we were finding the max key and the ordinal assigned to that key was used as the start point of the next
	upcoming ordinals. we don't need the largest sorted order key we need the largest ordinal that was placed.
*/

func TestStoreStringMappingVersion1(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("string-mappings-test-version1", utils.Mapping, encoder, tokenizer, false)

	value := "group1"

	err = store.PutStringMapping(value, encoder)

	assertions.Nil(err)

	found, mapping2, err := store.GetStringMapping(value)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(mapping2)

	// same mapping again

	err = store.PutStringMapping(value, encoder)

	assertions.Nil(err)

	found, mapping1, err := store.GetStringMapping(value)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(mapping1)

	assertions.Equal(mapping1, mapping2)

}

func TestStoreNumericMappingVersion1(t *testing.T) {

	assertions := assert.New(t)

	storeName := "numeric-mappings-test-version1"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	value := int64(123)

	err = store.PutNumericMapping(value, encoder)

	assertions.Nil(err)

	found, mapping, err := store.GetNumericMapping(value, encoder)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(mapping)

}

func TestStoreStringMappingVersion2(t *testing.T) {

	assertions := assert.New(t)

	storeName := "string-mappings-test-version2"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	poolIndex, values := encoder.MemoryPool.AcquireStringPool(5)

	defer encoder.MemoryPool.ReleaseStringPool(poolIndex)

	values[0] = "instance1"

	values[1] = "instance2"

	values[2] = "instance1"

	values[3] = "instance2"

	values[4] = "instance1"

	err, ordinalPoolIndex, ordinals := store.MapStringValues(poolIndex, encoder, 5)

	assertions.Nil(err)

	assertions.NotNil(ordinals)

	assertions.NotEqual(-1, ordinalPoolIndex)

	found, ordinal, err := store.GetStringMapping("instance1")

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(int32(3), ordinal)

	found, ordinal, err = store.GetStringMapping("instance2")

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(int32(5), ordinal)

	tempOrdinals := make([]int32, 5)

	tempOrdinals[0] = 3

	tempOrdinals[1] = 5

	tempOrdinals[2] = 3

	tempOrdinals[3] = 5

	tempOrdinals[4] = 3

	assertions.Equal(tempOrdinals, ordinals)

	encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

}

func TestStoreNumericMappingVersion2(t *testing.T) {

	assertions := assert.New(t)

	storeName := "numeric-mappings-test-version2"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	poolIndex, values := encoder.MemoryPool.AcquireINT64Pool(5)

	defer encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	values[0] = 123

	values[1] = 12

	values[2] = 12

	values[3] = 123

	values[4] = 12

	err, ordinalPoolIndex, ordinals := store.MapNumericValues(poolIndex, encoder, 5)

	assertions.Nil(err)

	assertions.NotNil(ordinals)

	assertions.NotEqual(-1, ordinalPoolIndex)

	found, ordinal, err := store.GetNumericMapping(values[0], encoder)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(int32(0), ordinal)

	found, ordinal, err = store.GetNumericMapping(values[1], encoder)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(int32(2), ordinal)

	tempOrdinals := make([]int32, 5)

	tempOrdinals[0] = 0

	tempOrdinals[1] = 2

	tempOrdinals[2] = 2

	tempOrdinals[3] = 0

	tempOrdinals[4] = 2

	assertions.Equal(tempOrdinals, ordinals)

	encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

}

func TestStoreNumericMappingVersion3(t *testing.T) {

	assertions := assert.New(t)

	storeName := "numeric-mappings-test-version3"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	poolIndex, values := encoder.MemoryPool.AcquireINT64Pool(5)

	defer encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	values[0] = 123

	values[1] = 12

	values[2] = 12

	values[3] = 123

	values[4] = 12

	err, ordinalPoolIndex, ordinals := store.MapNumericValues(poolIndex, encoder, 5)

	defer encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

	assertions.Nil(err)

	assertions.NotNil(ordinals)

	assertions.NotEqual(-1, ordinalPoolIndex)

	found, ordinal, err := store.GetNumericMapping(values[0], encoder)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(int32(0), ordinal)

	found, ordinal, err = store.GetNumericMapping(values[1], encoder)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(int32(2), ordinal)

	tempOrdinals := make([]int32, 5)

	tempOrdinals[0] = 0

	tempOrdinals[1] = 2

	tempOrdinals[2] = 2

	tempOrdinals[3] = 0

	tempOrdinals[4] = 2

	assertions.Equal(tempOrdinals, ordinals)

	//encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	poolIndex, values = encoder.MemoryPool.AcquireINT64Pool(5)

	defer encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	values[0] = 456

	values[1] = 345

	values[2] = 1234

	values[3] = 12345

	values[4] = 9089

	err, ordinalPoolIndex, ordinals = store.MapNumericValues(poolIndex, encoder, 5)

	defer encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

	assertions.Nil(err)

	assertions.NotNil(ordinals)

	assertions.NotEqual(-1, ordinalPoolIndex)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	found, ordinal, err = store.GetNumericMapping(values[0], encoder)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(int32(4), ordinal)

	found, ordinal, err = store.GetNumericMapping(values[1], encoder)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(int32(6), ordinal)

}

func TestStoreStringMappingVersion3(t *testing.T) {

	assertions := assert.New(t)

	storeName := "string-mappings-test-version3"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	poolIndex, values := encoder.MemoryPool.AcquireStringPool(5)

	defer encoder.MemoryPool.ReleaseStringPool(poolIndex)

	values[0] = ""

	values[1] = ""

	values[2] = ""

	values[3] = ""

	values[4] = ""

	err, ordinalPoolIndex, ordinals := store.MapStringValues(poolIndex, encoder, 5)

	assertions.Nil(err)

	assertions.NotNil(ordinals)

	assertions.NotEqual(-1, ordinalPoolIndex)

	stringMappings := swiss.NewMap[int32, []int](uint32(1000))

	numericMappings := swiss.NewMap[int32, []int](uint32(1000))

	store.cacheNumericMappings.Clear()

	store.cacheStringMappings.Clear()

	err, dataType, mapperPoolIndex := store.ResolveMapping(ordinalPoolIndex, encoder, stringMappings, numericMappings, len(ordinals))

	assertions.Equal(codec.String, dataType)

	mappedValues := encoder.MemoryPool.GetStringPool(mapperPoolIndex)

	assertions.Equal(values, mappedValues)

	encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

	encoder.MemoryPool.ReleaseStringPool(mapperPoolIndex)

}

func TestStoreStringMappingsVersion4(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("string-mappings-test-version4", utils.Mapping, encoder, tokenizer, false)

	value := "1"

	for index := 0; index < 1000_00; index++ {

		err = store.PutStringMapping(value, encoder)

		err = store.PutNumericMapping(int64(codec.StringToINT(value)), encoder)

		assertions.Nil(err)

		found, mapping2, err := store.GetStringMapping(value)

		assertions.True(found)

		assertions.Nil(err)

		assertions.NotNil(mapping2)

		found, mapping2, err = store.GetNumericMapping(int64(codec.StringToINT(value)), encoder)

		assertions.True(found)

		assertions.Nil(err)

		assertions.NotNil(mapping2)
	}

}

func TestStoreStringMappingsVersion5(t *testing.T) {

	assertions := assert.New(t)

	storeName := "string-mappings-test-version5"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	value := "xdvskdvh"

	value1 := "asdkfjbbg"

	value2 := "baksvhabv"

	value3 := "jsdvkahbv"

	value4 := "sdvskvan"

	value5 := "qweouqwer"

	err = store.PutStringMapping(value, encoder)

	found, mappings, err := store.GetStringMapping(value)

	assertions.True(found)

	assertions.Equal(int32(3), mappings)

	err = store.PutStringMapping(value1, encoder)

	found, mappings, err = store.GetStringMapping(value1)

	assertions.True(found)

	assertions.Equal(int32(5), mappings)

	err = store.PutStringMapping(value2, encoder)

	found, mappings, err = store.GetStringMapping(value2)

	assertions.True(found)

	assertions.Equal(int32(7), mappings)

	err = store.PutStringMapping(value3, encoder)

	found, mappings, err = store.GetStringMapping(value3)

	assertions.True(found)

	assertions.Equal(int32(9), mappings)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	err = store.PutStringMapping(value4, encoder)

	found, mappings, err = store.GetStringMapping(value4)

	assertions.True(found)

	assertions.Equal(int32(11), mappings)

	err = store.PutStringMapping(value5, encoder)

	found, mappings, err = store.GetStringMapping(value5)

	assertions.True(found)

	assertions.Equal(int32(13), mappings)

}

func TestStoreResolveMappingVersion1(t *testing.T) {

	assertions := assert.New(t)

	stringMappings := swiss.NewMap[int32, []int](uint32(1000))

	numericMappings := swiss.NewMap[int32, []int](uint32(1000))

	storeName := "resolve-mappings-test-version1"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	length := 5

	numericPoolIndex, numericValues := encoder.MemoryPool.AcquireINT64Pool(length)

	defer encoder.MemoryPool.ReleaseINT64Pool(numericPoolIndex)

	count := 0

	for i := int64(1); i <= int64(length); i++ {

		numericValues[count] = i

		count++
	}

	err, numericOrdinalPoolIndex, numericOrdinals := store.MapNumericValues(numericPoolIndex, encoder, length)

	defer encoder.MemoryPool.ReleaseINT32Pool(numericOrdinalPoolIndex)

	assertions.Nil(err)

	assertions.NotEqual(utils.NotAvailable, numericOrdinalPoolIndex)

	assertions.NotNil(numericOrdinals)

	store.cacheNumericMappings.Clear()

	store.cacheStringMappings.Clear()

	err, dataType, poolIndex := store.ResolveMapping(numericOrdinalPoolIndex, encoder, stringMappings, numericMappings, len(numericOrdinals))

	assertions.NotEqual(utils.NotAvailable, poolIndex)

	assertions.Equal(codec.Int64, dataType)

	assertions.Nil(err)

	resolvedValues1 := encoder.MemoryPool.GetINT64Pool(poolIndex)

	for index, value := range resolvedValues1[:len(numericValues)] {

		assertions.Equal(numericValues[index], value)
	}

	encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	stringPoolIndex, stringValues := encoder.MemoryPool.AcquireStringPool(length)

	defer encoder.MemoryPool.ReleaseStringPool(stringPoolIndex)

	count = 0

	for i := 1; i <= length; i++ {

		stringValues[count] = "value" + codec.INTToStringValue(i)

		count++
	}

	err, stringOrdinalPoolIndex, stringOrdinals := store.MapStringValues(stringPoolIndex, encoder, length)

	defer encoder.MemoryPool.ReleaseINT32Pool(stringOrdinalPoolIndex)

	assertions.Nil(err)

	assertions.NotEqual(utils.NotAvailable, stringOrdinalPoolIndex)

	assertions.NotNil(stringOrdinals)

	ordinalPoolIndex, ordinals := encoder.MemoryPool.AcquireINT32Pool(len(numericOrdinals) + len(stringOrdinals))

	defer encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

	assertions.NotNil(ordinals)

	copy(ordinals, numericOrdinals)

	copy(ordinals[len(numericOrdinals):], stringOrdinals)

	assertions.NotEqual(utils.NotAvailable, ordinalPoolIndex)

	store.cacheNumericMappings.Clear()

	store.cacheStringMappings.Clear()

	err, dataType, poolIndex = store.ResolveMapping(ordinalPoolIndex, encoder, stringMappings, numericMappings, len(ordinals))

	assertions.NotEqual(utils.NotAvailable, poolIndex)

	assertions.Equal(codec.String, dataType)

	assertions.Nil(err)

	resolvedValues2 := encoder.MemoryPool.GetStringPool(poolIndex)

	for index, value := range resolvedValues2[:len(numericValues)] {

		assertions.Equal(codec.INT64ToStringValue(numericValues[index]), value)
	}

	for index, value := range resolvedValues2[len(numericValues):] {

		assertions.Equal(stringValues[index], value)
	}

	err = store.Sync(encoder)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseStringPool(poolIndex)

	store.cacheNumericMappings.Clear()

	store.cacheStringMappings.Clear()

	err, dataType, poolIndex = store.ResolveMapping(ordinalPoolIndex, encoder, stringMappings, numericMappings, len(ordinals))

	assertions.NotEqual(utils.NotAvailable, poolIndex)

	assertions.Equal(codec.String, dataType)

	assertions.Nil(err)

	resolvedValues2 = encoder.MemoryPool.GetStringPool(poolIndex)

	for index, value := range resolvedValues2[:len(numericValues)] {

		assertions.Equal(codec.INT64ToStringValue(numericValues[index]), value)
	}

	for index, value := range resolvedValues2[len(numericValues):] {

		assertions.Equal(stringValues[index], value)
	}

	encoder.MemoryPool.ReleaseStringPool(poolIndex)

}

func TestStoreClearTempUsedMappings(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("cleanup", utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	store.ResetTempMapping()

	assertions.False(store.cleanup)

	assertions.Equal(store.tempMappingOrdinals.Count(), 0)
}

func TestRebuildStringMappings(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("rebuild-string-mappings", utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	err = store.PutStringMapping("key1", encoder)

	assertions.Nil(err)

	err = store.PutStringMapping("key2", encoder)

	assertions.Nil(err)

	err = store.PutStringMapping("key3", encoder)

	assertions.Nil(err)

	err = rebuildStringMapping(store, encoder, Partial)

	assertions.Nil(err)

	stringMappings := make(utils.MotadataMap)

	store.ListStringMappings(stringMappings)

	assertions.Equal(len(stringMappings), 3)

	assertions.True(stringMappings.Contains("key1"))

	assertions.True(stringMappings.Contains("key2"))

	assertions.True(stringMappings.Contains("key3"))

}

/*miscellaneous tests*/

func TestStoreGetPartition(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(uint8(0), getPartitionLength(utils.Mapping))

	assertions.Equal(uint8(2), getPartitionLength(utils.None))

}

func TestOpenInvalidStore(t *testing.T) {

	storeName := "open-invalid-store"

	store, err := OpenOrCreateStore(storeName, 255, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.NotNil(err)

	assertions.Nil(store)

	store, err = OpenOrCreateStore(utils.Empty, utils.None, encoder, tokenizer, false)

	assertions.Nil(store)

	assertions.NotNil(err)
}

func TestPartitionDoListingKeys(t *testing.T) {

	assertions := assert.New(t)

	storeName := "do-listing"

	store, err := OpenOrCreateStore(storeName, utils.Log, encoder, tokenizer, false)

	assertions.Nil(err)

	key1Bytes := []byte("stringKey1")

	key2Bytes := []byte("stringKey2")

	key3Bytes := []byte("stringKey3")

	key4Bytes := []byte("40000000000")

	key5Bytes := []byte("50000000000")

	poolIndex, encodedBytes, err := encoder.EncodeINT32Values([]int32{1}, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	assertions.Nil(err)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Put(key1Bytes, encodedBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put(key2Bytes, encodedBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put(key3Bytes, encodedBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put(key4Bytes, encodedBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put(key5Bytes, encodedBytes, encoder, tokenizer)

	assertions.Nil(err)

	stringValues := swiss.NewMap[string, int32](uint32(1000))

	stringValues.Put(string(key1Bytes), 1)

	stringValues.Put(string(key2Bytes), 1)

	numericValues := swiss.NewMap[int64, int32](uint32(1000))

	numericValues.Put(int64(binary.BigEndian.Uint64(key4Bytes)), 1)

	keyBuffers, err := store.GetKeys(stringValues, nil, true, codec.Invalid)

	assertions.NotNil(keyBuffers)

	assertions.Equal(string(keyBuffers[0]), string(key3Bytes))

	keyBuffers, err = store.GetKeys(stringValues, nil, false, codec.Invalid)

	assertions.NotNil(keyBuffers)

	assertions.Equal(len(keyBuffers), 5)

	keyBuffers, err = store.GetKeys(nil, numericValues, true, codec.Invalid)

	assertions.Equal(len(keyBuffers), 4)

	valid := false

	for _, key := range keyBuffers {

		if strings.EqualFold(string(key), string(key4Bytes)) {

			valid = true

			break
		}
	}

	assertions.False(valid)

}

func TestRebuildStringMapping(t *testing.T) {

	assertions := assert.New(t)

	storeName := "rebuild-string-mappings-test-version5"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	value := "xdvskdvh"

	value1 := "asdkfjbbg"

	value2 := "acbcd"

	err = store.PutStringMapping(value, encoder)

	found, mappings, err := store.GetStringMapping(value)

	assertions.True(found)

	assertions.Equal(int32(3), mappings)

	err = store.PutStringMapping(value1, encoder)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	err = store.PutStringMapping(value2, encoder)

	assertions.Nil(err)

	assertions.Nil(rebuildStringMapping(store, encoder, Partial))

}

func TestDoGreaterThanMappingVersion1(t *testing.T) {

	assertions := assert.New(t)

	storeName := "dummy-mappings"

	var conditionValue int64 = 12

	mappings := swiss.NewMap[int64, int32](uint32(1000))

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	poolIndex, values := encoder.MemoryPool.AcquireINT64Pool(2)

	defer encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	values[0] = 12

	values[1] = 24

	err, poolIndex, ordinals := store.MapNumericValues(poolIndex, encoder, 2)

	defer encoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.NotNil(ordinals)

	assertions.Nil(err)

	doGreaterThanMapping(store, conditionValue, true, mappings, encoder)

	assertions.Equal(2, mappings.Count())

	mappings = swiss.NewMap[int64, int32](uint32(1000))

	doGreaterThanMapping(store, conditionValue, false, mappings, encoder)

	assertions.Equal(1, mappings.Count())

}

func TestDoLessThanMappingVersion1(t *testing.T) {

	assertions := assert.New(t)

	storeName := "dummy-mappings1"

	var conditionValue int64 = 24

	mappings := swiss.NewMap[int64, int32](uint32(1000))

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	poolIndex, values := encoder.MemoryPool.AcquireINT64Pool(2)

	defer encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	values[0] = 12

	values[1] = 24

	err, poolIndex, ordinals := store.MapNumericValues(poolIndex, encoder, 2)

	defer encoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.NotNil(ordinals)

	assertions.Nil(err)

	mappings = swiss.NewMap[int64, int32](uint32(1000))

	doLessThanMapping(store, conditionValue, true, mappings, encoder)

	assertions.Equal(2, mappings.Count())

	mappings = swiss.NewMap[int64, int32](uint32(1000))

	doLessThanMapping(store, conditionValue, false, mappings, encoder)

	assertions.Equal(1, mappings.Count())

}

func TestDoPrefixMapping(t *testing.T) {

	assertions := assert.New(t)

	storeName := "prefix-mapping1"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	err = store.PutStringMapping("abcd", encoder)

	assertions.Nil(err)

	mappings := swiss.NewMap[string, int32](uint32(1000))

	conditionValue := "ab"

	doPrefixMapping(store, conditionValue, mappings)

	assertions.Equal(1, mappings.Count())

}

func TestDoSuffixMapping(t *testing.T) {

	assertions := assert.New(t)

	storeName := "suffix-mapping"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	err = store.PutStringMapping("abcd", encoder)

	assertions.Nil(err)

	mappings := swiss.NewMap[string, int32](uint32(1000))

	conditionValue := "cd"

	doSuffixMapping(store, conditionValue, mappings)

	assertions.Equal(1, mappings.Count())

}

func TestDoStringEqualMapping(t *testing.T) {

	assertions := assert.New(t)

	storeName := "equals-mapping"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	err = store.PutStringMapping("abcd", encoder)

	assertions.Nil(err)

	mappings := swiss.NewMap[string, int32](uint32(1000))

	conditionValue := "abcd"

	doStringEqualMapping(store, conditionValue, mappings)

	assertions.Equal(1, mappings.Count())

}

func TestDoStringContainMapping(t *testing.T) {

	assertions := assert.New(t)

	storeName := "contain-mapping"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	err = store.PutStringMapping("abcd", encoder)

	assertions.Nil(err)

	mappings := swiss.NewMap[string, int32](uint32(1000))

	conditionValue := "bc"

	doContainMapping(store, conditionValue, mappings)

	assertions.Equal(1, mappings.Count())

}

func TestListStringMappings(t *testing.T) {

	assertions := assert.New(t)

	storeName := "list-mapping"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	err = store.PutStringMapping("abcd", encoder)

	assertions.Nil(err)

	mappings := utils.MotadataMap{}

	listStringMappings(store, mappings)

	assertions.Equal(1, len(mappings))

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	mappings = utils.MotadataMap{}

	listStringMappings(store, mappings)

	assertions.Equal(1, len(mappings))

}

func TestMapStringValues(t *testing.T) {

	assertions := assert.New(t)

	stroreName := "mapString-mapping"

	encoder := codec.NewEncoder(utils.NewMemoryPool(6, utils.MaxPoolLength, false, utils.DefaultBlobPools))

	store, err := OpenOrCreateStore(stroreName, utils.Mapping, encoder, tokenizer, false)

	store.PutStringMapping("test1", encoder)

	assertions.Nil(err)

	size := 5

	poolIndex, values := encoder.MemoryPool.AcquireStringPool(size)

	defer encoder.MemoryPool.ReleaseStringPool(poolIndex)

	values[0] = "test1"
	values[1] = "test2"
	values[2] = "test3"
	values[3] = ""
	values[4] = "test5"

	err, _, ordinalValues := store.MapStringValues(poolIndex, encoder, size)

	expectedValues := []int32{3, 5, 7, 1, 9}

	assertions.Equal(expectedValues, ordinalValues)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore(stroreName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	poolIndex2, values := encoder.MemoryPool.AcquireStringPool(size)

	defer encoder.MemoryPool.ReleaseStringPool(poolIndex2)

	values[0] = "test1"
	values[1] = "test2"
	values[2] = "test3"
	values[3] = ""
	values[4] = "test5"

	err, _, ordinalValues = store.MapStringValues(poolIndex2, encoder, size)

	expectedValues = []int32{3, 5, 7, 1, 9}

	assertions.Equal(expectedValues, ordinalValues)

	store.PutStringMapping("test6", encoder)

	poolIndex3, values := encoder.MemoryPool.AcquireStringPool(size)

	defer encoder.MemoryPool.ReleaseStringPool(poolIndex3)

	values[0] = "test7"
	values[1] = "test8"
	values[2] = "test9"
	values[3] = "test10"
	values[4] = "test11"

	err, _, ordinalValues = store.MapStringValues(poolIndex3, encoder, size)

	expectedValues = []int32{13, 15, 17, 19, 21}

	assertions.Equal(expectedValues, ordinalValues)

	poolIndex4, values := encoder.MemoryPool.AcquireStringPool(size)

	defer encoder.MemoryPool.ReleaseStringPool(poolIndex4)

	values[0] = "test1"
	values[1] = "test11"
	values[2] = "test12"
	values[3] = "test13"
	values[4] = "test14"

	err, _, ordinalValues = store.MapStringValues(poolIndex4, encoder, size)

	expectedValues = []int32{3, 21, 23, 25, 27}

	assertions.Equal(expectedValues, ordinalValues)

	poolIndex5, values := encoder.MemoryPool.AcquireStringPool(size)

	defer encoder.MemoryPool.ReleaseStringPool(poolIndex5)

	values[0] = ""
	values[1] = "test1"
	values[2] = "test12"
	values[3] = "test13"
	values[4] = "test14"

	err, _, ordinalValues = store.MapStringValues(poolIndex5, encoder, size)

	expectedValues = []int32{1, 3, 23, 25, 27}

	assertions.Equal(expectedValues, ordinalValues)

	storeName2 := "mapStrings2-mapping"

	store, err = OpenOrCreateStore(storeName2, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	err, _, ordinalValues = store.MapStringValues(poolIndex5, encoder, size)

	expectedValues = []int32{1, 3, 5, 7, 9}

	assertions.Equal(expectedValues, ordinalValues)

	size = 0

	err, index, ordinalValues := store.MapStringValues(poolIndex5, encoder, size)

	assertions.Nil(ordinalValues)

	assertions.Equal(-1, index)

}

func TestOpenPartition(t *testing.T) {

	defer cleanup()

	assertions := assert.New(t)

	storeName := "openPartion-store"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	key := "1^instance^interface^0"

	values := []byte{12, 1, 1, 1, 1, 1, 1, 1}

	store.writeTxn([]byte(key), values)

	partition := store.GetPartition([]byte(key), tokenizer)

	err = store.putTxn(partition)

	assertions.Nil(err)

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.Nil(err)

	keys, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	isContainKey := false

	for index := range keys {

		if strings.Contains(string(keys[index]), key) {
			isContainKey = true
		}

	}

	assertions.True(isContainKey)

}

func TestOpenStoreWitOnlyMergedFile112(t *testing.T) {

	assertions := assert.New(t)

	storeName := "mergedfile-mapping" // + utils.HyphenSeparator+utils.Aggregations

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	store.PutNumericMapping(12, encoder)

	store.Close(encoder)

	err = os.Rename(store.path+utils.PathSeparator+FileMapping112, store.path+utils.PathSeparator+FileMerged112)

	assertions.Nil(err)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)
}

func TestOpenStoreWithInvalidTemp112File(t *testing.T) {

	assertions := assert.New(t)

	storeName := "invalidTempFile-mapping"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	store.PutNumericMapping(12, encoder)

	store.Close(encoder)

	err = os.MkdirAll(store.path+utils.PathSeparator+"temp.112", 0777)

	assertions.Nil(err)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.NotNil(store)

	assertions.Nil(err)

}

func TestOpenStoreWithInvalidFileMapping112File(t *testing.T) {

	assertions := assert.New(t)

	storeName := "noMappingFile-mapping"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	store.PutNumericMapping(12, encoder)

	store.Close(encoder)

	err = os.Remove(store.path + utils.PathSeparator + FileMapping112)

	assertions.Nil(err)

	err = os.MkdirAll(store.path+utils.PathSeparator+FileMapping112, 0777)

	assertions.Nil(err)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.NotNil(store)

	assertions.Nil(err)

}

func TestOpenStoreWitOnlyMergedFile176(t *testing.T) {

	assertions := assert.New(t)

	storeName := "mergedfile176-mapping" // + utils.HyphenSeparator+utils.Aggregations

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	store.PutStringMapping("test", encoder)

	store.Close(encoder)

	err = os.Rename(store.path+utils.PathSeparator+FileMapping176, store.path+utils.PathSeparator+FileMerged176)

	assertions.Nil(err)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)
}

func TestOpenStoreWitInvalidMergedFile176(t *testing.T) {

	assertions := assert.New(t)

	storeName := "noMergedfile176-mapping" // + utils.HyphenSeparator+utils.Aggregations

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	store.PutStringMapping("test", encoder)

	store.Close(encoder)

	err = os.Remove(store.path + utils.PathSeparator + FileMapping176)

	assertions.Nil(err)

	path := store.path

	err = os.Mkdir(store.path+utils.PathSeparator+FileMapping176, 0655)

	assertions.Nil(err)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	err = os.Mkdir(path+utils.PathSeparator+FileTemp176, 0655)

	assertions.Nil(err)

	os.Remove(path + utils.PathSeparator + FileMapping176)

	err = os.Mkdir(path+utils.PathSeparator+FileMapping176, 0655)

	assertions.Nil(err)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

}

func TestInvalidRebuildStringMapping(t *testing.T) {

	assertions := assert.New(t)

	storeName := "invalid-rebuild-string"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	store.PutStringMapping("test", encoder)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	err = rebuildStringMapping(store, encoder, Partial)

	assertions.Error(err)

}

func TestStoreInvalidBackupFile176Version1(t *testing.T) {

	assertions := assert.New(t)

	storeName := "invalid-bkp-file-176-v1"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	store.PutStringMapping("test", encoder)

	store.Close(encoder)

	err = os.WriteFile(store.path+utils.PathSeparator+FileTemp176, []byte{}, 0666)

	assertions.Nil(err)

	err = os.Remove(store.path + utils.PathSeparator + FileBKP176)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(store)

	assertions.NotNil(err)
}

func TestStoreInvalidBackupFile112Version1(t *testing.T) {

	assertions := assert.New(t)

	storeName := "invalid-bkp-file-112-v1"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	err = store.PutNumericMapping(12, encoder)

	store.Close(encoder)

	assertions.Nil(err)

	err = os.WriteFile(store.path+utils.PathSeparator+FileTemp112, []byte{}, 0666)

	assertions.Nil(err)

	err = os.Remove(store.path + utils.PathSeparator + FileBKP112)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(store)

	assertions.NotNil(err)
}

func TestStoreInvalidBackupFile176Version2(t *testing.T) {

	assertions := assert.New(t)

	storeName := "invalid-bkp-file-176-v2"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	err = store.PutStringMapping("test", encoder)

	assertions.Nil(err)

	store.Close(encoder)

	err = os.WriteFile(store.path+utils.PathSeparator+FileTemp176, []byte{}, 0666)

	assertions.Nil(err)

	err = os.Remove(store.path + utils.PathSeparator + FileBKP176)

	err = os.MkdirAll(store.path+utils.PathSeparator+FileBKP176, 0777)

	err = rebuildStringMapping(store, encoder, Full)

	assertions.NotNil(err)
}

func TestStoreInvalidBackupFile112Version2(t *testing.T) {

	assertions := assert.New(t)

	storeName := "invalid-bkp-file-112-v2"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	err = store.PutNumericMapping(12, encoder)

	store.Close(encoder)

	assertions.Nil(err)

	err = os.WriteFile(store.path+utils.PathSeparator+FileTemp112, []byte{}, 0666)

	assertions.Nil(err)

	err = os.Remove(store.path + utils.PathSeparator + FileBKP112)

	err = os.MkdirAll(store.path+utils.PathSeparator+FileBKP112, 0777)

	err = rebuildNumericMapping(store, encoder, Full)

	assertions.NotNil(err)
}

/*
	Bug fixes
	description:- the datastore type status metric was not considered while calculating the partition index
*/

func TestStoreGetPartitionKey(t *testing.T) {

	assertions := assert.New(t)

	storeName := "get-partition-key-test"

	key := GetPartitionKey(utils.ObjectStatusMetric, []byte("1^system.cpu.percent^0"), storeName, tokenizer)

	assertions.NotNil(key)

	assertions.Equal([]byte("1"), key)

	key = GetPartitionKey(utils.Index, []byte("tcp"), storeName, tokenizer)

	assertions.NotNil(key)

	assertions.Equal([]byte("tcp"), key)

	key = GetPartitionKey(utils.StaticMetric, []byte("tcp"), storeName, tokenizer)

	assertions.NotNil(key)

	assertions.Equal([]byte("tcp"), key)

	key = GetPartitionKey(utils.EventPolicy, []byte("123456789^severity^0"), "20072023-0-event.policy", tokenizer)

	assertions.NotNil(key)

	assertions.Equal([]byte("123456789^severity"), key)

	key = GetPartitionKey(utils.EventPolicy, []byte("123456789^severity^time^0"), "20072023-0-event.policy", tokenizer)

	assertions.NotNil(key)

	assertions.Equal([]byte("123456789^severity"), key)

	tick := codec.INT32ToStringValue(utils.UnixToSeconds(time.Now().Unix()))

	key = GetPartitionKey(utils.Log, []byte(tick+"^event.source^0"), storeName, tokenizer)

	assertions.NotNil(key)

	assertions.Equal([]byte(tick), key)
}

func TestFlowTooLargeSmallDeployment(t *testing.T) {

	storeName := "too_large_flow_small"

	poolLength := 10000

	dataStoreType := utils.Flow

	store, err := OpenOrCreateStore(storeName, dataStoreType, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	testKey := "1234566^volume.bytes"

	overflowLength := poolLength - ((poolLength * 10) / 100)

	int64Values := make([]int64, overflowLength)

	float64Values := make([]float64, overflowLength)

	for i := range int64Values {

		int64Values[i] = rand.Int63n(codec.MaxInt56)
	}

	codec.INT64ToFLOAT64Values(int64Values, float64Values)

	encodedPoolIndex, encodedBytes, err := encoder.EncodeFLOAT64Values(float64Values, codec.None, codec.Float64, utils.MaxValueBytes)

	assertions.Nil(err)

	assertions.True(len(encodedBytes) > 0)

	err = store.Put([]byte(testKey), encodedBytes, encoder, tokenizer)

	encoder.MemoryPool.ReleaseBytePool(encodedPoolIndex)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, dataStoreType, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	valueBufferBytes := make([]byte, poolLength*8)

	found, valueBytes, err := store.Get([]byte(testKey), valueBufferBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.NotNil(valueBytes)

	assertions.Nil(err)

	poolIndex, values, err := decoder.DecodeFLOAT64Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", "", 0)

	decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(len(float64Values), len(values))

	assertions.EqualValues(float64Values, values)
}

func TestLogTooLargeSmallDeployment(t *testing.T) {

	storeName := "too_large_log_small"

	poolLength := 5000

	dataStoreType := utils.Log

	store, err := OpenOrCreateStore(storeName, dataStoreType, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	testKey := "1234566^packets"

	overflowLength := poolLength - ((poolLength * 10) / 100)

	int64Values := make([]int64, overflowLength)

	float64Values := make([]float64, overflowLength)

	for i := range int64Values {

		int64Values[i] = rand.Int63n(codec.MaxInt56)
	}

	codec.INT64ToFLOAT64Values(int64Values, float64Values)

	encodedPoolIndex, encodedBytes, err := encoder.EncodeFLOAT64Values(float64Values, codec.None, codec.Float64, utils.MaxValueBytes)

	assertions.Nil(err)

	assertions.True(len(encodedBytes) > 0)

	err = store.Put([]byte(testKey), encodedBytes, encoder, tokenizer)

	encoder.MemoryPool.ReleaseBytePool(encodedPoolIndex)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, dataStoreType, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	valueBufferBytes := make([]byte, poolLength*8)

	found, valueBytes, err := store.Get([]byte(testKey), valueBufferBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.NotNil(valueBytes)

	assertions.Nil(err)

	poolIndex, values, err := decoder.DecodeFLOAT64Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", "", 0)

	decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(len(float64Values), len(values))

	assertions.EqualValues(float64Values, values)
}

func TestFlowTooLargeMediumDeployment(t *testing.T) {

	storeName := "too_large_flow_medium"

	poolLength := 20000

	dataStoreType := utils.Flow

	pool := utils.NewMemoryPool(5, poolLength, false, utils.DefaultBlobPools)

	encoder := codec.NewEncoder(pool)

	decoder := codec.NewDecoder(pool)

	store, err := OpenOrCreateStore(storeName, dataStoreType, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	testKey := "1234566^volume.bytes"

	overflowLength := poolLength - ((poolLength * 10) / 100)

	int64Values := make([]int64, overflowLength)

	float64Values := make([]float64, overflowLength)

	for i := range int64Values {

		int64Values[i] = rand.Int63n(codec.MaxInt56)
	}

	codec.INT64ToFLOAT64Values(int64Values, float64Values)

	encodedPoolIndex, encodedBytes, err := encoder.EncodeFLOAT64Values(float64Values, codec.None, codec.Float64, utils.MaxValueBytes)

	assertions.Nil(err)

	assertions.True(len(encodedBytes) > 0)

	err = store.Put([]byte(testKey), encodedBytes, encoder, tokenizer)

	encoder.MemoryPool.ReleaseBytePool(encodedPoolIndex)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, dataStoreType, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	valueBufferBytes := make([]byte, poolLength*8)

	found, valueBytes, err := store.Get([]byte(testKey), valueBufferBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.NotNil(valueBytes)

	assertions.Nil(err)

	poolIndex, values, err := decoder.DecodeFLOAT64Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", "", 0)

	decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(len(float64Values), len(values))

	assertions.EqualValues(float64Values, values)
}

func TestFlowTooLargeLargeDeployment(t *testing.T) {

	storeName := "too_large_flow_large"

	poolLength := 40000

	dataStoreType := utils.Flow

	pool := utils.NewMemoryPool(5, poolLength, false, utils.DefaultBlobPools)

	encoder := codec.NewEncoder(pool)

	decoder := codec.NewDecoder(pool)

	store, err := OpenOrCreateStore(storeName, dataStoreType, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	testKey := "1234566^volume.bytes"

	overflowLength := poolLength - ((poolLength * 10) / 100)

	int64Values := make([]int64, overflowLength)

	float64Values := make([]float64, overflowLength)

	for i := range int64Values {

		int64Values[i] = rand.Int63n(codec.MaxInt56)
	}

	codec.INT64ToFLOAT64Values(int64Values, float64Values)

	encodedPoolIndex, encodedBytes, err := encoder.EncodeFLOAT64Values(float64Values, codec.None, codec.Float64, utils.MaxValueBytes)

	assertions.Nil(err)

	assertions.True(len(encodedBytes) > 0)

	err = store.Put([]byte(testKey), encodedBytes, encoder, tokenizer)

	encoder.MemoryPool.ReleaseBytePool(encodedPoolIndex)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, dataStoreType, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	valueBufferBytes := make([]byte, poolLength*8)

	found, valueBytes, err := store.Get([]byte(testKey), valueBufferBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.NotNil(valueBytes)

	assertions.Nil(err)

	poolIndex, values, err := decoder.DecodeFLOAT64Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", "", 0)

	decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(len(float64Values), len(values))

	assertions.EqualValues(float64Values, values)
}

func TestLogTooLargeLargeDeployment(t *testing.T) {

	storeName := "too_large_log_large"

	poolLength := 10000

	dataStoreType := utils.Log

	store, err := OpenOrCreateStore(storeName, dataStoreType, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	testKey := "1234566^packets"

	overflowLength := poolLength - ((poolLength * 10) / 100)

	int64Values := make([]int64, overflowLength)

	float64Values := make([]float64, overflowLength)

	for i := range int64Values {

		int64Values[i] = rand.Int63n(codec.MaxInt56)
	}

	codec.INT64ToFLOAT64Values(int64Values, float64Values)

	encodedPoolIndex, encodedBytes, err := encoder.EncodeFLOAT64Values(float64Values, codec.None, codec.Float64, utils.MaxValueBytes)

	assertions.Nil(err)

	assertions.True(len(encodedBytes) > 0)

	err = store.Put([]byte(testKey), encodedBytes, encoder, tokenizer)

	encoder.MemoryPool.ReleaseBytePool(encodedPoolIndex)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, dataStoreType, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	valueBufferBytes := make([]byte, poolLength*8)

	found, valueBytes, err := store.Get([]byte(testKey), valueBufferBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.NotNil(valueBytes)

	assertions.Nil(err)

	poolIndex, values, err := decoder.DecodeFLOAT64Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", "", 0)

	decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(len(float64Values), len(values))

	assertions.EqualValues(float64Values, values)
}

func TestFlowTooLargeExtraLargeDeployment(t *testing.T) {

	storeName := "too_large_flow_extra_large"

	poolLength := 60000

	dataStoreType := utils.Flow

	pool := utils.NewMemoryPool(5, poolLength, false, utils.DefaultBlobPools)

	encoder := codec.NewEncoder(pool)

	decoder := codec.NewDecoder(pool)

	store, err := OpenOrCreateStore(storeName, dataStoreType, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	testKey := "1234566^volume.bytes"

	overflowLength := poolLength - ((poolLength * 10) / 100)

	int64Values := make([]int64, overflowLength)

	float64Values := make([]float64, overflowLength)

	for i := range int64Values {

		int64Values[i] = rand.Int63n(codec.MaxInt56)
	}

	codec.INT64ToFLOAT64Values(int64Values, float64Values)

	encodedPoolIndex, encodedBytes, err := encoder.EncodeFLOAT64Values(float64Values, codec.None, codec.Float64, utils.MaxValueBytes)

	assertions.Nil(err)

	assertions.True(len(encodedBytes) > 0)

	err = store.Put([]byte(testKey), encodedBytes, encoder, tokenizer)

	encoder.MemoryPool.ReleaseBytePool(encodedPoolIndex)

	assertions.Nil(err)

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, dataStoreType, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	valueBufferBytes := make([]byte, poolLength*8)

	found, valueBytes, err := store.Get([]byte(testKey), valueBufferBytes, encoder, event, waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.NotNil(valueBytes)

	assertions.Nil(err)

	poolIndex, values, err := decoder.DecodeFLOAT64Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", "", 0)

	decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.Equal(len(float64Values), len(values))

	assertions.EqualValues(float64Values, values)
}

func TestPartitionDeleteFlush(t *testing.T) {

	store, err := OpenOrCreateStore("dummy-testing-partition-type-3", utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	err = store.partitions[0].delete([]byte("dummy-key1"), encoder)

	assert.Nil(t, err)

}

func TestPartitionFlush(t *testing.T) {

	store, err := OpenOrCreateStore("dummy-testing-partition-type-4", utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	key := "dummy-key-1"

	value := "dummy-value"

	store.partitions[0].put([]byte(key), []byte(value), encoder, true)

	store.partitions[0].segmentFiles = map[byte]*os.File{}

	store.partitions[0].segmentBuffers = map[byte]MMap{}

	store.partitions[0].writingSegments = map[SegmentType]Segment{}

	err = store.partitions[0].flush([]byte(key), []byte(value), encoder)

	assert.Nil(t, err)

	store.partitions[0].segmentFiles = map[byte]*os.File{}

	store.partitions[0].segmentBuffers = map[byte]MMap{}

	err = store.partitions[0].flush([]byte(key), []byte(value), encoder)

	assert.NotNil(t, err)

	store.partitions[0].segmentFiles = map[byte]*os.File{}

	store.partitions[0].segmentBuffers = map[byte]MMap{}

	store.partitions[0].writingSegments = map[SegmentType]Segment{}

	store.partitions[0].tempIndexMemoryMappedBytes = MMap{0, 1, 2, 3, 4}

	store.partitions[0].tempIndexSize = 10000

	err = store.partitions[0].flush([]byte(key), []byte(value), encoder)

	assert.NotNil(t, err)

	value = "testing-partiotion-error-blocks testing-1 testing-2 testing-3"

	store.partitions[0].segmentFiles = map[byte]*os.File{}

	store.partitions[0].segmentBuffers = map[byte]MMap{}

	store.partitions[0].writingSegments[calculateWritingSegmentType(len(value))] = Segment0

	err = store.partitions[0].flush([]byte(key), []byte(value), encoder)

	assert.NotNil(t, err)

	store.partitions[0].tempIndexMemoryMappedBytes = MMap{0, 1, 2, 3, 4}

	store.partitions[0].tempIndexSize = 10000

	store.partitions[0].segmentFiles = map[byte]*os.File{}

	store.partitions[0].segmentBuffers = map[byte]MMap{}

	store.partitions[0].writingSegments = map[SegmentType]Segment{}

	err = store.partitions[0].flush([]byte(key), []byte(value), encoder)

	assert.NotNil(t, err)

}

func TestPartitionPutBlob(t *testing.T) {

	store, err := OpenOrCreateStore("dummy-testing-partition-type-6", utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	partition := store.partitions[0]

	partition.blobFile = nil

	os.MkdirAll(partition.path+utils.PathSeparator+BlobFile, 0777)

	_, err = partition.putBlob([]byte("testing-1"))

	assert.NotNil(t, err)

	file, err := os.Create("dummy-blobfile")

	defer func() {
		os.Remove("dummy-blobfile")
	}()

	assert.Nil(t, err)

	partition.blobFile = file

	file.Close()

	_, err = partition.putBlob([]byte("testing-1"))

	assert.NotNil(t, err)

}

func TestPartitionPut(t *testing.T) {

	store, err := OpenOrCreateStore("dummy-testing-partition-type-7", utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	partition := store.partitions[0]

	keyBytes := []byte("dummy-key-1")

	valueBytes := []byte("dummy-value")

	partition.put(keyBytes, valueBytes, encoder, true)

	partition.segmentFiles = map[byte]*os.File{}

	partition.segmentBuffers = map[byte]MMap{}

	partition.writingSegments = map[SegmentType]Segment{}

	err = partition.put(keyBytes, valueBytes, encoder, true)

	assert.Nil(t, err)

	partition.segmentFiles = map[byte]*os.File{}

	partition.segmentBuffers = map[byte]MMap{}

	err = partition.put(keyBytes, valueBytes, encoder, true)

	assert.NotNil(t, err)

	partition.segmentFiles = map[byte]*os.File{}

	partition.segmentBuffers = map[byte]MMap{}

	partition.writingSegments = map[SegmentType]Segment{}

	mappedBytes := partition.tempIndexMemoryMappedBytes

	partition.tempIndexMemoryMappedBytes = MMap{0, 1, 2, 3}

	err = partition.put(keyBytes, valueBytes, encoder, true)

	assert.NotNil(t, err)

	valueBytes = make([]byte, 50)

	partition.segmentFiles = map[byte]*os.File{}

	partition.segmentBuffers = map[byte]MMap{}

	partition.writingSegments = map[SegmentType]Segment{}

	err = partition.put(keyBytes, valueBytes, encoder, true)

	assert.NotNil(t, err)

	partition.tempIndexMemoryMappedBytes = mappedBytes

	partition.segmentFiles = map[byte]*os.File{}

	partition.segmentBuffers = map[byte]MMap{}

	err = partition.put(keyBytes, valueBytes, encoder, true)

	assert.NotNil(t, err)

	partition.segmentFiles = map[byte]*os.File{}

	partition.segmentBuffers = map[byte]MMap{}

	partition.writingSegments = map[SegmentType]Segment{}

	err = partition.put(keyBytes, valueBytes, encoder, true)

	assert.Nil(t, err)

}

func TestPartitionBuildIORequest(t *testing.T) {

	store, err := OpenOrCreateStore("dummy-testing-partition-type-8", utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	partition := store.partitions[0]

	keyBytes := []byte("dummy-key-1")

	valueBytes := []byte("dummy-valuesldfjlsjflsdjfldsjf")

	offsetBytes := []byte{0, 0, 0, 0, 0, 0, 0, 0}

	partition.entries = map[uint64]txnEntry{}

	partition.entries[utils.GetHash64(keyBytes)] = txnEntry{length: getSegmentBufferLength(SegmentType15Bytes)}

	partition.walFile = nil

	utils.SetLogLevel(utils.LogLevelTrace)

	_, err, _, _ = partition.buildIORequests(keyBytes, valueBytes, offsetBytes, true)

	assert.NotNil(t, err)

	_, err, _, _ = partition.buildIORequests(keyBytes, valueBytes[0:0], offsetBytes, true)

	assert.NotNil(t, err)

}

func TestStoreCopy1(t *testing.T) {

	storeName := "dummy-testing-copy-1"

	store, err := OpenOrCreateStore(storeName, utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	err = store.Put([]byte("testing-key-1"), []byte("testing-value-1"), encoder, tokenizer)

	assert.Nil(t, err)

	store.Close(encoder)

	destination := utils.CurrentDir + utils.PathSeparator + "tmp-datastore" + utils.PathSeparator + codec.ToString(time.Now().Unix())

	err = store.Clone(encoder, tokenizer, destination)

	assert.Nil(t, err)

	_, err = os.ReadDir(destination + utils.PathSeparator + storeName)

	assert.Nil(t, err)

}

func TestStoreCopy2(t *testing.T) {

	storeName := "dummy-testing-copy-2"

	store, err := OpenOrCreateStore(storeName, utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	err = store.Put([]byte("testing-key-1"), []byte("testing-value-1"), encoder, tokenizer)

	assert.Nil(t, err)

	store.Close(encoder)

	destination := utils.CurrentDir + utils.PathSeparator + "tmp-datastore" + utils.PathSeparator + codec.INT64ToStringValue(time.Now().Unix())

	err = store.Clone(encoder, tokenizer, destination)

	assert.Nil(t, err)

	cp.Copy(destination+utils.PathSeparator+storeName, utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+storeName)

	store, err = OpenOrCreateStore(storeName, utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	valueBufferBytes := make([]byte, utils.MaxPoolLength)

	found, valueBytes, err := store.Get([]byte("testing-key-1"), valueBufferBytes, encoder, event, waitGroup, tokenizer, false)

	assert.Nil(t, err)

	assert.True(t, found)

	assert.Equal(t, string(valueBytes), "value-1")

}

func TestRebuildIdx(t *testing.T) {

	store, err := OpenOrCreateStore("dummy-partiotion-testing-1", utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	os.Mkdir(store.partitions[0].path+utils.PathSeparator+TempIdx, 0644)

	assert.NotNil(t, rebuildIndex(store.partitions[0], encoder))

}

func TestPrefixLookup(t *testing.T) {

	store, err := OpenOrCreateStore("dummy-partiotion-testing-2", utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	store.Put([]byte("testing-key-1"), []byte("testing-value-1"), encoder, tokenizer)

	store.Put([]byte("dummy-testing-key-1"), []byte("testing-value-1"), encoder, tokenizer)

	keys, err := store.GetPrefixKeys([]byte("testing-key-1"), true)

	assert.Nil(t, err)

	assert.NotNil(t, keys)

}

func TestMapStringValuesV2(t *testing.T) {

	mapStringValues(&Store{name: "demo"}, 0, codec.Encoder{}, 0)

	bytes, err := utils.ReadLogFile("Mapping", "storage")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "occurred while mapping string values for store")

}

func TestMapNumericValues(t *testing.T) {

	mapNumericValues(&Store{name: "demo"}, 0, codec.Encoder{}, 0)

	bytes, err := utils.ReadLogFile("Mapping", "storage")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "occurred while mapping numeric values for store")

}

func TestMapRegion(t *testing.T) {

	_, err := MapRegion(nil, 1, 1, 0, 1)

	assert.NotNil(t, err)

	file, err := os.Create(utils.CurrentDir + utils.DatastoreDir + "dummy-test-file-mapregion")

	assert.Nil(t, err)

	_, err = MapRegion(file, -1, 1, 0, 0)

	assert.NotNil(t, err)

}

func TestPartitionV2(t *testing.T) {

	assertions := assert.New(t)

	storename := "dummy-store-name-2"

	store, err := OpenOrCreateStore(storename, utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	file, err := os.Create(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + "dummy-blobfile")

	assert.Nil(t, err)

	store.partitions[0].blobFile = file

	file.Close()

	store.partitions[0].sync(encoder, true)

	utils.AssertLogMessage(assertions, "Partition", "storage", "failed to flush blob file of the partition")

}

func TestPartitionV3(t *testing.T) {

	assertions := assert.New(t)

	storename := "dummy-store-name-3"

	store, err := OpenOrCreateStore(storename, utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	keyBytes := []byte("testing-key-1")

	valueBytes := []byte("value-bytes-dymmy")

	store.Put(keyBytes, valueBytes, encoder, tokenizer)

	store.Delete(keyBytes, encoder, tokenizer)

	store.GetKeys(nil, nil, false, codec.Invalid)

	utils.AssertLogMessage(assertions, "Partition", "storage", "deleted/corrupted entry")

}

func TestPartitionV4(t *testing.T) {

	storename := "dummy-store-name-4"

	store, err := OpenOrCreateStore(storename, utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	keyBytes := []byte("testing-key-1")

	valueBytes := []byte("value-bytes-dymmy")

	store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assert.Equal(t, store.Count(), 1)

}

func TestPartitionV5(t *testing.T) {

	assertions := assert.New(t)

	storename := "dummy-store-name-5"

	store, err := OpenOrCreateStore(storename, utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	keyBytes := []byte("testing-key-1")

	valueBytes := []byte("value-bytes-dymmy")

	store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assert.Equal(t, store.Count(), 1)

	keyBuffers := make([][]byte, 2)

	store.partitions[1].compact(encoder, keyBuffers, nil, batches[0], event, waitGroup)

	utils.AssertLogMessage(assertions, "Partition", "storage", "occurred while compacting data")

	store.partitions[1].closed = true

	err = store.partitions[0].compact(encoder, keyBuffers, nil, batches[0], event, waitGroup)

	err = store.partitions[1].compact(encoder, keyBuffers, nil, batches[0], event, waitGroup)

	assert.NotNil(t, err)

}

func TestPartitionV6(t *testing.T) {

	storename := "dummy-store-name-6"

	store, err := OpenOrCreateStore(storename, utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	keyBytes := []byte("testing-key-1")

	valueBytes := []byte("value-bytes-dymmy")

	store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assert.Equal(t, store.Count(), 1)

	keyBuffers := make([][]byte, 2)

	os.MkdirAll(store.partitions[1].path+utils.PathSeparator+Temp255+utils.PathSeparator, 0777)

	os.Create(store.partitions[1].path + utils.PathSeparator + Temp255 + utils.PathSeparator + "dummy")

	err = store.partitions[1].compact(encoder, keyBuffers, nil, batches[0], event, waitGroup)

	assert.NotNil(t, err)

}

func TestPartitionV7(t *testing.T) {

	storename := "dummy-store-name-7"

	store, err := OpenOrCreateStore(storename, utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	keyBytes := []byte("testing-key-1")

	valueBytes := []byte("value-bytes-dymmy")

	store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assert.Equal(t, store.Count(), 1)

	err = store.partitions[1].writeCompactedSegment(nil, nil, make([]byte, 10), nil, 0, "", nil)

	assert.NotNil(t, err)

	file, err := os.Create(store.partitions[1].path + utils.PathSeparator + "dummy-file")

	assert.Nil(t, err)

	assert.NotNil(t, file)

	bytes := make([]byte, 20)

	*(*uintptr)(unsafe.Pointer(&bytes)) = uintptr(0)

	err = store.partitions[1].writeCompactedSegment(file, bytes, make([]byte, 10), nil, 0, "", nil)

	assert.NotNil(t, err)

}

func TestPartitionV9(t *testing.T) {

	assertions := assert.New(t)

	storeName := "dummy-store-name-9"

	store, err := OpenOrCreateStore(storeName, utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBytes := []byte("testing-key-1")

	valueBytes := []byte("value-bytes-dymmy")

	partition := store.partitions[store.GetPartition(keyBytes, tokenizer)]

	store.writeTxn(keyBytes, valueBytes)

	defer cleanup()

	partition.closed = true

	partition.dirty = true

	partition.archive(nil, encoder, "")

	partition.Clone(encoder, "")

	utils.AssertLogMessage(assertions, "Partition", "storage", "failed to sync partition")

}

func TestWriteStringMapping(t *testing.T) {

	storename := "dummy-store-name-13"

	store, err := OpenOrCreateStore(storename, utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	keyBytes := []byte("testing-key-1")

	valueBytes := []byte("value-bytes-dymmy")

	store.Put(keyBytes, valueBytes, encoder, tokenizer)

	store.stringMappingTempFile = nil

	err = os.MkdirAll(store.path+utils.PathSeparator+FileTemp176, 0777)

	assert.Nil(t, err)

	err = WriteStringMapping("", 0, store, encoder)

	assert.NotNil(t, err)

}

func TestWriteStringMappingV2(t *testing.T) {

	storename := "dummy-store-name-14"

	store, err := OpenOrCreateStore(storename, utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	keyBytes := []byte("testing-keyBytes-1")

	valueBytes := []byte("valueBytes-bytes-dymmy")

	store.Put(keyBytes, valueBytes, encoder, tokenizer)

	file, err := os.Create(store.path + utils.PathSeparator + FileTemp176)

	assert.Nil(t, err)

	assert.NotNil(t, file)

	store.stringMappingTempFile = file

	file.Close()

	err = WriteStringMapping("", 0, store, encoder)

	assert.NotNil(t, err)

}

func TestStoreSyncWalFiles(t *testing.T) {

	assertions := assert.New(t)

	storeName := "store-sync-multiple-WALs"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	keyBytes := []byte("1^system.cpu.percent")

	partition := store.GetPartition(keyBytes, tokenizer)

	for i := 1; i < 20; i++ {

		store.writeTxn(keyBytes, append(make([]byte, utils.MaxValueBytes), byte(i)))

	}

	err = store.putTxn(partition)

	assertions.Nil(err)

	cleanup()

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	found, valueBytes, err := store.Get(keyBytes, valueBuffers[0], encoder, event, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal([]byte{19}, valueBytes)
}

func TestStoreOpenValidTempPatchMetadataFile(t *testing.T) {

	storeName := "store-open-valid-temp-patch-metadata-file"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	metadata := store.metadata.Clone()

	metadata[utils.Version] = codec.StringToFloat64(store.GetVariant()) + 0.01

	assertions := assert.New(t)

	assertions.NotEqual(store.metadata.GetStringValue(utils.Version), metadata.GetStringValue(utils.Version))

	bytes, err := json.MarshalIndent(metadata, "", " ")

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(store.path+utils.PathSeparator+utils.TempPatch+utils.MetadataFile, bytes, 0666)

	assertions.Nil(err)

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Equal(metadata.GetStringValue(utils.Version), store.GetVariant())

	_, err = os.Stat(store.path + utils.PathSeparator + utils.TempPatch + utils.MetadataFile)

	assertions.True(os.IsNotExist(err))

}

func TestStoreOpenInValidTempPatchMetadataFile(t *testing.T) {

	storeName := "store-open-invalid-temp-patch-metadata-file"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	metadata := store.metadata.Clone()

	metadata[utils.Version] = codec.StringToFloat64(store.GetVariant()) + 0.01

	assertions := assert.New(t)

	assertions.NotEqual(store.metadata.GetStringValue(utils.Version), metadata.GetStringValue(utils.Version))

	err = os.WriteFile(store.path+utils.PathSeparator+utils.TempPatch+utils.MetadataFile, []byte(""), 0666)

	assertions.Nil(err)

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.NotEqual(metadata.GetStringValue(utils.Version), store.GetVariant())

	_, err = os.Stat(store.path + utils.PathSeparator + utils.TempPatch + utils.MetadataFile)

	assertions.True(os.IsNotExist(err))
}

func TestMergeStringMappingV1(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("dummy-testing-store-1", utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	store.name = "dummy"

	store.tempStringMappings = swiss.NewMap[string, int32](1000)

	store.tempStringMappings.Put("demo", 1)

	store.tempStringMappings.Put("demo2", 2)

	encoder := codec.NewEncoder(utils.NewMemoryPool(10, 1, false, utils.DefaultBlobPools))

	store.tempStringMappingMMapBytes = nil

	mergeStringMapping(store, encoder, Normal)

	encoder.MemoryPool = nil

	store.tempStringMappings = swiss.NewMap[string, int32](1000)

	store.tempStringMappings.Put("demo", 1)

	store.tempStringMappings.Put("demo2", 2)

	mergeStringMapping(store, encoder, Normal)

	utils.AssertLogMessage(assertions, "Mapping", "storage", "occurred while merge string mapping for store")
}

func TestWriteNumericMappingV2(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("demo-testing-write-numeric-1", utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	file, err := os.Create(store.path + utils.PathSeparator + FileTemp112)

	assertions.Nil(err)

	err = file.Close()

	assertions.Nil(err)

	store.numericMappingTempFile = file

	store.tempNumericMappingMMapBytes = MMap{}

	writeNumericMapping(1, 1, store, encoder)

	store.numericMappingBackupFile = file

	store.numericMappingTempFile = nil

	store.cacheNumericMappings = intmap.New[int32, int64](10)

	writeNumericMapping(1, 1, store, encoder)

	store.tempNumericMappingMMapBytes.Unmap()

	err = store.numericMappingTempFile.Close()

	assertions.Nil(err)

	err = os.RemoveAll(store.path + utils.PathSeparator + FileTemp112)

	assertions.Nil(err)

	err = os.MkdirAll(store.path+utils.PathSeparator+FileTemp112, 0755)

	assertions.Nil(err)

	err = writeNumericMapping(1, 1, store, encoder)

	assertions.NotNil(err)

}

func TestMergeNumericMappingV1(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("dummy-testing-store-2", utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	store.name = "dummy"

	store.tempNumericMappings = intmap.New[int64, int32](1000)

	store.tempNumericMappings.Put(12, 1)

	store.tempNumericMappings.Put(15, 2)

	store.tempNumericMappingMMapBytes = MMap{}

	encoder := codec.NewEncoder(nil)

	mergeNumericMapping(store, encoder, Normal)

	utils.AssertLogMessage(assertions, "Mapping", "storage", "occurred while merge numeric mapping for store")

}

func TestRemapV1(t *testing.T) {

	assertions := assert.New(t)

	err := os.MkdirAll(utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir, 0777)

	assertions.Nil(err)

	file, err := os.Create(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + "dummy-mmap-file")

	file.WriteString("dummy string")

	assertions.Nil(err)

	bytes, err := Map(file, ReadWrite)

	assertions.Nil(err)

	file, err = os.Create(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + "dummy-mmap-file-2")

	assertions.Nil(err)

	file.Close()

	_, err = Remap(bytes, 500, file)

	assertions.NotNil(err)

}

func TestMMapV1(t *testing.T) {

	assertions := assert.New(t)

	err := os.MkdirAll(utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir, 0777)

	assertions.Nil(err)

	file, err := os.Create(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + "dummy-mmap-file-3")

	assertions.Nil(err)

	bytes, err := Map(file, ReadWrite)

	assertions.NotNil(err)

	assertions.Nil(bytes)
}

func TestCreateSegmentV1(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("dummy-testing-new-partition-store", utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	partition := store.partitions[0]

	partition.segmentFiles = nil

	partition.writingSegments = nil

	assertions.Equal(getSegmentBufferLength(SegmentType2600Bytes), 2600)

	assertions.Equal(getSegmentBufferLength(SegmentType3200Bytes), 3200)

	assertions.Equal(getSegmentBufferLength(SegmentType1500000Bytes+1), 15)

	assertions.Equal(calculateWritingSegmentType(10), SegmentType15Bytes)

	createSegment(SegmentType(3), partition)

	utils.AssertLogMessage(assertions, "Partition", "storage", "occurred while creating segment for partition")

}

func TestDoSuffixLookupV1(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("dumm-testing-do-suffix-v1", utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	partition := store.partitions[0]

	bytes := make([]byte, 1000)

	partition.put([]byte("dummy-key-v1"), []byte("dummy-value-v2"), encoder, true)

	partition.sync(encoder, true)

	assertions.NotNil(doSuffixLookup(partition, bytes, true))
}

func TestDoListingV1(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("dumm-testing-do-listing-v1", utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	partition := store.partitions[0]

	partition.put([]byte("dummy-key-v1"), []byte("dummy-value-v2"), encoder, true)

	partition.sync(encoder, true)

	numericValues := swiss.NewMap[int64, int32](100)

	numericValues.Put(1, 2)

	assertions.NotNil(doListing(partition, nil, numericValues, true, codec.Invalid))

}

func TestNewPartitionV2(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("dumm-testing-new-partition-v1", utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	partition := store.partitions[0]

	_ = partition

	file, err := os.Create(partition.path + utils.PathSeparator + MergedIdxFile)

	assertions.Nil(err)

	file.Close()

	indexFile, err := os.Create(partition.path + utils.PathSeparator + Idx255)

	assertions.Nil(err)

	indexFile.Close()

	newPartition(store, 1, encoder, tokenizer)

	file, err = os.Create(partition.path + utils.PathSeparator + Idx255)

	assertions.Nil(err)

	file.Close()

	indexFile, err = os.Create(partition.path + utils.PathSeparator + Temp255)

	assertions.Nil(err)

	indexFile.Close()

	newPartition(store, 1, encoder, tokenizer)

	os.Remove(partition.path + utils.PathSeparator + Idx255)

	file, err = os.Create(partition.path + utils.PathSeparator + Temp255)

	assertions.Nil(err)

	file.Close()

	_, err = newPartition(store, 1, encoder, tokenizer)

	assertions.NotNil(err)

}

func TestLockStoreV1(t *testing.T) {

	storeName := "dummy"

	_, err := lockStore(storeName)

	assertions := assert.New(t)

	assertions.NotNil(err)

}

func TestPartitionRemap(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("dummy-store-remap-testing-v1", utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	value := []byte("12345678dummy-values-1")

	for i := 0; i < 100000; i++ {

		store.writeTxn([]byte(fmt.Sprintf("dummy-testing-key-%d", i)), value)

	}

	for i := range store.partitions {

		err = store.putTxn(i)

		assertions.Nil(err)
	}

	valueBytes := make([]byte, 100000)

	wg := sync.WaitGroup{}

	found, data, err := store.Get([]byte(fmt.Sprintf("dummy-testing-key-%d", 500)), valueBytes, encoder, DiskIOEvent{}, &wg, tokenizer, true)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(data, []byte("dummy-values-1"))

	err = store.Sync(encoder)

	assertions.Nil(err)

	found, data, err = store.Get([]byte(fmt.Sprintf("dummy-testing-key-%d", 500)), valueBytes, encoder, DiskIOEvent{}, &wg, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(data, []byte("dummy-values-1"))

}

func TestMetricAggregatorPartingv1(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("metric-aggregator-1", utils.MetricAggregation, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.True(store.metadata.GetStringValue(MultipartRepaired) == utils.Yes)
}

func TestUpdateCacheEntry(t *testing.T) {

	utils.SetLogLevel(utils.LogLevelTrace)

	assertions := assert.New(t)

	storeName := "dummy-testing-update-cache-entry"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	store.maxMappingCacheRecords = 100

	assertions.Nil(err)

	assertions.NotNil(store)

	err = store.PutStringMapping("dummy1", encoder)

	assertions.Nil(err)

	err = store.PutStringMapping("dummy2", encoder)

	assertions.Nil(err)

	err = store.PutStringMapping("dummy3", encoder)

	assertions.Nil(err)

	err = store.PutNumericMapping(1, encoder)

	assertions.Nil(err)

	err = store.PutNumericMapping(2, encoder)

	assertions.Nil(err)

	err = store.PutNumericMapping(3, encoder)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	store.UpdateCacheMapping(1, true)

	poolIndex, pool := encoder.MemoryPool.AcquireINT32Pool(6)

	defer encoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	pool[0] = 3

	pool[1] = 5

	pool[2] = 7

	stringMappings := swiss.NewMap[int32, []int](10)

	numericMappings := swiss.NewMap[int32, []int](10)

	_, _, resolvedPoolIndex := store.ResolveMapping(poolIndex, encoder, stringMappings, numericMappings, 3)

	stringValues := encoder.MemoryPool.GetStringPool(resolvedPoolIndex)

	assertions.Equal(stringValues[0], "dummy1")

	assertions.Equal(stringValues[1], "dummy2")

	assertions.Equal(stringValues[2], "dummy3")

	encoder.MemoryPool.ReleaseStringPool(resolvedPoolIndex)

	pool[0] = 0

	pool[1] = 2

	pool[2] = 4

	_, _, resolvedPoolIndex = store.ResolveMapping(poolIndex, encoder, stringMappings, numericMappings, 3)

	numericValues := encoder.MemoryPool.GetINT64Pool(resolvedPoolIndex)

	assertions.Equal(numericValues[0], int64(1))

	assertions.Equal(numericValues[1], int64(2))

	assertions.Equal(numericValues[2], int64(3))

	encoder.MemoryPool.ReleaseStringPool(resolvedPoolIndex)

	assertions.Equal(store.cacheStringMappings.Count(), 1)

	assertions.Equal(store.cacheNumericMappings.Len(), 1)

	store.UpdateCacheMapping(2, true)

	assertions.Equal(store.cacheStringMappings.Count(), 2)

	assertions.Equal(store.cacheNumericMappings.Len(), 2)

	store.UpdateCacheMapping(3, true)

	assertions.Equal(store.cacheStringMappings.Count(), 3)

	assertions.Equal(store.cacheNumericMappings.Len(), 3)

	store.UpdateCacheMapping(4, true)

	assertions.Equal(store.cacheStringMappings.Count(), 3)

	assertions.Equal(store.cacheNumericMappings.Len(), 3)

}

func TestStorageV1(t *testing.T) {

	assertions := assert.New(t)

	storeName := "testing.store.1"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.NoError(err)

	destination := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + "dummy"

	os.Create(destination)

	assertions.Error(store.Clone(encoder, tokenizer, destination))

	os.Remove(destination)

	os.MkdirAll(destination+utils.PathSeparator+store.name+utils.PathSeparator+"dummy", os.ModePerm)

	os.Create(store.path + utils.PathSeparator + "dummy")

	assertions.Error(store.Clone(encoder, nil, destination))

	store.partitions = nil

	assertions.Error(store.Clone(encoder, tokenizer, destination))

	os.RemoveAll(store.path)

	assertions.Error(store.Clone(encoder, tokenizer, destination))

	assertions.Error(store.Clone(encoder, tokenizer, destination))

}

func TestStorageV2(t *testing.T) {

	assertions := assert.New(t)

	storeName := "testing.store.2"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.NoError(err)

	destination := utils.BackUpDir + utils.PathSeparator + store.name + utils.ZipExtension

	os.MkdirAll(destination, os.ModePerm)

	assertions.Error(store.Archive(encoder, ""))

	os.RemoveAll(store.path + utils.PathSeparator + "1")

	os.RemoveAll(destination)

	assertions.Error(store.Archive(encoder, ""))

	store.partitions = nil

	store.Close(encoder)

	os.RemoveAll(store.path)

	assertions.Error(store.Archive(encoder, ""))

}

func TestStorageV3(t *testing.T) {

	assertions := assert.New(t)

	storeName := "testing.store.3"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	file, err := os.Create(store.path + utils.PathSeparator + "dummy")

	assertions.NoError(err)

	file.Close()

	file, err = os.OpenFile(store.path+utils.PathSeparator+"dummy", os.O_RDONLY, 0755)

	assertions.NoError(err)

	store.SetMultipartKey(1, 1)

	store.multipartKeyFile.Close()

	store.multipartKeyFile = file

	assertions.Error(store.SetMultipartKey(2, 2))

	assertions.Error(store.AddMultipartKey(3, 3))

	err = os.Remove(store.path + utils.PathSeparator + utils.MultipartIdxFile)

	err = os.MkdirAll(store.path+utils.PathSeparator+utils.MultipartIdxFile, os.ModePerm)

	assertions.Error(store.loadMultipartKeys())

	store.multipartKeyFile.Close()

	err = os.Remove(store.path + utils.PathSeparator + utils.MultipartIdxFile)

	err = os.WriteFile(store.path+utils.PathSeparator+utils.MultipartIdxFile, []byte{1, 2, 3}, 0755)

	assertions.Error(store.loadMultipartKeys())

	file.Close()

	store.Close(encoder)

	err = os.RemoveAll(store.path)

	assertions.Error(store.loadMultipartKeys())

}

func TestStorageV4(t *testing.T) {

	assertions := assert.New(t)

	storeName := "testing.store.4"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.NoError(err)

	bitmap := bitmap.Bitmap{}

	store.closed = true

	store.RepairMappings(&bitmap, encoder, 10, 10)

	store.UpdateCacheMapping(1, false)

	store.GetAvailableNumericMappingOrdinals()

	store.GetAvailableStringMappingOrdinals()

	store.GetMaxNumericMappingOrdinal()

	store.GetMaxStringMappingOrdinal()

	store.ExistStringMapping("")

	store.ExistStringMapping("dummy")

	store.closed = false

	store.PutStringMapping("dummy", encoder)

	store.PutNumericMapping(100, encoder)

	store.Count()

	store.Sync(encoder)

	store.currentStringMappingOrdinal = 100

	assertions.Error(store.RepairMappings(&bitmap, encoder, 10, 10))

	store.stringMapping = nil

	assertions.Error(store.RepairMappings(&bitmap, encoder, 10, 10))

}

func TestStorageV5(t *testing.T) {

	assertions := assert.New(t)

	storeName := "testing.store.5"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.NoError(err)

	store.closed = true

	for _, partition := range store.partitions {

		partition.closed = true
	}

	_, err = store.GetLessThanKeys(1, false, false)

	assertions.Error(err)

	_, err = store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Error(err)

	_, err = store.GetSuffixKeys(nil, false)

	assertions.Error(err)

	_, err = store.GetPrefixKeys(nil, false)

	assertions.Error(err)

	_, err = store.GetGreaterThanKeys(0, false, false)

	assertions.Error(err)
}

func TestStorageV6(t *testing.T) {

	assertions := assert.New(t)

	storeName := "testing.store.6"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.NoError(err)

	os.RemoveAll(store.path + utils.PathSeparator + "1")

	os.Create(store.path + utils.PathSeparator + "1")

	assertions.Error(store.openPartitions(encoder, tokenizer, true))

	os.Remove(store.path + utils.PathSeparator + "1")

	os.MkdirAll(store.path+utils.PathSeparator+"1"+utils.PathSeparator+TempIdx+utils.PathSeparator+"dummy", os.ModePerm)

	assertions.Error(store.openPartitions(encoder, tokenizer, true))

}

func TestPartitionV10(t *testing.T) {

	assertions := assert.New(t)

	storeName := "testing.store.10"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.NoError(err)

	partition := store.partitions[0]

	partition.segmentBuffers = map[byte]MMap{0: {}}

	partition.unmapActiveSegments()

	partition.walSizeBytes = -1

	assertions.Error(partition.commitTxn(nil, nil, encoder))

	partition.walSizeBytes = 0

	partition.walFile = nil

	assertions.Error(partition.commitTxn(nil, nil, encoder))

	partition.walSizeBytes = 8

	partition.walFile = nil

	partition.closed = true

	assertions.Error(partition.commitTxn(nil, nil, encoder))

	partition.walBytes = nil

	partition.closed = false

	partition.walFilePosition = 1000

	assertions.Error(partition.commitTxn(nil, nil, encoder))

	partition.closed = true

	partition.unmapActiveSegments()

	assertions.Error(partition.commitTxn(nil, nil, encoder))

}

func TestPartitionV11(t *testing.T) {

	assertions := assert.New(t)

	storeName := "testing.store.11"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.NoError(err)

	partition := store.partitions[0]

	partition.walRecords = 12

	partition.walFilePosition = 100

	partition.sync(encoder, false)

	partition.closed = true

	assertions.Error(partition.sync(encoder, false))

}

func TestPartitionV12(t *testing.T) {

	assertions := assert.New(t)

	storeName := "testing.store.12"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.NoError(err)

	partition := store.partitions[0]

	keyBytes := []byte("key")

	partition.walBytes = make([]byte, 100)

	partition.entries = make(map[uint64]txnEntry)

	partition.entries[utils.GetHash64(keyBytes)] = txnEntry{offset: 0, length: 10}

	values := make([]byte, 10000)

	_, err, _, _ = partition.buildIORequests(keyBytes, values, values, true)

	assertions.Error(err)

	partition.put(keyBytes, []byte("12345678values"), encoder, true)

	partition.sync(encoder, false)

	partition.delete(keyBytes, encoder)

	partition.has(keyBytes)

	partition.closed = true

	partition.has(nil)

	event.partition = partition

	event.keyBytes = keyBytes

	event.valueBytes = make([]byte, 100)

	event.memoryPool = encoder.MemoryPool

	event.lookUpWAL = true

	_, _, err = get(&event, ioWorkers[0])

	assertions.Error(err)

	event.valueBytes = make([]byte, 2)

	_, _, err = get(&event, ioWorkers[0])

	assertions.Error(err)
}

func TestPartitionV13(t *testing.T) {

	assertions := assert.New(t)

	storeName := "testing.store.13"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.NoError(err)

	partition := store.partitions[0]

	partition.closed = true

	_, err = partition.getGreaterThanKeys(0, false, false)

	_, err = partition.getPrefixKeys(nil, false)

	_, err = partition.getSuffixKeys(nil, false)

	_, err = partition.getLessThanKeys(0, false, false)

	_, err = partition.listKeys(nil, nil, false, codec.Invalid)

	assertions.Equal(0, partition.count())

	assertions.Equal(0, partition.getSize())

}

func TestPartitionV14(t *testing.T) {

	assertions := assert.New(t)

	storeName := "testing.store.14"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.NoError(err)

	partition := store.partitions[0]

	partition.put([]byte("testing^key^0"), []byte("12345678testing-key-1"), encoder, true)

	partition.put([]byte("testing^key^1"), []byte("12345678testing-key-1"), encoder, true)

	partition.put([]byte("testing^key^2"), []byte("12345678testing-key-1"), encoder, true)

	partition.put([]byte("testing^key^3"), []byte("12345678testing-key-1"), encoder, true)

	partition.loadMetadata(encoder, tokenizer, store, true, false)

	partition.delete([]byte("testing^key^3"), encoder)

	partition.loadMetadata(encoder, tokenizer, store, false, true)

	assertions.Equal(store.GetMaxPart(utils.GetHash64([]byte("testing^key"))), uint16(3))

}

func TestPartitionV15(t *testing.T) {

	assertions := assert.New(t)

	storeName := "dummy.testing.partition.v15"

	store, err := OpenOrCreateStore(storeName, utils.MetricAggregation, encoder, tokenizer, false)

	assertions.NoError(err)

	assertions.NotNil(store)

	store.Put([]byte("dummy-key-^1"), []byte("dummy-value-12345678"), encoder, tokenizer)

	store.metadata[MultipartRepaired] = utils.No

	partitionId := int(utils.GetHash64(GetPartitionKey(store.storeType, []byte("dummy-key-1"), store.name, tokenizer)) % store.parts)

	store.multipartKeys = nil

	_, _ = newPartition(store, partitionId+1, encoder, tokenizer)

	utils.AssertLogMessage(assertions, "Partition", "storage", "failed to open partition")

}

func TestFlushSegmentMetadata(t *testing.T) {

	storeName := "flush-segment-metadata-1"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	key := []byte("1^column^part")

	partitionId := store.GetPartition(key, tokenizer)

	valueBytes := make([]byte, 5+utils.MaxValueBytes)

	valueBytes[utils.MaxValueBytes+0] = 1

	valueBytes[utils.MaxValueBytes+1] = 1

	valueBytes[utils.MaxValueBytes+2] = 1

	valueBytes[utils.MaxValueBytes+3] = 1

	valueBytes[utils.MaxValueBytes+4] = 1

	segmentType := calculateWritingSegmentType(len(valueBytes))

	segmentHeader := byte(segmentType) | byte(Segment0)

	partition := store.partitions[partitionId]

	assertions.NotContains(partition.segmentMetadataBitmaps, segmentHeader)

	err = partition.put(key, valueBytes, encoder, true)

	assertions.Nil(err)

	assertions.Contains(partition.segmentMetadataBitmaps, segmentHeader)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Count() == 1)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Contains(0))

	position, _ := partition.segmentMetadataBitmaps[segmentHeader].MinZero()

	assertions.Equal(uint32(1), position)

	err = partition.sync(encoder, true)

	assertions.Nil(err)

	_, err = os.Stat(partition.path + utils.PathSeparator + SegmentMeta)

	assertions.Nil(err)

	assertions.False(os.IsNotExist(err))

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.Contains(partition.segmentMetadataBitmaps, segmentHeader)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Count() == 1)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Contains(0))

	position, _ = partition.segmentMetadataBitmaps[segmentHeader].MinZero()

	assertions.Equal(uint32(1), position)

}

func TestInvalidLoadingSegmentMetadata(t *testing.T) {

	storeName := "failed-loading-segment-metadata"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	key := []byte("1^column^part")

	partitionId := store.GetPartition(key, tokenizer)

	valueBytes := make([]byte, 5+utils.MaxValueBytes)

	valueBytes[utils.MaxValueBytes+0] = 1

	valueBytes[utils.MaxValueBytes+1] = 1

	valueBytes[utils.MaxValueBytes+2] = 1

	valueBytes[utils.MaxValueBytes+3] = 1

	valueBytes[utils.MaxValueBytes+4] = 1

	segmentType := calculateWritingSegmentType(len(valueBytes))

	segmentHeader := byte(segmentType) | byte(Segment0)

	partition := store.partitions[partitionId]

	assertions.NotContains(partition.segmentMetadataBitmaps, segmentHeader)

	err = partition.put(key, valueBytes, encoder, true)

	assertions.Nil(err)

	assertions.Contains(partition.segmentMetadataBitmaps, segmentHeader)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Count() == 1)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Contains(0))

	position, _ := partition.segmentMetadataBitmaps[segmentHeader].MinZero()

	assertions.Equal(uint32(1), position)

	err = partition.sync(encoder, true)

	assertions.Nil(err)

	_, err = os.Stat(partition.path + utils.PathSeparator + SegmentMeta)

	assertions.Nil(err)

	assertions.False(os.IsNotExist(err))

	_ = os.RemoveAll(partition.path + utils.PathSeparator + SegmentMeta)

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.Contains(partition.segmentMetadataBitmaps, segmentHeader)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Count() == 1)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Contains(0))

	position, _ = partition.segmentMetadataBitmaps[segmentHeader].MinZero()

	assertions.Equal(uint32(1), position)

}

func TestLoadTempSegmentMetadata(t *testing.T) {

	storeName := "load-temp-segment-metadata"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	key := []byte("1^column^part")

	partitionId := store.GetPartition(key, tokenizer)

	valueBytes := make([]byte, 5+utils.MaxValueBytes)

	valueBytes[utils.MaxValueBytes+0] = 1

	valueBytes[utils.MaxValueBytes+1] = 1

	valueBytes[utils.MaxValueBytes+2] = 1

	valueBytes[utils.MaxValueBytes+3] = 1

	valueBytes[utils.MaxValueBytes+4] = 1

	segmentType := calculateWritingSegmentType(len(valueBytes))

	segmentHeader := byte(segmentType) | byte(Segment0)

	partition := store.partitions[partitionId]

	assertions.NotContains(partition.segmentMetadataBitmaps, segmentHeader)

	err = partition.put(key, valueBytes, encoder, true)

	assertions.Nil(err)

	assertions.Contains(partition.segmentMetadataBitmaps, segmentHeader)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Count() == 1)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Contains(0))

	position, _ := partition.segmentMetadataBitmaps[segmentHeader].MinZero()

	assertions.Equal(uint32(1), position)

	err = partition.sync(encoder, true)

	assertions.Nil(err)

	_, err = os.Stat(partition.path + utils.PathSeparator + SegmentMeta)

	assertions.Nil(err)

	assertions.False(os.IsNotExist(err))

	_ = os.Rename(partition.path+utils.PathSeparator+SegmentMeta, partition.path+utils.PathSeparator+utils.Temp+SegmentMeta)

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.Contains(partition.segmentMetadataBitmaps, segmentHeader)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Count() == 1)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Contains(0))

	position, _ = partition.segmentMetadataBitmaps[segmentHeader].MinZero()

	assertions.Equal(uint32(1), position)

}

func TestLoadEmptyTempSegmentMetadata(t *testing.T) {

	storeName := "load-empty-temp-segment-metadata"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	key := []byte("1^column^part")

	partitionId := store.GetPartition(key, tokenizer)

	valueBytes := make([]byte, 5+utils.MaxValueBytes)

	valueBytes[utils.MaxValueBytes+0] = 1

	valueBytes[utils.MaxValueBytes+1] = 1

	valueBytes[utils.MaxValueBytes+2] = 1

	valueBytes[utils.MaxValueBytes+3] = 1

	valueBytes[utils.MaxValueBytes+4] = 1

	segmentType := calculateWritingSegmentType(len(valueBytes))

	segmentHeader := byte(segmentType) | byte(Segment0)

	partition := store.partitions[partitionId]

	assertions.NotContains(partition.segmentMetadataBitmaps, segmentHeader)

	err = partition.put(key, valueBytes, encoder, true)

	assertions.Nil(err)

	assertions.Contains(partition.segmentMetadataBitmaps, segmentHeader)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Count() == 1)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Contains(0))

	position, _ := partition.segmentMetadataBitmaps[segmentHeader].MinZero()

	assertions.Equal(uint32(1), position)

	err = partition.sync(encoder, true)

	assertions.Nil(err)

	_, err = os.Stat(partition.path + utils.PathSeparator + SegmentMeta)

	assertions.Nil(err)

	assertions.False(os.IsNotExist(err))

	_ = os.Rename(partition.path+utils.PathSeparator+SegmentMeta, partition.path+utils.PathSeparator+utils.Temp+SegmentMeta)

	_ = os.Truncate(partition.path+utils.PathSeparator+utils.Temp+SegmentMeta, 0)

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.Contains(partition.segmentMetadataBitmaps, segmentHeader)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Count() == 1)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Contains(0))

	position, _ = partition.segmentMetadataBitmaps[segmentHeader].MinZero()

	assertions.Equal(uint32(1), position)

}

func TestLoadEmptySegmentMetadata(t *testing.T) {

	storeName := "load-empty-segment-metadata"

	store, err := OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions := assert.New(t)

	assertions.Nil(err)

	assertions.NotNil(store)

	key := []byte("1^column^part")

	partitionId := store.GetPartition(key, tokenizer)

	valueBytes := make([]byte, 5+utils.MaxValueBytes)

	valueBytes[utils.MaxValueBytes+0] = 1

	valueBytes[utils.MaxValueBytes+1] = 1

	valueBytes[utils.MaxValueBytes+2] = 1

	valueBytes[utils.MaxValueBytes+3] = 1

	valueBytes[utils.MaxValueBytes+4] = 1

	segmentType := calculateWritingSegmentType(len(valueBytes))

	segmentHeader := byte(segmentType) | byte(Segment0)

	partition := store.partitions[partitionId]

	assertions.NotContains(partition.segmentMetadataBitmaps, segmentHeader)

	err = partition.put(key, valueBytes, encoder, true)

	assertions.Nil(err)

	assertions.Contains(partition.segmentMetadataBitmaps, segmentHeader)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Count() == 1)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Contains(0))

	position, _ := partition.segmentMetadataBitmaps[segmentHeader].MinZero()

	assertions.Equal(uint32(1), position)

	err = partition.sync(encoder, true)

	assertions.Nil(err)

	_, err = os.Stat(partition.path + utils.PathSeparator + SegmentMeta)

	assertions.Nil(err)

	assertions.False(os.IsNotExist(err))

	_ = os.Truncate(partition.path+utils.PathSeparator+SegmentMeta, 0)

	store, err = OpenOrCreateStore(storeName, utils.PerformanceMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.Contains(partition.segmentMetadataBitmaps, segmentHeader)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Count() == 1)

	assertions.True(partition.segmentMetadataBitmaps[segmentHeader].Contains(0))

	position, _ = partition.segmentMetadataBitmaps[segmentHeader].MinZero()

	assertions.Equal(uint32(1), position)

}

// Motadata-4118

// for temp memory indices
func TestStoreGetKeys(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("14112024-0-dummystore1", utils.LogIndex, encoder, tokenizer, false)

	assertions.Nil(err)

	/* Scenario 1

	3 keys in the store  :-

	1. linux.syslog
	2. fortinet.traffic
	3. fortinet.utm
	*/

	key1 := "linux.syslog"

	key2 := "fortinet.traffic"

	key3 := "fortinet.utm"

	valueBytes := make([]byte, 50)

	err = store.Put([]byte(key1), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put([]byte(key2), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put([]byte(key3), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	//Scenario 1

	keys, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 3)

	//Scenario 2 - will not get any keys as excluded will work only when we have stringValues and numericValues set

	keys, err = store.GetKeys(nil, nil, true, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 0)

	//Scenario 3 - will not get any keys as excluded will work only when we have stringValues and numericValues set even if string datatype is set

	keys, err = store.GetKeys(nil, nil, true, codec.String)

	assertions.Nil(err)

	assertions.Equal(len(keys), 0)

	//Scenario 4 - will not get any keys as excluded will work only when we have stringValues and numericValues set even if numeric datatype is set

	keys, err = store.GetKeys(nil, nil, true, codec.Int64)

	assertions.Nil(err)

	assertions.Equal(len(keys), 0)

	stringValues := swiss.NewMap[string, int32](1000)

	stringValues.Put(key1, 1)

	//Scenario 5 - will set the stringvalues and excluded is false and datatype is not available and we will get all keys

	keys, err = store.GetKeys(stringValues, nil, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 3)

	//Scenario 6 - will set the stringValues and excluded is true and datatype is not available and we will get 2 keys excluding that key

	keys, err = store.GetKeys(stringValues, nil, true, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 2)

	for key := range keys {

		assertions.NotEqual(string(keys[key]), key1)
	}

	//Scenario 7 - will set the stringValues and excluded is false and datatype is not available and we will get only that key

	keys, err = store.GetKeys(stringValues, nil, false, codec.String)

	assertions.Nil(err)

	assertions.Equal(len(keys), 1)

	for key := range keys {

		assertions.Equal(string(keys[key]), key1)
	}

	//Scenario 8 - will set the stringValues and excluded is false and datatype is not available and we will get only that key

	keys, err = store.GetKeys(stringValues, nil, true, codec.String)

	assertions.Nil(err)

	assertions.Equal(len(keys), 2)

	for key := range keys {

		assertions.NotEqual(string(keys[key]), key1)
	}

	//Scenario 9 - will set the numericValues and excluded is false and datatype is not available and we will get only that key

	bytes := make([]byte, 8)

	/*

		3 numeric values 1 ,2 , 3

	*/

	value1 := int64(1)

	value2 := int64(2)

	value3 := int64(3)

	codec.WriteBigEndianINT64Value(value1, 0, bytes)

	err = store.Put(bytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	codec.WriteBigEndianINT64Value(value2, 0, bytes)

	err = store.Put(bytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	codec.WriteBigEndianINT64Value(value3, 0, bytes)

	err = store.Put(bytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	numericValues := swiss.NewMap[int64, int32](1000)

	numericValues.Put(value1, 1)

	//Scenario 10 - will set the numericValues and excluded is false and datatype is not available and we will get all string keys and numeric keys

	keys, err = store.GetKeys(nil, numericValues, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 6)

	//Scenario 11 - will set the numericValues and excluded is true and datatype is not available and we will get all string keys and numeric keys except that key

	keys, err = store.GetKeys(nil, numericValues, true, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 5)

	//Scenario 12 - will set the numericValues and excluded is false and datatype is Int64 and we will get only that numeric key
	keys, err = store.GetKeys(nil, numericValues, false, codec.Int64)

	assertions.Nil(err)

	assertions.Equal(len(keys), 1)

	//Scenario 13 - will set the numericValues and excluded is true and datatype is Int64 and we will get all string keys and numeric keys except that key
	keys, err = store.GetKeys(nil, numericValues, true, codec.Int64)

	assertions.Nil(err)

	assertions.Equal(len(keys), 5)

}

// for store idx
func TestStoreGetKeysV1(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("14112024-0-dummystore2", utils.LogIndex, encoder, tokenizer, false)

	assertions.Nil(err)

	/* Scenario 1

	3 keys in the store  :-

	1. linux.syslog
	2. fortinet.traffic
	3. fortinet.utm
	*/

	key1 := "linux.syslog"

	key2 := "fortinet.traffic"

	key3 := "fortinet.utm"

	valueBytes := make([]byte, 50)

	err = store.Put([]byte(key1), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put([]byte(key2), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put([]byte(key3), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	//Scenario 1

	keys, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 3)

	//Scenario 2 - will not get any keys as excluded will work only when we have stringValues and numericValues set

	keys, err = store.GetKeys(nil, nil, true, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 0)

	//Scenario 3 - will not get any keys as excluded will work only when we have stringValues and numericValues set even if string datatype is set

	keys, err = store.GetKeys(nil, nil, true, codec.String)

	assertions.Nil(err)

	assertions.Equal(len(keys), 0)

	//Scenario 4 - will not get any keys as excluded will work only when we have stringValues and numericValues set even if numeric datatype is set

	keys, err = store.GetKeys(nil, nil, true, codec.Int64)

	assertions.Nil(err)

	assertions.Equal(len(keys), 0)

	stringValues := swiss.NewMap[string, int32](1000)

	stringValues.Put(key1, 1)

	//Scenario 5 - will set the stringvalues and excluded is false and datatype is not available and we will get all keys

	keys, err = store.GetKeys(stringValues, nil, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 3)

	//Scenario 6 - will set the stringValues and excluded is true and datatype is not available and we will get 2 keys excluding that key

	keys, err = store.GetKeys(stringValues, nil, true, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 2)

	for key := range keys {

		assertions.NotEqual(string(keys[key]), key1)
	}

	//Scenario 7 - will set the stringValues and excluded is false and datatype is not available and we will get only that key

	keys, err = store.GetKeys(stringValues, nil, false, codec.String)

	assertions.Nil(err)

	assertions.Equal(len(keys), 1)

	for key := range keys {

		assertions.Equal(string(keys[key]), key1)
	}

	//Scenario 8 - will set the stringValues and excluded is false and datatype is not available and we will get only that key

	keys, err = store.GetKeys(stringValues, nil, true, codec.String)

	assertions.Nil(err)

	assertions.Equal(len(keys), 2)

	for key := range keys {

		assertions.NotEqual(string(keys[key]), key1)
	}

	//Scenario 9 - will set the numericValues and excluded is false and datatype is not available and we will get only that key

	bytes := make([]byte, 8)

	/*

		3 numeric values 1 ,2 , 3

	*/

	value1 := int64(1)

	value2 := int64(2)

	value3 := int64(3)

	codec.WriteBigEndianINT64Value(value1, 0, bytes)

	err = store.Put(bytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	codec.WriteBigEndianINT64Value(value2, 0, bytes)

	err = store.Put(bytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	codec.WriteBigEndianINT64Value(value3, 0, bytes)

	err = store.Put(bytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	numericValues := swiss.NewMap[int64, int32](1000)

	numericValues.Put(value1, 1)

	//Scenario 10 - will set the numericValues and excluded is false and datatype is not available and we will get all string keys and numeric keys

	keys, err = store.GetKeys(nil, numericValues, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 6)

	//Scenario 11 - will set the numericValues and excluded is true and datatype is not available and we will get all string keys and numeric keys except that key

	keys, err = store.GetKeys(nil, numericValues, true, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 5)

	//Scenario 12 - will set the numericValues and excluded is false and datatype is Int64 and we will get only that numeric key
	keys, err = store.GetKeys(nil, numericValues, false, codec.Int64)

	assertions.Nil(err)

	assertions.Equal(len(keys), 1)

	//Scenario 13 - will set the numericValues and excluded is true and datatype is Int64 and we will get all string keys and numeric keys except that key
	keys, err = store.GetKeys(nil, numericValues, true, codec.Int64)

	assertions.Nil(err)

	assertions.Equal(len(keys), 5)

}

// for cache
func TestStoreGetKeysV2(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("14112024-0-dummystore3", utils.StaticMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	/* Scenario 1

	3 keys in the store  :-

	1. linux.syslog
	2. fortinet.traffic
	3. fortinet.utm
	*/

	key1 := "linux.syslog"

	key2 := "fortinet.traffic"

	key3 := "fortinet.utm"

	valueBytes := make([]byte, 50)

	err = store.Put([]byte(key1), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put([]byte(key2), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put([]byte(key3), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	//Scenario 1

	keys, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 3)

	//Scenario 2 - will not get any keys as excluded will work only when we have stringValues and numericValues set

	keys, err = store.GetKeys(nil, nil, true, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 0)

	//Scenario 3 - will not get any keys as excluded will work only when we have stringValues and numericValues set even if string datatype is set

	keys, err = store.GetKeys(nil, nil, true, codec.String)

	assertions.Nil(err)

	assertions.Equal(len(keys), 0)

	//Scenario 4 - will not get any keys as excluded will work only when we have stringValues and numericValues set even if numeric datatype is set

	keys, err = store.GetKeys(nil, nil, true, codec.Int64)

	assertions.Nil(err)

	assertions.Equal(len(keys), 0)

	stringValues := swiss.NewMap[string, int32](1000)

	stringValues.Put(key1, 1)

	//Scenario 5 - will set the stringvalues and excluded is false and datatype is not available and we will get all keys

	keys, err = store.GetKeys(stringValues, nil, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 3)

	//Scenario 6 - will set the stringValues and excluded is true and datatype is not available and we will get 2 keys excluding that key

	keys, err = store.GetKeys(stringValues, nil, true, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 2)

	for key := range keys {

		assertions.NotEqual(string(keys[key]), key1)
	}

	//Scenario 7 - will set the stringValues and excluded is false and datatype is not available and we will get only that key

	keys, err = store.GetKeys(stringValues, nil, false, codec.String)

	assertions.Nil(err)

	assertions.Equal(len(keys), 1)

	for key := range keys {

		assertions.Equal(string(keys[key]), key1)
	}

	//Scenario 8 - will set the stringValues and excluded is false and datatype is not available and we will get only that key

	keys, err = store.GetKeys(stringValues, nil, true, codec.String)

	assertions.Nil(err)

	assertions.Equal(len(keys), 2)

	for key := range keys {

		assertions.NotEqual(string(keys[key]), key1)
	}

	//Scenario 9 - will set the numericValues and excluded is false and datatype is not available and we will get only that key

	bytes := make([]byte, 8)

	/*

		3 numeric values 1 ,2 , 3

	*/

	value1 := int64(1)

	value2 := int64(2)

	value3 := int64(3)

	codec.WriteBigEndianINT64Value(value1, 0, bytes)

	err = store.Put(bytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	codec.WriteBigEndianINT64Value(value2, 0, bytes)

	err = store.Put(bytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	codec.WriteBigEndianINT64Value(value3, 0, bytes)

	err = store.Put(bytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	numericValues := swiss.NewMap[int64, int32](1000)

	numericValues.Put(value1, 1)

	//Scenario 10 - will set the numericValues and excluded is false and datatype is not available and we will get all string keys and numeric keys

	keys, err = store.GetKeys(nil, numericValues, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 6)

	//Scenario 11 - will set the numericValues and excluded is true and datatype is not available and we will get all string keys and numeric keys except that key

	keys, err = store.GetKeys(nil, numericValues, true, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 5)

	//Scenario 12 - will set the numericValues and excluded is false and datatype is Int64 and we will get only that numeric key
	keys, err = store.GetKeys(nil, numericValues, false, codec.Int64)

	assertions.Nil(err)

	assertions.Equal(len(keys), 1)

	//Scenario 13 - will set the numericValues and excluded is true and datatype is Int64 and we will get all string keys and numeric keys except that key
	keys, err = store.GetKeys(nil, numericValues, true, codec.Int64)

	assertions.Nil(err)

	assertions.Equal(len(keys), 5)

}

// Key both in memory indices as well as in idx
func TestStoreGetKeysV3(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("14112024-0-dummystore5", utils.LogIndex, encoder, tokenizer, false)

	assertions.Nil(err)

	/* Scenario 1

	3 keys in the store  :-

	1. linux.syslog
	2. fortinet.traffic
	3. fortinet.utm
	*/

	key1 := "linux.syslog"

	key2 := "fortinet.traffic"

	key3 := "fortinet.utm"

	valueBytes := make([]byte, 50)

	err = store.Put([]byte(key1), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Put([]byte(key2), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	err = store.Put([]byte(key3), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	//Scenario 1

	keys, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 3)

	//Scenario 2 - will not get any keys as excluded will work only when we have stringValues and numericValues set

	keys, err = store.GetKeys(nil, nil, true, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 0)

	//Scenario 3 - will not get any keys as excluded will work only when we have stringValues and numericValues set even if string datatype is set

	keys, err = store.GetKeys(nil, nil, true, codec.String)

	assertions.Nil(err)

	assertions.Equal(len(keys), 0)

	//Scenario 4 - will not get any keys as excluded will work only when we have stringValues and numericValues set even if numeric datatype is set

	keys, err = store.GetKeys(nil, nil, true, codec.Int64)

	assertions.Nil(err)

	assertions.Equal(len(keys), 0)

	stringValues := swiss.NewMap[string, int32](1000)

	stringValues.Put(key1, 1)

	//Scenario 5 - will set the stringvalues and excluded is false and datatype is not available and we will get all keys

	keys, err = store.GetKeys(stringValues, nil, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 3)

	//Scenario 6 - will set the stringValues and excluded is true and datatype is not available and we will get 2 keys excluding that key

	keys, err = store.GetKeys(stringValues, nil, true, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 2)

	for key := range keys {

		assertions.NotEqual(string(keys[key]), key1)
	}

	//Scenario 7 - will set the stringValues and excluded is false and datatype is  available and we will get only that key

	keys, err = store.GetKeys(stringValues, nil, false, codec.String)

	assertions.Nil(err)

	assertions.Equal(len(keys), 1)

	for key := range keys {

		assertions.Equal(string(keys[key]), key1)
	}

	//Scenario 8 - will set the stringValues and excluded is false and datatype is not available and we will get only that key

	keys, err = store.GetKeys(stringValues, nil, true, codec.String)

	assertions.Nil(err)

	assertions.Equal(len(keys), 2)

	for key := range keys {

		assertions.NotEqual(string(keys[key]), key1)
	}

	//Scenario 9 - will set the numericValues and excluded is false and datatype is not available and we will get only that key

	bytes := make([]byte, 8)

	/*

		3 numeric values 1 ,2 , 3

	*/

	value1 := int64(1)

	value2 := int64(2)

	value3 := int64(3)

	codec.WriteBigEndianINT64Value(value1, 0, bytes)

	err = store.Put(bytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	codec.WriteBigEndianINT64Value(value2, 0, bytes)

	err = store.Put(bytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	codec.WriteBigEndianINT64Value(value3, 0, bytes)

	err = store.Put(bytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	err = store.Sync(encoder)

	numericValues := swiss.NewMap[int64, int32](1000)

	numericValues.Put(value1, 1)

	//Scenario 10 - will set the numericValues and excluded is false and datatype is not available and we will get all string keys and numeric keys

	keys, err = store.GetKeys(nil, numericValues, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 6)

	//Scenario 11 - will set the numericValues and excluded is true and datatype is not available and we will get all string keys and numeric keys except that key

	keys, err = store.GetKeys(nil, numericValues, true, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 5)

	//Scenario 12 - will set the numericValues and excluded is false and datatype is Int64 and we will get only that numeric key
	keys, err = store.GetKeys(nil, numericValues, false, codec.Int64)

	assertions.Nil(err)

	assertions.Equal(len(keys), 1)

	//Scenario 13 - will set the numericValues and excluded is true and datatype is Int64 and we will get all string keys and numeric keys except that key
	keys, err = store.GetKeys(nil, numericValues, true, codec.Int64)

	assertions.Nil(err)

	assertions.Equal(len(keys), 5)

}

// Key both in memory indices as well as in idx
func TestStoreGetKeysV4(t *testing.T) {

	assertions := assert.New(t)

	storeName := "testing.index.v4"

	store, err := OpenOrCreateStore(storeName, utils.LogIndex, encoder, tokenizer, false)

	assertions.Nil(err)

	partition := store.partitions[0]

	prefix := "12345678"

	/*
		scenario : first put 2 keys and then get offset of both keys

	*/

	key1 := "testing.key-1"

	key2 := "testing.key-2"

	valueBytes := make([]byte, 50)

	err = partition.put([]byte(key1), []byte(prefix+key1), encoder, true)

	assertions.Nil(err)

	err = partition.put([]byte(key2), []byte(prefix+key2), encoder, true)

	assertions.Nil(err)

	keys, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 2)

	keys1 := doListing(partition, nil, nil, false, codec.Invalid)

	assertions.Equal(len(keys1), 2)

	err = store.Sync(encoder)

	keys2 := doListing(partition, nil, nil, false, codec.Invalid)

	assertions.Equal(len(keys2), 2)

	for offset, key := range keys1 {

		assertions.Equal(keys2[offset], key)

	}

	dummy := ""

	for i := 0; i < 100; i++ {

		dummy += codec.INTToStringValue(i)
	}

	err = partition.put([]byte(key1), []byte(prefix+"updated-key-1-with-verty-large-value-to-change-segment"+dummy), encoder, true)

	assertions.Nil(err)

	err = partition.put([]byte(key2), []byte(prefix+"updated-key-2-with-verty-large-value-to-change-segment"+dummy), encoder, true)

	assertions.Nil(err)

	keys2 = doListing(partition, nil, nil, false, codec.Invalid)

	assertions.Equal(len(keys2), 2)

	for offset := range keys1 {

		_, found := keys2[offset]

		assertions.False(found)

	}

	keys, err = store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 2)

	key3 := "testing.key-3"

	err = partition.put([]byte(key3), valueBytes, encoder, true)

	assertions.Nil(err)

	//Scenario 1

	keys, err = store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 3)

	err = store.Sync(encoder)

	assertions.NoError(err)

	keys, err = store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	assertions.Equal(len(keys), 3)

}

// Key both in memory indices as well as in idx
func TestStoreGetKeysSTRINGDatatype(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("14112024-0-dummystore51", utils.LogIndex, encoder, tokenizer, false)

	assertions.Nil(err)

	/* Scenario 1

	3 keys in the store  :-

	1. linux.syslog
	2. fortinet.traffic
	3. fortinet.utm
	*/

	key1 := "linux.syslog"

	key2 := "fortinet.traffic"

	key3 := "fortinet.utm"

	key4 := "fortinet.utm.1"

	valueBytes := make([]byte, 50)

	err = store.partitions[0].put([]byte(key1), valueBytes, encoder, true)

	assertions.Nil(err)

	err = store.partitions[0].put([]byte(key2), valueBytes, encoder, true)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	// Below keys are in memory indices

	//Key 1 , key 2 ---> in idx
	//key 3 , Key 4 ----> in memory indices

	err = store.partitions[0].put([]byte(key3), valueBytes, encoder, true)

	assertions.Nil(err)

	err = store.partitions[0].put([]byte(key4), valueBytes, encoder, true)

	assertions.Nil(err)

	stringValues := swiss.NewMap[string, int32](1000)

	//Scenario 1 Getting 1 key from memory indices and one from idx
	stringValues.Put(key1, 1)

	stringValues.Put(key3, 1)

	keyBuffers, err := store.GetKeys(stringValues, nil, false, codec.String)

	assertions.Nil(err)

	count := 0

	assertions.Len(keyBuffers, 2)

	for _, key := range keyBuffers {

		if strings.EqualFold(string(key), key1) || strings.EqualFold(string(key), key3) {

			count += 1
		}
	}

	assertions.Equal(count, 2)

	count = 0

	// getting the key which is present only in the memory indices

	stringValues.Clear()

	stringValues.Put(key3, 1)

	keyBuffers, err = store.GetKeys(stringValues, nil, false, codec.String)

	assertions.Nil(err)

	assertions.Len(keyBuffers, 1)

	for _, key := range keyBuffers {

		if strings.EqualFold(string(key), key3) {

			count += 1
		}
	}

	assertions.Equal(count, 1)

	// getting the key which is present only in the idx

	count = 0

	stringValues.Clear()

	stringValues.Put(key2, 1)

	keyBuffers, err = store.GetKeys(stringValues, nil, false, codec.String)

	assertions.Nil(err)

	assertions.Len(keyBuffers, 1)

	for _, key := range keyBuffers {

		if strings.EqualFold(string(key), key2) {

			count += 1
		}
	}

	assertions.Equal(count, 1)

}

// Key both in int memory indices as well as in idx
func TestStoreGetKeysINTDatatype(t *testing.T) {

	assertions := assert.New(t)

	store, err := OpenOrCreateStore("14112024-0-dummystore52", utils.LogIndex, encoder, tokenizer, false)

	assertions.Nil(err)

	/* Scenario 1

	3 keys in the store  :-

	1. linux.syslog
	2. fortinet.traffic
	3. fortinet.utm
	*/

	key1 := 1

	key2 := 2

	key3 := 3

	key4 := 4

	valueBytes := make([]byte, 50)

	keyBytes := make([]byte, 8)

	binary.BigEndian.PutUint64(keyBytes, uint64(key1))

	err = store.partitions[0].put(keyBytes, valueBytes, encoder, true)

	assertions.Nil(err)

	keyBytes = make([]byte, 8)

	binary.BigEndian.PutUint64(keyBytes, uint64(key2))

	err = store.partitions[0].put(keyBytes, valueBytes, encoder, true)

	assertions.Nil(err)

	err = store.Sync(encoder)

	assertions.Nil(err)

	// Below keys are in memory indices

	//Key 1 , key 2 ---> in idx
	//key 3 , Key 4 ----> in memory indices

	keyBytes = make([]byte, 8)

	binary.BigEndian.PutUint64(keyBytes, uint64(key3))

	err = store.partitions[0].put(keyBytes, valueBytes, encoder, true)

	assertions.Nil(err)

	keyBytes = make([]byte, 8)

	binary.BigEndian.PutUint64(keyBytes, uint64(key4))

	err = store.partitions[0].put(keyBytes, valueBytes, encoder, true)

	assertions.Nil(err)

	numericValues := swiss.NewMap[int64, int32](1000)

	//Scenario 1 Getting 1 key from memory indices and one from idx
	numericValues.Put(int64(key1), 1)

	numericValues.Put(int64(key3), 1)

	keyBuffers, err := store.GetKeys(nil, numericValues, false, codec.Int64)

	assertions.Nil(err)

	count := 0

	assertions.Len(keyBuffers, 2)

	for _, key := range keyBuffers {

		if int(binary.BigEndian.Uint64(key)) == key1 || int(binary.BigEndian.Uint64(key)) == key3 {

			count += 1
		}
	}

	assertions.Equal(count, 2)

	count = 0

	// getting the key which is present only in the memory indices

	numericValues.Clear()

	numericValues.Put(int64(key3), 1)

	keyBuffers, err = store.GetKeys(nil, numericValues, false, codec.Int64)

	assertions.Nil(err)

	assertions.Len(keyBuffers, 1)

	for _, key := range keyBuffers {

		if int(binary.BigEndian.Uint64(key)) == key3 {

			count += 1
		}
	}

	assertions.Equal(count, 1)

	// getting the key which is present only in the idx

	count = 0

	numericValues.Clear()

	numericValues.Put(int64(key2), 1)

	keyBuffers, err = store.GetKeys(nil, numericValues, false, codec.Int64)

	assertions.Nil(err)

	assertions.Len(keyBuffers, 1)

	for _, key := range keyBuffers {

		if int(binary.BigEndian.Uint64(key)) == key2 {

			count += 1
		}
	}

	assertions.Equal(count, 1)

}

func TestRebuildStringMappingV1(t *testing.T) {

	assertions := assert.New(t)

	storeName := "invalid-temp-file-176-v3"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	data := []byte{1, 2, 3}

	err = os.WriteFile(store.path+utils.PathSeparator+FileTemp176, data, 0666)

	assertions.Nil(err)

	err = os.Remove(store.path + utils.PathSeparator + FileBKP176)

	store.tempNumericMappings = nil

	store.variant = version2 - 1

	store.Close(encoder)

	store, err = OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Error(err)

	assertions.Nil(store)

}

func TestRebuildStringMappingPartial(t *testing.T) {

	assertions := assert.New(t)

	storeName := "invalid-temp-file-176-v4"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	data := []byte{1, 2, 3}

	err = os.WriteFile(store.path+utils.PathSeparator+FileTemp176, data, 0666)

	assertions.Nil(err)

	err = os.Remove(store.path + utils.PathSeparator + FileBKP176)

	store.tempNumericMappings = nil

	file, err := os.CreateTemp(t.TempDir(), "dummy")

	assertions.NoError(err)

	store.stringMappingTempFile = file

	err = rebuildStringMapping(store, encoder, Partial)

	assertions.Error(err)

	file.Close()

}

func TestRebuildStringMappingFull(t *testing.T) {

	assertions := assert.New(t)

	storeName := "invalid-temp-file-176-v5"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	data := []byte{1, 2, 3}

	err = os.WriteFile(store.path+utils.PathSeparator+FileBKP176, data, 0666)

	assertions.Nil(err)

	store.tempNumericMappings = nil

	file, err := os.CreateTemp(t.TempDir(), "dummy")

	assertions.NoError(err)

	store.stringMappingBackupFile.Close()

	store.stringMappingBackupFile = file

	err = rebuildStringMapping(store, encoder, Full)

	assertions.Error(err)

	file.Close()

}

func TestRebuildStringMappingVersion1(t *testing.T) {

	assertions := assert.New(t)

	storeName := "invalid-temp-file-176-v6"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	value := "dummy1"

	ordinal := int32(2)

	buffer := bytes2.Buffer{}

	bufferIndex, bufferBytes := encoder.WriteUINT16Value(uint16(len(value)), 0)

	buffer.Write(bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	buffer.WriteString(value)

	bufferIndex, bufferBytes = encoder.WriteINT32Value(ordinal, 0)

	buffer.Write(bufferBytes)

	buffer.Write(utils.EOTBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	value = "dummy2"

	ordinal = int32(4)

	bufferIndex, bufferBytes = encoder.WriteUINT16Value(uint16(len(value)), 0)

	buffer.Write(bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	buffer.WriteString(value)

	bufferIndex, bufferBytes = encoder.WriteINT32Value(ordinal, 0)

	buffer.Write(bufferBytes)

	buffer.Write(utils.EOTBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	err = os.WriteFile(store.path+utils.PathSeparator+FileTemp176, buffer.Bytes(), 0666)

	assertions.Nil(err)

	store.tempNumericMappings = nil

	file, err := os.CreateTemp(t.TempDir(), "dummy")

	assertions.NoError(err)

	store.stringMappingBackupFile = file

	store.variant = version2 - 1

	err = rebuildStringMapping(store, encoder, Partial)

	assertions.NoError(err)

	found, ordinal, err := store.GetStringMapping("dummy1")

	assertions.True(found)

	assertions.Equal(ordinal, int32(2))

	assertions.NoError(err)

	found, ordinal, err = store.GetStringMapping("dummy2")

	assertions.True(found)

	assertions.Equal(ordinal, int32(4))

	assertions.NoError(err)

	file.Close()

}

func TestConstructStringMappingBackup(t *testing.T) {

	assertions := assert.New(t)

	storeName := "invalid-temp-file-176-v5"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.NoError(err)

	assertions.NotNil(store)

	err = store.PutStringMapping("value1", encoder)

	assertions.NoError(err)

	err = store.PutStringMapping("value2", encoder)

	assertions.NoError(err)

	err = store.Sync(encoder)

	assertions.NoError(err)

	constructStringMappingBackup(store, encoder)

	err = rebuildStringMapping(store, encoder, Full)

	assertions.NoError(err)

	found, ordinal, err := store.GetStringMapping("value1")

	assertions.NoError(err)

	assertions.True(found)

	assertions.Equal(ordinal, int32(3))

	found, ordinal, err = store.GetStringMapping("value2")

	assertions.NoError(err)

	assertions.True(found)

	assertions.Equal(ordinal, int32(5))

}

func TestConstructNumericMappingBackup(t *testing.T) {

	assertions := assert.New(t)

	storeName := "invalid-temp-file-112-v5"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.NoError(err)

	assertions.NotNil(store)

	err = store.PutNumericMapping(45, encoder)

	assertions.NoError(err)

	err = store.PutNumericMapping(18, encoder)

	assertions.NoError(err)

	err = store.Sync(encoder)

	assertions.NoError(err)

	constructNumericMappingBackup(store, encoder)

	err = rebuildNumericMapping(store, encoder, Full)

	assertions.NoError(err)

	found, ordinal, err := store.GetNumericMapping(45, encoder)

	assertions.NoError(err)

	assertions.True(found)

	assertions.Equal(ordinal, int32(0))

	found, ordinal, err = store.GetNumericMapping(18, encoder)

	assertions.NoError(err)

	assertions.True(found)

	assertions.Equal(ordinal, int32(2))

}

func TestConstructMappingRecover(t *testing.T) {

	assertions := assert.New(t)

	storeName := "invalid-temp-file-112-v5"

	store, err := OpenOrCreateStore(storeName, utils.Mapping, encoder, tokenizer, false)

	assertions.NoError(err)

	assertions.NotNil(store)

	err = store.PutNumericMapping(45, encoder)

	assertions.NoError(err)

	err = store.PutStringMapping("value1", encoder)

	assertions.NoError(err)

	err = store.Sync(encoder)

	assertions.NoError(err)

	encoder := codec.NewEncoder(nil)

	constructNumericMappingBackup(store, encoder)

	utils.AssertLogMessage(assertions, "Mapping", "storage", "occurred while rebuilding numeric mapping for store")

	constructStringMappingBackup(store, encoder)

	utils.AssertLogMessage(assertions, "Mapping", "storage", "occurred while rebuilding string mapping for store")

}

func TestStorePutMultiples(t *testing.T) {

	assertions := assert.New(t)

	storeName := "test-store-put-multiples"

	store, err := OpenOrCreateStore(storeName, utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBuffers := make([][]byte, 2)

	writeBuffers := make([][]byte, 2)

	keyBuffers[0] = []byte("1^system.cpu.percent^0")

	keyBuffers[1] = []byte("1^system.cpu.percent^time^0")

	writeBuffers[0] = make([]byte, utils.MaxValueBytes+5)

	copy(writeBuffers[0][utils.MaxValueBytes:], []byte{1, 2, 3, 4, 5})

	writeBuffers[1] = make([]byte, utils.MaxValueBytes+5)

	copy(writeBuffers[1][utils.MaxValueBytes:], []byte{6, 7, 8, 9, 10})

	err = store.PutMultiples(keyBuffers, writeBuffers, encoder, tokenizer)

	assertions.Nil(err)

	buffers, errs, err := store.GetMultiples(keyBuffers, valueBuffers, encoder, batches, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.Nil(errs[0])

	assertions.Nil(errs[1])

	assertions.Equal([]byte{1, 2, 3, 4, 5}, buffers[0])

	assertions.Equal([]byte{6, 7, 8, 9, 10}, buffers[1])

}

func TestStorePutMultiplesInvalid(t *testing.T) {

	assertions := assert.New(t)

	storeName := "test-store-put-multiples"

	store, err := OpenOrCreateStore(storeName, utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	keyBuffers := make([][]byte, 2)

	writeBuffers := make([][]byte, 2)

	keyBuffers[0] = []byte("1^system.cpu.percent^0")

	keyBuffers[1] = []byte("1^system.cpu.percent^time^0")

	writeBuffers[0] = make([]byte, utils.MaxValueBytes+5)

	copy(writeBuffers[0][utils.MaxValueBytes:], []byte{1, 2, 3, 4, 5})

	writeBuffers[1] = make([]byte, utils.MaxValueBytes+5)

	copy(writeBuffers[1][utils.MaxValueBytes:], []byte{6, 7, 8, 9, 10})

	for index := range store.partitions {

		store.partitions[index].closed = true
	}

	err = store.PutMultiples(keyBuffers, writeBuffers, encoder, tokenizer)

	assertions.NotNil(err)

	_, errs, err := store.GetMultiples(keyBuffers, valueBuffers, encoder, batches, waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(errs[0])

	assertions.NotNil(errs[1])

}

func TestCacheStore(t *testing.T) {

	assertions := assert.New(t)

	storeName := "test-cache-store"

	utils.CacheStores[utils.Index] = struct{}{}

	store, err := OpenOrCreateStore(storeName, utils.Index, encoder, tokenizer, false)

	assertions.NoError(err)

	err = store.Put([]byte("testing-key-1"), []byte("testing-value-1"), encoder, tokenizer)

	assertions.NoError(err)

	err = store.Put([]byte("testing-key-2"), []byte("testing-value-2"), encoder, tokenizer)

	assertions.NoError(err)

	store.Sync(encoder)

	store.Close(encoder)

	storeName = "test-cache-store"

	store, err = OpenOrCreateStore(storeName, utils.Index, encoder, tokenizer, false)

	assertions.NoError(err)

	assertions.Equal(store.partitions[0].cacheIndices.Count(), store.partitions[0].index.Len())

	assertions.Equal(store.partitions[1].cacheIndices.Count(), store.partitions[1].index.Len())

}

// helper function
func (store *Store) writeTxn(keyBytes, bufferBytes []byte) {

	partition := store.GetPartition(keyBytes, tokenizer)

	int32Bytes := make([]byte, 8)

	if size := len(bufferBytes) + 4 + len(keyBytes) + *txnOffsets[partition]; size > len(txnBuffers[partition]) {

		txnBuffers[partition] = utils.RemapBytes(txnBuffers[partition], size)
	}

	copy(bufferBytes, utils.CheckSumV1Bytes) // add checksum

	codec.WriteINT32Value(int32(len(bufferBytes)-utils.MaxValueBytes), 4, bufferBytes) //add length

	codec.WriteINT32Value(int32(len(keyBytes)), 0, int32Bytes) //calculate length of key bytes

	copy(txnBuffers[partition][*txnOffsets[partition]:], int32Bytes) //add key length bytes

	*txnOffsets[partition] += 4

	copy(txnBuffers[partition][*txnOffsets[partition]:], keyBytes)

	*txnOffsets[partition] += len(keyBytes)

	offset := *txnOffsets[partition]

	copy(txnBuffers[partition][*txnOffsets[partition]:], bufferBytes)

	*txnOffsets[partition] += len(bufferBytes)

	txnEntries[partition][utils.GetHash64(keyBytes)] = utils.TxnEntry{Length: len(bufferBytes), Offset: offset}

}

func (store *Store) putTxn(partition int) error {

	copy(txnBuffers[partition][*txnOffsets[partition]:], utils.EOTBytes)

	*txnOffsets[partition] += len(utils.EOTBytes)

	codec.WriteINT32Value(int32(*txnOffsets[partition]-4), 0, txnBuffers[partition])

	return store.CommitTxn(txnBuffers[partition][:*txnOffsets[partition]], txnEntries[partition], encoder, partition)

}

func cleanup() {

	for index := 0; index < len(txnOffsets); index++ {

		*txnOffsets[index] = 4

	}

	for index := 0; index < len(txnBuffers); index++ {

		clear(txnBuffers[index])
	}

	for index := 0; index < len(txnEntries); index++ {

		clear(txnEntries[index])
	}
}
