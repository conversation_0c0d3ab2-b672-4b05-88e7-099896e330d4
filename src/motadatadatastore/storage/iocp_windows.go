/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-06-04             <PERSON><PERSON><PERSON> Shah            MOTADATA-5780 Introduced New IOCPWorker for Windows that implements IOCP
 */

package storage

import (
	"fmt"
	"golang.org/x/sys/windows"
	"motadatadatastore/utils"
	"runtime"
	"strings"
	"sync"
	"unsafe"
)

var IOCPWorkerLogger = utils.NewLogger("IOCP Worker", "storage")

type IOCPWorker struct {
	id int

	handle windows.Handle
}

func NewIOCPWorker(id int) *IOCPWorker {

	handle, err := windows.CreateIoCompletionPort(windows.InvalidHandle, 0, 0, 0)

	if err != nil {

		IOCPWorkerLogger.Error(fmt.Sprintf("failed to create IOCP handle: %v", err))
	}

	return &IOCPWorker{

		id: id,

		handle: handle,
	}
}

func (worker *IOCPWorker) Start() {

	go func() {

		for {

			if utils.GlobalShutdown {

				break
			}

			worker.processCompletionEvent()
		}

	}()
}

func (worker *IOCPWorker) processCompletionEvent() {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			IOCPWorkerLogger.Error(fmt.Sprintf(errorIOWorker, err, worker.id))

			IOCPWorkerLogger.Error(fmt.Sprintf("!!!STACK TRACE for io-worker %v!!! \n %v", worker.id, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	var transferred uint32

	var key uintptr

	var overlapped *windows.Overlapped

	err := windows.GetQueuedCompletionStatus(worker.handle, &transferred, &key, &overlapped, 10000) // 100ms timeout

	if overlapped == nil {

		// Timeout or error with no overlapped structure
		return
	}

	request := (*ReadRequest)(unsafe.Pointer(overlapped))

	defer request.waitGroup.Done()

	if err != nil {

		request.err = err

		if utils.DebugEnabled() {

			IOCPWorkerLogger.Error(fmt.Sprintf("error %v occurred while doing i/o for worker %v", err, request.workerId))
		}

	}
}

func ExecuteIOCPRequests(requests []*ReadRequest, buffers [][]byte, errs []error, waitGroup *sync.WaitGroup) error {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			IOCPWorkerLogger.Error(fmt.Sprintf("error %v occurred while executing IOCP requests", err))

			IOCPWorkerLogger.Error(fmt.Sprintf("!!!STACK TRACE for execute IOCP requests !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	waitGroup.Wait()

	for _, request := range requests {

		if errs[request.index] == nil || strings.Contains(errs[request.index].Error(), "internal") {

			bytes := getValueBytes(buffers[request.index])

			if bytes != nil {

				buffers[request.index] = bytes

				errs[request.index] = nil

			} else if request.err == nil {

				// If there's no error from the request but the value bytes are invalid
				errs[request.index] = fmt.Errorf(utils.ErrorCorrupted)

			} else {

				errs[request.index] = request.err
			}
		}

	}

	return nil
}
