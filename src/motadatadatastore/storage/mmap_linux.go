/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package storage

import (
	"golang.org/x/sys/unix"
)

func mmap(length int, protection, fileDescriptor uintptr, offset int64) ([]byte, error) {

	flags := unix.MAP_SHARED

	protections := unix.PROT_READ

	switch {

	case protection&Copy != 0:

		protections |= unix.PROT_WRITE

		flags = unix.MAP_PRIVATE

	case protection&ReadWrite != 0:

		protections |= unix.PROT_WRITE
	}

	if protection&Execute != 0 {

		protections |= unix.PROT_EXEC
	}

	bytes, err := unix.Mmap(int(fileDescriptor), offset, length, protections, flags)

	if err != nil {
		return bytes, err
	}

	return bytes, nil
}

func (m MMap) unmap() error {
	return unix.Munmap(m)
}

func madvise(bytes []byte, advice int) (err error) {
	return unix.Madvise(bytes, advice)
}

func AdviceRandom(bytes []byte) error {
	return madvise(bytes, unix.MADV_RANDOM)
}

func (m MMap) FlushAsync() error {
	return unix.Msync(m, unix.MS_ASYNC)
}

func (m MMap) Flush() error {
	return unix.Msync(m, unix.MS_SYNC)
}
