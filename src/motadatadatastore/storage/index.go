/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
	The index is the key holder of DATASTORE, where we store all the keys of the store using FST.
*/

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-01             Vedant Dokania       	MOTADATA-5194  Handling panic in the rebuild index
* 2025-06-04             <PERSON><PERSON><PERSON> Shah            MOTADATA-5780 Handled File Open/Close logic for fst manually instead of vellum
 */

package storage

import (
	bytes2 "bytes"
	"encoding/binary"
	"errors"
	"fmt"
	"github.com/blevesearch/vellum"
	"github.com/blevesearch/vellum/regexp"
	"github.com/dolthub/swiss"
	"math"
	"motadatadatastore/codec"
	"motadatadatastore/utils"
	"os"
	"sort"
)

var indexLogger = utils.NewLogger("Index", "storage")

func writeTempIndex(bytes []byte, offset uint64, partition *Partition, encoder codec.Encoder) error {

	var err error

	index := utils.NotAvailable

	var encodedBytes []byte

	if partition.tempIndexFile == nil {

		partition.tempIndexFile, err = os.Create(partition.path + utils.PathSeparator + TempIdx)

		if err != nil {

			return errors.New(fmt.Sprintf("failed to create the temp index file for the partition %v, reason:%v", partition.name, err.Error()))

		}

		_ = partition.tempIndexFile.Truncate(int64(2+len(bytes)+8+3) * 4)

		partition.tempIndexMemoryMappedBytes, err = Map(partition.tempIndexFile, ReadWrite)

		if err != nil {

			_ = partition.tempIndexFile.Close()

			_ = os.Remove(partition.path + utils.PathSeparator + TempIdx)

			errs := fmt.Sprintf("failed to create the temp index for the partition %v, reason:%v", partition.name, err.Error())

			if partition.tempIndexMemoryMappedBytes != nil {

				err = partition.tempIndexMemoryMappedBytes.Unmap()

				if err != nil {

					indexLogger.Error(fmt.Sprintf("failed to unmap the temp index for the partition %v, reason:%v", partition.name, err.Error()))
				}
			}

			indexLogger.Error(errs)

			return errors.New(errs)

		}

		partition.memoryIndices = swiss.NewMap[string, uint64](100)
	} else {

		if len(partition.tempIndexMemoryMappedBytes) < partition.tempIndexSize+(2+len(bytes)+8+3) {

			partition.tempIndexMemoryMappedBytes, err = Remap(partition.tempIndexMemoryMappedBytes, (len(partition.tempIndexMemoryMappedBytes)+2+len(bytes)+8+3)*4, partition.tempIndexFile)

			if err != nil {

				errs := fmt.Sprintf("failed to extend the temp index for the partition %v, reason:%v", partition.name, err.Error())

				_ = partition.tempIndexFile.Close()

				if partition.tempIndexMemoryMappedBytes != nil {

					err = partition.tempIndexMemoryMappedBytes.Unmap()

					if err != nil {

						indexLogger.Error(fmt.Sprintf("failed to unmap the temp index for the partition %v, reason:%v", partition.name, err.Error()))
					}
				}

				indexLogger.Error(errs)

				return errors.New(errs)

			}
		}
	}

	//there might be a scenario in which temp idx file is written in the old manner so need to write the same in the old fashion as store variant is not updated in that store in case of new fashion
	if partition.variant < version2 {

		index, encodedBytes = writeIndexItem(bytes, offset, encoder)
	} else {

		index, encodedBytes = writeIndexItemV2(bytes, offset, encoder)

	}

	copy(partition.tempIndexMemoryMappedBytes[partition.tempIndexSize:], encodedBytes)

	partition.tempIndexSize += len(encodedBytes)

	partition.memoryIndices.Put(string(bytes), offset)

	encoder.MemoryPool.ReleaseBytePool(index)

	if partition.cacheIndices != nil {

		partition.cacheIndices.Put(string(bytes), offset)
	}

	return err
}

func writeIndexItem(keyBytes []byte, offset uint64, encoder codec.Encoder) (int, []byte) {

	index, bytes := encoder.MemoryPool.AcquireBytePool(2 + len(keyBytes) + 8 + 3)

	bufferIndex, bufferBytes := encoder.WriteUINT16Value(uint16(len(keyBytes)), 0)

	copy(bytes, bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	copy(bytes[2:], keyBytes)

	bufferIndex, bufferBytes = encoder.WriteUINT64Value(offset, 0)

	copy(bytes[2+len(keyBytes):], bufferBytes)

	copy(bytes[len(bytes)-3:], utils.EOTBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	return index, bytes

}

// new method to write temp idx in version 1.42
func writeIndexItemV2(keyBytes []byte, offset uint64, encoder codec.Encoder) (int, []byte) {

	index, bytes := encoder.MemoryPool.AcquireBytePool(4 + 2 + len(keyBytes) + 8 + 3)

	bufferIndex, bufferBytes := encoder.WriteINT32Value(int32(2+len(keyBytes)+8), 0)

	copy(bytes, bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	bufferIndex, bufferBytes = encoder.WriteUINT16Value(uint16(len(keyBytes)), 0)

	copy(bytes[4:], bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	copy(bytes[4+2:], keyBytes)

	bufferIndex, bufferBytes = encoder.WriteUINT64Value(offset, 0)

	copy(bytes[4+2+len(keyBytes):], bufferBytes)

	copy(bytes[len(bytes)-3:], utils.EOTBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	return index, bytes

}

func mergeIndex(partition *Partition, encoder codec.Encoder) {

	if partition.memoryIndices != nil && partition.memoryIndices.Count() > 0 {

		if utils.EnvironmentType != utils.DatastoreBenchUnitEnvironment {

			err := partition.tempIndexMemoryMappedBytes.Unmap()

			if err != nil {

				indexLogger.Fatal(fmt.Sprintf("failed to unmap temp index memory mapped file for partition %v, reason : %v", partition.path, err.Error()))
			}

			_ = partition.tempIndexFile.Close()

			partition.tempIndexMemoryMappedBytes = nil

			partition.tempIndexFile = nil

			partition.tempIndexSize = 0
		}

		var buffer bytes2.Buffer

		tempIndexWriter, err := vellum.New(&buffer, nil)

		if err != nil {

			indexLogger.Error(fmt.Sprintf("failed to create the temp index writer for the partition %v, reason:%v", partition.name, err.Error()))

			return
		}

		poolIndex := utils.NotAvailable

		var keys []string

		if partition.memoryIndices.Count() > encoder.MemoryPool.GetPoolLength() {

			keys = make([]string, partition.memoryIndices.Count(), partition.memoryIndices.Count())

		} else {

			poolIndex, keys = encoder.MemoryPool.AcquireStringPool(partition.memoryIndices.Count())
		}

		i := 0

		partition.memoryIndices.Iter(func(key string, _ uint64) (stop bool) {

			keys[i] = key

			i++

			return stop
		})

		sort.Strings(keys)

		for j := range keys {

			value, _ := partition.memoryIndices.Get(keys[j])

			_ = tempIndexWriter.Insert([]byte(keys[j]), value)
		}

		if poolIndex != utils.NotAvailable {

			encoder.MemoryPool.ReleaseStringPool(poolIndex)
		}

		_ = tempIndexWriter.Close()

		if utils.EnvironmentType != utils.DatastoreBenchUnitEnvironment {

			partition.memoryIndices = nil
		}

		if partition.index != nil && partition.index.Len() > 0 {

			iterators := make([]vellum.Iterator, 2)

			iterator, _ := partition.index.Iterator(nil, nil)

			iterators[0] = iterator

			var tempIndex *vellum.FST

			tempIndex, err = vellum.Load(buffer.Bytes())

			if err != nil {

				indexLogger.Error(fmt.Sprintf("failed to load temp index of the partition %v, reason:%v", partition.name, err.Error()))

				return
			}

			iterator, _ = tempIndex.Iterator(nil, nil)

			iterators[1] = iterator

			file, err := os.Create(partition.path + utils.PathSeparator + MergedIdxFile)

			if err != nil {

				indexLogger.Error(fmt.Sprintf("failed to create the merged index file for the partition %v, reason:%v", partition.name, err.Error()))

				_ = tempIndex.Close()

				return
			}

			err = vellum.Merge(file, nil, iterators, func(values []uint64) uint64 {

				if len(values) > 1 {
					return values[1]
				}

				return values[0]
			})

			if err != nil {

				indexLogger.Error(fmt.Sprintf("failed to merge index for the partition %v, reason:%v", partition.name, err.Error()))

				_ = file.Close()

				_ = os.Remove(partition.path + utils.PathSeparator + MergedIdxFile)

				_ = tempIndex.Close()

				return
			}

			_ = file.Close()

			_ = tempIndex.Close()

			if partition.indexMMapBytes != nil {

				_ = partition.indexMMapBytes.Unmap()
			}

			if partition.indexFile != nil {

				_ = partition.indexFile.Close()
			}

			_ = partition.index.Close()

			err = os.Remove(partition.path + utils.PathSeparator + IdxFile)

			if err != nil {

				indexLogger.Error(fmt.Sprintf("failed to delete index file of the partition %v, reason:%v", partition.name, err.Error()))

				return
			}

			err = os.Rename(partition.path+utils.PathSeparator+MergedIdxFile,
				partition.path+utils.PathSeparator+IdxFile)

			if err != nil {

				_ = os.Remove(partition.path + utils.PathSeparator + MergedIdxFile)

				indexLogger.Error(fmt.Sprintf("failed to rename the merged index file of the partition %v, reason:%v", partition.name, err.Error()))

				return
			}

		} else {

			err = os.WriteFile(partition.path+utils.PathSeparator+IdxFile, buffer.Bytes(), 0666)

			if err != nil {

				indexLogger.Error(fmt.Sprintf("failed to write the index file of the partition %v, reason:%v", partition.name, err.Error()))

				return
			}
		}

		partition.indexFile, err = os.OpenFile(partition.path+utils.PathSeparator+IdxFile, os.O_RDWR, 0666)

		if err != nil {

			if !errors.Is(err, utils.ErrorFileNotFound) {

				indexLogger.Error(fmt.Sprintf("failed to open the index file of the partition %v, reason:%v", partition.name, err.Error()))

				return
			}

		}

		if partition.indexFile != nil {

			partition.indexMMapBytes, err = Map(partition.indexFile, ReadWrite)

			if err != nil {

				_ = partition.indexFile.Close()

				indexLogger.Error(fmt.Sprintf("failed to open the index file of the partition %v, reason:%v", partition.name, err.Error()))

				return
			}

			partition.index, err = vellum.Load(partition.indexMMapBytes)

			if err != nil {

				_ = partition.indexMMapBytes.Unmap()

				_ = partition.indexFile.Close()

				indexLogger.Error(fmt.Sprintf("failed to open the index file of the partition %v, reason:%v", partition.name, err.Error()))

				return

			}
		}

		if utils.EnvironmentType != utils.DatastoreBenchUnitEnvironment {

			_ = os.Remove(partition.path + utils.PathSeparator + TempIdx)
		}

	}

}

func readIndexItem(bytes []byte) (string, uint64) {

	index := 0

	length := codec.ReadUINT16Value(bytes[:2], &index)

	return string(bytes[index : index+length]), codec.ReadUINT64Value(bytes[index+length : index+length+8])

}

/*
existing :-

Insertion:-
2 bytes length of key
next bytes key itself
8 bytes offset/ value
EOT
Retrieval :-
 1. Splits from EOT bytes and do the operation

NEW:-

Insertion :-
 1. First 4 bytes ( length of key +key itself + offset length )
 2. 4 bytes length of key
 3. next bytes key itself
 4. 8 bytes offset/value
 5. EOT bytes

Retrieval :-
 1. First read 4 bytes of length and after that length if EOT is present than only we will consider it as healthy record and we will continue till end of the buffer. No split will be there.
*/
func rebuildIndex(partition *Partition, encoder codec.Encoder) error {

	defer func() {

		if r := recover(); r != nil {

			partitionLogger.Error(fmt.Sprintf("failed to rebuild index for partition %v error %v", partition.name, r))

			_ = os.Remove(partition.path + utils.PathSeparator + TempIdx)

		}
	}()
	var buffer bytes2.Buffer

	buffers, err := os.ReadFile(partition.path + utils.PathSeparator + TempIdx)

	if err != nil {

		errs := errors.New(fmt.Sprintf("failed to open the temp index file for the partition %v, reason:%v", partition.name, err))

		indexLogger.Error(errs.Error())

		return errs

	}

	tempMemoryIndices := map[string]uint64{}

	//backward compatibility to read the previous closed stores

	if partition.variant < version2 {

		bufferBytes := bytes2.Split(buffers, utils.EOTBytes)

		// if last buffers has eot then we get last tokens as an empty so avoid it or if buffers not end with eot means corrupted record so skip that last record
		for i := 0; i < len(bufferBytes)-1; i++ {

			key, value := readIndexItem(bufferBytes[i])

			if len(key) > 0 {

				tempMemoryIndices[key] = value
			} else {

				break
			}
		}

	} else {

		position := int32(0)

		for position+4 < int32(len(buffers)) {

			length := codec.ReadINT32Value(buffers[position : position+4])

			position += 4

			if int32(len(buffers[position:])) >= length+3 && bytes2.Equal(buffers[position+length:position+length+3], utils.EOTBytes) {

				key, value := readIndexItem(buffers[position : position+length])

				if len(key) > 0 {

					tempMemoryIndices[key] = value

					position += length + 3 // 3 EOT Bytes
				} else {

					break
				}

			} else {

				//means corrupted record
				break
			}

		}

	}

	if len(tempMemoryIndices) > 0 {

		var tempIndexWriter *vellum.Builder

		tempIndexWriter, err = vellum.New(&buffer, nil)

		if err != nil {

			return errors.New(fmt.Sprintf("failed to create the temp index writer for the partition %v, reason:%v", partition.name, err.Error()))

		}

		poolIndex := utils.NotAvailable

		var keys []string

		if len(tempMemoryIndices) > encoder.MemoryPool.GetPoolLength() {

			keys = make([]string, len(tempMemoryIndices), len(tempMemoryIndices))
		} else {

			poolIndex, keys = encoder.MemoryPool.AcquireStringPool(len(tempMemoryIndices))
		}

		i := 0

		for key := range tempMemoryIndices {

			keys[i] = key

			i++

		}

		sort.Strings(keys)

		for _, key := range keys {

			_ = tempIndexWriter.Insert([]byte(key), tempMemoryIndices[key])
		}

		if poolIndex != utils.NotAvailable {

			encoder.MemoryPool.ReleaseStringPool(poolIndex)
		}

		_ = tempIndexWriter.Close()

		tempMemoryIndices = nil

		if partition.index != nil && partition.index.Len() > 0 {

			iterators := make([]vellum.Iterator, 2)

			iterator, _ := partition.index.Iterator(nil, nil)

			iterators[0] = iterator

			var tempIndex *vellum.FST

			tempIndex, err = vellum.Load(buffer.Bytes())

			if err != nil {

				return errors.New(fmt.Sprintf("failed to load temp index of the partition %v, reason:%v", partition.name, err.Error()))

			}

			iterator, _ = tempIndex.Iterator(nil, nil)

			iterators[1] = iterator

			var file *os.File

			file, err = os.Create(partition.path + utils.PathSeparator + MergedIdxFile)

			if err != nil {

				_ = tempIndex.Close()

				return errors.New(fmt.Sprintf("failed to create the merged index file for the partition %v, reason:%v", partition.name, err.Error()))

			}

			err = vellum.Merge(file, nil, iterators, func(values []uint64) uint64 {

				if len(values) > 1 {
					return values[1]
				}

				return values[0]
			})

			if err != nil {

				_ = file.Close()

				_ = os.Remove(partition.path + utils.PathSeparator + MergedIdxFile)

				_ = tempIndex.Close()

				return errors.New(fmt.Sprintf("failed to merge index for the partition %v, reason:%v", partition.name, err.Error()))

			}

			_ = file.Close()

			_ = tempIndex.Close()

			if partition.indexMMapBytes != nil {

				_ = partition.indexMMapBytes.Unmap()
			}

			if partition.indexFile != nil {

				_ = partition.indexFile.Close()
			}

			_ = partition.index.Close()

			err = os.Remove(partition.path + utils.PathSeparator + IdxFile)

			if err != nil {

				return errors.New(fmt.Sprintf("failed to delete index file of the partition %v, reason:%v", partition.name, err.Error()))

			}

			err = os.Rename(partition.path+utils.PathSeparator+MergedIdxFile,
				partition.path+utils.PathSeparator+IdxFile)

			if err != nil {

				_ = os.Remove(partition.path + utils.PathSeparator + MergedIdxFile)

				return errors.New(fmt.Sprintf("failed to rename the merged index file of the partition %v, reason:%v", partition.name, err.Error()))

			}

		} else {

			err = os.WriteFile(partition.path+utils.PathSeparator+IdxFile, buffer.Bytes(), 0666)

			if err != nil {

				return errors.New(fmt.Sprintf("failed to write the index file of the partition %v, reason:%v", partition.name, err.Error()))

			}
		}

		partition.indexFile, err = os.OpenFile(partition.path+utils.PathSeparator+IdxFile, os.O_RDWR, 0666)

		if err != nil {

			if !errors.Is(err, utils.ErrorFileNotFound) {

				return errors.New(fmt.Sprintf("failed to open the index file of the partition %v, reason:%v", partition.name, err.Error()))

			}

		}

		if partition.indexFile != nil {

			partition.indexMMapBytes, err = Map(partition.indexFile, ReadWrite)

			if err != nil {

				_ = partition.indexFile.Close()

				return errors.New(fmt.Sprintf("failed to open the index file of the partition %v, reason:%v", partition.name, err.Error()))
			}

			partition.index, err = vellum.Load(partition.indexMMapBytes)

			if err != nil {

				_ = partition.indexMMapBytes.Unmap()

				_ = partition.indexFile.Close()

				return errors.New(fmt.Sprintf("failed to open the index file of the partition %v, reason:%v", partition.name, err.Error()))

			}
		}

	}

	_ = os.Remove(partition.path + utils.PathSeparator + TempIdx)

	return nil

}

func searchIndex(bytes []byte, partition *Partition) (int, bool, error) {

	if partition.memoryIndices != nil && partition.memoryIndices.Count() > 0 {

		value, found := partition.memoryIndices.Get(string(bytes))

		if found {

			if value == math.MaxUint64 { //means deleted entry ... hence return false..

				indexLogger.Warn(fmt.Sprintf(utils.ErrorDeletedRecord, string(bytes), partition.name))

				return -1, false, errors.New(fmt.Sprintf(utils.ErrorDeletedRecord, string(bytes), partition.name))
			}

			return int(value), found, nil

		}
	}

	if partition.index != nil {

		value, found, err := partition.index.Get(bytes)

		if err != nil {

			return -1, found, err
		}

		if value == math.MaxUint64 { //means deleted entry ... hence return false..

			indexLogger.Warn(fmt.Sprintf(utils.ErrorDeletedRecord, string(bytes), partition.name))

			return -1, false, errors.New(fmt.Sprintf(utils.ErrorDeletedRecord, string(bytes), partition.name))
		}

		return int(value), found, nil
	}

	return -1, false, nil

}

/////////////////////// Lookup Related Method //////////////////////////////

func doGreaterThanLookup(partition *Partition, conditionValue uint64, inclusive bool, excluded bool) map[uint64][]byte {

	keys := map[uint64][]byte{}

	if partition.cacheIndices != nil && partition.cacheIndices.Count() > 0 {

		doGreaterThanCacheLookup(partition.cacheIndices, keys, conditionValue, inclusive, excluded)

		return keys
	}

	if partition.memoryIndices != nil && partition.memoryIndices.Count() > 0 {

		doGreaterThanCacheLookup(partition.memoryIndices, keys, conditionValue, inclusive, excluded)
	}

	if partition.index != nil {

		valueBytes := make([]byte, 8)

		var iterator vellum.Iterator

		var err error

		if inclusive {

			binary.BigEndian.PutUint64(valueBytes, conditionValue)

		} else {

			binary.BigEndian.PutUint64(valueBytes, conditionValue+1)
		}

		if excluded {

			iterator, err = partition.index.Iterator(nil, valueBytes)

		} else {

			iterator, err = partition.index.Iterator(valueBytes, nil)
		}

		for err == nil {

			keyBytes, value := iterator.Current()

			bytes := make([]byte, len(keyBytes))

			copy(bytes, keyBytes)

			keys[value] = bytes

			err = iterator.Next()
		}
	}

	return keys
}

func doGreaterThanCacheLookup(indices *swiss.Map[string, uint64], keys map[uint64][]byte, conditionValue uint64, inclusive, excluded bool) {

	indices.Iter(func(key string, offset uint64) (stop bool) {

		value := binary.BigEndian.Uint64([]byte(key))

		if inclusive {

			if excluded {

				if value < conditionValue {

					keys[offset] = []byte(key)
				}
			} else {

				if value >= conditionValue {

					keys[offset] = []byte(key)

				}
			}
		} else if !inclusive {

			if excluded {

				if value < conditionValue {

					keys[offset] = []byte(key)
				}
			} else {

				if value > conditionValue {

					keys[offset] = []byte(key)
				}
			}
		}

		return stop
	})
}

func doLessThanLookup(partition *Partition, conditionValue uint64, inclusive bool, excluded bool) map[uint64][]byte {

	keys := map[uint64][]byte{}

	if partition.cacheIndices != nil && partition.cacheIndices.Count() > 0 {

		doLessThanCacheLookup(partition.cacheIndices, keys, conditionValue, inclusive, excluded)

		return keys
	}

	if partition.memoryIndices != nil && partition.memoryIndices.Count() > 0 {

		doLessThanCacheLookup(partition.memoryIndices, keys, conditionValue, inclusive, excluded)
	}

	if partition.index != nil {

		valueBytes := make([]byte, 8)

		var iterator vellum.Iterator

		var err error

		if inclusive {

			binary.BigEndian.PutUint64(valueBytes, conditionValue+1)

		} else {

			binary.BigEndian.PutUint64(valueBytes, conditionValue)
		}

		if excluded {

			iterator, err = partition.index.Iterator(valueBytes, nil)

		} else {

			iterator, err = partition.index.Iterator(nil, valueBytes)
		}

		for err == nil {

			keyBytes, value := iterator.Current()

			bytes := make([]byte, len(keyBytes))

			copy(bytes, keyBytes)

			keys[value] = bytes

			err = iterator.Next()
		}

	}

	return keys
}

func doLessThanCacheLookup(indices *swiss.Map[string, uint64], keys map[uint64][]byte, conditionValue uint64, inclusive, excluded bool) {

	indices.Iter(func(key string, offset uint64) (stop bool) {

		value := binary.BigEndian.Uint64([]byte(key))

		if inclusive {

			if excluded {

				if value > conditionValue {

					keys[offset] = []byte(key)
				}
			} else {

				if value <= conditionValue {

					keys[offset] = []byte(key)

				}
			}
		} else if !inclusive {

			if excluded {

				if value > conditionValue {

					keys[offset] = []byte(key)
				}
			} else {

				if value < conditionValue {

					keys[offset] = []byte(key)
				}
			}
		}

		return stop
	})
}

func doPrefixLookup(partition *Partition, bytes []byte, exclude bool) map[uint64][]byte {

	keys := map[uint64][]byte{}

	if partition.cacheIndices != nil && partition.cacheIndices.Count() > 0 {

		doPrefixCacheLookup(partition.cacheIndices, keys, bytes, exclude)

		return keys
	}

	if partition.memoryIndices != nil && partition.memoryIndices.Count() > 0 {

		doPrefixCacheLookup(partition.memoryIndices, keys, bytes, exclude)
	}

	if partition.index != nil {

		if !exclude {

			query, _ := regexp.New(string(bytes) + ".*")

			iterator, err := partition.index.Search(query, nil, nil)

			for err == nil {

				keyBytes, value := iterator.Current()

				valueBytes := make([]byte, len(keyBytes))

				copy(valueBytes, keyBytes)

				keys[value] = valueBytes

				err = iterator.Next()
			}

		} else {

			iterator, err := partition.index.Iterator(nil, nil)

			for err == nil {

				keyBytes, value := iterator.Current()

				if !bytes2.HasPrefix(keyBytes, bytes) {

					valueBytes := make([]byte, len(keyBytes))

					copy(valueBytes, keyBytes)

					keys[value] = valueBytes

				}

				err = iterator.Next()

			}
		}

	}

	return keys
}

func doPrefixCacheLookup(indices *swiss.Map[string, uint64], keys map[uint64][]byte, bytes []byte, exclude bool) {

	if !exclude {

		indices.Iter(func(key string, offset uint64) (stop bool) {

			if bytes2.HasPrefix([]byte(key), bytes) {

				keys[offset] = []byte(key)
			}

			return stop
		})

	} else {

		indices.Iter(func(key string, offset uint64) (stop bool) {

			if !bytes2.HasPrefix([]byte(key), bytes) {

				keys[offset] = []byte(key)
			}

			return stop
		})
	}
}

func doSuffixLookup(partition *Partition, bytes []byte, exclude bool) map[uint64][]byte {

	keys := map[uint64][]byte{}

	if partition.cacheIndices != nil && partition.cacheIndices.Count() > 0 {

		doSuffixCacheLookup(partition.cacheIndices, keys, bytes, exclude)

		return keys
	}

	if partition.memoryIndices != nil && partition.memoryIndices.Count() > 0 {

		doSuffixCacheLookup(partition.memoryIndices, keys, bytes, exclude)
	}

	if partition.index != nil {

		if !exclude {

			query, _ := regexp.New(".*" + string(bytes))

			iterator, err := partition.index.Search(query, nil, nil)

			for err == nil {

				keyBytes, value := iterator.Current()

				valueBytes := make([]byte, len(keyBytes))

				copy(valueBytes, keyBytes)

				keys[value] = valueBytes

				err = iterator.Next()
			}

		} else {

			iterator, err := partition.index.Iterator(nil, nil)

			for err == nil {

				keyBytes, value := iterator.Current()

				if !bytes2.HasSuffix(keyBytes, bytes) {

					valueBytes := make([]byte, len(keyBytes))

					copy(valueBytes, keyBytes)

					keys[value] = valueBytes
				}

				err = iterator.Next()

			}
		}

	}

	return keys
}

func doSuffixCacheLookup(indices *swiss.Map[string, uint64], keys map[uint64][]byte, bytes []byte, exclude bool) {

	if !exclude {

		indices.Iter(func(key string, offset uint64) (stop bool) {

			if bytes2.HasSuffix([]byte(key), bytes) {

				keys[offset] = []byte(key)
			}

			return stop
		})
	} else {

		indices.Iter(func(key string, offset uint64) (stop bool) {

			if !bytes2.HasSuffix([]byte(key), bytes) {

				keys[offset] = []byte(key)
			}

			return stop
		})
	}
}

func doContainLookup(partition *Partition, bytes []byte, exclude bool) map[uint64][]byte {

	keys := map[uint64][]byte{}

	if partition.cacheIndices != nil && partition.cacheIndices.Count() > 0 {

		doContainCacheLookup(partition.cacheIndices, keys, bytes, exclude)

		return keys
	}

	if partition.memoryIndices != nil && partition.memoryIndices.Count() > 0 {

		doContainCacheLookup(partition.memoryIndices, keys, bytes, exclude)
	}

	if partition.index != nil {

		if !exclude {

			query, _ := regexp.New(".*" + string(bytes) + ".*")

			iterator, err := partition.index.Search(query, nil, nil)

			for err == nil {

				keyBytes, value := iterator.Current()

				valueBytes := make([]byte, len(keyBytes))

				copy(valueBytes, keyBytes)

				keys[value] = valueBytes

				err = iterator.Next()
			}

		} else {

			iterator, err := partition.index.Iterator(nil, nil)

			for err == nil {

				keyBytes, value := iterator.Current()

				if !bytes2.Contains(keyBytes, bytes) {

					valueBytes := make([]byte, len(keyBytes))

					copy(valueBytes, keyBytes)

					keys[value] = valueBytes

				}

				err = iterator.Next()

			}
		}

	}

	return keys
}

func doContainCacheLookup(indices *swiss.Map[string, uint64], keys map[uint64][]byte, bytes []byte, exclude bool) {

	if exclude {

		indices.Iter(func(key string, offset uint64) (stop bool) {

			if !bytes2.Contains([]byte(key), bytes) {

				keys[offset] = []byte(key)
			}

			return stop
		})

	} else {

		indices.Iter(func(key string, offset uint64) (stop bool) {

			if bytes2.Contains([]byte(key), bytes) {

				keys[offset] = []byte(key)
			}

			return stop
		})
	}
}

func doListing(partition *Partition, stringValues *swiss.Map[string, int32], numericValues *swiss.Map[int64, int32], excluded bool, dataType codec.DataType) map[uint64][]byte {

	keys := map[uint64][]byte{}

	// means listing all keys
	if dataType == codec.Invalid {

		if partition.cacheIndices != nil && partition.cacheIndices.Count() > 0 {

			doCacheListing(partition.cacheIndices, keys, stringValues, numericValues, excluded)

			return keys
		}

		if partition.memoryIndices != nil && partition.memoryIndices.Count() > 0 {

			doCacheListing(partition.memoryIndices, keys, stringValues, numericValues, excluded)
		}

		if partition.index != nil {

			iterator, err := partition.index.Iterator(nil, nil)

			if !excluded {

				for err == nil {

					keyBytes, value := iterator.Current()

					bytes := make([]byte, len(keyBytes))

					copy(bytes, keyBytes)

					keys[value] = bytes

					err = iterator.Next()
				}
			} else {

				if stringValues != nil && stringValues.Count() > 0 {

					for err == nil {

						keyBytes, value := iterator.Current()

						if !stringValues.Has(string(keyBytes)) {

							bytes := make([]byte, len(keyBytes))

							copy(bytes, keyBytes)

							keys[value] = bytes
						}

						err = iterator.Next()
					}

				} else if numericValues != nil && numericValues.Count() > 0 {

					for err == nil {

						keyBytes, value := iterator.Current()

						if !numericValues.Has(int64(binary.BigEndian.Uint64(keyBytes))) {

							bytes := make([]byte, len(keyBytes))

							copy(bytes, keyBytes)

							keys[value] = bytes
						}

						err = iterator.Next()
					}
				}
			}
		}
	} else {

		// means listing keys only that stringValues or numericValues contains
		if dataType == codec.String && stringValues != nil && stringValues.Count() > 0 {

			if partition.cacheIndices != nil && partition.cacheIndices.Count() > 0 {

				if !excluded {

					partition.cacheIndices.Iter(func(key string, offset uint64) (stop bool) {

						if stringValues.Has(key) {

							keys[offset] = []byte(key)
						}

						return stop
					})

				} else {

					partition.cacheIndices.Iter(func(key string, offset uint64) (stop bool) {

						if !stringValues.Has(key) {

							keys[offset] = []byte(key)
						}

						return stop
					})
				}

				return keys
			}

			if partition.index != nil {

				iterator, err := partition.index.Iterator(nil, nil)

				if !excluded {

					for err == nil {

						keyBytes, value := iterator.Current()

						if stringValues.Has(string(keyBytes)) {

							bytes := make([]byte, len(keyBytes))

							copy(bytes, keyBytes)

							keys[value] = bytes
						}

						err = iterator.Next()
					}
				} else {

					for err == nil {

						keyBytes, value := iterator.Current()

						if !stringValues.Has(string(keyBytes)) {

							bytes := make([]byte, len(keyBytes))

							copy(bytes, keyBytes)

							keys[value] = bytes
						}

						err = iterator.Next()
					}
				}
			}

			if partition.memoryIndices != nil && partition.memoryIndices.Count() > 0 {

				if !excluded {

					partition.memoryIndices.Iter(func(key string, offset uint64) (stop bool) {

						if stringValues.Has(key) {

							keys[offset] = []byte(key)
						}

						return stop
					})

				} else {

					partition.memoryIndices.Iter(func(key string, offset uint64) (stop bool) {

						if !stringValues.Has(key) {

							keys[offset] = []byte(key)
						}

						return stop
					})
				}

			}

		} else if numericValues != nil && numericValues.Count() > 0 {

			if partition.cacheIndices != nil && partition.cacheIndices.Count() > 0 {

				if !excluded {

					partition.cacheIndices.Iter(func(key string, offset uint64) (stop bool) {

						if numericValues.Has(int64(binary.BigEndian.Uint64([]byte(key)))) {

							keys[offset] = []byte(key)
						}

						return stop
					})

				} else {

					partition.cacheIndices.Iter(func(key string, offset uint64) (stop bool) {

						if !numericValues.Has(int64(binary.BigEndian.Uint64([]byte(key)))) {

							keys[offset] = []byte(key)
						}

						return stop
					})
				}

				return keys
			}

			if partition.index != nil {

				iterator, err := partition.index.Iterator(nil, nil)

				if !excluded {

					for err == nil {

						keyBytes, value := iterator.Current()

						if numericValues.Has(int64(binary.BigEndian.Uint64(keyBytes))) {

							bytes := make([]byte, len(keyBytes))

							copy(bytes, keyBytes)

							keys[value] = bytes
						}

						err = iterator.Next()
					}
				} else {

					for err == nil {

						keyBytes, value := iterator.Current()

						if !numericValues.Has(int64(binary.BigEndian.Uint64(keyBytes))) {

							bytes := make([]byte, len(keyBytes))

							copy(bytes, keyBytes)

							keys[value] = bytes
						}

						err = iterator.Next()
					}
				}
			}

			if partition.memoryIndices != nil && partition.memoryIndices.Count() > 0 {

				if !excluded {

					partition.memoryIndices.Iter(func(key string, offset uint64) (stop bool) {

						if numericValues.Has(int64(binary.BigEndian.Uint64([]byte(key)))) {

							keys[offset] = []byte(key)
						}

						return stop
					})

				} else {

					partition.memoryIndices.Iter(func(key string, offset uint64) (stop bool) {

						if !numericValues.Has(int64(binary.BigEndian.Uint64([]byte(key)))) {

							keys[offset] = []byte(key)
						}

						return stop
					})
				}
			}

		}
	}

	return keys
}

func doCacheListing(indices *swiss.Map[string, uint64], keys map[uint64][]byte, stringValues *swiss.Map[string, int32], numericValues *swiss.Map[int64, int32], excluded bool) {

	if !excluded {

		indices.Iter(func(key string, offset uint64) (stop bool) {

			keys[offset] = []byte(key)

			return stop
		})

	} else {

		if stringValues != nil && stringValues.Count() > 0 {

			indices.Iter(func(key string, offset uint64) (stop bool) {

				if !stringValues.Has(key) {

					keys[offset] = []byte(key)
				}

				return stop
			})

		} else if numericValues != nil && numericValues.Count() > 0 {

			indices.Iter(func(key string, offset uint64) (stop bool) {

				if !numericValues.Has(int64(binary.BigEndian.Uint64([]byte(key)))) {

					keys[offset] = []byte(key)
				}

				return stop
			})
		}
	}
}
