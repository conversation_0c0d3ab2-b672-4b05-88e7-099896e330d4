/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-04-09			 <PERSON><PERSON><PERSON>-4859  Refactoring "fmt.Sprintf" to update go version to 1.24
* 2025-06-04             <PERSON><PERSON><PERSON> <PERSON>            MOTADATA-5780  Removed Read function and shifted to read_windows and read_linux for seperate Implementations.
												Handled file opens using custom OpenFile function to enable IOCP support on Windows.
*/

package storage

import (
	bytes2 "bytes"
	"errors"
	"fmt"
	"github.com/kelindar/bitmap"
	"motadatadatastore/codec"
	"motadatadatastore/utils"
	"os"
	"runtime"
)

var diskIOBatchEvents = make(chan *DiskIOEventBatch, 1_00_000)

var DiskIOEvents = make(chan *DiskIOEvent, 1_00_000)

//bitwise layout of writingSegments type... we will use most 4 bits only

/*
	 128     64      32     16     8     4      2     1
	 7    |  6   |   5  |   4   |  3  |  2   |  1  |  0   |
	|-----------------------------------------------------|
	| 7-2 bits : writingSegments type   1-0 bits : writingSegments no     |
	|													  |
	| 													  |
	|													  |

*/

type Segment uint8

type SegmentType uint8

const maxSegmentSizeBytes = 2 * 1024 * 1024 * 1024 //2 GB

const (
	SegmentType15Bytes      SegmentType = 0                                       //000000 > (0)
	SegmentType20Bytes      SegmentType = 1 << 2                                  // 000001 (4)
	SegmentType30Bytes      SegmentType = 1 << 3                                  // 000010 (8)
	SegmentType40Bytes      SegmentType = 1<<2 | 1<<3                             // 000011 (12)
	SegmentType50Bytes      SegmentType = 1 << 4                                  // 000100 (16)
	SegmentType60Bytes      SegmentType = 1<<2 | 1<<4                             // 000101 (20)
	SegmentType70Bytes      SegmentType = 1<<3 | 1<<4                             // 000110 (24)
	SegmentType80Bytes      SegmentType = 1<<2 | 1<<3 | 1<<4                      // 000111 (28)
	SegmentType90Bytes      SegmentType = 1 << 5                                  // 001000 (32)
	SegmentType100Bytes     SegmentType = 1<<2 | 1<<5                             // 001001 (36)
	SegmentType150Bytes     SegmentType = 1<<3 | 1<<5                             // 001010 (40)
	SegmentType200Bytes     SegmentType = 1<<2 | 1<<3 | 1<<5                      // 001011 (44)
	SegmentType250Bytes     SegmentType = 1<<4 | 1<<5                             // 001100 (48)
	SegmentType300Bytes     SegmentType = 1<<2 | 1<<4 | 1<<5                      // 001101 (52)
	SegmentType350Bytes     SegmentType = 1<<3 | 1<<4 | 1<<5                      // 001110 (56)
	SegmentType400Bytes     SegmentType = 1<<2 | 1<<3 | 1<<4 | 1<<5               // 001111 (60)
	SegmentType450Bytes     SegmentType = 1 << 6                                  //010000 (64)
	SegmentType500Bytes     SegmentType = 1<<2 | 1<<6                             //010001 (68)
	SegmentType600Bytes     SegmentType = 1<<3 | 1<<6                             //010010 (72)
	SegmentType700Bytes     SegmentType = 1<<2 | 1<<3 | 1<<6                      //010011 (76)
	SegmentType800Bytes     SegmentType = 1<<4 | 1<<6                             //010100 (80)
	SegmentType900Bytes     SegmentType = 1<<2 | 1<<4 | 1<<6                      //010101 (84)
	SegmentType1000Bytes    SegmentType = 1<<3 | 1<<4 | 1<<6                      //010110 (88)
	SegmentType1200Bytes    SegmentType = 1<<2 | 1<<3 | 1<<4 | 1<<6               //010111 (92)
	SegmentType1500Bytes    SegmentType = 1<<5 | 1<<6                             //011000 (96)
	SegmentType1700Bytes    SegmentType = 1<<2 | 1<<5 | 1<<6                      //011001 (100)
	SegmentType2000Bytes    SegmentType = 1<<3 | 1<<5 | 1<<6                      //011010 (104)
	SegmentType2200Bytes    SegmentType = 1<<2 | 1<<3 | 1<<5 | 1<<6               //011011 (108)
	SegmentType2400Bytes    SegmentType = 1<<4 | 1<<5 | 1<<6                      //011100 (112)
	SegmentType2600Bytes    SegmentType = 1<<2 | 1<<4 | 1<<5 | 1<<6               //011101 (116)
	SegmentType2800Bytes    SegmentType = 1<<3 | 1<<4 | 1<<5 | 1<<6               //011110 (120)
	SegmentType3000Bytes    SegmentType = 1<<2 | 1<<3 | 1<<4 | 1<<5 | 1<<6        //011111 (124)
	SegmentType3200Bytes    SegmentType = 1 << 7                                  //100000 (128)
	SegmentType3500Bytes    SegmentType = 1<<2 | 1<<7                             //100001 (132)
	SegmentType4000Bytes    SegmentType = 1<<3 | 1<<7                             //100010 (136)
	SegmentType4500Bytes    SegmentType = 1<<2 | 1<<3 | 1<<7                      //100011 (140)
	SegmentType5000Bytes    SegmentType = 1<<4 | 1<<7                             //100100 (144)
	SegmentType5500Bytes    SegmentType = 1<<2 | 1<<4 | 1<<7                      //100101 (148)
	SegmentType6000Bytes    SegmentType = 1<<3 | 1<<4 | 1<<7                      //100110 (152)
	SegmentType7000Bytes    SegmentType = 1<<2 | 1<<3 | 1<<4 | 1<<7               //100111 (156)
	SegmentType8000Bytes    SegmentType = 1<<5 | 1<<7                             //101000 (160)
	SegmentType10000Bytes   SegmentType = 1<<2 | 1<<5 | 1<<7                      //101001 (164)
	SegmentType13000Bytes   SegmentType = 1<<3 | 1<<5 | 1<<7                      //101010 (168)
	SegmentType15000Bytes   SegmentType = 1<<2 | 1<<3 | 1<<5 | 1<<7               //101011 (172)
	SegmentType20000Bytes   SegmentType = 1<<4 | 1<<5 | 1<<7                      //101100 (176)
	SegmentType25000Bytes   SegmentType = 1<<2 | 1<<4 | 1<<5 | 1<<7               //101101 (180)
	SegmentType30000Bytes   SegmentType = 1<<3 | 1<<4 | 1<<5 | 1<<7               //101110 (184)
	SegmentType40000Bytes   SegmentType = 1<<2 | 1<<3 | 1<<4 | 1<<5 | 1<<7        //101111 (188)
	SegmentType50000Bytes   SegmentType = 1<<6 | 1<<7                             //110000 (192)
	SegmentType60000Bytes   SegmentType = 1<<2 | 1<<6 | 1<<7                      //110001 (196)
	SegmentType70000Bytes   SegmentType = 1<<3 | 1<<6 | 1<<7                      //110010 (200)
	SegmentType80000Bytes   SegmentType = 1<<2 | 1<<3 | 1<<6 | 1<<7               //110011 (204)
	SegmentType100000Bytes  SegmentType = 1<<4 | 1<<6 | 1<<7                      //110100 (208)
	SegmentType160000Bytes  SegmentType = 1<<2 | 1<<4 | 1<<6 | 1<<7               //110101 (212)
	SegmentType240000Bytes  SegmentType = 1<<3 | 1<<4 | 1<<6 | 1<<7               //110110 (216)
	SegmentType320000Bytes  SegmentType = 1<<2 | 1<<3 | 1<<4 | 1<<6 | 1<<7        //110111 (220)
	SegmentType400000Bytes  SegmentType = 1<<5 | 1<<6 | 1<<7                      //111000 (224)
	SegmentType480000Bytes  SegmentType = 1<<2 | 1<<5 | 1<<6 | 1<<7               //111001 (228)
	SegmentType600000Bytes  SegmentType = 1<<3 | 1<<5 | 1<<6 | 1<<7               //111010 (232)
	SegmentType700000Bytes  SegmentType = 1<<2 | 1<<3 | 1<<5 | 1<<6 | 1<<7        //111011 (236)
	SegmentType800000Bytes  SegmentType = 1<<4 | 1<<5 | 1<<6 | 1<<7               //111100 (240)
	SegmentType900000Bytes  SegmentType = 1<<2 | 1<<4 | 1<<5 | 1<<6 | 1<<7        //111101 (244)
	SegmentType1000000Bytes SegmentType = 1<<3 | 1<<4 | 1<<5 | 1<<6 | 1<<7        //111110 (248)
	SegmentType1500000Bytes SegmentType = 1<<2 | 1<<3 | 1<<4 | 1<<5 | 1<<6 | 1<<7 //111111 (252)

	Segment0 Segment = 0
	Segment1 Segment = 1 << 0
	Segment2 Segment = 1 << 1
	Segment3 Segment = 1<<0 | 1<<1
)

const (
	NotCompacted byte = 0

	Compacted byte = 1
)

func readByMemoryMappedIO(segmentHeader byte, segmentLength, offset int, bytes []byte, partition *Partition) ([]byte, error) {

	memoryMappedBytes := partition.segmentBuffers[segmentHeader]

	copy(bytes, memoryMappedBytes[offset:offset+segmentLength])

	valueBytes := getValueBytes(bytes)

	if valueBytes == nil {

		return nil, errors.New(utils.ErrorCorrupted)
	}

	return valueBytes, nil
}

func getValueBytes(bytes []byte) []byte {

	if !bytes2.EqualFold(bytes[:4], utils.CheckSumV1Bytes) {

		return nil
	}

	length := uint32(bytes[4]) |
		uint32(bytes[5])<<8 |
		uint32(bytes[6])<<16 |
		uint32(bytes[7])<<24

	return bytes[utils.MaxValueBytes : utils.MaxValueBytes+length]
}

// when data is moves to large segment type
func move(segmentHeader byte, offset int, bytes []byte, partition *Partition) (byte, int, error) {

	segmentType := calculateWritingSegmentType(len(bytes))

	segment := Segment0

	existingOffset := offset

	existingSegmentHeader := segmentHeader

	var err error

	if segment, offset, err = getWritableSegment(segmentType, partition, len(bytes)); err != nil {

		return 0, -1, errors.New(fmt.Sprintf("failed to find the writable segment file for segment type %v of the partition %v, reason:%v", segmentType, partition.name, err.Error()))
	}

	segmentHeader = byte(segmentType) | byte(segment)

	err = writeSegmentBuffer(offset, segmentType, segmentHeader, bytes, partition)

	if err != nil {

		return 0, -1, err
	}

	/*Scenario :- Compacted segment is moved from 10bytes to 20 bytes , and there already 10bytes segment exists , so we don't need to free the existing segment*/

	freeExistingSegmentBuffer(existingSegmentHeader, getSegmentType(existingSegmentHeader), existingOffset, partition)

	return segmentHeader, offset, nil
}

func write(bytes []byte, partition *Partition) (byte, int, error) {

	segmentType := calculateWritingSegmentType(len(bytes))

	segment := Segment0

	var err error

	offset := 0

	if segment, offset, err = getWritableSegment(segmentType, partition, len(bytes)); err != nil {

		return 0, -1, errors.New(fmt.Sprintf("failed to find the writable segment file for segment type %v of the partition %v, reason:%v", segmentType, partition.name, err.Error()))
	}

	segmentHeader := byte(segmentType) | byte(segment)

	return segmentHeader, offset, writeSegmentBuffer(offset, segmentType, segmentHeader, bytes, partition)
}

func update(segmentHeader byte, bytes []byte, offset int, partition *Partition) error {

	var segmentBuffer MMap

	var err error

	if _, ok := partition.segmentBuffers[segmentHeader]; !ok {

		if _, found := partition.segmentFiles[segmentHeader]; !found {

			return errors.New("segment file not found")
		}

		segmentBuffer, err = Map(partition.segmentFiles[segmentHeader], ReadWrite)

		if err != nil {

			return errors.New(fmt.Sprintf("failed to map the segment file %v of the partition %v, reason:%v", segmentHeader, partition.name, err.Error()))
		}

		partition.segmentBuffers[segmentHeader] = segmentBuffer

	} else {

		segmentBuffer = partition.segmentBuffers[segmentHeader]
	}

	copy(segmentBuffer[offset:], bytes)

	partition.segmentBuffers[segmentHeader] = segmentBuffer

	partition.segmentDirtyFlags[segmentHeader] = true

	return nil
}

func getWritableSegment(segmentType SegmentType, partition *Partition, length int) (Segment, int, error) {

	segment := Segment3

	found := false

	if segment, found = partition.writingSegments[segmentType]; !found {

		for segment = Segment3; true; segment-- {

			if _, ok := partition.segmentFiles[byte(segmentType)|byte(segment)]; ok {

				found = true

				break
			} else if segment == 0 {

				break
			}

		}

	}

	var err error

	offset := 0

	if !found {

		segment, err = createSegment(segmentType, partition)

		if err != nil {

			return 255, 0, err
		}

		return segment, 0, nil

	}

	segmentHeader := byte(segmentType) | byte(segment)

	if partition.segmentStats[segmentHeader] >= maxSegmentSizeBytes || partition.segmentStats[segmentHeader]+length >= maxSegmentSizeBytes {

		segment, offset = findWritableSegmentBuffer(segmentType, partition)

		if offset == 0 {

			if segment, err = createSegment(segmentType, partition); err != nil {

				return 0, -1, errors.New(fmt.Sprintf("failed to create the segment file for %v of the partition %v, reason:%v", segmentType, partition.name, err.Error()))
			}

		}

	} else {

		segment, offset = findWritableSegmentBuffer(segmentType, partition)

		if offset == -1 {

			return 0, -1, errors.New(fmt.Sprintf("failed to find writable segement buffer for %v of the partition %v", segmentType, partition.name))

		}

	}

	partition.writingSegments[segmentType] = segment

	return segment, offset, nil

}

func createSegment(segmentType SegmentType, partition *Partition) (Segment, error) {

	defer func() {

		if r := recover(); r != nil {

			stackTraceBytes := make([]byte, 1<<20)

			partitionLogger.Error(fmt.Sprintf("error %v occurred while creating segment for partition %v", r, partition.name))

			partitionLogger.Error(fmt.Sprintf("!!!STACK TRACE for creating segment !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

	}()

	segment := Segment0

	i := Segment0

	for i = Segment0; i < Segment3; i++ { //3(4) is the max segment we can have

		if _, ok := partition.segmentFiles[byte(segmentType)|byte(i)]; !ok {

			segment = i

			break
		}
	}

	if i > Segment3 {

		return 255, errors.New(fmt.Sprintf("no more segments available for segment type %v ", segmentType))
	}

	segmentFile, err := OpenFile(partition.path+utils.PathSeparator+segmentHeaderToString(byte(segmentType)|byte(segment))+".segment", utils.OpenAlways, utils.FileAttributeNormal|utils.FileFlagOverlapped)

	if err != nil {

		return 255, err
	}

	partition.writingSegments[segmentType] = segment

	segmentHeader := byte(segmentType) | byte(segment)

	partition.segmentMetadataBitmaps[segmentHeader] = bitmap.Bitmap{}

	partition.segmentFiles[segmentHeader] = segmentFile

	partition.segmentDirtyFlags[segmentHeader] = false

	_ = segmentFile.Truncate(int64(getSegmentBufferLength(segmentType) * 4))

	segmentBuffer, err := Map(segmentFile, ReadWrite)

	if err != nil {

		delete(partition.writingSegments, segmentType)

		delete(partition.segmentMetadataBitmaps, segmentHeader)

		delete(partition.segmentFiles, segmentHeader)

		delete(partition.segmentDirtyFlags, segmentHeader)

		if segmentBuffer != nil {

			_ = segmentBuffer.Unmap()
		}

		_ = segmentFile.Close()

		_ = os.Remove(segmentFile.Name())

		return 255, err
	}

	partition.segmentBuffers[segmentHeader] = segmentBuffer

	partition.segmentStats[segmentHeader] = getSegmentBufferLength(segmentType) * os.Getpagesize()

	return segment, nil

}

func deleteSegment(segmentHeader byte, partition *Partition) {

	segment := getSegment(segmentHeader)

	segmentType := getSegmentType(segmentHeader)

	delete(partition.segmentStats, segmentHeader)

	if writingSegment, ok := partition.writingSegments[segmentType]; ok {

		if writingSegment == segment {

			delete(partition.writingSegments, segmentType)
		}
	}

	if segmentMetadataBitmap, ok := partition.segmentMetadataBitmaps[segmentHeader]; ok {

		segmentMetadataBitmap.Clear()

		delete(partition.segmentMetadataBitmaps, segmentHeader)

	}

	if segmentBuffer, ok := partition.segmentBuffers[segmentHeader]; ok {

		err := segmentBuffer.Unmap()

		if err != nil {

			partitionLogger.Fatal(fmt.Sprintf("failed to unmap segment file %v-%v for partition %v, reason : %v", segmentType, segment, partition.path, err))
		}

		delete(partition.segmentBuffers, segmentHeader)
	}

	delete(partition.segmentDirtyFlags, segmentHeader)

	if segmentFile, ok := partition.segmentFiles[segmentHeader]; ok {

		_ = segmentFile.Close()

		_ = os.Remove(segmentFile.Name())

		delete(partition.segmentFiles, segmentHeader)

	}

}

func getSegmentBufferLength(segmentType SegmentType) int {

	switch segmentType {

	case SegmentType15Bytes:

		return 15

	case SegmentType20Bytes:

		return 20

	case SegmentType30Bytes:

		return 30

	case SegmentType40Bytes:

		return 40

	case SegmentType50Bytes:

		return 50

	case SegmentType60Bytes:

		return 60

	case SegmentType70Bytes:

		return 70

	case SegmentType80Bytes:

		return 80

	case SegmentType90Bytes:

		return 90

	case SegmentType100Bytes:

		return 100

	case SegmentType150Bytes:

		return 150

	case SegmentType200Bytes:

		return 200

	case SegmentType250Bytes:

		return 250

	case SegmentType300Bytes:

		return 300

	case SegmentType350Bytes:

		return 350

	case SegmentType400Bytes:

		return 400

	case SegmentType450Bytes:

		return 450

	case SegmentType500Bytes:

		return 500

	case SegmentType600Bytes:

		return 600

	case SegmentType700Bytes:

		return 700

	case SegmentType800Bytes:

		return 800

	case SegmentType900Bytes:

		return 900

	case SegmentType1000Bytes:

		return 1000

	case SegmentType1200Bytes:

		return 1200

	case SegmentType1500Bytes:

		return 1500

	case SegmentType1700Bytes:

		return 1700

	case SegmentType2000Bytes:

		return 2000

	case SegmentType2200Bytes:

		return 2200

	case SegmentType2400Bytes:

		return 2400

	case SegmentType2600Bytes:

		return 2600

	case SegmentType2800Bytes:

		return 2800

	case SegmentType3000Bytes:

		return 3000

	case SegmentType3200Bytes:

		return 3200

	case SegmentType3500Bytes:

		return 3500

	case SegmentType4000Bytes:

		return 4000

	case SegmentType4500Bytes:

		return 4500

	case SegmentType5000Bytes:

		return 5000

	case SegmentType5500Bytes:

		return 5500

	case SegmentType6000Bytes:

		return 6000

	case SegmentType7000Bytes:

		return 7000

	case SegmentType8000Bytes:

		return 8000

	case SegmentType10000Bytes:

		return 10000

	case SegmentType13000Bytes:

		return 13000

	case SegmentType15000Bytes:

		return 15000

	case SegmentType20000Bytes:

		return 20000

	case SegmentType25000Bytes:

		return 25000

	case SegmentType30000Bytes:

		return 30000

	case SegmentType40000Bytes:

		return 40000

	case SegmentType50000Bytes:

		return 50000

	case SegmentType60000Bytes:

		return 60000

	case SegmentType70000Bytes:

		return 70000

	case SegmentType80000Bytes:

		return 80000

	case SegmentType100000Bytes:

		return 100000

	case SegmentType160000Bytes:

		return 160000

	case SegmentType240000Bytes:

		return 240000

	case SegmentType320000Bytes:

		return 320000

	case SegmentType400000Bytes:

		return 400000

	case SegmentType480000Bytes:

		return 480000

	case SegmentType600000Bytes:

		return 600000

	case SegmentType700000Bytes:

		return 700000

	case SegmentType800000Bytes:

		return 800000

	case SegmentType900000Bytes:

		return 900000

	case SegmentType1000000Bytes:

		return 1000000

	case SegmentType1500000Bytes:

		return 1500000

	}

	return 15

}

func getSegmentBufferPosition(segmentType SegmentType, offset int) uint32 {

	return uint32(offset / getSegmentBufferLength(segmentType))
}

func calculateWritingSegmentType(length int) SegmentType {

	switch {

	case length <= 15:

		return SegmentType15Bytes

	case length <= 20:

		return SegmentType20Bytes

	case length <= 70:

		return SegmentType70Bytes

	case length <= 100:

		return SegmentType100Bytes

	case length <= 200:

		return SegmentType200Bytes

	case length <= 350:

		return SegmentType350Bytes

	case length <= 500:

		return SegmentType500Bytes

	case length <= 800:

		return SegmentType800Bytes

	case length <= 1500:

		return SegmentType1500Bytes

	case length <= 2400:

		return SegmentType2400Bytes

	case length <= 3000:

		return SegmentType3000Bytes

	case length <= 4000:

		return SegmentType4000Bytes

	case length <= 5500:

		return SegmentType5500Bytes

	case length <= 8000:

		return SegmentType8000Bytes

	case length <= 15000:

		return SegmentType15000Bytes

	case length <= 25000:

		return SegmentType25000Bytes

	case length <= 40000:

		return SegmentType40000Bytes

	case length <= 80000:

		return SegmentType80000Bytes

	case length <= 100000:

		return SegmentType100000Bytes

	case length <= 160000:

		return SegmentType160000Bytes

	case length <= 240000:

		return SegmentType240000Bytes

	case length <= 320000:

		return SegmentType320000Bytes

	case length <= 400000:

		return SegmentType400000Bytes

	case length <= 480000:

		return SegmentType480000Bytes

	case length <= 600000:

		return SegmentType600000Bytes

	case length <= 900000:

		return SegmentType900000Bytes

	case length <= 1500000:

		return SegmentType1500000Bytes
	}

	return SegmentType15Bytes
}

func getSegment(header byte) Segment {

	segment := Segment0

	if header == 0 {

		return segment
	}

	//first unset the 2-7 (writingSegments type bits)
	header &^= 1 << 2
	header &^= 1 << 3
	header &^= 1 << 4
	header &^= 1 << 5
	header &^= 1 << 6
	header &^= 1 << 7

	//now let's find out the 0-1 bits status

	segment = Segment(header&(0x80>>6) | header&(0x80>>7))

	return segment
}

func getSegmentType(header byte) SegmentType {

	segmentType := SegmentType15Bytes

	if header == 0 {

		return segmentType
	}

	//first unset the 0-1 (writingSegments bits)

	header &^= 1 << 0
	header &^= 1 << 1
	//now let's find out the 2-7 bits status

	segmentType = SegmentType(header&(0x80>>0) |
		header&(0x80>>1) |
		header&(0x80>>2) |
		header&(0x80>>3) |
		header&(0x80>>4) |
		header&(0x80>>5))

	return segmentType
}

func allocateNewSegmentBuffer(segmentHeader byte, segmentType SegmentType, offset int, partition *Partition) {

	segmentMetadataBitmap := partition.segmentMetadataBitmaps[segmentHeader]

	segmentMetadataBitmap.Set(getSegmentBufferPosition(segmentType, offset))

	partition.segmentMetadataBitmaps[segmentHeader] = segmentMetadataBitmap

}

func freeExistingSegmentBuffer(segmentHeader byte, segmentType SegmentType, offset int, partition *Partition) {

	partition.segmentDirtyFlags[segmentHeader] = true

	segmentMetadataBitmap := partition.segmentMetadataBitmaps[segmentHeader]

	segmentMetadataBitmap.Remove(getSegmentBufferPosition(segmentType, offset))

	partition.segmentMetadataBitmaps[segmentHeader] = segmentMetadataBitmap
}

func findWritableSegmentBuffer(segmentType SegmentType, partition *Partition) (Segment, int) {

	segment := Segment0

	offset := -1

	segmentBufferLength := getSegmentBufferLength(segmentType)

	for i := Segment0; i < Segment3; i++ { //3(4) is the max segment we can have

		if segmentMetadataBitmap, ok := partition.segmentMetadataBitmaps[byte(segmentType)|byte(i)]; ok {

			/*
				let's take
				maxSegmentSizeBytes :- 2gb
				segmentBufferLength :- 10 bytes (for 10 byte segment)

				for first value position would be 0/10 (offset being 0 for first value)
				bitmap will be set as 0000000001

				for second value we need the offset we can get that by looking at the bitmap
				position 0 is occupied hence we will start with position 1
				and for position 1 the offset would be 1*10

				for second value position would be 10/10 (offset calculated as 10)

				same for the rest values which would be 20/10, 30/10, 40/10....until

				the offset is reached at its peak which is maxSegmentSizeBytes, and we can calculate it by

				maxSegmentSizeBytes/10 (in this case 10 for SegmentType10Bytes)  we will get the number of max insertions
			*/

			if position, found := segmentMetadataBitmap.MinZero(); position < uint32(maxSegmentSizeBytes/segmentBufferLength) {

				if !found {

					offset = segmentBufferLength * segmentMetadataBitmap.Count()

				} else {

					offset = segmentBufferLength * int(position)
				}

				segment = i

				break
			}

		} else {

			partition.segmentMetadataBitmaps[byte(segmentType)|byte(i)] = bitmap.Bitmap{}

			segment = i

			offset = 0

			break
		}
	}

	return segment, offset
}

func writeSegmentBuffer(offset int, segmentType SegmentType, segmentHeader byte, bytes []byte, partition *Partition) error {

	var err error

	var segmentBuffer MMap

	if _, ok := partition.segmentBuffers[segmentHeader]; !ok {

		segmentBuffer, err = Map(partition.segmentFiles[segmentHeader], ReadWrite)

		if err != nil {

			return errors.New(fmt.Sprintf("failed to map the segment file %v of the partition %v, reason:%v", segmentHeader, partition.name, err.Error()))

		}

		_ = AdviceRandom(segmentBuffer)

		partition.segmentBuffers[segmentHeader] = segmentBuffer

	} else {

		segmentBuffer = partition.segmentBuffers[segmentHeader]
	}

	allocateNewSegmentBuffer(segmentHeader, segmentType, offset, partition)

	if offset+getSegmentBufferLength(segmentType) > len(segmentBuffer) {

		segmentBuffer, err = Remap(segmentBuffer, len(segmentBuffer)*4, partition.segmentFiles[segmentHeader])

		if err != nil {

			return errors.New(fmt.Sprintf("failed to extend the segment file %v of the partition %v, reason:%v", segmentHeader, err.Error(), partition.name))
		}

		partition.segmentBuffers[segmentHeader] = segmentBuffer

		partition.segmentStats[segmentHeader] = len(segmentBuffer)
	}

	copy(segmentBuffer[offset:], bytes)

	partition.segmentBuffers[segmentHeader] = segmentBuffer

	partition.segmentDirtyFlags[segmentHeader] = true

	return nil
}

func segmentHeaderToString(segmentHeader byte) string {

	return codec.INTToStringValue(int(getSegmentType(segmentHeader))) + utils.HyphenSeparator + codec.INTToStringValue(int(getSegment(segmentHeader)))
}
