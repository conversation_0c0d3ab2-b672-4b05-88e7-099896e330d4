/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-01             Vedant Dokania       	MOTADATA-5194  Handle returning nil partition in case of error
* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 introduced put multiples method which loops over put method
												introduced the lock variable as boolean type to control locking over the looping
* 2025-05-08  			 A<PERSON>l Shah			MOTADATA-6073 Implemented Async Flush for Dirty Segments
* 2025-06-23             <PERSON><PERSON><PERSON>            MOTADATA-6642  Added comments and memory aligned the struct
* 2025-06-23             <PERSON><PERSON><PERSON>ra            MOTADATA-6642  Coonfigurable Cache stores feature added for memory optimisation
* 2025-06-04             Aashil Shah            MOTADATA-5780 Handled File Open/Close logic for fst manually instead of vellum.
												Opened Segment File and Segment255 using FileFlagOverlapped to support IOCP.
												Closed Segment255 file while compacting.
*/

/*
	Partition provides high-performance key-value storage with advanced features for data management.

	The partition is the fundamental storage unit that handles:
	1. Key-value data storage using optimized segment files
	2. Fast key lookups using FST (Finite State Transducer) indexing
	3. ACID transactions with Write-Ahead Logging (WAL)
	4. Data compaction for space optimization and performance
	5. Memory-mapped I/O for efficient data access
	6. Concurrent access with fine-grained locking

	Storage Architecture:

		Index Management:
		- Uses FST (vellum.FST) for efficient string-to-uint64 key mapping
		- Stores keys in FST with values pointing to segment file positions
		- Maintains in-memory indices for frequently accessed data
		- Supports cache indices for high-performance lookups

		Segment Storage:
		- Values are stored in segment files based on their size
		- Multiple segment types optimize storage for different value sizes
		- Each segment type has dedicated files for better I/O patterns
		- Memory-mapped segment buffers provide fast access

		Transaction Support:
		- ACID transactions ensure data consistency
		- Write-Ahead Logging (WAL) provides durability and recovery
		- Transaction entries are buffered before commit
		- Rollback support through WAL file deletion

		Compaction Process:
		- Removes deleted entries to reclaim space
		- Reorganizes data for optimal access patterns
		- Updates indices to point to compacted segments
		- Maintains data integrity during compaction

		Concurrency Control:
		- Read-write locks for partition-level access control
		- Transaction locks for WAL operations
		- Fine-grained locking minimizes contention
		- Lock-free operations where possible

	Performance Optimizations:

		Memory Management:
		- Memory-mapped files for efficient I/O
		- Pre-allocated buffers reduce allocation overhead
		- Segment-specific buffer pools optimize memory usage

		I/O Optimization:
		- Asynchronous I/O using io_uring where available
		- Batch operations reduce system call overhead
		- Sequential writes optimize disk performance

		Caching Strategy:
		- In-memory indices for hot data
		- Cache indices for frequently accessed keys
		- Metadata caching reduces file system calls

*/

package storage

import (
	"archive/zip"
	bytes2 "bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/blevesearch/vellum"
	"github.com/dolthub/swiss"
	"github.com/kelindar/bitmap"
	"io"
	"math"
	"motadatadatastore/codec"
	. "motadatadatastore/utils"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

var (
	// partitionLogger provides logging functionality for partition operations
	// Used throughout the partition to record events, errors, and performance metrics
	partitionLogger = NewLogger("Partition", "storage")
)

// txnWALFile defines the filename for transaction Write-Ahead Log files
// This file stores uncommitted transaction data for durability and recovery
const txnWALFile = "hsvd.wal" //BC you guys abused me now asking for credit :)

const (
	// File and directory names for partition storage components

	// Segment255 is the filename for the compacted segment file
	// This file contains all compacted data after the compaction process
	Segment255 = "255"

	// SegmentMeta is the filename for segment metadata information
	// Contains metadata about segment files and their properties
	SegmentMeta = "segment.meta"

	// IdxFile is the main index file containing the FST structure
	// This file provides fast key-to-offset mapping for data retrieval
	IdxFile = "datastore.idx"

	// MergedIdxFile is the temporary index file created during merging operations
	// Used during index rebuilding and compaction processes
	MergedIdxFile = "merged.idx"

	// BlobFile stores large data objects that exceed normal segment sizes
	// Used for values that are too large for regular segment storage
	BlobFile = "blob.dat"

	// Temp255 is the temporary file used during compaction operations
	// Ensures atomic updates to the compacted segment file
	Temp255 = "255.tmp"

	// Idx255 is the temporary index file used during compaction
	// Contains the index for the compacted segment file
	Idx255 = "255.idx"

	// TempIdx is the temporary index file for in-memory index operations
	// Used to persist in-memory indices before merging with main index
	TempIdx = "temp.idx"

	// Metric names for monitoring and performance tracking

	// DataSizeBytes tracks the total size of data stored in the partition
	DataSizeBytes = "Data Size (bytes)"

	// ReadLatencyNanos measures the latency of read operations in nanoseconds
	ReadLatencyNanos = "Read Latency (ns)"

	// CacheStringMappings tracks the number of string mapping cache entries
	CacheStringMappings = "String Mapping Cache Entries"

	// CacheNumericMappings tracks the number of numeric mapping cache entries
	CacheNumericMappings = "Numeric Mapping Cache Entries"

	// MappingCacheMaxRecords tracks the maximum number of mapping cache records
	MappingCacheMaxRecords = "Mapping Cache Max Records"

	// IndexEntries tracks the total number of entries in the main index
	IndexEntries = "Index Entries"

	// IndexSizeBytes tracks the total size of index files in bytes
	IndexSizeBytes = "Index Size (bytes)"

	// MemoryIndexEntries tracks the number of entries in memory indices
	MemoryIndexEntries = "Memory Index Entries"

	// Moves tracks the number of data move operations during compaction
	Moves = "Moves"

	// PutLatencyNanos measures the latency of put operations in nanoseconds
	PutLatencyNanos = "Put Latency (ns)"

	// Puts tracks the total number of put operations performed
	Puts = "Puts"

	// Reads tracks the total number of read operations performed
	Reads = "Reads"

	// SegmentFiles tracks the number of active segment files
	SegmentFiles = "Segment Files"

	// Updates tracks the number of update operations performed
	Updates = "Updates"

	// Writes tracks the number of write operations performed
	Writes = "Writes"

	// DummyKey is used for FST validation and testing purposes
	// Used to verify FST integrity during partition initialization
	DummyKey = "dummy.key"
)

// txnEntry represents a single transaction entry in the Write-Ahead Log.
//
// This struct stores the essential information needed to locate and manage
// transaction data within the WAL file. It provides the offset and length
// for data retrieval and includes the transaction ID for tracking.
//
// Memory layout is optimized for 64-bit systems:
// - 4-byte aligned fields (int) are placed first
// - Smaller types (uint32) follow to minimize padding
type txnEntry struct {
	// 4-byte aligned fields (int on 64-bit systems is typically 8 bytes, but we group them)
	offset int // Byte offset of the transaction data within the WAL file
	length int // Length of the transaction data in bytes

	// 4-byte aligned fields
	txnId uint32 // Unique transaction identifier for tracking and recovery
}

// Partition represents a high-performance storage partition with advanced indexing and transaction support.
//
// This struct is the core of the storage engine, providing:
// 1. Fast key-value storage with FST-based indexing
// 2. ACID transactions with Write-Ahead Logging
// 3. Memory-mapped I/O for optimal performance
// 4. Concurrent access with fine-grained locking
// 5. Data compaction and space optimization
// 6. Multiple segment types for different value sizes
//
// The partition manages multiple types of storage:
// - FST index for fast key lookups
// - Segment files for value storage based on size
// - WAL files for transaction durability
// - Blob files for large objects
// - Memory-mapped buffers for efficient access
//
// Concurrency is handled through:
// - Read-write locks for general partition access
// - Transaction locks for WAL operations
// - Atomic operations for counters and timestamps
//
// Memory layout is optimized for 64-bit systems:
// - Pointers and maps (8 bytes each) are placed first
// - Atomic types and 8-byte values follow
// - Strings and 4-byte values come next
// - Smaller types (int32, bool) are placed last to minimize padding
type Partition struct {
	// 8-byte aligned fields (pointers, maps, slices, atomic types)
	lock          *sync.RWMutex              // Main read-write lock for partition access control
	txnLock       *sync.RWMutex              // Transaction lock for WAL operations synchronization
	index         *vellum.FST                // Main FST index for fast key-to-offset mapping
	memoryIndices *swiss.Map[string, uint64] // In-memory index for frequently accessed keys
	cacheIndices  *swiss.Map[string, uint64] // Cache index for high-performance lookups

	indexFile                    *os.File
	tempIndexFile                *os.File // Temporary index file for atomic index updates
	blobFile                     *os.File // File handle for large blob data storage
	segment255                   *os.File // File handle for the compacted segment file
	walFile                      *os.File // File handle for the Write-Ahead Log file
	indexMMapBytes               MMap
	tempIndexMemoryMappedBytes   MMap                    // Memory-mapped bytes for temporary index operations
	walBytes                     MMap                    // Memory-mapped bytes for WAL file access
	segmentFiles                 map[byte]*os.File       // Map of segment headers to their file handles
	entries                      map[uint64]txnEntry     // Map of transaction hashes to their WAL entries
	writingSegments              map[SegmentType]Segment // Map of segment types to their current writing segments
	segmentMetadataBitmaps       map[byte]bitmap.Bitmap  // Bitmaps tracking allocated space in each segment
	segmentBuffers               map[byte]MMap           // Memory-mapped buffers for each segment file
	segmentStats                 map[byte]int            // Statistics tracking size of each segment file
	segmentDirtyFlags            map[byte]bool           // Flags indicating which segments need synchronization
	lastWriteOpsTimestampSeconds atomic.Int64            // Atomic timestamp of the last write operation

	// 8-byte aligned fields (int64, float64)
	writes  int64   // Counter for total write operations performed
	moves   int64   // Counter for data move operations during compaction
	updates int64   // Counter for update operations performed
	variant float64 // Partition variant factor for performance tuning

	// String fields (variable size, but typically larger than basic types)
	name string // Unique name identifier for this partition
	path string // File system path to the partition directory

	// 4-byte aligned fields
	tempIndexSize   int   // Size of the temporary index file in bytes
	blobSize        int   // Current size of the blob file in bytes
	walFilePosition int   // Current write position in the WAL file
	walSizeBytes    int   // Total size of the WAL file in bytes
	walRecords      int32 // Number of records currently in the WAL file

	// 1-byte aligned fields (placed last to minimize struct padding)
	dirty  bool // Flag indicating if the partition has unsaved changes
	closed bool // Flag indicating if the partition has been closed
}

// SetLastWriteTimestamp atomically updates the last write operation timestamp.
//
// This method is used to track when the partition was last modified, which is
// essential for:
// 1. Cleanup operations that target stale partitions
// 2. Performance monitoring and metrics collection
// 3. Cache invalidation strategies
// 4. Backup and synchronization processes
//
// The timestamp is stored atomically to ensure thread-safe access without
// requiring locks, making it suitable for high-frequency updates.
//
// Parameters:
//   - timestamp: Unix timestamp in seconds representing the last write time
func (partition *Partition) SetLastWriteTimestamp(timestamp int64) {

	// Atomically store the timestamp to ensure thread-safe access
	// This allows concurrent reads and writes without lock contention
	partition.lastWriteOpsTimestampSeconds.Store(timestamp)
}

/////////////////////////////////////////// Init Code //////////////////////////////////////////////

// newPartition creates and initializes a new Partition instance with comprehensive setup.
//
// This constructor performs extensive initialization including:
// 1. Directory structure creation and validation
// 2. Index file loading and corruption detection
// 3. Segment file discovery and validation
// 4. WAL recovery and transaction replay
// 5. Cache initialization for performance optimization
// 6. Metadata loading and repair operations
//
// The initialization process is designed to be robust and handle various
// failure scenarios including corrupted files, incomplete operations,
// and system crashes during previous runs.
//
// Parameters:
//   - store: Parent store containing configuration and metadata
//   - partitionId: Unique identifier for this partition within the store
//   - encoder: Encoder instance for data serialization operations
//   - tokenizer: Tokenizer for string processing operations
//
// Returns:
//   - partition: Fully initialized partition ready for operations
//   - error: Any error encountered during initialization
//
// Error handling:
// - Recovers from panics and converts them to errors
// - Logs detailed error information including stack traces
// - Ensures partial initialization doesn't leave inconsistent state
func newPartition(store *Store, partitionId int, encoder codec.Encoder, tokenizer *Tokenizer) (partition *Partition, err error) {

	// Set up panic recovery for robust error handling
	// This prevents partition initialization failures from crashing the system
	defer func() {

		if r := recover(); r != nil {

			// Allocate buffer for stack trace capture (1MB should be sufficient)
			stackTraceBytes := make([]byte, 1<<20)

			// Convert panic to initialization error with context
			err = errors.New(fmt.Sprintf("failed to open partition %v for store %v, reason: %v", partitionId, store.name, r))

			// Log the initialization error for debugging and monitoring
			partitionLogger.Error(fmt.Sprintf("%v", err.Error()))

			// Log detailed stack trace for debugging the panic cause
			partitionLogger.Error(fmt.Sprintf("!!!STACK TRACE for new partition !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

	}()

	// Get store name for directory structure creation
	storeName := store.GetName()

	// Construct partition directory path following the standard hierarchy
	// Structure: CurrentDir/DatastoreDir/StoreName/PartitionId
	partitionDir := CurrentDir + PathSeparator + DatastoreDir + PathSeparator + storeName + PathSeparator + codec.INTToStringValue(partitionId)

	// Check if partition directory exists
	_, err = os.Stat(partitionDir)

	// Create partition directory if it doesn't exist
	if os.IsNotExist(err) {

		// Create directory with read/write/execute permissions for owner
		// and read/execute permissions for group and others (0755)
		err = os.MkdirAll(partitionDir, 0755)

		if err != nil {

			// Return immediately on directory creation failure
			return nil, err
		}

	}

	// Initialize new partition instance with zero values
	partition = &Partition{}

	// Set initial timestamp to trigger cleanup threshold
	// Subtracting CleanupThresholdSeconds ensures the partition is not immediately cleaned up
	partition.SetLastWriteTimestamp(time.Now().Unix() - CleanupThresholdSeconds)

	// Set partition name using store name and partition ID
	// Format: StoreName-PartitionId
	partition.name = storeName + HyphenSeparator + codec.INTToStringValue(partitionId)

	// Store the partition directory path for file operations
	partition.path = partitionDir

	// Configure WAL file size based on store type
	// Different store types may have different WAL size requirements
	partition.walSizeBytes = GetStoreWALSizeBytes(store.storeType)

	// Initialize all map fields to prevent nil pointer access
	// These maps will be populated as segment files are discovered and created

	// Map of segment headers to their file handles
	partition.segmentFiles = map[byte]*os.File{}

	// Map of segment headers to their memory-mapped buffers
	partition.segmentBuffers = map[byte]MMap{}

	// Map of segment headers to their current file sizes
	partition.segmentStats = map[byte]int{}

	// Map of segment types to their current writing segments
	partition.writingSegments = map[SegmentType]Segment{}

	// Map of segment headers to their metadata bitmaps
	partition.segmentMetadataBitmaps = map[byte]bitmap.Bitmap{}

	// Map of segment headers to their dirty flags
	partition.segmentDirtyFlags = map[byte]bool{}

	// Initialize synchronization primitives
	partition.lock = &sync.RWMutex{}

	partition.txnLock = &sync.RWMutex{}

	// Copy variant factor from store configuration
	// This affects performance tuning and behavior
	partition.variant = store.variant

	// Initialize blob file for large object storage
	// Blob files store data that exceeds normal segment size limits
	partition.blobFile, err = os.OpenFile(partitionDir+PathSeparator+BlobFile, os.O_RDWR|os.O_APPEND, 0666)

	// Get current blob file size if file was opened successfully
	if err == nil && partition.blobFile != nil {

		// Retrieve file statistics to determine current size
		if stats, _ := partition.blobFile.Stat(); stats != nil {

			// Store blob file size for space management
			partition.blobSize = int(stats.Size())
		}
	}

	// Handle index file merging and cleanup operations
	// This section manages various index file states that can occur after crashes or incomplete operations

	// Check if a merged index file exists from a previous merge operation
	if _, err = os.Stat(partition.path + PathSeparator + MergedIdxFile); err == nil {

		// If main index file doesn't exist, rename merged file to main file
		if _, err = os.Stat(partition.path + PathSeparator + IdxFile); errors.Is(err, os.ErrNotExist) {

			// Atomically replace main index with merged index
			err = os.Rename(partition.path+PathSeparator+MergedIdxFile,
				partition.path+PathSeparator+IdxFile)
		}
	}

	// Handle compaction index file cleanup and recovery
	// This manages the state after compaction operations
	if _, err = os.Stat(partition.path + PathSeparator + Idx255); err == nil {

		// Check if temporary compaction file exists
		if _, err = os.Stat(partition.path + PathSeparator + Temp255); err == nil {

			// Both files exist - compaction was interrupted, clean up temporary files
			_ = os.Remove(partition.path + PathSeparator + Temp255)

			_ = os.Remove(partition.path + PathSeparator + Idx255)

		} else {

			// Only compaction index exists - compaction completed, use it as main index
			partition.index = nil

			// Remove old main index file
			_ = os.Remove(partition.path + PathSeparator + IdxFile)

			// Replace main index with compaction index
			if err = os.Rename(partition.path+PathSeparator+Idx255, partition.path+PathSeparator+IdxFile); err != nil {

				// Return error if index replacement fails
				return nil, errors.New(fmt.Sprintf(ErrorCreatePartition, err))
			}

		}
	} else {

		// No compaction index exists, clean up any temporary files
		if _, err = os.Stat(partition.path + PathSeparator + Temp255); err == nil {

			// Remove orphaned temporary file
			_ = os.Remove(partition.path + PathSeparator + Temp255)
		}
	}

	// Load the main FST index file for fast key-to-offset mapping
	// The FST provides efficient string-to-uint64 mapping for data retrieval
	partition.indexFile, err = os.OpenFile(partitionDir+PathSeparator+IdxFile, os.O_RDWR, 0666)

	if err != nil {

		if !errors.Is(err, ErrorFileNotFound) {

			// Only return error for non-missing file errors
			// Missing index files are normal for new partitions
			return nil, errors.New(fmt.Sprintf(ErrorCreatePartition, err))

		}

	}

	if partition.indexFile != nil {

		partition.indexMMapBytes, err = Map(partition.indexFile, ReadWrite)

		if err != nil {

			_ = partition.indexFile.Close()

			return nil, errors.New(fmt.Sprintf(ErrorCreatePartition, err))
		}

		partition.index, err = vellum.Load(partition.indexMMapBytes)

		if err != nil {

			_ = partition.indexMMapBytes.Unmap()

			_ = partition.indexFile.Close()

			return nil, errors.New(fmt.Sprintf(ErrorCreatePartition, err))
		}
	}

	// Check for temporary index file that indicates incomplete index operations
	// This file contains in-memory index data that needs to be merged
	_, err = os.Stat(partitionDir + PathSeparator + TempIdx)

	if err == nil {

		// Rebuild index by merging temporary index data with main index
		// This recovers from incomplete index operations
		if err = rebuildIndex(partition, encoder); err != nil {

			// Return error if index rebuilding fails
			return nil, errors.New(fmt.Sprintf(ErrorCreatePartition, err))
		}
	}

	// Validate FST integrity by performing a test lookup
	// This detects corruption that might not be caught during loading
	// The "invalid address" error indicates FST corruption that requires rebuilding
	if partition.index != nil && partition.index.Len() > 0 {

		// Perform test lookup with dummy key to validate FST integrity
		if _, _, err := partition.index.Get([]byte(DummyKey)); err != nil {

			// Return error if FST validation fails
			// This indicates the index is corrupted and needs rebuilding
			return nil, err
		}
	}

	// Initialize cache indices for stores that benefit from caching
	// Cache stores are configured to keep frequently accessed data in memory
	if _, ok := CacheStores[store.storeType]; ok {

		// Create cache indices map with initial capacity of 100
		// Reduced from 10000 to optimize memory usage since most partitions
		// have fewer than 100 entries and the map can grow dynamically
		partition.cacheIndices = swiss.NewMap[string, uint64](100)

		// Populate cache with all existing index entries if index is available
		// This provides immediate cache warmup for better performance
		if partition.index != nil && partition.index.Len() > 0 {

			// Create iterator to traverse all entries in the FST index
			var iterator *vellum.FSTIterator

			iterator, err = partition.index.Iterator(nil, nil)

			// Iterate through all index entries and populate cache
			for err == nil {

				// Get current key-offset pair from iterator
				keyBytes, offset := iterator.Current()

				// Store key-offset mapping in cache for fast access
				// Convert key bytes to string for cache storage
				partition.cacheIndices.Put(string(keyBytes), offset)

				// Move to next entry in the index
				err = iterator.Next()
			}
		}
	}

	// Discover and initialize existing segment files in the partition directory
	// This process identifies all segment files and sets up their metadata
	files, err := os.ReadDir(partitionDir)

	if err == nil {

		// Iterate through all files in the partition directory
		for _, file := range files {

			// Only process regular files, skip directories
			if !file.IsDir() {

				// Process segment files with .segment extension
				// These files store actual key-value data organized by size
				if strings.HasSuffix(file.Name(), ".segment") {

					// Open segment file for read/write operations with append mode
					// Append mode ensures new data is written at the end
					if segmentFile, err := OpenFile(partitionDir+PathSeparator+file.Name(), OpenExisting, FileAttributeNormal|FileFlagOverlapped); err == nil {

						// Parse segment filename to extract segment type and number
						// Format: "segmentType-segmentNumber.segment"
						Split(file.Name(), ".", tokenizer)

						Split(tokenizer.Tokens[0], HyphenSeparator, tokenizer)

						// Extract segment type from filename (determines value size category)
						segmentType := SegmentType(codec.ToINT(tokenizer.Tokens[0]))

						// Extract segment number from filename (multiple segments per type)
						segment := Segment(codec.ToINT(tokenizer.Tokens[1]))

						// Create segment header by combining type and segment number
						// This header is used as a unique identifier for the segment
						segmentHeader := byte(segmentType) | byte(segment)

						// Register segment file handle for future operations
						partition.segmentFiles[segmentHeader] = segmentFile

						// Initialize dirty flag to false (file is clean on startup)
						partition.segmentDirtyFlags[segmentHeader] = false

						// Get current file size for space management
						stats, _ := segmentFile.Stat()

						// Store segment size for capacity planning and statistics
						partition.segmentStats[segmentHeader] = int(stats.Size())
					}
				}

				// Process the special compacted segment file (255)
				// This file contains all compacted data after compaction operations
				if strings.EqualFold(file.Name(), Segment255) {

					// Open compacted segment file for read/write operations
					if segment255, err := OpenFile(partitionDir+PathSeparator+file.Name(), OpenExisting, FileAttributeNormal|FileFlagOverlapped); err == nil {

						// Store handle to compacted segment file
						partition.segment255 = segment255
					}

				}

			}

		}
	}

	// Determine if metadata repair is needed for metric aggregation stores
	// Repair is required if the store hasn't been marked as repaired
	repair := store.GetDatastoreType() == MetricAggregation && store.metadata.GetStringValue(MultipartRepaired) != Yes

	// Load segment metadata only if segments exist or repair is needed
	// After compaction, metadata loading is not necessary as all data is in segment255
	if len(partition.segmentFiles) > 0 || repair {

		// Load metadata for all discovered segments
		// This initializes bitmaps and other metadata structures
		partition.loadMetadata(encoder, tokenizer, store, repair, len(partition.segmentFiles) > 0)

	}

	// Clean up unused segment files that may exist after abnormal shutdown
	// This handles cases where segment files exist but are not referenced in the index
	if partition.segment255 != nil && len(partition.segmentFiles) > 0 {

		// Get all keys from the index to determine which segments are actually used
		keys := doListing(partition, nil, nil, false, codec.Invalid)

		// Acquire buffer for offset processing
		poolIndex, offsetBytes := encoder.MemoryPool.AcquireBytePool(8)

		defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

		// Track which segment headers are actually referenced in the index
		headers := map[byte]struct{}{}

		// Iterate through all offsets in the index
		for offset := range keys {

			// Convert offset to bytes for analysis
			codec.WriteINT64Value(int64(offset), 0, offsetBytes)

			// Check if this is a non-compacted entry (references a segment file)
			if offsetBytes[0] == NotCompacted {

				// Extract segment header from offset and mark as used
				headers[offsetBytes[1]] = struct{}{}

			}

		}

		// Remove segment files that are not referenced in the index
		for header := range partition.segmentFiles {

			// If segment header is not found in used headers, remove the file
			if _, ok := headers[header]; !ok {

				// Close the segment file handle
				_ = partition.segmentFiles[header].Close()

				// Clean up all associated metadata for this segment
				delete(partition.segmentMetadataBitmaps, header)

				delete(partition.segmentDirtyFlags, header)

				delete(partition.segmentStats, header)

				// Remove the physical segment file from disk
				_ = os.Remove(partition.path + PathSeparator + partition.segmentFiles[header].Name())

				// Parse filename for cleanup (though not used in this context)
				Split(partition.segmentFiles[header].Name(), DotSeparator, tokenizer)

				// Remove segment from the files map
				delete(partition.segmentFiles, header)

			}
		}
	}

	// Perform WAL recovery to restore uncommitted transactions
	// This ensures data durability by replaying transactions from the WAL file
	bytes, err := os.ReadFile(partition.path + PathSeparator + txnWALFile)

	// Log WAL read errors (except for missing file which is normal)
	if err != nil && !os.IsNotExist(err) {

		partitionLogger.Error(fmt.Sprintf(ErrorSyncWAL, txnWALFile, partition.name, err.Error()))
	}

	// Process WAL file if it contains data
	if len(bytes) > 0 {

		// Read the number of transaction records from the WAL header
		records := codec.ReadINT32Value(bytes)

		// Replay transactions if records exist
		if records > 0 {

			// Synchronize WAL data to persistent storage
			// This commits all transactions that were in the WAL
			if err = partition.syncWAL(encoder, bytes, records); err != nil {

				// Log WAL synchronization errors but continue initialization
				partitionLogger.Error(fmt.Sprintf(ErrorSyncWAL, txnWALFile, partition.name, err.Error()))

			}
		}

		// Remove WAL file after successful recovery
		// This prevents duplicate replay on subsequent startups
		err = os.RemoveAll(partition.path + PathSeparator + txnWALFile)

		if err != nil {

			partitionLogger.Error(fmt.Sprintf("failed to remove WAL file for partition %v, reason: %v", partition.name, err.Error()))
		}

	}

	// Return fully initialized partition
	return partition, nil

}

/////////////////////////////////////////// Transaction Put Code //////////////////////////////////////////////

// commitTxn commits a transaction by writing it to the Write-Ahead Log (WAL).
//
// This method implements ACID transaction support by:
// 1. Acquiring exclusive transaction lock for consistency
// 2. Initializing WAL file if not already created
// 3. Managing WAL file size and remapping when needed
// 4. Writing transaction data to WAL for durability
// 5. Updating transaction entry mappings for recovery
//
// The WAL ensures that all transaction data is persisted before the transaction
// is considered committed, providing durability guarantees even in case of
// system crashes.
//
// Parameters:
//   - bytes: Serialized transaction data to write to WAL
//   - entries: Map of transaction entry hashes to their metadata
//   - encoder: Encoder instance for data serialization operations
//
// Returns:
//   - error: Any error encountered during transaction commit
//
// Error handling:
// - Cleans up partially created WAL files on initialization failure
// - Handles WAL file size expansion through remapping
// - Ensures atomic transaction commit or complete rollback
func (partition *Partition) commitTxn(bytes []byte, entries map[uint64]TxnEntry, encoder codec.Encoder) (err error) {

	// Acquire exclusive transaction lock to ensure WAL consistency
	// This prevents concurrent transaction commits from corrupting the WAL
	partition.txnLock.Lock()

	defer partition.txnLock.Unlock()

	// Initialize WAL file if this is the first transaction
	if partition.walFile == nil {

		// Initialize transaction entries map with reasonable capacity
		partition.entries = make(map[uint64]txnEntry, 50)

		// Create WAL file with truncation to ensure clean state
		if partition.walFile, err = os.OpenFile(partition.path+PathSeparator+txnWALFile, os.O_CREATE|os.O_TRUNC|os.O_RDWR, 0666); err != nil {

			return errors.New(fmt.Sprintf("failed to create WAL file for partition %v, reason: %v", partition.name, err.Error()))
		}

		// Pre-allocate WAL file to configured size for performance
		if err = partition.walFile.Truncate(int64(partition.walSizeBytes)); err != nil {

			// Clean up partially created file on truncation failure
			_ = partition.walFile.Close()

			_ = os.Remove(partition.walFile.Name())

			return errors.New(fmt.Sprintf("failed to truncate WAL file for partition %v, reason: %v", partition.name, err.Error()))
		}

		// Memory-map WAL file for efficient read/write operations
		if partition.walBytes, err = Map(partition.walFile, ReadWrite); err != nil {

			// Clean up partially created file on mapping failure
			_ = partition.walFile.Close()

			_ = os.Remove(partition.walFile.Name())

			return errors.New(fmt.Sprintf("failed to memory map WAL file for partition %v, reason: %v", partition.name, err.Error()))
		}

		// Initialize WAL header with zero record count
		codec.WriteINT32Value(0, 0, partition.walBytes)

		// Initialize WAL state variables
		partition.walRecords = 0

		// Skip past the record count header (4 bytes)
		partition.walFilePosition += 4
	}

	// Check if WAL file has sufficient space for the new transaction
	// If not, sync existing data and potentially expand the WAL file
	if partition.walFilePosition+len(bytes) > partition.walSizeBytes {

		// Temporarily release lock to perform sync operation
		// Sync operations may take time and shouldn't block other operations
		partition.txnLock.Unlock()

		// Log WAL sync operation when debug is enabled
		if DebugEnabled() {

			partitionLogger.Debug(fmt.Sprintf("partition %v wal file sync, reason: previous size %v, current size %v and wal file size %v", partition.name, partition.walFilePosition, len(bytes), partition.walSizeBytes))
		}

		// Sync WAL data to persistent storage to free up space
		err = partition.sync(encoder, true)

		// Re-acquire lock after sync operation
		partition.txnLock.Lock()

		if err != nil {

			return err
		}

		// Check if space is still insufficient after sync
		// If so, expand the WAL file by remapping
		if partition.walFilePosition+len(bytes) > partition.walSizeBytes {

			// Calculate new size: current size + double the transaction size
			// This provides buffer for future transactions
			size := len(partition.walBytes) + (len(bytes) * 2)

			partitionLogger.Info(fmt.Sprintf("remapping txn wal file buffer for partition %v from size %v to size: %v", partition.name, partition.walSizeBytes, size))

			// Remap WAL file to larger size
			if partition.walBytes, err = Remap(partition.walBytes, size, partition.walFile); err != nil {

				return errors.New(fmt.Sprintf("failed to remap the txn wal file for partition %v, reason: %v", partition.name, err))
			}

			// Update WAL size tracking
			partition.walSizeBytes = len(partition.walBytes)
		}
	}

	// Check if partition has been closed during the operation
	if partition.closed {

		return errors.New(fmt.Sprintf(ErrorPartitionDeleted, partition.name))
	}

	// Write transaction data to WAL at current position
	copy(partition.walBytes[partition.walFilePosition:], bytes)

	// Increment record count for this transaction
	partition.walRecords++

	// Update WAL header with new record count
	codec.WriteINT32Value(partition.walRecords, 0, partition.walBytes)

	// Update transaction entry mappings for recovery
	// Each entry maps transaction hash to its location in WAL
	for hash, entry := range entries {

		partition.entries[hash] = txnEntry{offset: partition.walFilePosition + entry.Offset, length: entry.Length}
	}

	// Advance WAL file position past the written data
	partition.walFilePosition += len(bytes)

	// Update last write timestamp for cleanup tracking
	partition.SetLastWriteTimestamp(time.Now().Unix())

	// Mark partition as dirty to ensure sync on close
	partition.dirty = true

	return nil

}

/////////////////////////////////////////// Cleanup Code //////////////////////////////////////////////

// delete marks a key as deleted without physically removing it from the FST.
//
// This method implements logical deletion by:
// 1. Acquiring exclusive partition lock for consistency
// 2. Searching for the key in the index
// 3. Freeing the associated segment buffer space
// 4. Marking the key as deleted using MaxUint64 value
// 5. Writing the deletion marker to the temporary index
//
// Logical deletion is used instead of physical removal because:
// - FST structures are immutable and cannot be modified in-place
// - Physical deletion would require rebuilding the entire index
// - Logical deletion allows for efficient batch cleanup during compaction
// - Deleted entries are filtered out during reads
//
// The deletion marker (MaxUint64) is recognized throughout the system
// as indicating a deleted, blacklisted, or corrupted entry.
//
// Parameters:
//   - keyBytes: The key to mark as deleted
//   - encoder: Encoder instance for data serialization operations
//
// Returns:
//   - error: Any error encountered during the deletion process
//
// Note: This function is typically called when data corruption is detected
// or when explicit key deletion is requested.
func (partition *Partition) delete(keyBytes []byte, encoder codec.Encoder) error {

	// Acquire exclusive partition lock for consistency
	// This ensures no concurrent operations interfere with deletion
	partition.lock.Lock()

	defer partition.lock.Unlock()

	// Check if partition has been closed
	if partition.closed {

		return errors.New(fmt.Sprintf(ErrorPartitionDeleted, partition.name))
	}

	// Mark partition as dirty to ensure changes are persisted
	partition.dirty = true

	// Search for the key in the index to get its current offset
	offset, found, _ := searchIndex(keyBytes, partition)

	// If key doesn't exist, deletion is already complete
	if !found {

		return nil
	}

	// Convert offset to bytes for segment analysis
	index, offsetBytes := encoder.WriteINT64Value(int64(offset), 0)

	// Free the segment buffer space occupied by this key's value
	// This reclaims space in the segment metadata bitmap
	freeExistingSegmentBuffer(offsetBytes[1], getSegmentType(offsetBytes[1]), codec.ReadINTValue(offsetBytes[2:]), partition)

	// Release the temporary buffer used for offset conversion
	encoder.MemoryPool.ReleaseBytePool(index)

	// Mark the key as deleted by writing MaxUint64 to the temporary index
	// This value is recognized throughout the system as a deletion marker
	_ = writeTempIndex(keyBytes, math.MaxUint64, partition, encoder)

	// Log the deletion event for monitoring and debugging
	partitionLogger.Warn(fmt.Sprintf("key %v of partition %v deleted, reason: %v", string(keyBytes), partition.name, ErrorCorrupted))

	return nil
}

// unmapActiveSegments unmaps all memory-mapped segment buffers and cleans up resources.
//
// This method is used for memory management and resource cleanup by:
// 1. Acquiring exclusive partition lock for consistency
// 2. Flushing all dirty segment buffers to disk
// 3. Unmapping memory-mapped segment files
// 4. Cleaning up associated metadata and writing segments
// 5. Resetting dirty flags for all segments
//
// This operation is typically performed during:
// - Partition shutdown or cleanup
// - Memory pressure situations
// - Before major operations like compaction
//
// The method ensures that all pending writes are flushed before unmapping
// to prevent data loss.
//
// Returns:
//   - bool: true if any segments were unmapped, false if no segments were active
//
// Error handling:
// - Logs fatal errors for unmapping failures as they indicate serious system issues
// - Continues cleanup even if individual segments fail to unmap
// - Ensures partial cleanup doesn't leave inconsistent state
func (partition *Partition) unmapActiveSegments() bool {

	// Acquire exclusive partition lock for consistency
	// This ensures no concurrent operations access segments during unmapping
	partition.lock.Lock()

	defer partition.lock.Unlock()

	// Return false if partition is already closed
	if partition.closed {

		return false
	}

	// Count segments to determine if any work was done
	count := len(partition.segmentBuffers)

	// Iterate through all active segment buffers
	for segmentHeader, buffer := range partition.segmentBuffers {

		// Remove segment buffer from active map
		delete(partition.segmentBuffers, segmentHeader)

		// Reset dirty flag since we're flushing and unmapping
		partition.segmentDirtyFlags[segmentHeader] = false

		// Flush any pending writes to disk before unmapping
		// This ensures data consistency
		_ = buffer.Flush()

		// Unmap the memory-mapped segment file
		err := buffer.Unmap()

		if err != nil {

			// Log fatal error for unmapping failures
			// This indicates serious system issues that need immediate attention
			partitionLogger.Fatal(fmt.Sprintf("failed to unmap segment memory mapped file for partition %v, reason : %v", partition.path, err))
		}

		// Clean up writing segment reference if it exists
		// This prevents references to unmapped segments
		if _, found := partition.writingSegments[getSegmentType(segmentHeader)]; found {

			delete(partition.writingSegments, getSegmentType(segmentHeader))
		}
	}

	// Return true if any segments were processed
	return count > 0
}

// close performs comprehensive cleanup and resource deallocation for the partition.
//
// This method ensures proper shutdown by:
// 1. Acquiring exclusive partition lock for consistency
// 2. Synchronizing any dirty data to persistent storage
// 3. Cleaning up WAL files and memory mappings
// 4. Closing FST index and clearing cache structures
// 5. Unmapping and closing all segment files
// 6. Releasing all file handles and memory resources
//
// The cleanup process is designed to be thorough and handle partial failures
// gracefully. Each resource type is cleaned up independently to ensure
// maximum cleanup even if some operations fail.
//
// This method should be called when:
// - The partition is being permanently closed
// - The store is shutting down
// - Resource cleanup is needed due to errors
//
// Parameters:
//   - encoder: Encoder instance for final synchronization operations
//
// Error handling:
// - Ignores individual cleanup errors to ensure complete cleanup
// - Continues cleanup even if some resources fail to close
// - Sets all references to nil to prevent accidental access
func (partition *Partition) close(encoder codec.Encoder) {

	// Acquire exclusive partition lock for consistency
	// This ensures no concurrent operations interfere with cleanup
	partition.lock.Lock()

	defer partition.lock.Unlock()

	// Synchronize any dirty data before closing
	// This ensures all changes are persisted
	if partition.dirty {

		_ = partition.sync(encoder, false)
	}

	// Clean up WAL file and memory mapping
	if partition.walBytes != nil {

		// Reset WAL record count
		partition.walRecords = 0

		// Unmap WAL memory-mapped file
		_ = partition.walBytes.unmap()

		partition.walBytes = nil

		// Close WAL file handle
		_ = partition.walFile.Close()

		// Remove WAL file from disk (it's been synced)
		_ = os.Remove(partition.walFile.Name())

		partition.walFile = nil
	}

	// Close FST index and release resources
	if partition.index != nil {

		if partition.indexMMapBytes != nil {

			_ = partition.indexMMapBytes.Unmap()

			partition.indexMMapBytes = nil
		}

		if partition.indexFile != nil {

			_ = partition.indexFile.Close()

			partition.indexFile = nil
		}

		_ = partition.index.Close()

		partition.index = nil
	}

	// Clear all index and cache structures
	partition.memoryIndices = nil

	partition.cacheIndices = nil

	partition.entries = nil

	partition.segmentDirtyFlags = nil

	partition.segmentMetadataBitmaps = nil

	// Clean up all segment memory mappings
	if partition.segmentBuffers != nil && len(partition.segmentBuffers) > 0 {

		for _, segmentBuffer := range partition.segmentBuffers {

			// Flush any pending writes
			_ = segmentBuffer.Flush()

			// Unmap memory-mapped segment
			_ = segmentBuffer.Unmap()

			// Clear reference
			segmentBuffer = nil

		}
	}

	partition.segmentBuffers = nil

	// Close all segment file handles
	if partition.segmentFiles != nil && len(partition.segmentFiles) > 0 {

		for _, file := range partition.segmentFiles {

			_ = file.Close()
		}

	}

	// Clear all segment-related structures
	partition.segmentFiles = nil

	partition.segmentStats = nil

	partition.writingSegments = nil

	// Close blob file if it exists
	if partition.blobFile != nil {

		_ = partition.blobFile.Close()

		partition.blobFile = nil
	}

	// Close compacted segment file if it exists
	if partition.segment255 != nil {

		_ = partition.segment255.Close()

		partition.segment255 = nil
	}

	if partition.tempIndexFile != nil {

		_ = partition.tempIndexMemoryMappedBytes.Unmap()

		_ = partition.tempIndexFile.Close()

		partition.tempIndexFile = nil
	}
}

// flush writes a key-value pair to the partition with intelligent segment management.
//
// This method implements the core write logic by:
// 1. Determining the optimal segment type based on value size
// 2. Checking if the key already exists and its current location
// 3. Deciding whether to update in-place, move to different segment, or write new
// 4. Managing segment metadata and index updates
// 5. Tracking operation statistics for monitoring
//
// Write strategies:
// - Update: If value fits in same segment type, update in-place
// - Move: If value requires different segment type, move to appropriate segment
// - Write: If key doesn't exist or data is compacted, write to new location
//
// The method optimizes for performance by:
// - Avoiding unnecessary index updates for same-segment updates
// - Using appropriate segment types for different value sizes
// - Handling compacted data specially
//
// Parameters:
//   - keyBytes: The key to write
//   - valueBytes: The value data to store
//   - encoder: Encoder instance for data serialization operations
//
// Returns:
//   - error: Any error encountered during the write operation
//
// Error handling:
// - Handles missing segment files by creating new segments
// - Manages segment type changes through move operations
// - Ensures index consistency even when segment operations fail
func (partition *Partition) flush(keyBytes, valueBytes []byte, encoder codec.Encoder) error {

	var err error

	// Acquire buffer for offset calculations
	index, bytes := encoder.MemoryPool.AcquireBytePool(8)

	defer encoder.MemoryPool.ReleaseBytePool(index)

	// Determine the optimal segment type based on value size
	// This ensures values are stored in appropriately sized segments
	currentSegmentType := calculateWritingSegmentType(len(valueBytes))

	// Initialize existing segment type to current (may change if key exists)
	exitingSegmentType := currentSegmentType

	// Variable to hold the segment header for the final storage location
	var segmentHeader byte

	// Search for existing key in the index
	offset, found, _ := searchIndex(keyBytes, partition)

	// Flag to track if existing data is in compacted segment
	compacted := false

	// If key exists, analyze its current storage location
	if found {

		// Convert offset to bytes for analysis
		codec.WriteINT64Value(int64(offset), 0, bytes)

		// Check if data is in compacted segment (segment 255)
		compacted = bytes[0] == Compacted
	}

	// Handle existing key that is not in compacted storage
	if found && !compacted {

		// Extract the segment type where data currently resides
		exitingSegmentType = getSegmentType(bytes[1])

		// Check if value can be updated in the same segment type
		if currentSegmentType == exitingSegmentType {

			// Perform in-place update - no index change needed
			// This is the most efficient operation
			err = update(bytes[1], valueBytes, codec.ReadINTValue(bytes[2:]), partition)

			// Handle case where segment file is missing (cleanup scenario)
			if err != nil && strings.EqualFold(err.Error(), "segment file not found") {

				// Write to new segment since original is missing
				segmentHeader, offset, err = write(valueBytes, partition)

				// Update offset for index
				codec.WriteINT64Value(int64(offset), 0, bytes)

				// Increment write counter
				partition.writes++

				if err != nil {

					return err
				}

				// Update index with new location
				err = writeTempIndex(keyBytes, codec.ReadUINT64Value(append([]byte{NotCompacted, segmentHeader}, bytes...)), partition, encoder)

				if err != nil {

					return err
				}

			} else if err == nil {

				// Successful in-place update
				partition.updates++

			}

		} else {

			// Value size changed - need to move to different segment type
			// This requires both index and data updates
			segmentHeader, offset, err = move(bytes[1], codec.ReadINTValue(bytes[2:]), valueBytes, partition)

			// Increment move counter for statistics
			partition.moves++

			if err != nil {

				return err
			}

			// Update offset for new location
			codec.WriteINT64Value(int64(offset), 0, bytes)

			// Update index with new segment and offset
			err = writeTempIndex(keyBytes, codec.ReadUINT64Value(append([]byte{NotCompacted, segmentHeader}, bytes...)), partition, encoder)

			if err != nil {

				return err
			}

		}

	} else {

		// New key or compacted data - write to new location
		segmentHeader, offset, err = write(valueBytes, partition)

		// Update offset for index
		codec.WriteINT64Value(int64(offset), 0, bytes)

		// Increment write counter
		partition.writes++

		if err != nil {

			return err
		}

		// Add new entry to index
		err = writeTempIndex(keyBytes, codec.ReadUINT64Value(append([]byte{NotCompacted, segmentHeader}, bytes...)), partition, encoder)

		if err != nil {

			return err
		}
	}

	return err
}

// syncWAL replays transactions from the Write-Ahead Log to ensure data durability.
//
// This method implements WAL recovery by:
// 1. Parsing WAL records from the provided byte array
// 2. Extracting individual transaction entries
// 3. Validating transaction format and integrity
// 4. Replaying each key-value operation through the flush method
// 5. Ensuring all transactions are committed to persistent storage
//
// WAL Format:
// - Header: 4 bytes for record count
// - Each record: 4 bytes length + transaction data
// - Transaction data: multiple key-value pairs + EOT marker
// - Key-value format: 4 bytes key length + key + 4 bytes value length + value
//
// This method is critical for crash recovery and ensures ACID properties
// by replaying all committed transactions that were in the WAL.
//
// Parameters:
//   - encoder: Encoder instance for data serialization operations
//   - bytes: WAL file contents as byte array
//   - records: Number of transaction records to process
//
// Returns:
//   - error: Any error encountered during WAL replay
//
// Error handling:
// - Recovers from panics and logs detailed stack traces
// - Validates WAL format integrity before processing
// - Stops processing on first transaction replay error
func (partition *Partition) syncWAL(encoder codec.Encoder, bytes []byte, records int32) (err error) {

	// Set up panic recovery for robust error handling
	// WAL replay is critical and failures need detailed logging
	defer func() {

		if r := recover(); r != nil {

			// Allocate buffer for stack trace capture (1MB should be sufficient)
			stackTraceBytes := make([]byte, 1<<20)

			// Convert panic to WAL sync error
			err = errors.New(fmt.Sprintf("err %v occurred while syncing wal file of  store %v", r, partition.name))

			// Log the error for monitoring and debugging
			partitionLogger.Error(err.Error())

			// Log detailed stack trace for debugging the panic cause
			partitionLogger.Error(fmt.Sprintf("!!!STACK TRACE for sync wal !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}

	}()

	// Start parsing after the 4-byte record count header
	offset := 4

	// Process each transaction record in the WAL
	for i := int32(0); i < records; i++ {

		// Read the length of this transaction record
		length := int(codec.ReadINT32Value(bytes[offset:]))

		offset += 4

		// Extract the transaction data bytes
		walBytes := bytes[offset : offset+length]

		offset += length

		// Check for End-of-Transaction (EOT) marker at the end
		// EOT marker is 3 bytes and indicates a valid transaction
		position := len(walBytes) - 3

		if position > 0 && bytes2.Equal(walBytes[position:position+3], EOTBytes) {

			// Parse individual key-value pairs within the transaction
			position = 0

			// Process all key-value pairs until we reach the EOT marker
			for position < len(walBytes)-3 {

				// Read key length
				length = int(codec.ReadINT32Value(walBytes[position : position+4]))

				position += 4

				// Extract key bytes
				keyBytes := walBytes[position : position+length]

				position += length

				// Read value length (includes 4-byte header)
				length = int(codec.ReadINT32Value(walBytes[position+4 : position+8]))

				// Extract value bytes including the length header
				valueBytes := walBytes[position : position+length+8]

				position += length + 8

				// Replay the key-value operation through normal write path
				// This ensures the data is properly written to segments and index
				err = partition.flush(keyBytes, valueBytes, encoder)

				if err != nil {

					// Stop processing on first error to maintain consistency
					return err
				}

			}

		} else {

			// Invalid WAL format - missing or corrupted EOT marker
			return errors.New(fmt.Sprintf("invalid WAL file of partition %v", partition.name))
		}

	}

	return err
}

// sync put the data form .wal files to segment and index and also in memory held keys
func (partition *Partition) sync(encoder codec.Encoder, lock bool) error {

	defer func() {

		if r := recover(); r != nil {

			stackTraceBytes := make([]byte, 1<<20)

			partitionLogger.Error(fmt.Sprintf("err %v occurred while syncing store %v", r, partition.name))

			partitionLogger.Error(fmt.Sprintf("!!!STACK TRACE for sync !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

	}()

	var err error

	if lock {

		partition.lock.Lock()

		defer partition.lock.Unlock()

		if partition.closed {

			return errors.New(fmt.Sprintf(ErrorPartitionDeleted, partition.name))
		}
	}

	timestamp := time.Now().UnixMilli()

	if partition.walRecords > 0 {

		partition.txnLock.Lock()

		defer partition.txnLock.Unlock()

		if partition.closed {

			return errors.New(fmt.Sprintf(ErrorPartitionDeleted, partition.name))
		}

		err = partition.syncWAL(encoder, partition.walBytes[:partition.walFilePosition], partition.walRecords)

		if err != nil {

			partitionLogger.Error(fmt.Sprintf(ErrorSyncWAL, txnWALFile, partition.name, err))
		}

		clear(partition.entries)

		codec.WriteINT32Value(partition.walRecords, 0, partition.walBytes)

		partition.walRecords = 0

		partition.walFilePosition = 4

	}

	if partition.blobFile != nil {

		err = partition.blobFile.Sync()

		if err != nil {

			partitionLogger.Error(fmt.Sprintf(ErrorBlobFlush, partition.name, err))
		}

	}

	flushSegmentMetadata := false

	for segmentHeader, dirty := range partition.segmentDirtyFlags {

		if dirty {

			if partition.segmentBuffers[segmentHeader] != nil {

				err = partition.segmentBuffers[segmentHeader].FlushAsync()

				if err != nil {

					partitionLogger.Warn(fmt.Sprintf("failed to sync %v segment file, reason: %v", segmentHeaderToString(segmentHeader), err))
				}
			}

			flushSegmentMetadata = true

			partition.segmentDirtyFlags[segmentHeader] = false

			if segmentMetadataBitmap := partition.segmentMetadataBitmaps[segmentHeader]; segmentMetadataBitmap != nil {

				if segmentMetadataBitmap.Count() == 0 {

					deleteSegment(segmentHeader, partition)

				}
			}

		}

	}

	mergeIndex(partition, encoder)

	if flushSegmentMetadata {

		partition.flushSegmentMetadata()
	}

	partition.dirty = false

	if DebugEnabled() {

		partitionLogger.Debug(fmt.Sprintf("partition %v sync took %v ms", partition.name, time.Now().UnixMilli()-timestamp))
	}

	return err
}

/////////////////////////////////////////// Normal PUT Code //////////////////////////////////////////////

// we write bytes in blob file and return starting position
func (partition *Partition) putBlob(bytes []byte) (int, error) {

	var err error

	offset := 0

	partition.lock.Lock()

	defer partition.lock.Unlock()

	if partition.closed {

		return -1, errors.New(fmt.Sprintf(ErrorPartitionDeleted, partition.name))
	}

	partition.dirty = true

	if partition.blobFile == nil {

		if partition.blobFile, err = os.OpenFile(partition.path+PathSeparator+BlobFile, os.O_CREATE|os.O_RDWR|os.O_APPEND, 0666); err != nil {

			partitionLogger.Error(fmt.Sprintf(ErrorWriteBlob, partition.name, err.Error()))

			return -1, errors.New(fmt.Sprintf(ErrorWriteBlob, partition.name, err.Error()))

		}

	}

	if err = writeBlob(partition.blobFile, bytes); err != nil {

		partitionLogger.Error(fmt.Sprintf(ErrorWriteBlob, partition.name, err.Error()))

		return -1, errors.New(fmt.Sprintf(ErrorWriteBlob, partition.name, err.Error()))

	} else {

		offset = partition.blobSize

		partition.blobSize += len(bytes)
	}

	partition.SetLastWriteTimestamp(time.Now().Unix())

	return offset, nil
}

func writeBlob(file *os.File, bytes []byte) error {

	_, err := file.Write(bytes)

	return err

}

/*
When we simply put the data, we do not write directly in the index; instead,
we write the data in the segment file and then maintain the offset in memory,
as well as a backup for that file known as the temp index in case of a database shutdown before sync.
*/
func (partition *Partition) put(keyBytes, valueBytes []byte, encoder codec.Encoder, lock bool) error {

	var err error

	index, bytes := encoder.MemoryPool.AcquireBytePool(8)

	defer encoder.MemoryPool.ReleaseBytePool(index)

	codec.WriteINT32Value(int32(len(valueBytes)-MaxValueBytes), 0, bytes)

	copy(valueBytes, CheckSumV1Bytes)

	copy(valueBytes[4:8], bytes[:4])

	currentSegmentType := calculateWritingSegmentType(len(valueBytes))

	exitingSegmentType := currentSegmentType

	var segmentHeader byte

	if lock {

		partition.lock.Lock()

		defer partition.lock.Unlock()
	}

	if partition.closed {

		return errors.New(fmt.Sprintf(ErrorPartitionDeleted, partition.name))
	}

	/*
						G1(put method)-> suppose this thread is waiting for the write lock
		G2 (cleanup method) -> suppose this thread has the lock of partition

						Scenario :- if the partition is closed by G2 when the lock is acquired by G1,
		the partition must be reopened
	*/

	partition.dirty = true

	offset, found, _ := searchIndex(keyBytes, partition)

	bytes = bytes[:8]

	compacted := false

	if found {

		codec.WriteINT64Value(int64(offset), 0, bytes)

		compacted = bytes[0] == Compacted
	}

	if found && !compacted {

		exitingSegmentType = getSegmentType(bytes[1])

		if currentSegmentType == exitingSegmentType {

			//just update the data... no need to update the index...

			err = update(bytes[1], valueBytes, codec.ReadINTValue(bytes[2:]), partition)

			if err != nil && strings.EqualFold(err.Error(), "segment file not found") {

				segmentHeader, offset, err = write(valueBytes, partition)

				codec.WriteINT64Value(int64(offset), 0, bytes)

				partition.writes++

				if err != nil {

					return err
				}

				err = writeTempIndex(keyBytes, codec.ReadUINT64Value(append([]byte{NotCompacted, segmentHeader}, bytes...)), partition, encoder)

				if err != nil {

					return err
				}

			} else if err == nil {

				partition.updates++

			}

		} else {

			//index and data both needs to be updated along with segment metadata...

			segmentHeader, offset, err = move(bytes[1], codec.ReadINTValue(bytes[2:]), valueBytes, partition)

			partition.moves++

			if err != nil {

				return err
			}

			codec.WriteINT64Value(int64(offset), 0, bytes)

			err = writeTempIndex(keyBytes, codec.ReadUINT64Value(append([]byte{NotCompacted, segmentHeader}, bytes...)), partition, encoder)

			if err != nil {

				return err
			}

		}

	} else {

		segmentHeader, offset, err = write(valueBytes, partition)

		codec.WriteINT64Value(int64(offset), 0, bytes)

		partition.writes++

		if err != nil {

			return err
		}

		err = writeTempIndex(keyBytes, codec.ReadUINT64Value(append([]byte{NotCompacted, segmentHeader}, bytes...)), partition, encoder)

		if err != nil {

			return err
		}
	}

	partition.SetLastWriteTimestamp(time.Now().Unix())

	return err
}

/////////////////////////////////////////// GET Code //////////////////////////////////////////////

/*
2 cases err will not be nil

1 either the entry is marked as deleted/corrupted
2 error returned while reading from io-uring
3 if segment buffer length less than offset
*/

func (partition *Partition) buildIORequests(keyBytes, valueBytes, offsetBytes []byte, lookupWAL bool) (bytes []byte, err error, fd, segmentLength int) {

	defer func() {

		if r := recover(); r != nil {

			err = errors.New(fmt.Sprintf("error %v occurred while building iouring requests for partition %v", r, partition.name))

			fd, segmentLength = 0, 0

			stackTraceBytes := make([]byte, 1<<20)

			partitionLogger.Error(fmt.Sprintf("error %v occurred while building iouring requests for partition %v", r, partition.name))

			partitionLogger.Error(fmt.Sprintf("!!!STACK TRACE for build io requests !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	// get data which written in transaction form .wal files
	if lookupWAL {

		if entry, ok := partition.entries[GetHash64(keyBytes)]; ok {

			if entry.length > len(valueBytes) {

				return nil, errors.New(ErrorTooLarge), 0, 0
			}

			copy(valueBytes, partition.walBytes[entry.offset:entry.offset+entry.length])

			bytes = getValueBytes(valueBytes)

			if bytes == nil {

				return nil, errors.New(ErrorCorrupted), 0, 0
			}

			return bytes, nil, -1, -1

		}

	}

	offset, found, err := searchIndex(keyBytes, partition)

	if err != nil {

		return nil, err, 0, 0
	}

	if !found {

		return nil, errors.New(fmt.Sprintf(ErrorNotFound, string(keyBytes), partition.name)), 0, 0
	}

	writeINT64Value(int64(offset), offsetBytes)

	// if the target segment is mapped into the memory, then use Memory mapped I/O rather than Disk I/O

	segmentType := getSegmentType(offsetBytes[1])

	segmentLength = getSegmentBufferLength(segmentType)

	if segmentLength > len(valueBytes) {

		return nil, errors.New(ErrorTooLarge), 0, 0
	}

	if offsetBytes[0] == NotCompacted {

		if segment, ok := partition.writingSegments[segmentType]; ok && segment == getSegment(offsetBytes[1]) {

			bytes, err = readByMemoryMappedIO(offsetBytes[1], segmentLength, codec.ReadINTValue(offsetBytes[2:]), valueBytes, partition)

			if err != nil {

				return bytes, err, 0, 0
			}

			return bytes, nil, -1, -1
		}

		return nil, nil, int(partition.segmentFiles[offsetBytes[1]].Fd()), segmentLength
	}

	return nil, nil, int(partition.segment255.Fd()), segmentLength

}

func writeINT64Value(value int64, bytes []byte) {

	bytes[0] = byte(value)

	bytes[1] = byte(value >> 8)

	bytes[2] = byte(value >> 16)

	bytes[3] = byte(value >> 24)

	bytes[4] = byte(value >> 32)

	bytes[5] = byte(value >> 40)

	bytes[6] = byte(value >> 48)

	bytes[7] = byte(value >> 56)
}

func (partition *Partition) has(keyBytes []byte) (bool, error) {

	var err error

	partition.lock.RLock()

	defer partition.lock.RUnlock()

	if partition.closed {

		return false, errors.New(fmt.Sprintf(ErrorPartitionDeleted, partition.name))
	}

	_, found, err := searchIndex(keyBytes, partition)

	if err != nil {

		return false, err
	}

	if !found {

		return false, err

	}

	return true, err

}

func (partition *Partition) getBlob(bytes []byte, offset int, event DiskIOEvent, waitGroup *sync.WaitGroup) ([]byte, error) {

	event.partition = partition

	event.valueBytes = bytes

	event.waitGroup = waitGroup

	event.length = len(bytes)

	event.offset = uint64(offset)

	event.blob = true

	event.waitGroup.Add(1)

	DiskIOEvents <- &event

	event.waitGroup.Wait()

	return event.bufferBytes, event.err

}

/////////////////////////////////////////// Index Scanning Code //////////////////////////////////////////////

// getKeys filters and converts a map of offset-key pairs to a slice of valid key bytes.
//
// This method processes the results from index scanning operations by:
// 1. Filtering out deleted entries (marked with MaxUint64 offset)
// 2. Converting the map structure to a slice for easier processing
// 3. Logging warnings for deleted records encountered
// 4. Compacting the result to remove gaps from filtered entries
//
// The method is used by various scanning operations (prefix, suffix, range, etc.)
// to provide a consistent interface for key retrieval.
//
// Deleted entry handling:
// - Entries with offset MaxUint64 are considered deleted
// - Deleted entries are logged as warnings for monitoring
// - Deleted entries are excluded from the returned results
//
// Parameters:
//   - keys: Map of offsets to key bytes from index scanning operations
//
// Returns:
//   - [][]byte: Slice of valid key bytes, excluding deleted entries
func (partition *Partition) getKeys(keys map[uint64][]byte) [][]byte {

	// Return nil for empty input
	if len(keys) == 0 {

		return nil
	}

	// Pre-allocate slice with maximum possible size
	keyBuffers := make([][]byte, len(keys))

	// Track actual number of valid keys
	index := 0

	// Process each offset-key pair
	for offset, bytes := range keys {

		// Check if this is a valid (non-deleted) entry
		if offset != math.MaxUint64 {

			// Add valid key to result slice
			keyBuffers[index] = bytes

			index++
		} else {

			// Log warning for deleted record encountered
			partitionLogger.Warn(fmt.Sprintf(ErrorDeletedRecord, string(bytes), partition.name))

		}
	}

	// Return slice trimmed to actual number of valid keys
	return keyBuffers[:index]
}

func (partition *Partition) getGreaterThanKeys(value uint64, inclusive, excluded bool) ([][]byte, error) {

	partition.lock.RLock()

	defer partition.lock.RUnlock()

	if partition.closed {

		return nil, errors.New(fmt.Sprintf(ErrorPartitionDeleted, partition.name))
	}

	return partition.getKeys(doGreaterThanLookup(partition, value, inclusive, excluded)), nil
}

func (partition *Partition) getPrefixKeys(bytes []byte, exclude bool) ([][]byte, error) {

	partition.lock.RLock()

	defer partition.lock.RUnlock()

	if partition.closed {

		return nil, errors.New(fmt.Sprintf(ErrorPartitionDeleted, partition.name))
	}

	return partition.getKeys(doPrefixLookup(partition, bytes, exclude)), nil

}

func (partition *Partition) getSuffixKeys(bytes []byte, exclude bool) ([][]byte, error) {

	partition.lock.RLock()

	defer partition.lock.RUnlock()

	if partition.closed {

		return nil, errors.New(fmt.Sprintf(ErrorPartitionDeleted, partition.name))
	}

	return partition.getKeys(doSuffixLookup(partition, bytes, exclude)), nil
}

func (partition *Partition) listKeys(stringValues *swiss.Map[string, int32], numericValues *swiss.Map[int64, int32], excluded bool, dataType codec.DataType) ([][]byte, error) {

	partition.lock.RLock()

	defer partition.lock.RUnlock()

	if partition.closed {

		return nil, errors.New(fmt.Sprintf(ErrorPartitionDeleted, partition.name))
	}

	return partition.getKeys(doListing(partition, stringValues, numericValues, excluded, dataType)), nil
}

func (partition *Partition) getContainKeys(bytes []byte, exclude bool) ([][]byte, error) {

	partition.lock.RLock()

	defer partition.lock.RUnlock()

	if partition.closed {

		return nil, errors.New(fmt.Sprintf(ErrorPartitionDeleted, partition.name))
	}

	return partition.getKeys(doContainLookup(partition, bytes, exclude)), nil
}

func (partition *Partition) getLessThanKeys(value uint64, inclusive, excluded bool) ([][]byte, error) {

	partition.lock.RLock()

	defer partition.lock.RUnlock()

	if partition.closed {

		return nil, errors.New(fmt.Sprintf(ErrorPartitionDeleted, partition.name))
	}

	return partition.getKeys(doLessThanLookup(partition, value, inclusive, excluded)), nil
}

/*func (partition *Partition) getRangeKeys(start, end uint64, inclusiveStart, inclusiveEnd bool) ([][]byte, error) {

	partition.lock.RLock()

	defer partition.lock.RUnlock()

	return partition.getKeys(doRangeLookup(partition, start, end, inclusiveStart, inclusiveEnd)), nil
}*/

/////////////////////////////////////////// Partition Metrics Code //////////////////////////////////////////////

func (partition *Partition) count() int {

	partition.lock.RLock()

	defer partition.lock.RUnlock()

	if partition.closed {

		return 0
	}

	count := 0

	if partition.index != nil {

		count = partition.index.Len()
	}

	if partition.memoryIndices != nil {

		count += partition.memoryIndices.Count()
	}

	return count
}

func (partition *Partition) getSize() int {

	partition.lock.RLock()

	defer partition.lock.RUnlock()

	if partition.closed {

		return 0
	}

	size := partition.blobSize

	if partition.segmentStats != nil && len(partition.segmentStats) > 0 {

		for _, segmentSize := range partition.segmentStats {

			size += segmentSize
		}
	}

	stats, _ := os.Stat(partition.path + PathSeparator + IdxFile)

	if stats != nil {

		size += int(stats.Size())
	}

	return size
}

func (partition *Partition) getMetrics() map[string]int64 {

	partition.lock.RLock()

	defer partition.lock.RUnlock()

	metrics := map[string]int64{}

	metrics[Updates] = partition.updates

	metrics[Writes] = partition.writes

	metrics[Moves] = partition.moves

	metrics[MemoryIndexEntries] = 0

	metrics[IndexEntries] = 0

	metrics[SegmentFiles] = 0

	metrics[DataSizeBytes] = 0

	metrics[IndexSizeBytes] = 0

	if partition.memoryIndices != nil {

		metrics[MemoryIndexEntries] = int64(partition.memoryIndices.Count())
	}

	if partition.index != nil {

		metrics[IndexEntries] = int64(partition.index.Len())
	}

	if partition.segmentFiles != nil {

		metrics[SegmentFiles] = int64(len(partition.segmentFiles))
	}

	size := 0

	if partition.segmentStats != nil && len(partition.segmentStats) > 0 {

		for _, segmentSize := range partition.segmentStats {

			size += segmentSize
		}

		metrics[DataSizeBytes] = int64(size + partition.blobSize)
	}

	size = 0

	stats, _ := os.Stat(partition.path + PathSeparator + IdxFile)

	var indexSize int64 = 0

	if stats != nil {

		indexSize = stats.Size()
	}

	metrics[IndexSizeBytes] = int64(size) + indexSize

	return metrics
}

/////////////////////////////////////////// Compaction Job Code //////////////////////////////////////////////

/*
Index writing format

0-1 --> len(keyBytes)

2---len(keyBytes) ---> original Key Bytes

len(keyBytes)+2 ---len(keyBytes)+2 +8 ----> offsetBytes

len(keyBytes)+2 +8 --- len(keyBytes)+2+8+3 ----> EOT Bytes

What is there in offsetBytes?

0 --->  Compacted Segment or Non Compacted segment (NonCompacted -0 Compacted 1)

1-2 ---->Segment Header  (SegmentType | SegmentNumber)

3-8 -----> Offset in the segment
*/

/*

Steps :-

1. Get all keys from do Listing keys

2. Prepare request for the keys and in case of large column , use blob pool as a buffer

3. Store the key and new offset in tempMemoryIndex

4. Take partition lock and check if the lastWriteOpsTimestamp is less than compacted time, if less than delete the temp files , else rename the temp files
and delete unused functions.
*/

// compact performs comprehensive data compaction to optimize storage and performance.
//
// This method implements a sophisticated compaction algorithm that:
// 1. Consolidates all segment files into a single compacted segment (255)
// 2. Rebuilds the FST index for optimal access patterns
// 3. Removes deleted and fragmented entries to reclaim space
// 4. Eliminates unused segment files and metadata
// 5. Provides atomic updates through temporary files
//
// Compaction benefits:
// - Reduces storage fragmentation and reclaims deleted space
// - Improves read performance by consolidating data
// - Optimizes index structure for faster lookups
// - Simplifies segment management by using single file
// - Removes obsolete metadata and segment files
//
// The process is designed to be atomic and recoverable:
// - Uses temporary files to ensure consistency
// - Implements comprehensive error handling and cleanup
// - Provides detailed logging for monitoring and debugging
// - Handles concurrent access through careful lock management
//
// Parameters:
//   - encoder: Encoder instance for data serialization operations
//   - keyBuffers: Pre-allocated buffers for key data during batch operations
//   - valueBuffers: Pre-allocated buffers for value data during batch operations
//   - batchEvent: Reusable batch event structure for efficient I/O operations
//   - event: Reusable single event structure for blob operations
//   - waitGroup: Synchronization primitive for coordinating I/O operations
//
// Returns:
//   - error: Any error encountered during compaction process
//
// Error handling:
// - Recovers from panics and cleans up temporary files
// - Provides detailed error logging with stack traces
// - Ensures atomic operations through careful file management
// - Handles concurrent modifications during compaction
func (partition *Partition) compact(encoder codec.Encoder, keyBuffers, valueBuffers [][]byte, batchEvent DiskIOEventBatch, event DiskIOEvent, waitGroup *sync.WaitGroup) error {

	// Record compaction start time for performance monitoring
	timestamp := time.Now().UnixMilli()

	// Log compaction start for monitoring and debugging
	partitionLogger.Info(fmt.Sprintf("compaction started for partition %v", partition.name))

	// Synchronize all pending changes before starting compaction
	// This ensures we're working with the most current data
	err := partition.sync(encoder, true)

	if err != nil {

		return err
	}

	// Track read lock state for proper cleanup in defer function
	readLockAcquired := true

	// Acquire read lock to prevent writes during initial phase
	// This ensures data consistency during the compaction setup
	partition.lock.RLock()

	// Set up comprehensive error recovery and cleanup
	defer func() {

		// Handle panics during compaction to prevent system crashes
		if r := recover(); r != nil {

			// Allocate buffer for stack trace capture (1MB should be sufficient)
			stackTraceBytes := make([]byte, 1<<20)

			// Log the panic error for debugging and monitoring
			partitionLogger.Error(fmt.Sprintf("error %v occurred while compacting data", r))

			// Log detailed stack trace for debugging the panic cause
			partitionLogger.Error(fmt.Sprintf("!!!STACK TRACE for compaction !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			// Clean up temporary files created during compaction
			// This prevents orphaned files from consuming disk space
			_ = os.Remove(partition.path + PathSeparator + Temp255)

			_ = os.Remove(partition.path + PathSeparator + Idx255)

		}

		// Release read lock if it's still held
		// This ensures proper lock cleanup even during error conditions
		if readLockAcquired {

			partition.lock.RUnlock()
		}
	}()

	// Log partition size before compaction for monitoring and comparison
	partitionLogger.Info(fmt.Sprintf("partition %v size before compaction is %v bytes", partition.name, getSize(partition.path)))

	// Initialize variables for compaction process
	var currentOffset, outstandingRequests int // Track file position and pending operations
	var index int8                             // Current position in batch buffers
	var segmentFile *os.File                   // Handle for temporary compacted segment file

	// Create in-memory index to track key-offset mappings during compaction
	// This will be used to build the new FST index
	memoryIndices := make(map[string]uint64)

	// Initialize working buffers for batch I/O operations
	buffers := make([][]byte, len(valueBuffers))

	// Initialize error tracking array for batch operations
	errs := make([]error, MaxWorkerEventKeyGroupLength)

	// Get all keys from the current index for compaction
	// This includes both regular and deleted entries
	keys := doListing(partition, nil, nil, false, codec.Invalid)

	// If no keys exist, compaction is not needed
	if len(keys) == 0 {

		return nil
	}

	// Release read lock before file operations to prevent deadlocks
	// File operations may take time and shouldn't hold locks
	partition.lock.RUnlock()

	readLockAcquired = false

	// Clean up any existing temporary files from previous failed compactions
	// This ensures we start with a clean state
	_ = os.Remove(partition.path + PathSeparator + Temp255)

	// Create temporary segment file for compacted data
	// This file will contain all data in a single, optimized format
	segmentFile, err = os.Create(partition.path + PathSeparator + Temp255)

	if err != nil {

		// Clean up on creation failure
		if segmentFile != nil {

			_ = segmentFile.Close()

			_ = os.Remove(partition.path + PathSeparator + Temp255)
		}

		return errors.New(fmt.Sprintf("error %v occurred while creating temp index for compaction", err))
	}

	// Acquire memory pool resources for efficient buffer management
	// These pools prevent frequent allocations during the compaction process

	// Acquire byte pool for offset calculations (8 bytes for uint64 offsets)
	poolIndex, offsetBytes := encoder.MemoryPool.AcquireBytePool(8)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	// Acquire integer pool for tracking batch positions
	// This tracks which elements in the batch are being processed
	positionPoolIndex, _ := encoder.MemoryPool.AcquireINTPool(MaxWorkerEventKeyGroupLength)

	defer encoder.MemoryPool.ReleaseINTPool(positionPoolIndex)

	// Allocate sorted keys array for FST index building
	// Use memory pool if available, otherwise allocate directly
	var sortedKeys []string

	if len(keys) > encoder.MemoryPool.GetPoolLength() {

		// Too many keys for memory pool, allocate directly
		// Pre-allocate with exact capacity for efficiency
		sortedKeys = make([]string, len(keys), len(keys))

	} else {

		// Use memory pool for better memory management
		sortedKeyPoolIndex := -1

		sortedKeyPoolIndex, sortedKeys = encoder.MemoryPool.AcquireStringPool(len(keys))

		defer encoder.MemoryPool.ReleaseStringPool(sortedKeyPoolIndex)
	}

	// Track number of keys processed for sorted array indexing
	keyElementSize := 0

	// Acquire padding bytes for segment data formatting
	// Used for checksum and length headers in compacted segment
	paddingPoolIndex, paddingBytes := encoder.MemoryPool.AcquireBytePool(8)

	defer encoder.MemoryPool.ReleaseBytePool(paddingPoolIndex)

	// Main key processing loop - iterate through all keys for compaction
	// This loop builds batches of keys for efficient I/O processing
	for offset, bytes := range keys {

		// Store key-offset mapping for new index construction
		// This builds the in-memory index that will become the new FST
		memoryIndices[string(bytes)] = offset

		// Add key to current batch buffer
		keyBuffers[index] = bytes

		// Advance to next position in batch
		index++

		// Track total number of keys processed
		outstandingRequests++

		// Set position tracking for batch processing
		// This helps coordinate which buffer positions are active
		encoder.MemoryPool.GetINTPool(positionPoolIndex)[index] = int(index)

		// Process batch when it's full or when all keys are queued
		// This ensures efficient I/O while handling the final partial batch
		if (int(index) >= MaxWorkerEventKeyGroupLength-1) || (outstandingRequests == len(keys)) {

			// Configure batch event for I/O processing
			// Set all necessary parameters for the disk I/O worker
			batchEvent.keyElementSize = index

			batchEvent.errs = errs

			batchEvent.lookUpWAL = false // No WAL lookup needed during compaction

			batchEvent.partition = partition

			batchEvent.buffers = buffers

			batchEvent.bytePoolIndex = poolIndex

			batchEvent.keyBuffers = keyBuffers

			batchEvent.valueBuffers = valueBuffers

			batchEvent.memoryPool = encoder.MemoryPool

			batchEvent.positionPoolIndex = positionPoolIndex

			batchEvent.waitGroup = waitGroup

			// Submit batch for asynchronous processing
			batchEvent.waitGroup.Add(1)

			diskIOBatchEvents <- &batchEvent

			// Wait for batch processing to complete before continuing
			batchEvent.waitGroup.Wait()

			// Prepare padding bytes for compacted segment format
			// First 4 bytes: checksum, next 4 bytes: length, then value data
			copy(paddingBytes[:4], CheckSumV1Bytes)

			// Process each buffer in the completed batch
			// Handle both regular data and large blob data
			for position := range buffers[:index] {

				// Initialize blob handling variables
				blobPoolIndex := NotAvailable

				var blobBytes []byte

				// Handle errors from batch I/O processing
				if errs[position] != nil {

					// Check if error is due to data being too large for regular segments
					if !strings.EqualFold(errs[position].Error(), ErrorTooLarge) {

						// Log non-blob errors and skip this key
						partitionLogger.Error(fmt.Sprintf("error %v occurred while getting data for the key %v hence skipping the key from the compaction ", errs[position], string(keyBuffers[position])))

						continue
					}

					// Handle large data that requires blob storage
					// Acquire blob buffer for large data processing
					blobPoolIndex, blobBytes = encoder.MemoryPool.AcquireBlobPool(NotAvailable)

					// Configure single I/O event for blob data retrieval
					event.err = err

					event.lookUpWAL = false // No WAL lookup needed during compaction

					event.blob = false // Will be set to true by the I/O system if needed

					event.partition = partition

					event.keyBytes = keyBuffers[position]

					event.valueBytes = blobBytes

					event.memoryPool = encoder.MemoryPool

					// Submit blob read request
					waitGroup.Add(1)

					event.waitGroup = waitGroup

					DiskIOEvents <- &event

					// Wait for blob read to complete
					waitGroup.Wait()

					// Handle blob read errors
					if event.err != nil {

						// Log blob read error and skip this key
						partitionLogger.Error(fmt.Sprintf("error %v occurred while getting data for the key %v hence skipping the key from the compaction ", event.err, string(keyBuffers[position])))

						// Release blob buffer on error
						encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

						continue
					}

					// Use blob data for compaction
					buffers[position] = event.bufferBytes

				}

				// Write data to compacted segment with proper formatting
				// This includes checksum, length header, and value data
				err = partition.writeCompactedSegment(segmentFile, buffers[position], paddingBytes, offsetBytes, currentOffset, string(keyBuffers[position]), memoryIndices)

				// Release blob buffer if it was used
				if blobPoolIndex != NotAvailable {

					encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)
				}

				// Handle segment writing errors
				if err != nil {

					return errors.New(fmt.Sprintf("error %v occurred while compacting data", err))

				}

				// Add key to sorted array for FST index building
				sortedKeys[keyElementSize] = string(keyBuffers[position])

				keyElementSize++

				// Update current offset for next write
				// Add 8 bytes for checksum (4) and length (4) headers
				currentOffset += len(buffers[position]) + 8

			}

			// Reset batch index for next iteration
			index = 0

		}

	}

	// Build new FST index from collected key-offset mappings
	// This creates an optimized index for the compacted data

	// Create buffer for FST index data
	var buffer bytes2.Buffer

	// Initialize FST index writer
	indexWriter, err := vellum.New(&buffer, nil)

	// Sort keys for optimal FST structure
	// Sorted keys create more efficient FST trees
	SortStringValues(sortedKeys)

	// Insert all key-offset pairs into the new FST index
	for _, key := range sortedKeys {

		_ = indexWriter.Insert([]byte(key), memoryIndices[key])

	}

	// Finalize FST index construction
	_ = indexWriter.Close()

	// Write new index to temporary file
	// This ensures atomic index replacement
	err = os.WriteFile(partition.path+PathSeparator+Idx255, buffer.Bytes(), 0666)

	if err != nil {

		return errors.New(fmt.Sprintf("error %v occurred while writing index for compacted data", err))
	}

	// Ensure all compacted data is written to disk before proceeding
	_ = segmentFile.Sync()

	// Acquire exclusive lock for atomic file replacement
	// This ensures no concurrent operations interfere with the final steps
	partition.lock.Lock()

	defer partition.lock.Unlock()

	// Check if partition was closed during compaction
	if partition.closed {

		return errors.New(fmt.Sprintf(ErrorPartitionDeleted, partition.name))
	}

	// Check if partition became dirty during compaction
	// If so, retry compaction to ensure data consistency
	if time.Now().Unix()-partition.lastWriteOpsTimestampSeconds.Load() < CleanupThresholdSeconds {

		partitionLogger.Info(fmt.Sprintf("retrying compaction job for partition %v as partition is dirty", partition.name))

		// Close temporary file and signal retry needed
		_ = segmentFile.Close()

		return errors.New(fmt.Sprintf("partition %v is dirty", partition.name))

	}

	if partition.segment255 != nil {

		_ = partition.segment255.Close()
	}

	// Close temporary segment file before renaming
	err = segmentFile.Close()

	if err != nil {

		return errors.New(fmt.Sprintf("error %v occurred while closing the compacted segment", err))
	}

	// Atomically replace old segment with compacted segment
	// This ensures data consistency during the replacement
	err = os.Rename(partition.path+PathSeparator+Temp255, partition.path+PathSeparator+Segment255)

	if err != nil {

		return errors.New(fmt.Sprintf("error %v occurred while renaming the 255.tmp file", err))
	}

	if partition.indexMMapBytes != nil {

		_ = partition.indexMMapBytes.Unmap()
	}

	if partition.indexFile != nil {

		_ = partition.indexFile.Close()
	}

	// Close the old FST index before replacement
	// This releases file handles and memory resources
	err = partition.index.Close()

	if err != nil {

		return errors.New(fmt.Sprintf("error %v occurred while closing the index file", err))
	}

	// Atomically replace old index with new optimized index
	// This ensures index consistency during the replacement
	err = os.Rename(partition.path+PathSeparator+Idx255, partition.path+PathSeparator+IdxFile)

	if err != nil {

		return errors.New(fmt.Sprintf("error %v occurred while renaming the 255.idx file", err))
	}

	// Open and assign the new compacted segment file
	// This replaces all previous segment files with a single optimized file
	var file *os.File

	if file, err = OpenFile(partition.path+PathSeparator+Segment255, OpenExisting, FileAttributeNormal|FileFlagOverlapped); err == nil {

		partition.segment255 = file

	} else {

		return errors.New(fmt.Sprintf("error %v occurred while opening the compacted file", err))

	}

	// Open and assign the new optimized FST index
	// This provides faster lookups than the previous fragmented index
	partition.indexFile, err = os.OpenFile(partition.path+PathSeparator+IdxFile, os.O_RDWR, 0666)

	if err != nil {

		if !errors.Is(err, ErrorFileNotFound) {

			return errors.New(fmt.Sprintf("error %v occurred while opening the index file", err))

		}

	}

	if partition.indexFile != nil {

		partition.indexMMapBytes, err = Map(partition.indexFile, ReadWrite)

		if err != nil {

			_ = partition.indexFile.Close()

			return errors.New(fmt.Sprintf("error %v occurred while opening the index file", err))
		}

		partition.index, err = vellum.Load(partition.indexMMapBytes)

		if err != nil {

			_ = partition.indexMMapBytes.Unmap()

			_ = partition.indexFile.Close()

			return errors.New(fmt.Sprintf("error %v occurred while opening the index file", err))
		}

	}

	// Clean up all old segment files and metadata structures
	// This removes fragmented data and reclaims disk space

	// Close and remove all old segment files
	header := byte(0)

	for header, file = range partition.segmentFiles {

		// Close file handle to release resources
		_ = file.Close()

		// Remove from active segment files map
		delete(partition.segmentFiles, header)

	}

	// Clear all segment-related metadata structures
	// These are no longer needed after compaction
	clear(partition.segmentMetadataBitmaps)

	clear(partition.segmentDirtyFlags)

	clear(partition.segmentStats)

	// Remove all obsolete files from the partition directory
	// Keep only essential files: compacted segment, index, blob, and WAL
	files, err := os.ReadDir(partition.path)

	if err == nil {

		for _, file := range files {

			// Only process regular files, skip directories
			if !file.IsDir() {

				// Preserve essential files, remove everything else
				if file.Name() == Segment255 || file.Name() == IdxFile || file.Name() == BlobFile || file.Name() == txnWALFile {

					continue
				}

				// Remove obsolete segment files and metadata
				_ = os.Remove(partition.path + PathSeparator + file.Name())

			}

		}

	}

	// Reset last write timestamp to indicate clean state
	// This prevents immediate cleanup of the newly compacted partition
	partition.lastWriteOpsTimestampSeconds.Store(0)

	// Log compaction completion with size comparison
	partitionLogger.Info(fmt.Sprintf("partition %v size after compaction is %v bytes", partition.name, getSize(partition.path)))

	// Log compaction performance metrics
	partitionLogger.Info(fmt.Sprintf("partition %v compaction took %v ms", partition.name, time.Now().UnixMilli()-timestamp))

	return nil
}

func getSize(path string) int64 {

	size := int64(0)

	_ = filepath.Walk(path, func(_ string, fileInfo os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !fileInfo.IsDir() {
			size += fileInfo.Size()
		}
		return nil
	})

	return size
}

func (partition *Partition) writeCompactedSegment(segmentFile *os.File, bytes, paddingBytes, offsetBytes []byte, currentOffset int, key string, memoryIndices map[string]uint64) (err error) {

	//write segment file
	codec.WriteINT32Value(int32(len(bytes)), 4, paddingBytes)

	if _, err = segmentFile.Write(paddingBytes); err != nil {

		partitionLogger.Error(fmt.Sprintf("error occurred while writing length bytes in compacted segment , partition - %v , error %v", partition.name, err))

		return err
	}

	if _, err = segmentFile.Write(bytes); err != nil {

		partitionLogger.Error(fmt.Sprintf("error occurred while writing value bytes in compacted segment , partition - %v , error %v", partition.name, err))

		return err
	}

	//write index

	codec.WriteINT64Value(int64(memoryIndices[key]), 0, offsetBytes)

	offsetBytes[0] = Compacted

	codec.WriteINT48Value(int64(currentOffset), 2, offsetBytes)

	memoryIndices[key] = codec.ReadUINT64Value(offsetBytes)

	return err

}

// load metadata files. Only load metadata for non compacted segments
func (partition *Partition) loadMetadata(encoder codec.Encoder, tokenizer *Tokenizer, store *Store, repair, populateSegmentMetadata bool) {

	poolIndex, offsetBytes := encoder.MemoryPool.AcquireBytePool(8)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	var err error

	updated := false

	if _, err = os.Stat(partition.path + PathSeparator + Temp + SegmentMeta); err == nil {

		var bytes []byte

		bytes, err = os.ReadFile(partition.path + PathSeparator + Temp + SegmentMeta)

		if len(bytes) > 0 {

			err = json.Unmarshal(bytes, &partition.segmentMetadataBitmaps)

			if err == nil {

				updated = true
			}
		}

		if !updated {

			partitionLogger.Warn(fmt.Sprintf("failed to read the temp segment metadata file,reason: %v", err))
		}

	}

	if !updated {

		if _, err = os.Stat(partition.path + PathSeparator + SegmentMeta); err == nil {

			var bytes []byte

			bytes, err = os.ReadFile(partition.path + PathSeparator + SegmentMeta)

			if len(bytes) > 0 {

				err = json.Unmarshal(bytes, &partition.segmentMetadataBitmaps)

				if err == nil {

					updated = true
				}
			}

			if !updated {

				partitionLogger.Warn(fmt.Sprintf("failed to read the segment metadata file,reason: %v", err))
			}
		}
	}

	var keys map[uint64][]byte

	// first time flush segment metadata on store open of previous version.
	flushSegmentMetadata := false

	if !updated || repair {

		keys = doListing(partition, nil, nil, false, codec.Invalid)

		flushSegmentMetadata = true
	}

	if populateSegmentMetadata {

		for offset := range keys {

			if offset == math.MaxUint64 {

				continue
			}

			codec.WriteINT64Value(int64(offset), 0, offsetBytes)

			if offsetBytes[0] == Compacted {

				continue
			}

			if _, ok := partition.segmentMetadataBitmaps[offsetBytes[1]]; ok {

				segmentMetadataBitmap := partition.segmentMetadataBitmaps[offsetBytes[1]]

				segmentMetadataBitmap.Set(getSegmentBufferPosition(getSegmentType(offsetBytes[1]), codec.ReadINTValue(offsetBytes[2:])))

				partition.segmentMetadataBitmaps[offsetBytes[1]] = segmentMetadataBitmap

			} else {

				segmentMetadataBitmap := bitmap.Bitmap{}

				segmentMetadataBitmap.Set(getSegmentBufferPosition(getSegmentType(offsetBytes[1]), codec.ReadINTValue(offsetBytes[2:])))

				partition.segmentMetadataBitmaps[offsetBytes[1]] = segmentMetadataBitmap
			}
		}

		if flushSegmentMetadata {

			partition.flushSegmentMetadata()
		}

	}

	if repair {

		maxParts := make(map[string]int)

		for _, key := range keys {

			Split(string(key), KeySeparator, tokenizer)

			multipartKey := tokenizer.Tokens[0] + KeySeparator + tokenizer.Tokens[1]

			part := codec.StringToINT(tokenizer.Tokens[(tokenizer.Counts)-1])

			if _, ok := maxParts[multipartKey]; !ok {

				maxParts[multipartKey] = part

			} else if part > maxParts[multipartKey] {

				maxParts[multipartKey] = part

			}
		}

		for key, part := range maxParts {

			if part != 0 {

				_ = store.SetMultipartKey(GetHash64([]byte(key)), uint16(part))

			}

		}
	}
}

func (partition *Partition) archive(writer *zip.Writer, encoder codec.Encoder, partitionId string) error {

	if partition.dirty {

		err := partition.sync(encoder, true)

		if err != nil {

			partitionLogger.Error(fmt.Sprintf("failed to sync partition %v, reason %v", partition.name, err.Error()))
		}
	}

	partition.lock.RLock()

	defer partition.lock.RUnlock()

	files, _ := os.ReadDir(partition.path)

	for index := range files {

		if strings.HasSuffix(files[index].Name(), ".wal") { //uncommitted files

			continue
		}

		info, err := files[index].Info()

		if err != nil {

			return err
		}

		header, err := zip.FileInfoHeader(info)

		if err != nil {

			return err
		}

		header.Name = partitionId + PathSeparator + files[index].Name() //relative path

		zipWriter, err := writer.CreateHeader(header)

		if err != nil {

			return err
		}

		file, err := os.Open(partition.path + PathSeparator + files[index].Name())

		if err != nil {

			return err
		}

		_, err = io.Copy(zipWriter, file)

		_ = file.Close()

		if err != nil {

			return err
		}

	}

	return nil

}

/////////////////////////////////////// copy //////////////////////////////////////

func (partition *Partition) Clone(encoder codec.Encoder, destination string) (err error) {

	if partition.dirty {

		if err = partition.sync(encoder, true); err != nil {

			partitionLogger.Error(fmt.Sprintf("failed to sync partition %v, reason %v", partition.name, err.Error()))
		}
	}

	partition.lock.RLock()

	defer partition.lock.RUnlock()

	if err = CloneDirectory(partition.path, destination); err != nil {

		return errors.New(fmt.Sprintf("error occurred while copying partition directory partition : %s reason : %s", partition.path, err.Error()))
	}

	return nil
}

func (partition *Partition) flushSegmentMetadata() {

	var segmentMetadataBytes []byte

	var err error

	if segmentMetadataBytes, err = json.Marshal(partition.segmentMetadataBitmaps); err == nil {

		if err = os.WriteFile(partition.path+PathSeparator+Temp+SegmentMeta, segmentMetadataBytes, 0644); err == nil {

			err = os.Rename(partition.path+PathSeparator+Temp+SegmentMeta, partition.path+PathSeparator+SegmentMeta)
		}
	}

	if err != nil {

		partitionLogger.Error(fmt.Sprintf("failed to update the segment metadata bitmap for partition: %v, reason: %v", partition.name, err.Error()))
	}
}

func (partition *Partition) putMultiples(keyBuffers [][]byte, buffers [][]byte, encoder codec.Encoder) (err error) {

	partition.lock.Lock()

	defer partition.lock.Unlock()

	for index := range keyBuffers {

		if err = partition.put(keyBuffers[index], buffers[index], encoder, false); err != nil {

			return err
		}
	}

	return nil
}
