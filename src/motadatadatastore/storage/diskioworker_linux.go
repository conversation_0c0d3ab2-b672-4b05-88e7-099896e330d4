/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-06-04             <PERSON><PERSON><PERSON> <PERSON>            MOTADATA-5780 Transferred executeIOuring functionality from segment to diskioworker
* 2025-06-23             <PERSON><PERSON>val <PERSON>ADATA-6642  Added comments and memory aligned the struct
 */

/*
	DiskIOWorker package provides high-performance asynchronous disk I/O operations using io_uring.

	The IO worker is responsible for handling read-related requests that arrive via channels,
	executing them asynchronously using Linux's io_uring interface for optimal performance.
	This design allows for efficient batch processing of disk operations with minimal
	system call overhead and improved throughput.
*/

package storage

import (
	"errors"
	"fmt"
	"github.com/motadata2025/gouring"
	"motadatadatastore/codec"
	"motadatadatastore/utils"
	"os"
	"runtime"
	"sync"
	"syscall"
)

// diskIOWorkerLogger provides logging functionality for disk I/O worker operations
// Used throughout the worker to record events, errors, and performance metrics
var diskIOWorkerLogger = utils.NewLogger("Disk IO Worker", "storage")

// errorIOWorker is a format string for logging worker-specific errors
// Provides consistent error message formatting across all worker operations
const errorIOWorker = "error %v occurred in io-worker %v"

// DiskIOWorker manages asynchronous disk I/O operations using io_uring.
//
// This worker provides high-performance disk I/O by:
// 1. Using Linux's io_uring interface for asynchronous operations
// 2. Processing requests from global channels in a dedicated goroutine
// 3. Handling both single events and batch events efficiently
// 4. Managing shutdown gracefully with proper resource cleanup
//
// The worker operates continuously until a shutdown signal is received,
// processing disk I/O requests as they arrive and coordinating with
// partition locks to ensure data consistency.
//
// Memory layout is optimized for 64-bit systems:
// - Pointers and channels (8 bytes each) are placed first
// - Smaller types (int) are placed last to minimize padding
type DiskIOWorker struct {
	// 8-byte aligned fields (pointers, channels)
	iouring               *gouring.IoUring // io_uring instance for asynchronous I/O operations
	ShutdownNotifications chan bool        // Channel for receiving shutdown signals from external components

	// 4-byte aligned fields
	workerId int // Unique identifier for this worker instance
}

// NewIOWorker creates and initializes a new DiskIOWorker instance.
//
// This constructor sets up all necessary components for high-performance disk I/O:
// 1. Initializes an io_uring instance with 256 submission queue entries
// 2. Sets up the worker with a unique identifier for tracking and debugging
// 3. Creates a buffered shutdown notification channel
//
// io_uring configuration:
// - Queue depth: 256 entries (balances memory usage with performance)
// - Flags: 0 (uses default configuration for maximum compatibility)
// - Provides asynchronous I/O with minimal system call overhead
//
// Parameters:
//   - id: Unique identifier for this worker instance (used for logging and debugging)
//
// Returns:
//   - *DiskIOWorker: Fully initialized worker ready to start processing
//
// Error handling:
// - Logs io_uring initialization errors but continues with nil iouring
// - Worker can still function for some operations even without io_uring
// - Graceful degradation ensures system stability
func NewIOWorker(id int) *DiskIOWorker {

	// Initialize io_uring with 256 submission queue entries
	// This provides a good balance between memory usage and I/O performance
	ring, err := gouring.New(256, 0)

	if err != nil {

		// Log initialization error but continue - worker can still function
		// Some operations may fall back to synchronous I/O if needed
		diskIOWorkerLogger.Error(fmt.Sprintf("error %v occurred while init iouring for io-worker %v", err, id))
	}

	return &DiskIOWorker{

		// Set unique worker identifier for tracking and debugging
		workerId: id,

		// Assign io_uring instance (may be nil if initialization failed)
		iouring: ring,

		// Create buffered channel for shutdown notifications (capacity: 5)
		// Buffer allows multiple shutdown signals without blocking senders
		ShutdownNotifications: make(chan bool, 5),
	}
}

// DiskIOEventBatch represents a batch of disk I/O operations for efficient processing.
//
// This struct is designed for high-performance batch processing of multiple
// disk read operations. It groups related I/O requests together to:
// 1. Minimize system call overhead by batching operations
// 2. Improve cache locality by processing related data together
// 3. Reduce lock contention by processing multiple operations under a single lock
// 4. Optimize memory usage through shared buffers and pools
//
// The batch processing approach significantly improves throughput compared to
// individual I/O operations, especially for workloads with many small reads.
//
// Memory layout is optimized for 64-bit systems:
// - Pointers and slices (8 bytes each) are placed first
// - 4-byte aligned fields (int) follow
// - Smaller types (int8, bool) are placed last to minimize padding
type DiskIOEventBatch struct {
	// 8-byte aligned fields (pointers, slices)
	memoryPool   *utils.MemoryPool // Memory pool for efficient buffer management and reuse
	partition    *Partition        // Target partition for the batch I/O operations
	waitGroup    *sync.WaitGroup   // Synchronization primitive for coordinating batch completion
	keyBuffers   [][]byte          // Pre-allocated buffers containing keys for batch lookup
	valueBuffers [][]byte          // Pre-allocated buffers for storing retrieved values
	buffers      [][]byte          // Working buffers for I/O operations and data processing
	errs         []error           // Error array corresponding to each operation in the batch

	// 4-byte aligned fields
	bytePoolIndex     int // Index into memory pool's byte buffer array
	positionPoolIndex int // Index into memory pool's position buffer array

	// 1-byte aligned fields (placed last to minimize struct padding)
	keyElementSize int8 // Number of valid elements in the key buffers array
	lookUpWAL      bool // Flag indicating whether to check Write-Ahead Log during reads
}

// DiskIOEvent represents a single disk I/O operation with all necessary context.
//
// This struct encapsulates everything needed to perform an individual disk read:
// 1. Target partition and memory management resources
// 2. Input key and output buffers for the operation
// 3. Error handling and synchronization primitives
// 4. Special handling flags for WAL lookup and blob operations
//
// The event can handle different types of reads:
// - Regular partition reads with optional WAL lookup
// - Blob file reads for large data objects
// - Memory-mapped reads when data is already in memory
//
// Memory layout is optimized for 64-bit systems:
// - Pointers and slices (8 bytes each) are placed first
// - 8-byte aligned fields (uint64) follow
// - 4-byte aligned fields (int) come next
// - Smaller types (bool) are placed last to minimize padding
type DiskIOEvent struct {
	// 8-byte aligned fields (pointers, slices, interfaces)
	memoryPool  *utils.MemoryPool // Memory pool for efficient buffer management and reuse
	partition   *Partition        // Target partition for the I/O operation
	waitGroup   *sync.WaitGroup   // Synchronization primitive for coordinating operation completion
	keyBytes    []byte            // Input key data for the lookup operation
	valueBytes  []byte            // Pre-allocated buffer for storing the retrieved value
	bufferBytes []byte            // Working buffer for I/O operations and data processing
	err         error             // Error result from the I/O operation

	// 8-byte aligned fields (uint64)
	offset uint64 // File offset for blob reads or segment positioning

	// 4-byte aligned fields
	length int // Expected length of data to read (for blob operations)

	// 1-byte aligned fields (placed last to minimize struct padding)
	lookUpWAL bool // Flag indicating whether to check Write-Ahead Log during reads
	blob      bool // Flag indicating this is a blob file read operation
}

// Start initializes and starts the DiskIOWorker in a separate goroutine.
//
// This method starts the main event processing loop that handles:
// 1. Batch I/O events from the global diskIOBatchEvents channel
// 2. Individual I/O events from the global DiskIOEvents channel
// 3. Shutdown notifications for graceful termination
//
// The worker operates continuously until a shutdown signal is received,
// processing disk I/O requests as they arrive. The select statement ensures
// fair processing of different event types while maintaining responsiveness
// to shutdown requests.
//
// Event processing priorities:
// - Batch events: Processed for maximum throughput
// - Individual events: Handled for low-latency operations
// - Shutdown events: Immediate termination with resource cleanup
//
// Resource management:
// - Properly closes io_uring resources during shutdown
// - Logs shutdown events for monitoring and debugging
// - Ensures clean termination without resource leaks
func (worker *DiskIOWorker) Start() {

	// Start background goroutine for continuous I/O event processing
	go func() {

		// Main event processing loop - continues until shutdown
		for {

			select {

			// Handle batch I/O events for high-throughput operations
			case event := <-diskIOBatchEvents:

				// Process multiple I/O operations together for efficiency
				worker.processIOEventBatch(event)

			// Handle individual I/O events for low-latency operations
			case event := <-DiskIOEvents:

				// Process single I/O operation with minimal overhead
				worker.processIOEvent(event)

			// Handle shutdown notifications for graceful termination
			case <-worker.ShutdownNotifications:

				// Close io_uring resources if available
				if worker.iouring != nil {

					worker.iouring.Close()
				}

				// Log shutdown event for monitoring and debugging
				diskIOWorkerLogger.Info("shutting down...")

				// Exit the processing loop and terminate goroutine
				return
			}
		}

	}()

}

// processIOEventBatch handles a batch of disk I/O read requests efficiently.
//
// This method implements high-performance batch processing by:
// 1. Acquiring appropriate locks for data consistency
// 2. Setting up panic recovery for robust error handling
// 3. Building io_uring requests for all valid operations
// 4. Executing all requests together for maximum throughput
// 5. Handling various error conditions gracefully
//
// Lock management:
// - Acquires partition read lock for data consistency
// - Optionally acquires transaction lock for WAL operations
// - Uses deferred unlocking to ensure proper cleanup
//
// Error handling:
// - Recovers from panics and marks all operations as failed
// - Logs detailed error information including stack traces
// - Ensures batch completion even when individual operations fail
//
// Performance optimizations:
// - Processes multiple operations under a single lock
// - Uses io_uring for asynchronous I/O with minimal overhead
// - Batches system calls to reduce context switching
//
// Parameters:
//   - event: Batch event containing all operations to process
func (worker *DiskIOWorker) processIOEventBatch(event *DiskIOEventBatch) {

	// Acquire partition read lock for data consistency
	// This ensures the partition state doesn't change during batch processing
	event.partition.lock.RLock()

	defer event.partition.lock.RUnlock()

	// Acquire transaction lock if WAL lookup is required
	// This ensures consistency with ongoing write operations
	if event.lookUpWAL {

		event.partition.txnLock.RLock()

		defer event.partition.txnLock.RUnlock()
	}

	// Signal batch completion when function exits
	// This allows callers to wait for batch processing to complete
	defer event.waitGroup.Done()

	// Set up panic recovery for robust error handling
	// This prevents batch failures from crashing the entire worker
	defer func() {

		if err := recover(); err != nil {

			// Allocate buffer for stack trace capture (1MB should be sufficient)
			stackTraceBytes := make([]byte, 1<<20)

			// Mark all operations in the batch as failed due to panic
			// This ensures callers receive error information for all operations
			for _, index := range event.memoryPool.GetINTPool(event.positionPoolIndex)[:event.keyElementSize] {

				event.errs[index] = errors.New(fmt.Sprintf("error %v occurred while reading in partition %v", err, event.partition.name))
			}

			// Log the panic error for debugging and monitoring
			diskIOWorkerLogger.Error(fmt.Sprintf(errorIOWorker, err, worker.workerId))

			// Log detailed stack trace for debugging the panic cause
			diskIOWorkerLogger.Error(fmt.Sprintf("!!!STACK TRACE for io-worker %v!!! \n %v", worker.workerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	// Check if partition has been closed/deleted
	// This prevents operations on invalid partitions
	if event.partition.closed {

		// Mark all operations as failed due to partition deletion
		for _, index := range event.memoryPool.GetINTPool(event.positionPoolIndex)[:event.keyElementSize] {

			event.errs[index] = errors.New(fmt.Sprintf(utils.ErrorPartitionDeleted, event.partition.name))
		}
		return
	}

	// Initialize variables for I/O request building
	var err error

	requestElementSize, fd, segmentLength := 0, 0, 0

	// Get byte buffer for offset calculations from memory pool
	offsetBytes := event.memoryPool.GetBytePool(event.bytePoolIndex)

	// Build I/O requests for each operation in the batch
	// This prepares all operations before submitting them together
	for _, index := range event.memoryPool.GetINTPool(event.positionPoolIndex)[:event.keyElementSize] {

		// Build I/O request for this specific key-value pair
		// Returns buffer, error, file descriptor, and segment length
		event.buffers[index], err, fd, segmentLength = event.partition.buildIORequests(event.keyBuffers[index], event.valueBuffers[index], offsetBytes, event.lookUpWAL)

		if err != nil {

			// Mark this operation as failed and continue with others
			event.errs[index] = err

			continue
		}

		// Check for invalid file descriptor or segment length
		// These indicate preparation failures
		if fd == 0 || segmentLength == 0 {

			// Mark operation as failed due to preparation error
			event.errs[index] = errors.New(fmt.Sprintf(utils.ErrorIOURing, "prep failed"))

			continue
		}

		// Check if data was read from memory-mapped file
		// NotAvailable indicates the data is already available in memory
		if fd == utils.NotAvailable || segmentLength == utils.NotAvailable {

			// Skip io_uring submission for memory-mapped data
			continue
		}

		// Increment count of operations that need io_uring processing
		requestElementSize++

		// Get submission queue entry for this I/O operation
		sqe := worker.iouring.GetSqe()

		// Set user data to track which operation this is
		sqe.UserData.SetUint64(uint64(index))

		// Prepare read operation with file descriptor, buffer, length, and offset
		gouring.PrepRead(sqe, fd, &event.valueBuffers[index][0], segmentLength, uint64(codec.ReadINTValue(offsetBytes[2:])))

		// Set buffer reference for result handling
		event.buffers[index] = event.valueBuffers[index]

		// Mark operation as pending (will be updated when I/O completes)
		event.errs[index] = errors.New(fmt.Sprintf(utils.ErrorIOURing, "internal"))
	}

	// Reset error for batch execution
	err = nil

	// Execute all prepared I/O requests if any are ready
	if requestElementSize > 0 {

		// Submit all requests and wait for completion
		// This executes all operations together for maximum efficiency
		err = executeIOURingRequests(event.partition.name, requestElementSize, event.buffers, event.errs, worker)

		if err != nil {

			// Log batch execution error but don't fail individual operations
			// Individual operation errors are handled separately
			diskIOWorkerLogger.Error(fmt.Sprintf("error %v occurred while doing io for partition %v", err, event.partition.name))
		}

	}
}

// processIOEvent handles a single disk I/O read request with specialized logic.
//
// This method processes individual I/O operations with support for:
// 1. Regular partition reads with optional WAL lookup
// 2. Blob file reads for large data objects
// 3. Proper lock management for data consistency
// 4. Comprehensive error handling and recovery
//
// Operation types:
// - Blob reads: Direct file reads for large objects stored separately
// - Partition reads: Standard key-value lookups with optional WAL checking
// - Memory-mapped reads: Efficient access to data already in memory
//
// Lock management:
// - Always acquires partition read lock for consistency
// - Conditionally acquires transaction lock for WAL operations
// - Uses deferred unlocking for proper cleanup
//
// Error handling:
// - Recovers from panics and converts them to operation errors
// - Logs detailed error information for debugging
// - Ensures operation completion even when errors occur
//
// Parameters:
//   - event: Single I/O event containing all operation context
func (worker *DiskIOWorker) processIOEvent(event *DiskIOEvent) {

	// Acquire partition read lock for data consistency
	// This ensures the partition state doesn't change during the operation
	event.partition.lock.RLock()

	defer event.partition.lock.RUnlock()

	// Handle blob file reads separately from regular partition reads
	// Blob files store large objects that don't fit in regular segments
	if event.blob {

		// Signal operation completion when blob read finishes
		defer event.waitGroup.Done()

		// Perform specialized blob read operation
		event.bufferBytes, event.err = worker.readBlob(event)

		// Reset blob-specific fields after completion
		event.length = 0

		event.blob = false

		event.valueBytes = nil

		event.offset = 0

		return
	}

	// Acquire transaction lock if WAL lookup is required
	// This ensures consistency with ongoing write operations
	if event.lookUpWAL {

		event.partition.txnLock.RLock()

		defer event.partition.txnLock.RUnlock()
	}

	// Signal operation completion when function exits
	defer event.waitGroup.Done()

	// Set up panic recovery for robust error handling
	// This prevents operation failures from crashing the worker
	defer func() {

		if err := recover(); err != nil {

			// Allocate buffer for stack trace capture
			stackTraceBytes := make([]byte, 1<<20)

			// Convert panic to operation error
			event.err = errors.New(fmt.Sprintf(errorIOWorker, err, worker.workerId))

			// Log the panic error for debugging and monitoring
			diskIOWorkerLogger.Error(fmt.Sprintf(errorIOWorker, err, worker.workerId))

			// Log detailed stack trace for debugging the panic cause
			diskIOWorkerLogger.Error(fmt.Sprintf("!!!STACK TRACE for io-worker %v!!! \n %v", worker.workerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	// Check if partition has been closed/deleted
	// This prevents operations on invalid partitions
	if event.partition.closed {

		// Set operation error for partition deletion
		event.err = errors.New(fmt.Sprintf(utils.ErrorPartitionDeleted, event.partition.name))

		return
	}

	// Perform the actual partition read operation
	// This handles key lookup, value retrieval, and optional WAL checking
	_, event.bufferBytes, event.err = get(event, worker)

}

// readBlob performs a specialized read operation for large blob data.
//
// This method handles reading large data objects that are stored in separate
// blob files rather than in the regular partition segments. Blob reads are
// used for data that exceeds the normal segment size limits.
//
// Operation flow:
// 1. Sets up panic recovery for robust error handling
// 2. Prepares an io_uring read operation for the blob file
// 3. Submits the request and waits for completion
// 4. Handles completion queue events with retry logic
// 5. Returns the read data or appropriate errors
//
// Error handling:
// - Recovers from panics and converts them to errors
// - Retries on transient errors (EINTR, EAGAIN, ETIME)
// - Handles negative completion results as system errors
// - Logs detailed error information for debugging
//
// Performance considerations:
// - Uses io_uring for asynchronous I/O with minimal overhead
// - Implements retry logic for transient system errors
// - Yields CPU during retries to avoid busy waiting
//
// Parameters:
//   - event: Blob read event containing file, offset, length, and buffer information
//
// Returns:
//   - bytes: The data read from the blob file
//   - error: Any error encountered during the read operation
func (worker *DiskIOWorker) readBlob(event *DiskIOEvent) (bytes []byte, err error) {

	// Set up panic recovery for robust error handling
	// This prevents blob read failures from crashing the worker
	defer func() {

		if r := recover(); r != nil {

			// Allocate buffer for stack trace capture
			stackTraceBytes := make([]byte, 1<<20)

			// Convert panic to operation error
			err = errors.New(fmt.Sprintf("error %v occurred while reading in io worker %v", r, worker.workerId))

			// Log the panic error for debugging and monitoring
			diskIOWorkerLogger.Error(fmt.Sprintf("error %v occurred while reading in io-worker %v", r, worker.workerId))

			// Log detailed stack trace for debugging the panic cause
			diskIOWorkerLogger.Error(fmt.Sprintf("!!!STACK TRACE for reading io-worker %v!!! \n %v", worker.workerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	// Prepare io_uring read operation for the blob file
	// Parameters: file descriptor, buffer, length, offset
	gouring.PrepRead(worker.iouring.GetSqe(), int(event.partition.blobFile.Fd()), &event.valueBytes[0], event.length, event.offset)

	// Submit the read request and wait for at least one completion
	_, err = worker.iouring.SubmitAndWait(1)

	if err != nil {

		// Return immediately on submission errors
		return nil, err
	}

	// Variable to hold completion queue entry
	var cqe *gouring.IoUringCqe

	// Retry counter for transient errors
	retries := 1

	// Completion handling loop with retry logic
	for {

		// Wait for completion queue entry
		err = worker.iouring.WaitCqe(&cqe)

		// Handle transient errors that should be retried
		if err == syscall.EINTR || err == syscall.EAGAIN || err == syscall.ETIME {

			// Log retry attempts only when debug is enabled
			if utils.DebugEnabled() {

				diskIOWorkerLogger.Warn(fmt.Sprintf("error %v occurred while doing i/o for worker %v, retrying i/o for %v times", err, worker.workerId, retries))
			}

			// Yield CPU to avoid busy waiting
			runtime.Gosched()

			// Increment retry counter for monitoring
			retries++

			// Continue with retry
			continue
		}

		// Handle non-transient errors
		if err != nil {

			// Log the error with context information
			diskIOWorkerLogger.Error(fmt.Sprintf("error %v occurred while doing i/o for partition %v, worker %v", err, event.partition.name, worker.workerId))

			return nil, err
		}

		// Handle negative completion results (system errors)
		if cqe.Res < 0 {

			// Convert negative result to system error
			err = fmt.Errorf(utils.ErrorIOURing, syscall.Errno(-cqe.Res))

			// Log the system error with context
			diskIOWorkerLogger.Error(fmt.Sprintf("%v occurred for partition %v, worker %v", fmt.Sprintf(utils.ErrorIOURing, syscall.Errno(-cqe.Res)), event.partition.name, worker.workerId))

			// Mark completion as seen and return error
			worker.iouring.SeenCqe(cqe)

			return nil, err
		}

		// Extract the read data from the buffer (up to actual bytes read)
		bytes = event.valueBytes[:cqe.Res]

		// Mark completion as seen to free the entry
		worker.iouring.SeenCqe(cqe)

		// Return successfully read data
		return bytes, nil
	}
}

func executeIOURingRequests(partitionName string, requests int, buffers [][]byte, errs []error, worker *DiskIOWorker) error {

	request := 0

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			partitionLogger.Error(fmt.Sprintf("error %v occurred while executing iouring requests", err))

			partitionLogger.Error(fmt.Sprintf("!!!STACK TRACE for execute iouring requests !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	_, err := worker.iouring.SubmitAndWait(1)

	if err != nil {

		return err
	}

	var cqe *gouring.IoUringCqe

	for i := 0; i < requests; i++ {

		retries := 1

		for {

			err = worker.iouring.WaitCqe(&cqe)

			if err == syscall.EINTR || err == syscall.EAGAIN || err == syscall.ETIME {

				if utils.DebugEnabled() {

					diskIOWorkerLogger.Warn(fmt.Sprintf("error %v occurred while doing i/o for partition %v and worker %v, retrying i/o for %v times", err, partitionName, worker.workerId, retries))
				}

				runtime.Gosched()

				retries++

				continue
			}

			if err != nil {

				diskIOWorkerLogger.Error(fmt.Sprintf("error %v occurred while doing i/o for partition %v, worker %v", err, partitionName, worker.workerId))

				break
			}

			request = int(cqe.UserData.GetUint64())

			if cqe.Res < 0 {

				errs[request] = errors.New(fmt.Sprintf(utils.ErrorIOURing, syscall.Errno(-cqe.Res)))

				diskIOWorkerLogger.Error(fmt.Sprintf("%v occurred for partition %v, worker %v", fmt.Sprintf(utils.ErrorIOURing, syscall.Errno(-cqe.Res)), partitionName, worker.workerId))
			} else {

				bytes := getValueBytes(buffers[request][:cqe.Res])

				if bytes != nil {

					buffers[request] = bytes

					errs[request] = nil
				} else {

					errs[request] = errors.New(utils.ErrorCorrupted)
				}
			}

			worker.iouring.SeenCqe(cqe)

			break
		}
	}

	return nil
}

func get(event *DiskIOEvent, worker *DiskIOWorker) (bool, []byte, error) {

	var err error

	// First tier: Check WAL for uncommitted transactions
	// This ensures read-your-writes consistency for active transactions
	if event.lookUpWAL {

		// Look up key hash in transaction entries map
		if entry, ok := event.partition.entries[utils.GetHash64(event.keyBytes)]; ok {

			// Validate that the value buffer is large enough
			if entry.length > len(event.valueBytes) {

				return false, nil, errors.New(utils.ErrorTooLarge)
			}

			// Copy WAL data to value buffer for processing
			copy(event.valueBytes, event.partition.walBytes[entry.offset:entry.offset+entry.length])

			// Extract actual value bytes from the transaction format
			bytes := getValueBytes(event.valueBytes)

			// Validate data integrity
			if bytes == nil {

				return false, nil, errors.New(utils.ErrorCorrupted)
			}

			return true, bytes, nil

		}

	}

	// Second tier: Search the main index for persistent data
	// This locates the key in storage
	offset, found, err := searchIndex(event.keyBytes, event.partition)

	// Handle search errors
	if err != nil {

		return found, nil, err
	}

	// Handle key not found
	if !found {

		return found, nil, err

	}

	// Acquire buffer for offset processing
	index, offsetBytes := event.memoryPool.AcquireBytePool(8)

	defer event.memoryPool.ReleaseBytePool(index)

	// Convert offset to bytes for storage access
	writeINT64Value(int64(offset), offsetBytes)

	// Third tier: Read data from storage using the located offset
	event.valueBytes, err = read(offsetBytes, event.valueBytes, event.partition, worker)

	if err != nil {

		return false, nil, err
	}

	return found, event.valueBytes, err

}

func read(offsetBytes, bytes []byte, partition *Partition, worker *DiskIOWorker) ([]byte, error) {

	// if the target segment is mapped into the memory then use Memory mapped I/O rather than Disk I/O
	var cqe *gouring.IoUringCqe

	segmentHeader := offsetBytes[1]

	offset := codec.ReadINTValue(offsetBytes[2:])

	segmentType := getSegmentType(segmentHeader)

	segmentLength := getSegmentBufferLength(segmentType)

	if segmentLength > len(bytes) {

		return nil, errors.New(utils.ErrorTooLarge)
	}

	if segment, ok := partition.writingSegments[segmentType]; ok && offsetBytes[0] == NotCompacted && segment == getSegment(segmentHeader) {

		segmentBuffer := partition.segmentBuffers[segmentHeader]

		if len(segmentBuffer) < offset+getSegmentBufferLength(segmentType) {

			return nil, errors.New(utils.ErrorCorrupted)

		}

		copy(bytes, segmentBuffer[offset:offset+segmentLength])

	} else {

		if offsetBytes[0] == NotCompacted {

			gouring.PrepRead(worker.iouring.GetSqe(), int(partition.segmentFiles[segmentHeader].Fd()), &bytes[0], segmentLength, uint64(offset))

		} else {

			gouring.PrepRead(worker.iouring.GetSqe(), int(partition.segment255.Fd()), &bytes[0], segmentLength, uint64(offset))
		}

		_, err := worker.iouring.SubmitAndWait(1)

		if err != nil {

			return nil, err
		}

		retries := 1

		for {

			err = worker.iouring.WaitCqe(&cqe)

			if err == syscall.EINTR || err == syscall.EAGAIN || err == syscall.ETIME {

				if utils.DebugEnabled() {

					diskIOWorkerLogger.Warn(fmt.Sprintf("error %v occurred while doing i/o for partition %v and worker %v, retrying i/o for %v times", err, partition.name, worker.workerId, retries))
				}

				runtime.Gosched()

				retries++

				continue
			}

			if err != nil {

				diskIOWorkerLogger.Error(fmt.Sprintf("error %v occurred while doing i/o for partition %v, worker %v", err, partition.name, worker.workerId))

				return nil, err
			}

			if cqe.Res < 0 {

				err = fmt.Errorf(utils.ErrorIOURing, syscall.Errno(-cqe.Res))

				worker.iouring.SeenCqe(cqe)

				diskIOWorkerLogger.Error(fmt.Sprintf("%v occurred for partition %v, worker %v", fmt.Sprintf(utils.ErrorIOURing, syscall.Errno(-cqe.Res)), partition.name, worker.workerId))

				return nil, err
			}

			bytes = bytes[:cqe.Res]

			worker.iouring.SeenCqe(cqe)

			break
		}
	}

	valueBytes := getValueBytes(bytes)

	if valueBytes == nil {

		return nil, errors.New(utils.ErrorCorrupted)
	}

	return valueBytes, nil
}

func OpenFile(name string, createMode int, flags int) (*os.File, error) {

	return os.OpenFile(name, createMode, 0666)
}
