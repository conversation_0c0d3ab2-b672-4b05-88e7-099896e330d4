/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
	Broker is a middleware between MOTADATA and DATASTORE, where data first arrives at the broker before being processed by the DATASTORE.
	When BROKER is ready to accept more data, the broker will send a data request to MOTADATA, and MOTADATA will send the data.

	communication link between BROKER and DATASTORE
		- folder (datastore-events)

*/

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-10			 <PERSON><PERSON><PERSON>-4913  Altered modulo operator with new modulo function
* 2025-03-05			 <PERSON><PERSON><PERSON>-5190  Updated comments to match Sonar Standard
* 2025-06-03			 <PERSON><PERSON><PERSON>-6393  Updated With Master Branch
* 2025-06-04			 <PERSON><PERSON>l Shah			Motadata-5780  Introduced new processRequest method and called it from the switch case
 */

package broker

import (
	"encoding/binary"
	"fmt"
	"github.com/golang/snappy"
	"io/fs"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

var (
	brokerLogger = utils.NewLogger("Broker", "broker")

	eventDirs = []string{

		utils.EventDir + utils.PathSeparator + utils.VerticalFormat,
		utils.EventDir + utils.PathSeparator + utils.HorizontalFormat,
		utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations,
		utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.HealthStatistics,
	}

	partitionIdleTimeSeconds = 3600
)

type (
	Broker struct {
		Requests chan []byte

		ShutdownNotifications chan bool

		writers []*DataWriter

		aggregators []*DataAggregator

		trapFlapWriters int

		shutdown bool
	}

	DataEvent struct {
		tick, plugin, storeFormat string

		storeType utils.DatastoreType

		bytes []byte
	}
)

func NewBroker(writers []*DataWriter, aggregators []*DataAggregator) *Broker {

	return &Broker{

		Requests: make(chan []byte, utils.GetBrokerEventChannelSize()),

		ShutdownNotifications: make(chan bool, 5),

		writers: writers,

		aggregators: aggregators,
	}
}

/*
	Start, init point of the Broker service.
	this method will not stop until the global shutdown event occur.
	Broker service will be started by DATASTORE which execute the BROKER exe datastore broker
*/

func (broker *Broker) Start() {

	utils.Publish(utils.MotadataMap{

		utils.OperationType: utils.DatastoreBrokerInit,
	})

	//tmp-aggregations files are incomplete hence, deleting the files
	_ = filepath.WalkDir(utils.TempDir+utils.HyphenSeparator+utils.Aggregations, func(path string, entry fs.DirEntry, err error) error {

		if err != nil {

			return err
		}

		if entry.IsDir() {

			return nil
		}

		_ = os.Remove(path)

		return err
	})

	go func() {

		for {

			if broker.shutdown || utils.GlobalShutdown {

				break
			}

			broker.process()
		}

	}()
}

// process is the main event processing loop for the broker.
//
// This method continuously listens for incoming data events and routes them to the appropriate
// writers or aggregators based on their type and content. It handles message decoding,
// event extraction, and writer selection, ensuring that data is properly processed and stored.
//
// The broker acts as a central hub for data flow in the system, receiving data from external
// sources and distributing it to the appropriate internal components for processing and storage.
func (broker *Broker) process() {
	// Create a timer for periodic cleanup of inactive partitions
	// This helps manage disk space and system resources
	cleanupTimer := time.NewTicker(time.Hour * 2)

	// Set up panic recovery to ensure the broker can recover from unexpected errors
	// This is critical for system stability as the broker is a central component
	defer func() {
		if err := recover(); err != nil {
			// Capture and log the stack trace for debugging
			stackTraceBytes := make([]byte, 1<<20)
			brokerLogger.Error(fmt.Sprintf("error %v occurred in data broker", err))
			brokerLogger.Error(fmt.Sprintf("!!!STACK TRACE for data broker!!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			// Stop the cleanup timer to prevent further triggers during recovery
			cleanupTimer.Stop()
		}
	}()

	// Initialize the trap flap writers assignment
	// Trap flap events require special handling, so we pre-calculate which writers will handle them
	// This uses a hash-based distribution for load balancing
	broker.trapFlapWriters = utils.GetFastModN(utils.GetHash64([]byte(datastore.TrapFlapPlugin)), utils.DataWriters)

	// Main event loop
	for {
		select {
		// Handle periodic cleanup of inactive partitions
		// This runs every 2 hours to manage disk space and system resources
		case <-cleanupTimer.C:
			broker.cleanup()

		// Process incoming data requests
		// These come from external sources via ZeroMQ
		case bytes := <-broker.Requests:

			broker.processRequest(bytes)

		case <-broker.ShutdownNotifications:

			brokerLogger.Info("shutting down data broker")

			broker.shutdown = true

			return
		}
	}
}

func (broker *Broker) processRequest(bytes []byte) {

	// Special handling for single-byte control messages
	// The broker handles some control operations directly since it doesn't have a router
	if len(bytes) == 1 {
		// Handle heartbeat messages
		// These maintain the ZeroMQ connection and verify system health
		if int(bytes[0]) == utils.HeartBeat {
			utils.KeepAliveNotifications <- struct{}{}

			// Handle diagnostic messages
			// These trigger system diagnostics for monitoring and debugging
		} else if int(bytes[0]) == utils.Diagnostic {
			// Run diagnostics in a separate goroutine to avoid blocking the broker
			go utils.StartDiagnostic()
		}

		// Skip further processing for control messages
		return
	}

	// Special handling for test environments
	// In test environments, the message format is slightly different
	// This removes the acknowledgement ID that's not present in production
	if utils.EnvironmentType == utils.DatastoreTestEnvironment || utils.EnvironmentType == utils.DatastoreBenchIntegrationEnvironment {
		// Skip the 8-byte acknowledgement ID and 1-byte operation type
		bytes = bytes[8+1:]
	}

	// Initialize variables for message parsing
	offset, pluginLength, writerId, length := 0, 0, 0, 0

	// Process all events in the message
	// A single message may contain multiple events
	for offset < len(bytes) {
		// Determine the length of the current event
		// In production, the entire message is a single event
		length = len(bytes)

		// In test environments, each event has a 4-byte length prefix
		if utils.EnvironmentType == utils.DatastoreTestEnvironment || utils.EnvironmentType == utils.DatastoreBenchIntegrationEnvironment {
			// Read the length prefix
			length = int(binary.LittleEndian.Uint32(bytes[offset : offset+4]))
			// Move past the length prefix
			offset += 4
		}

		// Decompress the event data using Snappy
		// This reduces network bandwidth and storage requirements
		bufferBytes, err := snappy.Decode(nil, bytes[offset:offset+length])

		// Move to the next event in the message
		offset += length

		// Handle decompression errors
		if err != nil {
			brokerLogger.Error(fmt.Sprintf("error %v occurred while decode snappy bytes", err))
			// Skip this event and continue with the next one
			continue
		}

		// Initialize position for parsing the event data
		position := 0

		// Create a new data event to hold the parsed information
		event := &DataEvent{}

		// Extract the timestamp (tick) from the event data
		// This is an 8-byte little-endian uint64
		event.tick = codec.UINT64ToStringValue(binary.LittleEndian.Uint64(bufferBytes[position : position+8]))
		position += 8

		// Extract the plugin name length from the event data
		// This is a 4-byte little-endian uint32
		pluginLength = int(binary.LittleEndian.Uint32(bufferBytes[position : position+4]))
		position += 4

		// Extract the plugin name from the event data
		// This is a variable-length string
		event.plugin = string(bufferBytes[position : position+pluginLength])
		position += pluginLength

		// Extract the store format from the event data
		// This is a 1-byte value indicating horizontal or vertical format
		event.storeFormat = codec.INTToStringValue(int(bufferBytes[position : position+1][0]))
		position++

		// Extract the store type from the event data
		// This is a 1-byte value indicating the type of data (metric, log, etc.)
		event.storeType = utils.DatastoreType(bufferBytes[position : position+1][0])
		position++

		// Log trace information if enabled
		// This helps with debugging and performance analysis
		if utils.TraceEnabled() {
			brokerLogger.Trace(fmt.Sprintf("received insertion for datastore format %v, datastore type %v, plugin %v and tick %v", event.storeFormat, event.storeType, event.plugin, event.tick))
		}

		// Remove the parsed header from the buffer
		// The remaining data contains the actual event payload
		bufferBytes = bufferBytes[position:]

		event.bytes = bufferBytes

		writerId = utils.GetFastModN(utils.GetHash64([]byte(event.tick+event.plugin)), utils.DataWriters)

		broker.writers[writerId].Events <- event

		// we are writing event policy and trap in horizontal as well as vertical so need to send in vertical writer also
		if event.storeType == utils.EventPolicy && event.plugin != datastore.TrapPolicyPlugin {

			writerId = utils.GetFastModN(utils.GetHash64([]byte(event.plugin)), utils.DataWriters)

			broker.writers[writerId].Events <- &DataEvent{

				plugin: event.plugin,

				tick: event.tick,

				storeFormat: utils.VerticalFormat,

				storeType: utils.EventPolicy,

				bytes: bufferBytes,
			}

		} else if event.storeType == utils.Trap {

			writerId = broker.trapFlapWriters

			broker.writers[writerId].Events <- &DataEvent{

				plugin: event.plugin,

				tick: event.tick,

				storeFormat: utils.VerticalFormat,

				storeType: utils.TrapFlapHistory,

				bytes: bufferBytes,
			}
		}
	}
}

func (broker *Broker) cleanup() {

	for _, dir := range eventDirs {

		partitions, err := os.ReadDir(dir)

		if err != nil {

			brokerLogger.Error(fmt.Sprintf("failed to read directory while cleanup directory: %v, reason: %v", dir, err.Error()))

			continue
		}

		for _, partition := range partitions {

			directory := dir + utils.PathSeparator + partition.Name()

			stat, err := os.Stat(directory)

			if err != nil {

				brokerLogger.Error(fmt.Sprintf("failed to read partition statistics while cleanup partition: %v, reason: %v", directory, err.Error()))

				continue
			}

			if time.Since(stat.ModTime()).Seconds() > float64(partitionIdleTimeSeconds) {

				if err = os.Remove(directory); err != nil {

					if !strings.Contains(err.Error(), "directory not empty") {

						brokerLogger.Error(fmt.Sprintf("failed to remove partition directory while cleanup partition: %v, reason: %v", directory, err.Error()))

					}

				}
			}
		}
	}
}
