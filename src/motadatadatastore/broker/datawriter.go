/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
	Every time new data is received, the datawriter class receives a request from the broker.
	If the data format is horizontal and available in aggregations, it will be unpacked and sent to the dataaggregator.
	Raw data will also be written in the file. Vertical formats will be simply written in the file.

	Data<PERSON> will push files into corresponding store format folders if they are in buffer for more than sync seconds,
	or if they exceed the maximum buffer size.

	NOTE :
		- All buffered files will be written to the tmp folder; if the file life exceeds sync seconds or the size exceeds the maximum buffer size,
		  the datawriter will push the file into the corresponding store format folder;
		- all tmp folders are for datawriter and broker, while folders ("0", "1", "1-aggregations") are for DATASTORE.
		- After pushing the file into another folder, DATASTORE is responsible for any further lookout.

*/

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-10			 Dhaval Bera			Motadata-4913  Altered modulo operator with new modulo function
* 2025-03-05			 Aashil Shah			Motadata-5190  Updated constants from datastore package to utils package to match SonarQube Standard
* 2025-04-02			 Dhaval Bera			Motadata-4859  Added NetRoute Status Metric Datastore Type
* 2025-04-21			 Aashil Shah			Motadata-5873  Introduced new storetypes map and getSyncTimerSeconds function to implement dynamic flush interval
* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-10             Dhaval Bera            MOTADATA-6174  Added Unmap function to release memory-mapped resources
* 2025-05-26             Aashil Shah            MOTADATA-6275  Handled panic in move function and catched Stack Trace
* 2025-06-04             Aashil Shah            MOTADATA-5780  Shifted pending files sync logic in a new method and introduced custom Openfile function to handle differences in Windows and Linux.
 */

package broker

import (
	bytes2 "bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"io/fs"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"runtime"
	"time"
)

var (
	dataWriterLogger = utils.NewLogger("Data Writer", "broker")
)

type (
	DataWriter struct {
		Events chan *DataEvent

		dataAggregators []*DataAggregator

		ShutdownNotifications chan bool

		HorizontalAggregationChangeNotifications chan []byte

		tokenizer *utils.Tokenizer

		buffer *bytes2.Buffer

		trackers map[string]int64

		files map[string]*os.File

		positions map[string]int

		records map[string][]int

		storeTypes map[string]int

		encoder Encoder

		id, length, maxBufferBytes, position int

		aggregations map[string]map[string]utils.MotadataMap

		columns map[string]byte

		aggregationColumns, indexableColumns []string

		shutdown bool
	}
)

// NewDataWriter will simply return a pointer to the newly initialised Datawriter.
func NewDataWriter(id int, dataAggregators []*DataAggregator) *DataWriter {

	writer := &DataWriter{

		id: id,

		dataAggregators: dataAggregators,

		maxBufferBytes: utils.GetDataWriterValueBufferBytes(),

		encoder: NewEncoder(utils.NewMemoryPool(2, 20000, true, utils.DefaultBlobPools)),

		Events: make(chan *DataEvent, utils.GetDataWriterEventChannelSize()),

		ShutdownNotifications: make(chan bool, 5),

		files: make(map[string]*os.File, 100),

		trackers: make(map[string]int64, 100),

		positions: make(map[string]int, 100),

		storeTypes: make(map[string]int, 100),

		aggregations: make(map[string]map[string]utils.MotadataMap, 100),

		columns: make(map[string]byte),

		HorizontalAggregationChangeNotifications: make(chan []byte, 10),

		aggregationColumns: make([]string, 50),

		indexableColumns: make([]string, 50),

		records: make(map[string][]int, 50),
	}

	writer.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	writer.buffer = bytes2.NewBuffer(make([]byte, utils.MaxBlobBytes))

	bytes, _ := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.HorizontalAggregations)

	if len(bytes) > 0 {

		_ = json.Unmarshal(bytes, &writer.aggregations)

	}

	return writer
}

// Start datawriter and listen for incoming requests from the broker.
func (writer *DataWriter) Start() {

	go func() {

		utils.BrokerEngineShutdownMutex.Add(1)

		for {

			if writer.shutdown || utils.GlobalShutdown {

				break
			}

			writer.process(true)

		}

		utils.BrokerEngineShutdownMutex.Done()
	}()
}

func (writer *DataWriter) process(init bool) {

	timer := time.NewTicker(time.Second * time.Duration(utils.DatastoreBrokerWriterFlushTimerSeconds))

	ticker := time.NewTicker(time.Minute)

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			dataWriterLogger.Error(fmt.Sprintf("error %v occurred in data writer %v", err, writer.id))

			dataWriterLogger.Error(fmt.Sprintf("!!!STACK TRACE for data writer %v!!! \n %v", writer.id, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			dataWriterLogger.Error(fmt.Sprintf("data writer %v restarted", writer.id))

			timer.Stop()

			ticker.Stop()

			for _, file := range writer.files {

				if file != nil {

					_ = file.Close()
				}
			}

		}
	}()

	writer.sync(init)

	for {

		select {

		// at interval will flush the file which exceeds the sync seconds
		case <-timer.C:

			writer.flush()

		case <-ticker.C:

			if utils.DebugEnabled() {

				for plugin, records := range writer.records {

					dataWriterLogger.Debug(fmt.Sprintf("for writer %v := plugin %v has total %v bytes with count %v and avg %v", writer.id, plugin, records[0], records[1], records[0]/records[1]))
				}
			}

			clear(writer.records)

		// 	event comes form the broker when new data arrives
		case event := <-writer.Events:

			err := writer.enrich(event)

			if err != nil {

				dataWriterLogger.Error(fmt.Sprintf("failed to write data for tick %v, datastore type %v, plugin %v, reason: %v in data writer %v", event.tick, event.storeType, event.plugin, err.Error(), writer.id))
			}

		/*
			this event occurs when new aggregation view created or deleted,
			request comes config watcher from server when aggregation file edited.
		*/
		case bytes := <-writer.HorizontalAggregationChangeNotifications:

			if len(bytes) > 0 {

				_ = json.Unmarshal(bytes, &writer.aggregations)
			}

		case <-writer.ShutdownNotifications:

			writer.flush()

			dataWriterLogger.Info(fmt.Sprintf("shutting down data writer %v", writer.id))

			writer.shutdown = true

			writer.encoder.MemoryPool.Unmap()

			return

		}
	}

}

// flush, will iterate over all the files and move the file which are exceeds the sync seconds.
func (writer *DataWriter) flush() {

	defer writer.encoder.MemoryPool.TestPoolLeak()

	index := 0

	poolIndex, files := writer.encoder.MemoryPool.AcquireStringPool(utils.NotAvailable)

	expanded := false

	currentTick := time.Now().Unix() // no need to perform time.Now.Unix() multiple times

	for file, tick := range writer.trackers {

		if currentTick-tick >= utils.DataWriterSyncTimers[writer.storeTypes[file]] || utils.GlobalShutdown {

			if index == len(files) {

				expanded = true

				files = writer.encoder.MemoryPool.ExpandStringPool(poolIndex, len(files)+writer.encoder.MemoryPool.GetPoolLength()/2)
			}

			files[index] = file

			index++
		}
	}

	if index > 0 {

		utils.SortNumericStringsWithSep(files[:index], utils.SpecialSeparator, writer.tokenizer)

		for _, file := range files[:index] {

			writer.move(file)
		}
	}

	writer.encoder.MemoryPool.ReleaseStringPool(poolIndex)

	if expanded {

		writer.encoder.MemoryPool.ShrinkPool()

	}
}

// enrich method process the data and write the data into file
func (writer *DataWriter) enrich(event *DataEvent) error {

	writer.reset()

	if utils.DebugEnabled() {

		if _, ok := writer.records[event.plugin]; !ok {

			writer.records[event.plugin] = make([]int, 2)
		}

		writer.records[event.plugin][0] += len(event.bytes)

		writer.records[event.plugin][1] += 1
	}

	if event.storeType == utils.ObjectStatusMetric || event.storeType == utils.NetRouteStatusMetric {

		return writer.enrichStatusMetric(event)
	}

	//skipping first 4 bytes to store length of buffer

	EncodeINT32Value(0, writer.buffer)

	file := event.tick + utils.SpecialSeparator + event.plugin + utils.SpecialSeparator + INTToStringValue(int(event.storeType)) + utils.SpecialSeparator + event.storeFormat

	//pack column buffer for horizontal format
	if event.storeFormat == utils.HorizontalFormat {

		if event.storeType == utils.HealthMetric {

			file += utils.SpecialSeparator + INTToStringValue(utils.GetFastModN(utils.GetHash64([]byte(event.tick+event.plugin)), utils.HealthMetricWriters))

		} else {

			file += utils.SpecialSeparator + INTToStringValue(utils.GetFastModN(utils.GetHash64([]byte(event.tick+event.plugin)), utils.HorizontalWriters))

		}

	} else {

		if event.storeType == utils.EventPolicy || event.storeType == utils.TrapFlapHistory {

			file += utils.SpecialSeparator + INTToStringValue(utils.GetFastModN(utils.GetHash64([]byte(event.plugin)), utils.VerticalWriters))

		} else {

			var partition uint64

			if event.storeType == utils.NetRouteMetric {

				partition = utils.GetHash64([]byte(UINT64ToStringValue(binary.LittleEndian.Uint64(event.bytes[0:8]))))

			} else {

				partition = utils.GetHash64([]byte(UINT32ToStringValue(binary.LittleEndian.Uint32(event.bytes[0:4]))))

			}

			file += utils.SpecialSeparator + INTToStringValue(utils.GetFastModN(partition, utils.VerticalWriters))
		}

	}

	return writer.write(file, event.bytes)
}

/*
write, will write buffer in the file and will append teh EOT bytes,
and will check if size exceeds max buffer size then it will move the file.
*/
func (writer *DataWriter) write(file string, bytes []byte) error {

	//write buffer

	if len(bytes) > 0 {

		writer.buffer.Write(bytes)
	}

	//write whole buffer length .l SKipping first 4 bytes of length itself
	EncodeINT32ValueAt(int32(writer.buffer.Len())-4, 0, writer.buffer)

	writer.buffer.Write(utils.EOTBytes)

	//--------------------------------------------------------------//

	var err error

	if _, ok := writer.files[file]; !ok {

		writer.files[file], err = storage.OpenFile(utils.TempDir+utils.PathSeparator+file, utils.OpenAlways, utils.FileAttributeNormal)

		writer.trackers[file] = time.Now().Unix()

		writer.positions[file] = 0

		utils.Split(file, utils.SpecialSeparator, writer.tokenizer)

		writer.storeTypes[file] = ToINT(writer.tokenizer.Tokens[2])
	}

	if err != nil {

		writer.cleanup(file)

		return err
	}

	if _, err = writer.files[file].Write(writer.buffer.Bytes()); err != nil {

		writer.cleanup(file)

		return err
	}

	writer.positions[file] += len(writer.buffer.Bytes())

	if writer.positions[file] >= writer.maxBufferBytes {

		writer.move(file)
	}

	return nil
}

/*
move will unpack the data for aggregation plugin and will send it to the dataaggregator,
for non aggregations and vertical format simply file will be pushed into corresponding format folder.
*/
func (writer *DataWriter) move(file string) {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			dataWriterLogger.Error(fmt.Sprintf("error %v occurred while moving file %v in data writer %v", err, file, writer.id))

			dataWriterLogger.Error(fmt.Sprintf("!!!STACK TRACE for data writer %v!!! \n %v", writer.id, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			_ = os.Remove(utils.TempDir + utils.PathSeparator + file)

			writer.cleanup(file)
		}

	}()

	if _, ok := writer.files[file]; ok {

		err := writer.files[file].Close()

		if err != nil {

			dataWriterLogger.Error(fmt.Sprintf("error %v occurred while closing file %v in data writer %v", err, file, writer.id))
		}

		writer.cleanup(file)
	}

	utils.Split(file, utils.SpecialSeparator, writer.tokenizer)

	// tick -0 , plugin -1 , datastoreType -2 ,format - 3

	storeType := utils.DatastoreType(StringToINT(writer.tokenizer.Tokens[2]))

	if writer.tokenizer.Tokens[3] == utils.HorizontalFormat &&
		((storeType == utils.Log && writer.tokenizer.Tokens[1] != datastore.LogStatPlugin) || writer.aggregations[writer.tokenizer.Tokens[1]] != nil) {

		clear(writer.columns)

		stringFields, numericFields, err := writer.unpackResponse(utils.TempDir + utils.PathSeparator + file)

		if err == nil {

			var plugins []string

			if writer.aggregations[writer.tokenizer.Tokens[1]] != nil {

				plugins = append(plugins, writer.tokenizer.Tokens[1])
			}

			if storeType == utils.Log && writer.tokenizer.Tokens[1] != datastore.LogStatPlugin {

				plugins = append(plugins, datastore.EventSearchPlugin)
			}

			for _, plugin := range plugins {

				dataTypes := map[string]DataType{}

				for _, views := range writer.aggregations[plugin] {

					for column, values := range views {

						if views.Contains(utils.Filters) {

							continue
						}

						if column == utils.IndexableColumns {

							for column = range utils.ToMap(values) {

								if _, ok := dataTypes[column]; !ok {

									if _, ok = numericFields[column]; ok {

										dataTypes[column] = Int64
									} else {

										dataTypes[column] = String
									}
								}
							}

							continue
						}

						if column == utils.Type {

							continue
						}

						if ToINT(values) == datastore.StringColumn {

							dataTypes[column] = String

							if numericValues, ok := numericFields[column]; ok {

								stringValues := make([]string, len(numericValues))

								INT64ToStringValues(numericValues, stringValues)

								stringFields[column] = stringValues

								delete(numericFields, column)
							}
						} else {

							dataTypes[column] = Int64

							if stringValues, ok := stringFields[column]; ok {

								numericValues := make([]int64, len(stringValues))

								for i := range numericValues {

									numericValues[i] = 1
								}

								numericFields[column] = numericValues

								delete(stringFields, column)
							}
						}
					}
				}

				batchSize, resolvedStringFields, resolvedNumericFields := writer.prepareAggregationContext(dataTypes, stringFields, numericFields)

				for aggregation, views := range writer.aggregations[plugin] {

					if views.Contains(utils.Filters) {

						continue
					}

					aggregationColumnElementSize, indexableColumnElementSize := 0, 0

					for column := range views.GetMapValue(utils.IndexableColumns) {

						writer.indexableColumns[indexableColumnElementSize] = column

						indexableColumnElementSize++
					}

					for column := range views {

						if column == utils.IndexableColumns || column == utils.Type {

							continue
						}

						writer.aggregationColumns[aggregationColumnElementSize] = column

						aggregationColumnElementSize++
					}

					if indexableColumnElementSize > 0 {

						utils.SortStringValues(writer.indexableColumns[:indexableColumnElementSize])
					}

					if aggregationColumnElementSize > 0 {

						utils.SortStringValues(writer.aggregationColumns[:aggregationColumnElementSize])
					}

					tick := INT32ToStringValue(utils.RoundOffSeconds(utils.StringToInt32(writer.tokenizer.Tokens[0]), utils.EventAggregationIntervals[0]))

					aggregationColumns := make([]string, aggregationColumnElementSize)

					indexableColumns := make([]string, indexableColumnElementSize)

					copy(aggregationColumns, writer.aggregationColumns[:aggregationColumnElementSize])

					copy(indexableColumns, writer.indexableColumns[:indexableColumnElementSize])

					resolvedDataTypes := make(map[string]DataType, len(dataTypes))

					for key, value := range dataTypes {

						resolvedDataTypes[key] = value
					}

					writer.dataAggregators[utils.GetFastModN(utils.GetHash64([]byte(tick+aggregation)), utils.DataAggregators)].Requests <- DataAggregationEvent{

						tick: tick, plugin: writer.tokenizer.Tokens[1], interval: utils.EventAggregationIntervals[0], view: aggregation, batchSize: batchSize,

						aggregationColumns: aggregationColumns, indexableColumns: indexableColumns, dataTypes: resolvedDataTypes,

						storeType: storeType,

						stringFields: resolvedStringFields, numericFields: resolvedNumericFields,
					}

				}

			}
		}

	}

	qualifiedTime := time.UnixMilli(1000 * StringToINT64(writer.tokenizer.Tokens[0])).UTC()

	if storeType == utils.HealthMetric {

		writer.tokenizer.Tokens[3] += utils.HyphenSeparator + utils.HealthStatistics

	}

	if err := os.Rename(utils.TempDir+utils.PathSeparator+file, utils.EventDir+utils.PathSeparator+writer.tokenizer.Tokens[3]+
		utils.PathSeparator+INT64ToStringValue(time.Date(qualifiedTime.Year(), qualifiedTime.Month(),
		qualifiedTime.Day(), 0, 0, 0, 0, time.UTC).Unix())+utils.PathSeparator+file+utils.SpecialSeparator+INT64ToStringValue(time.Now().UnixNano())); err != nil {

		if os.IsNotExist(err) {

			if err = os.MkdirAll(utils.EventDir+utils.PathSeparator+writer.tokenizer.Tokens[3]+
				utils.PathSeparator+INT64ToStringValue(time.Date(qualifiedTime.Year(), qualifiedTime.Month(),
				qualifiedTime.Day(), 0, 0, 0, 0, time.UTC).Unix()), 0755); err != nil {

				dataWriterLogger.Error(fmt.Sprintf(utils.ErrorEventFileMove, err, file, writer.id))

			}

			if err := os.Rename(utils.TempDir+utils.PathSeparator+file, utils.EventDir+utils.PathSeparator+writer.tokenizer.Tokens[3]+
				utils.PathSeparator+INT64ToStringValue(time.Date(qualifiedTime.Year(), qualifiedTime.Month(),
				qualifiedTime.Day(), 0, 0, 0, 0, time.UTC).Unix())+utils.PathSeparator+file+utils.SpecialSeparator+INT64ToStringValue(time.Now().UnixNano())); err != nil {

				dataWriterLogger.Error(fmt.Sprintf(utils.ErrorEventFileMove, err, file, writer.id))

			}

		} else {

			dataWriterLogger.Error(fmt.Sprintf(utils.ErrorEventFileMove, err, file, writer.id))
		}
	}
}

func (writer *DataWriter) reset() {

	writer.length = 0

	writer.buffer.Reset()
}

// to remove file name form writer's data structure's
func (writer *DataWriter) cleanup(file string) {

	delete(writer.positions, file)

	delete(writer.files, file)

	delete(writer.trackers, file)

	delete(writer.storeTypes, file)
}

/*
As for status metric, from MOTADATA packing is generic as in vertical format, but for optimization
we unpack the status metric and pack only value and then write in file.
*/
func (writer *DataWriter) enrichStatusMetric(event *DataEvent) error {

	bufferPosition := 0

	objectId := ""

	if event.storeType == utils.NetRouteStatusMetric {

		objectId = UINT64ToStringValue(binary.LittleEndian.Uint64(event.bytes[bufferPosition : bufferPosition+8]))

		bufferPosition += 8

	} else {

		objectId = UINT32ToStringValue(binary.LittleEndian.Uint32(event.bytes[bufferPosition : bufferPosition+4]))

		bufferPosition += 4
	}

	instance := readStringValue(event.bytes, bufferPosition)

	bufferPosition += 4 + len(instance)

	writerId := INTToStringValue(utils.GetFastModN(utils.GetHash64([]byte(objectId)), utils.VerticalWriters))

	for bufferPosition < len(event.bytes) {

		//metric

		bufferPosition += 1 // skipping dataType bytes

		bufferPosition += 4 + len(readStringValue(event.bytes, bufferPosition)) //read column name

		column := readStringValue(event.bytes, bufferPosition)

		bufferPosition += 4 + len(column)

		//status

		bufferPosition += 1 // skipping dataType bytes

		bufferPosition += 4 + len(readStringValue(event.bytes, bufferPosition)) //read column name

		status := readStringValue(event.bytes, bufferPosition)

		bufferPosition += 4 + len(status)

		//skipping reason column

		bufferPosition += 1 // skipping dataType bytes

		bufferPosition += 4 + len(readStringValue(event.bytes, bufferPosition)) //read column name

		reason := readStringValue(event.bytes, bufferPosition)

		bufferPosition += 4 + len(reason)

		//duration

		bufferPosition += 1 // skipping dataType bytes

		bufferPosition += 4 + len(readStringValue(event.bytes, bufferPosition)) //read column name

		duration := int32(binary.LittleEndian.Uint64(event.bytes[bufferPosition : bufferPosition+8]))

		bufferPosition += 8

		writer.reset()

		EncodeINT32Value(0, writer.buffer)

		// objectId --> instance --> status -->reason -->--->column-->duration

		// objectId
		EncodeINT32Value(int32(len(objectId)), writer.buffer)

		writer.buffer.WriteString(objectId)

		// instance
		EncodeINT32Value(int32(len(instance)), writer.buffer)

		writer.buffer.WriteString(instance)

		// status
		EncodeINT32Value(int32(len(status)), writer.buffer)

		writer.buffer.WriteString(status)

		// reason
		EncodeINT32Value(int32(len(reason)), writer.buffer)

		writer.buffer.WriteString(reason)

		// column
		EncodeINT32Value(int32(len(column)), writer.buffer)

		writer.buffer.WriteString(column)

		EncodeINT32Value(duration, writer.buffer)

		file := event.tick + utils.SpecialSeparator + event.plugin + utils.SpecialSeparator + INTToStringValue(int(event.storeType)) +
			utils.SpecialSeparator + utils.VerticalFormat + utils.SpecialSeparator + writerId

		err := writer.write(file, nil)

		if err != nil {

			return err
		}
	}

	return nil
}

func readStringValue(bytes []byte, position int) string {

	length := int(binary.LittleEndian.Uint32(bytes[position : position+4]))

	position += 4

	value := string(bytes[position : position+length])

	position += length

	return value

}

// unpacking
func (writer *DataWriter) unpackResponse(file string) (map[string][]string, map[string][]int64, error) {

	bytes, err := os.ReadFile(file)

	if err != nil {

		return nil, nil, err
	}

	writer.position = 0

	for writer.position < len(bytes) {

		length := int(binary.LittleEndian.Uint32(bytes[writer.position : writer.position+4]))

		writer.position += 4

		//Batch is corrupted so don't read the batch
		if length > len(bytes) || !bytes2.Equal(bytes[writer.position+length:writer.position+length+3], utils.EOTBytes) {

			dataWriterLogger.Warn(fmt.Sprintf("invalid batch %v for file hence skipping records ", file))

			break

		}

		writer.decodeColumns(length, bytes)

		writer.position += 3 // skipping EOT bytes
	}

	//fields unpacking

	writer.position = 0

	stringFields := make(map[string][]string, 50)

	numericFields := make(map[string][]int64, 50)

	for writer.position < len(bytes) {

		length := int(binary.LittleEndian.Uint32(bytes[writer.position : writer.position+4]))

		writer.position += 4

		//Batch is corrupted so don't read the batch
		if length > len(bytes) || !bytes2.Equal(bytes[writer.position+length:writer.position+length+3], utils.EOTBytes) {

			dataWriterLogger.Warn(fmt.Sprintf("invalid batch %v for file hence skipping records ", file))

			break

		}

		writer.processHorizontalBatch(length, stringFields, numericFields, bytes)

		writer.position += 3 // skipping EOT bytes

	}

	return stringFields, numericFields, nil

}

// decode columns is for getting all the columns while data will be retrieved in processHorizontalBatch
func (writer *DataWriter) decodeColumns(length int, bytes []byte) {

	position := writer.position
	//skipping eventSourceBytes
	writer.length = int(binary.LittleEndian.Uint16(bytes[writer.position : writer.position+2]))

	writer.position += 2

	writer.position += len(bytes[writer.position : writer.position+writer.length])

	//unpack columns and its datatyes
	for writer.position < position+length {

		columnDataType := bytes[writer.position : writer.position+1][0]

		writer.position += 1

		writer.length = int(binary.LittleEndian.Uint32(bytes[writer.position : writer.position+4]))

		writer.position += 4

		column := string(bytes[writer.position : writer.position+writer.length])

		writer.position += len(column)

		if dataType, ok := writer.columns[column]; !ok || dataType != datastore.StringColumn {

			writer.columns[column] = columnDataType
		}

		//we don't need value here, but we need to skip value length here for next record

		if columnDataType == datastore.StringColumn {

			writer.length = int(binary.LittleEndian.Uint32(bytes[writer.position : writer.position+4]))

			writer.position = writer.position + 4 + writer.length

		} else {

			writer.position += 8
		}

	}
}

// processHorizontalBatch will unpack the data and will also add the columns with the dummy data which are not present in any records but present in writer.columns.
func (writer *DataWriter) processHorizontalBatch(length int, stringFields map[string][]string, numericFields map[string][]int64, bytes []byte) {

	position := writer.position

	eventSourceLength := int(binary.LittleEndian.Uint16(bytes[writer.position : writer.position+2]))

	writer.position += 2

	eventSource := string(bytes[writer.position : writer.position+eventSourceLength])

	writer.position += eventSourceLength

	resolvedColumns := map[string]struct{}{}

	var value interface{}

	for writer.position < position+length {

		if utils.GlobalShutdown {

			break
		}

		dataType := bytes[writer.position : writer.position+1][0]

		writer.position++

		// field bytes
		field := readStringValue(bytes, writer.position)

		writer.position += 4 + len(field)

		if dataType == datastore.IntegerColumn {

			value = int(binary.LittleEndian.Uint64(bytes[writer.position : writer.position+8]))

			writer.position += 8

		} else {

			//value bytes
			value = readStringValue(bytes, writer.position)

			writer.position += 4 + len(ToString(value))
		}

		resolvedColumns[field] = struct{}{}

		dataType = getDataType(dataType, field, stringFields, numericFields)

		if dataType == datastore.IntegerColumn {

			numericValue := ToINT(value)

			if _, found := numericFields[field]; !found {

				numericFields[field] = []int64{int64(numericValue)}

			} else {

				numericFields[field] = append(numericFields[field], int64(numericValue))

			}

		} else if dataType == datastore.StringColumn {

			// for blob column no need to append in [] and increase heap size, so for count only we will append field itself to align []
			if field == utils.Event || field == utils.Message || field == utils.TrapMessage || field == utils.TrapRawMessage {

				stringFields[field] = append(stringFields[field], field)

				continue
			}

			stringFields[field] = append(stringFields[field], ToString(value))

		}
	}

	if len(eventSource) > 0 {

		stringFields[utils.EventSource] = append(stringFields[utils.EventSource], eventSource)
	}

	for column, dataType := range writer.columns {

		if _, ok := resolvedColumns[column]; !ok {

			if dataType == datastore.StringColumn {

				stringFields[column] = append(stringFields[column], utils.Empty)
			} else {

				numericFields[column] = append(numericFields[column], utils.DummyINT64Value)
			}
		}
	}

}

/*
After unpacking the data, all columns in stringfields and numericfields will be available;
however, we only need columns that are required for views, so we will discard columns that are not required for views,
and append a dummy value to columns that are not present but required for views.
*/
func (writer *DataWriter) prepareAggregationContext(dataTypes map[string]DataType, stringFields map[string][]string, numericFields map[string][]int64) (int, map[string][]string, map[string][]int64) {

	batchSize := 0

	resolvedStringFields := make(map[string][]string, len(stringFields))

	for key, value := range stringFields {

		resolvedStringFields[key] = make([]string, len(value))

		copy(resolvedStringFields[key], value)
	}

	resolvedNumericFields := make(map[string][]int64, len(numericFields))

	for key, value := range numericFields {

		resolvedNumericFields[key] = make([]int64, len(value))

		copy(resolvedNumericFields[key], value)
	}

	//removing unwanted string fields and numeric fields

	for field := range resolvedStringFields {

		if _, ok := dataTypes[field]; ok {

			batchSize = len(resolvedStringFields[field])

			continue
		}

		delete(resolvedStringFields, field)

	}

	for field := range resolvedNumericFields {

		if _, ok := dataTypes[field]; ok {

			batchSize = len(resolvedNumericFields[field])

			continue
		}

		delete(resolvedNumericFields, field)

	}

	//append missing column
	if len(resolvedStringFields)+len(resolvedNumericFields) < len(dataTypes) {

		dummyINT64Values := make([]int64, batchSize)

		// as of now we have only sum and count in event aggregation, when we introduce min/max need to pass dummy value here and skip it in data aggregator,
		// at that time event aggregator will not find this missing column and send it to probe in offline job but as of now no need to send to offline job hence passing 0 instead of dummy value so event aggregator will receive all column
		for index := range dummyINT64Values {

			dummyINT64Values[index] = 0
		}

		dummyStringValues := make([]string, batchSize)

		for field, dataType := range dataTypes {

			if _, ok := resolvedStringFields[field]; ok {

				continue
			} else if _, ok := resolvedNumericFields[field]; ok {

				continue
			}

			if dataType == String {

				resolvedStringFields[field] = dummyStringValues
			} else {

				resolvedNumericFields[field] = dummyINT64Values
			}

		}
	}

	return batchSize, resolvedStringFields, resolvedNumericFields
}

func (writer *DataWriter) sync(init bool) {

	// To process the remaining unprocessed file in tmp directory while shutting down the broker
	if init && writer.id == 0 {

		getFiles := func(dir string) []string {

			files := make([]string, 0)

			_ = utils.WalkDir(dir, utils.SpecialSeparator, writer.tokenizer, func(file string, entry fs.DirEntry, err error) error {

				if err != nil {

					return err
				}

				if entry.IsDir() {

					return nil
				}

				utils.Split(file, utils.PathSeparator, writer.tokenizer)

				file = writer.tokenizer.Tokens[writer.tokenizer.Counts-1]

				files = append(files, file)

				return nil
			})

			return files
		}

		files := getFiles(utils.TempDir)

		if len(files) > 0 {

			for _, file := range files {

				writer.move(file)
			}
		}
	}
}

func getDataType(dataType byte, field string, stringFields map[string][]string, numericFields map[string][]int64) byte {

	if dataType == datastore.IntegerColumn {

		if _, found := stringFields[field]; found {

			return datastore.StringColumn

		}

		return datastore.IntegerColumn

	}

	//append previous numeric values into string values
	if values, found := numericFields[field]; found {

		delete(numericFields, field)

		stringFields[field] = append(stringFields[field], INT64ToStringValues(values, make([]string, len(values)))...)

	}

	return datastore.StringColumn

}
