/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>-5190  Updated constants from datastore package to utils package to match SonarQube Standard
* 2025-04-21			 A<PERSON><PERSON>			Mo<PERSON>ata-5873  Added Test Case for Dynamic Flush Interval Changes
* 2025-05-26			 A<PERSON><PERSON>-6275  Added Test Case for Corrupted Event file
* 2025-06-04			 <PERSON><PERSON><PERSON>-5780  Restructured the testcase for each testcase to start their data writer
 */

package broker

import (
	"bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"github.com/pbnjay/memory"
	"github.com/stretchr/testify/assert"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/utils"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"strings"
	"testing"
	"time"
)

func TestMain(m *testing.M) {

	for _, arg := range os.Args {

		if strings.Contains(arg, "bench") {

			fmt.Println("skipped.")

			return
		}
	}

	runtime.GC()

	_ = os.RemoveAll(utils.EventDir)

	utils.PublisherResponses = make(chan []uint8, 2)

	var err error

	configBytes, err := os.ReadFile((filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	if err != nil {

		panic(err)

	}

	memoryBytes := int64(memory.TotalMemory())

	debug.SetMemoryLimit((memoryBytes * 85) / 100)

	utils.SystemBootSequence = utils.Broker

	utils.EnvironmentType = utils.DatastoreTestEnvironment

	utils.InitConfigs(configBytes)

	utils.EnvironmentType = utils.DatastoreTestEnvironment

	utils.SystemBootSequence = utils.Datastore

	utils.InitTestSetup()

	if utils.InitConfigs(configBytes) {

		utils.CleanUpStores()

		utils.Create(utils.TempDir)

		utils.Create(utils.TempDir + utils.HyphenSeparator + utils.Aggregations)

		utils.Create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

		utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

		utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations)

		utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.HealthStatistics)

		datastore.Init()

		utils.DatastoreFlushTimerSeconds = 3

		utils.DatastoreBrokerWriterFlushTimerSeconds = 3

		utils.DataWriterSyncMaxTimerSeconds = 3

		utils.DataWriterSyncMinTimerSeconds = 3

		utils.HorizontalFormatSyncNotifications = make(chan utils.WriterSyncEvent, 1_00_00)

		utils.DatastoreFlushTimerSeconds = 2

		utils.DatastoreBrokerWriterFlushTimerSeconds = 2

		utils.SetLogLevel(0)

		utils.DataAggregators = 1

		m.Run()

	}
}

func TestToLowerASCII(t *testing.T) {

	message := "número Добрый день <189> devname=\\fg_firewall\\ devid=\\FG200FT922949996\\ eventtime=032885327 tz=\\+0530\\ logid=\\**********\\ type=\\traffic\\ subtype=\\forward\\ level=\\notice\\ vd=\\root\\ srcip=*********** srcport=54102 srcintf=\\port1\\ srcintfrole=\\lan\\ dstip=******* dstport=53 dstintf=\\port14\\ dstintfrole=\\wan\\ srccountry=\\Reserved\\ dstcountry=\\United States\\ sessionid=********** proto=17 action=\\accept\\ policyid=45 policytype=\\policy\\ poluuid=\\5c62740a-d20b-51ed-64a8-c64ee590df70\\ policyname=\\Allow_DNS_SMTP_Team\\ service=\\DNS\\ trandisp=\\snat\\ transip=************** transport=54102 appid=16195 app=\\DNS\\ appcat=\\Network.Service\\ apprisk=\\elevated\\ applist=\\App_Control_Motadata\\ duration=60 sentbyte=115 rcvdbyte=197 sentpkt=1 rcvdpkt=1 shapingpolicyid=9 shaperperipname=\\WiFi-40Mbps\\ shaperperipdropbyte=0 vwlid=7 vwlquality=\\Seq_num(3 port14) alive selected\\ vwlname=\\motadata-DMZ-LAN\\"

	records := 50000 //this test is dependent on the test environment load as it asserts time

	asciiToLowerTime := time.Now().UnixMilli()

	for i := 0; i < records; i++ {

		utils.ToLower(message)
	}

	asciiToLowerTime = time.Now().UnixMilli() - asciiToLowerTime

	toLowerTime := time.Now().UnixMilli()

	for i := 0; i < records; i++ {

		strings.ToLower(message)
	}

	toLowerTime = time.Now().UnixMilli() - toLowerTime

	assertions := assert.New(t)

	assertions.True(toLowerTime > asciiToLowerTime)
}

func TestStringContainsToLowerASCII(t *testing.T) {

	message := "número Добрый день <189> devname=\\fg_firewall\\ devid=\\FG200FT922949996\\ eventtime=032885327 tz=\\+0530\\ logid=\\**********\\ type=\\traffic\\ subtype=\\forward\\ level=\\notice\\ vd=\\root\\ srcip=*********** srcport=54102 srcintf=\\port1\\ srcintfrole=\\lan\\ dstip=******* dstport=53 dstintf=\\port14\\ dstintfrole=\\wan\\ srccountry=\\Reserved\\ dstcountry=\\United States\\ sessionid=********** proto=17 action=\\accept\\ policyid=45 policytype=\\policy\\ poluuid=\\5c62740a-d20b-51ed-64a8-c64ee590df70\\ policyname=\\Allow_DNS_SMTP_Team\\ service=\\DNS\\ trandisp=\\snat\\ transip=************** transport=54102 appid=16195 app=\\DNS\\ appcat=\\Network.Service\\ apprisk=\\elevated\\ applist=\\App_Control_Motadata\\ duration=60 sentbyte=115 rcvdbyte=197 sentpkt=1 rcvdpkt=1 shapingpolicyid=9 shaperperipname=\\WiFi-40Mbps\\ shaperperipdropbyte=0 vwlid=7 vwlquality=\\Seq_num(3 port14) alive selected\\ vwlname=\\motadata-DMZ-LAN\\"

	keyWord := "forward"

	records := 50000 //this test is dependent on the test environment load as it asserts time

	stringContainsTime := time.Now().UnixMilli()

	for i := 0; i < records; i++ {

		strings.Contains(strings.ToLower(message), keyWord)
	}

	stringContainsTime = time.Now().UnixMilli() - stringContainsTime

	bytesContainsTime := time.Now().UnixMilli()

	for i := 0; i < records; i++ {

		bytes.Contains(utils.StringToUnsafeBytes(utils.ToLower(message)), utils.StringToUnsafeBytes(keyWord))
	}

	bytesContainsTime = time.Now().UnixMilli() - bytesContainsTime

	assertions := assert.New(t)

	assertions.True(stringContainsTime > bytesContainsTime)
}

func TestDataWriterHealthStatistics(t *testing.T) {

	dataWriter := NewDataWriter(5, nil)

	bytes, _ := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.HorizontalAggregations)

	if len(bytes) > 0 {

		_ = json.Unmarshal(bytes, &dataWriter.aggregations)

	}

	assertions := assert.New(t)

	plugin := "500014-health.metric"

	bufferBytes := packEventBatchV3([]string{utils.EventSource, "column1", "column2", "column3"}, []interface{}{"10.20.40.141", "abc", 1, 12}, []byte{datastore.StringColumn, datastore.StringColumn, datastore.IntegerColumn, datastore.IntegerColumn})

	event := DataEvent{

		tick: codec.INT64ToStringValue(time.Now().Unix()),

		plugin: plugin,

		storeFormat: utils.HorizontalFormat,

		storeType: utils.HealthMetric,

		bytes: bufferBytes,
	}

	dataWriter.maxBufferBytes = 100

	err := dataWriter.enrich(&event)

	dataWriter.maxBufferBytes = utils.GetDataWriterValueBufferBytes()

	assertions.Nil(err)

	assertions.Nil(err)

	var fileName string

	for file := range dataWriter.files {

		fileName = file
	}

	qualifiedTimeStamp := time.Unix(codec.StringToINT64(codec.INT64ToStringValue(time.Now().Unix())), 0).UTC()

	baseTick := utils.INT64ToStringValue(time.Date(qualifiedTimeStamp.Year(), qualifiedTimeStamp.Month(), qualifiedTimeStamp.Day(), 0, 0, 0, 0, time.UTC).Unix())

	err = os.Mkdir(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.HealthStatistics+utils.PathSeparator+baseTick, 0755)

	assertions.Nil(err)

	dataWriter.move(fileName)

	entries, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.HealthStatistics + utils.PathSeparator + baseTick)

	assertions.Nil(err)

	found := false

	for _, entry := range entries {

		if strings.Contains(entry.Name(), plugin) {

			found = true
		}
	}

	assertions.True(found)
}

func TestDataWriterLogHorizontalBatch(t *testing.T) {

	dataWriter := NewDataWriter(5, nil)

	assertions := assert.New(t)

	plugin := "50001-log"

	bufferBytes := packEventBatchV3([]string{utils.EventSource, "column1", "column2", "column3"}, []interface{}{"10.20.40.141", "abc", 1, 12}, []byte{datastore.StringColumn, datastore.StringColumn, datastore.IntegerColumn, datastore.IntegerColumn})

	event := DataEvent{

		tick: codec.INT64ToStringValue(time.Now().Unix()),

		plugin: plugin,

		storeFormat: utils.HorizontalFormat,

		storeType: utils.Log,

		bytes: bufferBytes,
	}

	dataWriter.maxBufferBytes = 100

	err := dataWriter.enrich(&event)

	dataWriter.maxBufferBytes = utils.GetDataWriterValueBufferBytes()

	assertions.Nil(err)

}

func TestDataWriterTrapHorizontalBatch(t *testing.T) {

	dataWriter := NewDataWriter(5, nil)

	assertions := assert.New(t)

	plugin := "50002-trap"

	bufferBytes := packEventBatchV3([]string{utils.EventSource, "column1", "column2", "column3"}, []interface{}{"10.20.40.141", "abc", 1, 12}, []byte{datastore.StringColumn, datastore.StringColumn, datastore.IntegerColumn, datastore.IntegerColumn})

	tick := time.Now().Unix()

	qualifiedTime := time.Unix(tick, 0).UTC()

	baseTime := time.Date(qualifiedTime.Year(), qualifiedTime.Month(), qualifiedTime.Day(), 0, 0, 0, 0, time.UTC)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	_ = dataWriter.enrich(&DataEvent{

		tick: codec.INT64ToStringValue(tick),

		plugin: plugin,

		storeFormat: utils.HorizontalFormat,

		storeType: utils.Trap,

		bytes: bufferBytes,
	})

	_ = dataWriter.enrich(&DataEvent{

		tick: codec.INT64ToStringValue(tick),

		plugin: plugin,

		storeFormat: utils.VerticalFormat,

		storeType: utils.TrapFlapHistory,

		bytes: bufferBytes,
	})

	for file := range dataWriter.trackers {

		dataWriter.move(file)

		delete(dataWriter.trackers, file)
	}

	dirs, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

	assertions.Nil(err)

	assertions.True(len(dirs) > 0)

	var valid bool

	for _, dir := range dirs {

		files, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.PathSeparator + dir.Name())

		assert.Nil(t, err)

		for _, file := range files {

			if strings.Contains(file.Name(), plugin) {

				valid = true
			}
		}

	}

	assertions.True(valid)

	for file := range dataWriter.trackers {

		dataWriter.move(file)

		delete(dataWriter.trackers, file)
	}

	valid = false

	dirs, err = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	assertions.Nil(err)

	assertions.True(len(dirs) > 0)

	for _, dir := range dirs {

		files, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.PathSeparator + dir.Name())

		assert.Nil(t, err)

		for _, file := range files {

			if strings.Contains(file.Name(), plugin) {

				valid = true
			}
		}

	}

	assertions.True(valid)

}

func TestDataWriterPerformanceMetricBatch(t *testing.T) {

	dataWriter := NewDataWriter(5, nil)

	assertions := assert.New(t)

	plugin := "101-performance"

	columns := []string{"column1", "column2", "column3", "column4"}

	values := []interface{}{"abc", 1, 3, 1.45}

	dataTypes := []byte{datastore.StringColumn, datastore.IntegerColumn, datastore.IntegerColumn, datastore.FloatingColumn}

	objectId := int32(1)

	instance := "x"

	batch := packMetricBatchV4(columns, values, dataTypes, objectId, instance)

	tick := time.Now().Unix()

	qualifiedTime := time.Unix(tick, 0).UTC()

	baseTime := time.Date(qualifiedTime.Year(), qualifiedTime.Month(), qualifiedTime.Day(), 0, 0, 0, 0, time.UTC)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	_ = dataWriter.enrich(&DataEvent{

		tick: codec.INT64ToStringValue(tick),

		plugin: plugin,

		storeType: utils.PerformanceMetric,

		storeFormat: utils.VerticalFormat,

		bytes: batch,
	})

	for file := range dataWriter.trackers {

		dataWriter.move(file)

		delete(dataWriter.trackers, file)
	}

	dirs, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	assertions.Nil(err)

	assertions.True(len(dirs) > 0)

	found := false

	for _, dir := range dirs {

		files, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.PathSeparator + dir.Name())

		assert.Nil(t, err)

		for _, file := range files {

			if strings.Contains(file.Name(), plugin) {

				found = true

				break
			}
		}

	}

	assertions.True(found)

}

func TestDataWriterStatusMetricBatch(t *testing.T) {

	dataWriter := NewDataWriter(5, nil)

	assertions := assert.New(t)

	plugin := "101-status"

	columns := []string{"metric", "status", "reason", "duration"}

	values := []interface{}{"system.cpu.percent", "uptime", "uptime.time", 10}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.IntegerColumn}

	objectId := int32(1)

	instance := "x"

	batch := packMetricBatchV4(columns, values, dataTypes, objectId, instance)

	tick := time.Now().Unix()

	qualifiedTime := time.Unix(tick, 0).UTC()

	baseTime := time.Date(qualifiedTime.Year(), qualifiedTime.Month(), qualifiedTime.Day(), 0, 0, 0, 0, time.UTC)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	_ = dataWriter.enrich(&DataEvent{

		tick: codec.INT64ToStringValue(tick),

		plugin: plugin,

		storeType: utils.ObjectStatusMetric,

		storeFormat: utils.VerticalFormat,

		bytes: batch,
	})

	for file := range dataWriter.trackers {

		dataWriter.move(file)

		delete(dataWriter.trackers, file)
	}

	dirs, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	assertions.Nil(err)

	assertions.True(len(dirs) > 0)

	found := false

	for _, dir := range dirs {

		files, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.PathSeparator + dir.Name())

		assert.Nil(t, err)

		for _, file := range files {

			if strings.Contains(file.Name(), plugin) {

				found = true
			}
		}

	}

	assertions.True(found)

}

// This testcase should run at last
func TestDataWriterStatusMetricBatchV1(t *testing.T) {

	dataWriter := NewDataWriter(5, nil)

	assertions := assert.New(t)

	plugin := "102-status"

	columns := []string{"metric", "status", "reason", "duration"}

	values := []interface{}{"system.cpu.percent", "uptime", "uptime.time", 10}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.IntegerColumn}

	objectId := int32(1)

	instance := "x"

	batch := packMetricBatchV4(columns, values, dataTypes, objectId, instance)

	tick := time.Now().Unix()

	qualifiedTime := time.Unix(tick, 0).UTC()

	baseTime := time.Date(qualifiedTime.Year(), qualifiedTime.Month(), qualifiedTime.Day(), 0, 0, 0, 0, time.UTC)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	_ = dataWriter.enrich(&DataEvent{

		tick: codec.INT64ToStringValue(tick),

		plugin: plugin,

		storeType: utils.ObjectStatusMetric,

		storeFormat: utils.VerticalFormat,

		bytes: batch,
	})

	for file := range dataWriter.trackers {

		dataWriter.move(file)

		delete(dataWriter.trackers, file)
	}

	dirs, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	assertions.Nil(err)

	assertions.True(len(dirs) > 0)

	found := false

	for _, dir := range dirs {

		files, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.PathSeparator + dir.Name())

		assert.Nil(t, err)

		for _, file := range files {

			if strings.Contains(file.Name(), plugin) {

				found = true
			}
		}

	}

	assertions.True(found)

}

func TestDataWriterProcessPreviousBatch(t *testing.T) {

	dataWriter := NewDataWriter(0, nil)

	assertions := assert.New(t)

	tick := time.Unix(1701254778, 0).Unix()

	qualifiedTime := time.Unix(tick, 0).UTC()

	baseTime := time.Date(qualifiedTime.Year(), qualifiedTime.Month(), qualifiedTime.Day(), 0, 0, 0, 0, time.UTC)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	err := os.WriteFile(utils.EventDir+utils.PathSeparator+"tmp"+utils.PathSeparator+"1701254778§50001-log§11§1§0§1701254780591958350", make([]byte, 500), 0666)

	dataWriter.sync(true)

	assertions.Nil(err)

	time.Sleep(time.Second * 3)

	dirs, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

	assertions.Nil(err)

	assertions.True(len(dirs) > 0)

	var valid bool

	for _, dir := range dirs {

		files, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.PathSeparator + dir.Name())

		assert.Nil(t, err)

		for _, file := range files {

			if strings.Contains(file.Name(), "50001-log") {

				valid = true
			}
		}

	}

	assertions.True(valid)

}

func TestBrokerDiagnostics(t *testing.T) {

	assertions := assert.New(t)

	bytes, _ := json.Marshal(utils.MotadataMap{
		utils.DiagnosticProfileDurationSeconds: 2,
	})

	err, _ := utils.ReloadConfigs(bytes)

	assertions.NoError(err)

	broker := NewBroker(nil, nil)

	broker.processRequest([]byte{utils.HeartBeat})

	broker.processRequest([]byte{utils.Diagnostic})

	time.Sleep(time.Second)

	utils.AssertLogMessage(assertions, "Diagnostic", "system", "Diagnostic started for")

}

// sending shutdown notification while data is writing continuously
func TestDataWriterStatusMetricBatchV2(t *testing.T) {

	dataWriter := NewDataWriter(5, nil)

	assertions := assert.New(t)

	plugin := "102-status"

	columns := []string{"metric", "status", "reason", "duration"}

	values := []interface{}{"system.cpu.percent", "uptime", "uptime.time", 10}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.IntegerColumn}

	objectId := int32(1)

	instance := "x"

	batch := packMetricBatchV4(columns, values, dataTypes, objectId, instance)

	tick := time.Now().Unix()

	qualifiedTime := time.Unix(tick, 0).UTC()

	baseTime := time.Date(qualifiedTime.Year(), qualifiedTime.Month(), qualifiedTime.Day(), 0, 0, 0, 0, time.UTC)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	for index := 0; index < 20; index++ {

		_ = dataWriter.enrich(&DataEvent{

			tick: codec.INT64ToStringValue(tick),

			plugin: plugin,

			storeType: utils.ObjectStatusMetric,

			storeFormat: utils.VerticalFormat,

			bytes: batch,
		})
	}

	dirs, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	assertions.Nil(err)

	assertions.True(len(dirs) > 0)

	found := false

	for _, dir := range dirs {

		files, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.PathSeparator + dir.Name())

		assert.Nil(t, err)

		for _, file := range files {

			if strings.Contains(file.Name(), plugin) {

				found = true

				break
			}
		}

	}

	assertions.True(found)

	bytes, err := utils.ReadLogFile("Data Writer", "broker")

	assert.Nil(t, err)

	assert.NotContains(t, string(bytes), "!!!STACK TRACE for data writer")

}

func TestIncorrectEncode(t *testing.T) {

	broker := NewBroker(nil, nil)

	broker.processRequest([]byte{2, 0, 0, 0, 0, 0})

	bytes, err := utils.ReadLogFile("Broker", "broker")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "error")

}

func TestBrokerPanicRecover(t *testing.T) {

	utils.PublisherResponses = make(chan []uint8, 2)

	writers := make([]*DataWriter, utils.DataWriters)

	aggregators := make([]*DataAggregator, utils.DataAggregators)

	broker := NewBroker(writers, aggregators)

	broker.Start()

	utils.EnvironmentType = utils.DatastoreTestEnvironment

	utils.DeploymentType = utils.Large

	broker.Requests <- make([]byte, 0)

	time.Sleep(time.Second * 1)

	bytes, err := utils.ReadLogFile("Broker", "broker")

	assert.Nil(t, err)

	assert.True(t, strings.Contains(string(bytes), "error runtime error:"))

	broker.ShutdownNotifications <- true

}

func TestDataWriterPanicRecover(t *testing.T) {

	dataWriter := NewDataWriter(0, nil)

	defer func() {

		dataWriter.ShutdownNotifications <- true

		time.Sleep(time.Millisecond)

	}()

	dataWriter.Start()

	event := DataEvent{}

	event.storeType = utils.ObjectStatusMetric

	dataWriter.Events <- &event

	time.Sleep(time.Second * 1)

	bytes, err := utils.ReadLogFile("Data Writer", "broker")

	assert.Nil(t, err)

	assert.NotNil(t, bytes)

	assert.Contains(t, string(bytes), "error runtime error: slice bounds out of range [:4] with capacity 0 occurred in data writer")

}

func TestBrokerPendingEvents(t *testing.T) {

	_ = os.RemoveAll(utils.EventDir)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat, 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat, 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.Aggregations, 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.HealthStatistics, 0755)

	_ = os.MkdirAll(utils.TempDir+utils.HyphenSeparator+utils.Aggregations, 0755)

	_ = os.MkdirAll(utils.TempDir, 0755)

	qualifiedTime := time.Now().UTC()

	baseTime := time.Date(qualifiedTime.Year(), qualifiedTime.Month(), qualifiedTime.Day(), 0, 0, 0, 0, time.UTC)

	fileName := utils.INT64ToStringValue(qualifiedTime.Unix()) + utils.SpecialSeparator + "plugin" + utils.SpecialSeparator + codec.INTToStringValue(int(utils.PerformanceMetric)) + utils.SpecialSeparator + utils.VerticalFormat + utils.SpecialSeparator + "1" + utils.SpecialSeparator + utils.INT64ToStringValue(time.Now().UnixNano())

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.AddDate(0, 0, -1).Unix()), 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.AddDate(0, 0, -1).Unix()), 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.Aggregations+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.Aggregations+utils.PathSeparator+utils.INT64ToStringValue(baseTime.AddDate(0, 0, -1).Unix()), 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.HealthStatistics+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.HealthStatistics+utils.PathSeparator+utils.INT64ToStringValue(baseTime.AddDate(0, 0, -1).Unix()), 0755)

	_, _ = os.Create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.PathSeparator + utils.INT64ToStringValue(baseTime.Unix()) + utils.PathSeparator + fileName)

	_, _ = os.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.PathSeparator + utils.INT64ToStringValue(baseTime.Unix()) + utils.PathSeparator + fileName)

	_, _ = os.Create(utils.TempDir + utils.HyphenSeparator + utils.Aggregations + utils.PathSeparator + utils.INT64ToStringValue(baseTime.Unix()) + utils.PathSeparator + fileName)

	_, _ = os.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations + utils.PathSeparator + utils.INT64ToStringValue(baseTime.Unix()) + utils.PathSeparator + fileName)

	_, _ = os.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.HealthStatistics + utils.PathSeparator + utils.INT64ToStringValue(baseTime.Unix()) + utils.PathSeparator + fileName)

	//validating the removal of temp-aggregation files.
	entries, _ := os.ReadDir(utils.TempDir + utils.HyphenSeparator + utils.Aggregations)

	assertions := assert.New(t)

	assertions.Equal(0, len(entries))

	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	assertions.Equal(2, len(entries))

	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

	assertions.Equal(2, len(entries))

	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations)

	assertions.Equal(2, len(entries))

	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.HealthStatistics)

	assertions.Equal(2, len(entries))

	//validating the not removal of empty partition in broker start files.
	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.PathSeparator + utils.INT64ToStringValue(baseTime.Unix()))

	assertions.Equal(1, len(entries))

	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.PathSeparator + utils.INT64ToStringValue(baseTime.Unix()))

	assertions.Equal(1, len(entries))

	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations + utils.PathSeparator + utils.INT64ToStringValue(baseTime.Unix()))

	assertions.Equal(1, len(entries))

	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.HealthStatistics + utils.PathSeparator + utils.INT64ToStringValue(baseTime.Unix()))

	assertions.Equal(1, len(entries))

	//validating the not removal of empty partition in broker start files.
	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.PathSeparator + utils.INT64ToStringValue(baseTime.AddDate(0, 0, -1).Unix()))

	assertions.Equal(0, len(entries))

	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.PathSeparator + utils.INT64ToStringValue(baseTime.AddDate(0, 0, -1).Unix()))

	assertions.Equal(0, len(entries))

	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations + utils.PathSeparator + utils.INT64ToStringValue(baseTime.AddDate(0, 0, -1).Unix()))

	assertions.Equal(0, len(entries))

	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.HealthStatistics + utils.PathSeparator + utils.INT64ToStringValue(baseTime.AddDate(0, 0, -1).Unix()))

	assertions.Equal(0, len(entries))

	for _, eventDirectory := range eventDirs {

		partitions, err := os.ReadDir(eventDirectory)

		assertions.NoError(err)

		assertions.NotNil(partitions)

		for _, partition := range partitions {

			stat, err := os.Stat(path.Join(eventDirectory, partition.Name()))

			assertions.NoError(err)

			assertions.True(time.Since(stat.ModTime()).Seconds() < float64(partitionIdleTimeSeconds))

		}
	}

}

func TestBrokerSyncTimerSeconds(t *testing.T) {

	dataWriter := NewDataWriter(0, nil)

	assertions := assert.New(t)

	utils.DataWriterSyncTimers[1] = 10

	plugin := "1010-performance"

	columns := []string{"column1", "column2", "column3", "column4"}

	values := []interface{}{"abc", 1, 3, 1.45}

	dataTypes := []byte{datastore.StringColumn, datastore.IntegerColumn, datastore.IntegerColumn, datastore.FloatingColumn}

	objectId := int32(1)

	instance := "x"

	batch := packMetricBatchV4(columns, values, dataTypes, objectId, instance)

	tick := time.Now().Unix()

	qualifiedTime := time.Unix(tick, 0).UTC()

	baseTime := time.Date(qualifiedTime.Year(), qualifiedTime.Month(), qualifiedTime.Day(), 0, 0, 0, 0, time.UTC)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	_ = dataWriter.enrich(&DataEvent{

		tick: codec.INT64ToStringValue(tick),

		plugin: plugin,

		storeType: utils.PerformanceMetric,

		storeFormat: utils.VerticalFormat,

		bytes: batch,
	})

	dirs, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	assertions.Nil(err)

	assertions.True(len(dirs) > 0)

	found := false

	for _, dir := range dirs {

		files, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.PathSeparator + dir.Name())

		assert.Nil(t, err)

		for _, file := range files {

			if strings.Contains(file.Name(), plugin) {

				found = true

				break
			}
		}

	}

	assertions.False(found)

	utils.DataWriterSyncTimers[1] = utils.DataWriterSyncMaxTimerSeconds

}

func TestBrokerClearIdlePartitions(t *testing.T) {

	broker := NewBroker(nil, nil)

	_ = os.RemoveAll(utils.EventDir)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat, 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat, 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.Aggregations, 0755)

	_ = os.MkdirAll(utils.TempDir+utils.HyphenSeparator+utils.Aggregations, 0755)

	_ = os.MkdirAll(utils.TempDir, 0755)

	qualifiedTime := time.Now().UTC()

	baseTime := time.Date(qualifiedTime.Year(), qualifiedTime.Month(), qualifiedTime.Day(), 0, 0, 0, 0, time.UTC)

	fileName := utils.INT64ToStringValue(qualifiedTime.Unix()) + utils.SpecialSeparator + "plugin" + utils.SpecialSeparator + codec.INTToStringValue(int(utils.PerformanceMetric)) + utils.SpecialSeparator + utils.VerticalFormat + utils.SpecialSeparator + "1" + utils.SpecialSeparator + utils.INT64ToStringValue(time.Now().UnixNano())

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.AddDate(0, 0, -1).Unix()), 0755)

	_, _ = os.Create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.PathSeparator + utils.INT64ToStringValue(baseTime.Unix()) + utils.PathSeparator + fileName)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+utils.INT64ToStringValue(baseTime.AddDate(0, 0, -1).Unix()), 0755)

	_, _ = os.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.PathSeparator + utils.INT64ToStringValue(baseTime.Unix()) + utils.PathSeparator + fileName)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.Aggregations+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.Aggregations+utils.PathSeparator+utils.INT64ToStringValue(baseTime.AddDate(0, 0, -1).Unix()), 0755)

	_, _ = os.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations + utils.PathSeparator + utils.INT64ToStringValue(baseTime.Unix()) + utils.PathSeparator + fileName)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.HealthStatistics+utils.PathSeparator+utils.INT64ToStringValue(baseTime.Unix()), 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.HealthStatistics+utils.PathSeparator+utils.INT64ToStringValue(baseTime.AddDate(0, 0, -1).Unix()), 0755)

	_, _ = os.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.HealthStatistics + utils.PathSeparator + utils.INT64ToStringValue(baseTime.Unix()) + utils.PathSeparator + fileName)

	partitionIdleTimeSeconds = 1

	time.Sleep(time.Second * 2)

	broker.cleanup()

	entries, _ := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	assert.Equal(t, 1, len(entries))

	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

	assert.Equal(t, 1, len(entries))

	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations)

	assert.Equal(t, 1, len(entries))

	entries, _ = os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.HealthStatistics)

	assert.Equal(t, 1, len(entries))

}

func TestBrokerCreatePartition(t *testing.T) {

	dataWriter := NewDataWriter(0, nil)

	_ = os.RemoveAll(utils.EventDir)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat, 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat, 0755)

	_ = os.MkdirAll(utils.TempDir+utils.HyphenSeparator+utils.Aggregations, 0755)

	_ = os.MkdirAll(utils.TempDir, 0755)

	timeStamp := utils.INT64ToStringValue(time.Now().UTC().Unix())

	baseTimeStamp := time.Unix(codec.StringToINT64(timeStamp), 0).UTC()

	baseTick := utils.INT64ToStringValue(time.Date(baseTimeStamp.Year(), baseTimeStamp.Month(), baseTimeStamp.Day(), 0, 0, 0, 0, time.UTC).Unix())

	file := baseTick + utils.SpecialSeparator + "402-dummy.plugin" + utils.SpecialSeparator + "1" + utils.SpecialSeparator + "0" + utils.SpecialSeparator + "1"

	_, err := os.Create(utils.TempDir + utils.PathSeparator + file)

	assertions := assert.New(t)

	assertions.NoError(err)

	dataWriter.move(file)

	_, err = os.Stat(utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.PathSeparator + baseTick)

	assertions.NoError(err)
}

func TestDataWriterDataTypeChanges(t *testing.T) {

	dataWriter := NewDataWriter(0, nil)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	assertions := assert.New(t)

	clear(dataWriter.trackers)

	tick := codec.INT64ToStringValue(time.Now().Unix())

	dataWriter.dataAggregators = make([]*DataAggregator, utils.DataAggregators)

	for i := range dataWriter.dataAggregators {

		dataWriter.dataAggregators[i] = NewDataAggregator(i)

		dataWriter.dataAggregators[i].Start()
	}

	plugin := "500014-testing.datatype.change"

	bufferBytes := packEventBatchV3([]string{utils.EventSource, "testing.column1", "testing.column2", "testing.column3"}, []interface{}{"10.20.40.141", "abc", 1, "12"}, []byte{datastore.StringColumn, datastore.StringColumn, datastore.IntegerColumn, datastore.StringColumn})

	datastore.AddHorizontalAggregation(plugin, plugin+utils.AggregationSeparator+"0", utils.MotadataMap{
		utils.IndexableColumns: utils.MotadataMap{
			"testing.column3": struct{}{},
		},
		"testing.column3": 0,
		"type":            12,
	})

	utils.EventAggregationIntervals = []int{5}

	bytes, _ := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.HorizontalAggregations)

	if len(bytes) > 0 {

		_ = json.Unmarshal(bytes, &dataWriter.aggregations)

	}

	event := DataEvent{

		tick: tick,

		plugin: plugin,

		storeFormat: utils.HorizontalFormat,

		storeType: utils.HealthMetric,

		bytes: bufferBytes,
	}

	dataWriter.enrich(&event)

	for file := range dataWriter.trackers {

		dataWriter.move(file)
	}

	bufferBytes = packEventBatchV3([]string{utils.EventSource, "testing.column1", "testing.column2", "testing.column3"}, []interface{}{"10.20.40.141", "abc", 1, 12}, []byte{datastore.StringColumn, datastore.StringColumn, datastore.IntegerColumn, datastore.IntegerColumn})

	event = DataEvent{

		tick: tick,

		plugin: plugin,

		storeFormat: utils.HorizontalFormat,

		storeType: utils.HealthMetric,

		bytes: bufferBytes,
	}

	_ = dataWriter.enrich(&event)

	for file := range dataWriter.trackers {

		dataWriter.move(file)
	}

	time.Sleep(time.Second * 2)

	utils.AssertLogMessageInverse(assertions, "Data Aggregator", "broker", "!!!STACK TRACE for data aggregator")

}

func TestDataWriterDataTypeChangesV2(t *testing.T) {

	dataAggregator := NewDataAggregator(0)

	dataWriter := NewDataWriter(0, []*DataAggregator{dataAggregator})

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	assertions := assert.New(t)

	clear(dataWriter.trackers)

	tick := codec.INT64ToStringValue(time.Now().Unix())

	plugin := "500014-testing.datatype.changev2"

	bufferBytes := packEventBatchV3([]string{utils.EventSource, "testing.column1", "testing.column2", "testing.column3"}, []interface{}{"10.20.40.141", "abc", 1, 12}, []byte{datastore.StringColumn, datastore.StringColumn, datastore.IntegerColumn, datastore.IntegerColumn})

	datastore.AddHorizontalAggregation(plugin, plugin+utils.AggregationSeparator+"0", utils.MotadataMap{
		utils.IndexableColumns: utils.MotadataMap{
			"testing.column3": struct{}{},
		},
		"testing.column3": 1,
		"type":            12,
	})

	utils.EventAggregationIntervals = []int{5}

	bytes, _ := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.HorizontalAggregations)

	if len(bytes) > 0 {

		_ = json.Unmarshal(bytes, &dataWriter.aggregations)

	}

	event := DataEvent{

		tick: tick,

		plugin: plugin,

		storeFormat: utils.HorizontalFormat,

		storeType: utils.HealthMetric,

		bytes: bufferBytes,
	}

	dataWriter.enrich(&event)

	for file := range dataWriter.trackers {

		dataWriter.move(file)
	}

	bufferBytes = packEventBatchV3([]string{utils.EventSource, "testing.column1", "testing.column2", "testing.column3"}, []interface{}{"10.20.40.141", "abc", 1, "12"}, []byte{datastore.StringColumn, datastore.StringColumn, datastore.IntegerColumn, datastore.StringColumn})

	event = DataEvent{

		tick: tick,

		plugin: plugin,

		storeFormat: utils.HorizontalFormat,

		storeType: utils.HealthMetric,

		bytes: bufferBytes,
	}

	dataWriter.enrich(&event)

	for file := range dataWriter.trackers {

		dataWriter.move(file)
	}

	time.Sleep(time.Second * 2)

	utils.AssertLogMessageInverse(assertions, "Data Aggregator", "broker", "!!!STACK TRACE for data aggregator")

}

func TestDataWriterV1(t *testing.T) {

	assertions := assert.New(t)

	dataWriter := NewDataWriter(0, nil)

	dataWriter.shutdown = true

	dataWriter.Start()

	os.MkdirAll(utils.TempDir+utils.PathSeparator+"dummy", 0755)

	os.RemoveAll(utils.TempDir + utils.PathSeparator + "dummy")

	dataWriter.write("dummy", []byte{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})

	file, _ := os.Create(utils.TempDir + utils.PathSeparator + "dummy")

	file.Close()

	dataWriter.files["dummy"] = file

	dataWriter.files["dummy2"] = nil

	dataWriter.move("dummy2")

	assertions.Error(dataWriter.write("dummy", []byte{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}))

}

func TestCorruptEventFile(t *testing.T) {

	assertions := assert.New(t)

	fileName := "1747922130§500000-flow§12§1§3"

	time.Sleep(time.Second)

	data := bytes.Buffer{}

	length := make([]byte, 4)

	binary.LittleEndian.PutUint32(length, uint32(len(fileName)))

	data.Write(length)

	data.Write([]byte(fileName))

	data.Write(utils.EOTBytes)

	err := os.WriteFile(utils.EventDir+utils.PathSeparator+"tmp"+utils.PathSeparator+fileName, data.Bytes(), os.ModePerm)

	assertions.NoError(err)

	dataWriter := NewDataWriter(0, nil)

	dataWriter.aggregations["500000-flow"] = make(map[string]utils.MotadataMap)

	dataWriter.Start()

	time.Sleep(time.Millisecond * 200)

	utils.AssertLogMessage(assertions, "Data Writer", "broker", fmt.Sprintf("occurred while moving file %v in data writer %v", fileName, 0))

}

func packEventBatchV3(columns []string, values []interface{}, dataTypes []byte) []byte {

	buffer := bytes.Buffer{}

	var eventSource string

	for index, column := range columns {

		if column == utils.EventSource {

			eventSource = codec.ToString(values[index])

			break
		}

	}

	// 2 bytes event source length

	codec.EncodeINT16Value(int16(len(eventSource)), &buffer)

	buffer.WriteString(eventSource)

	for index, column := range columns {

		if column == utils.EventSource {

			continue
		}

		buffer.WriteByte(dataTypes[index])

		// 4 bytes column length
		codec.EncodeINT32Value(int32(len(column)), &buffer)

		// column
		buffer.WriteString(column)

		if dataTypes[index] == datastore.StringColumn {

			//4 bytes value length

			codec.EncodeINT32Value(int32(len(codec.ToString(values[index]))), &buffer)

			//value
			buffer.WriteString(codec.ToString(values[index]))

		} else {

			codec.EncodeINT64Value(int64(codec.ToINT(values[index])), &buffer)
		}

	}

	return buffer.Bytes()
}

func packMetricBatchV4(columns []string, values []interface{}, dataTypes []byte, objectId int32, instance string) []byte {

	buffer := &bytes.Buffer{}

	codec.EncodeINT32Value(objectId, buffer)

	codec.EncodeINT32Value(int32(len(instance)), buffer)

	buffer.WriteString(instance)

	for index, column := range columns {

		buffer.WriteByte(dataTypes[index])

		codec.EncodeINT32Value(int32(len(column)), buffer)

		buffer.WriteString(column)

		if dataTypes[index] == datastore.StringColumn {

			value := codec.ToString(values[index])

			codec.EncodeINT32Value(int32(len(value)), buffer)

			buffer.WriteString(value)

		} else if dataTypes[index] == datastore.IntegerColumn {

			codec.EncodeINT64Value(int64(codec.ToINT(values[index])), buffer)

		} else {

			codec.EncodeFLOAT64Value(utils.ToFlOAT(values[index]), buffer)

		}

	}

	return buffer.Bytes()
}
