/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>			Motadata-5190  Migrated constants from datastore to utils to match SonarQube Standard
* 2025-03-05             Vedant Dokania         Motadata-5451  Status Flap New datastore type introduced and new map for resolving ordinals new testcases
* 2025-04-02			 Dhaval <PERSON>ra			<PERSON>tadata-4859  Added Test Case For 'WriteBlobColumnValues' method
* 2025-05-02             Vedant <PERSON><PERSON><PERSON>tadata-6080  Instead of removing whole plugin from indexable , remove particular columns from indexable
* 2025-05-05			 Swapnil <PERSON><PERSON>-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-06-04             Aashil Shah            MOTADATA-5780 Test Case Refactoring and initialized IOCPWorkers
 */

package datastore

import (
	"encoding/json"
	"github.com/kelindar/bitmap"
	cmap "github.com/orcaman/concurrent-map"
	"github.com/stretchr/testify/assert"
	"math/rand"
	"motadatadatastore/codec"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"
	"time"
)

var (
	storeEncoder = codec.NewEncoder(utils.NewMemoryPool(6, utils.MaxPoolLength, false, utils.DefaultBlobPools))

	memoryPool = utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools)

	encoder = codec.NewEncoder(memoryPool)

	decoder = codec.NewDecoder(memoryPool)

	keyBuffers [][]byte

	valueBuffers [][]byte

	batchDiskIOEvents []storage.DiskIOEventBatch

	events []storage.DiskIOEvent

	tokenizer *utils.Tokenizer

	waitGroup *sync.WaitGroup
)

func TestMain(m *testing.M) {

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	if err != nil {
		panic(err)
	}

	utils.InitTestSetup()

	utils.EnvironmentType = utils.DatastoreTestEnvironment

	if utils.InitConfigs(bytes) {

		tokenizer = &utils.Tokenizer{

			Tokens: make([]string, utils.TokenizerLength),
		}

		utils.CleanUpStores()

		Init()

		keyBuffers = make([][]byte, 2)

		valueBuffers = make([][]byte, 2)

		for i := 0; i < 2; i++ {

			keyBuffers[i] = make([]byte, utils.MaxValueBufferBytes)

			valueBuffers[i] = make([]byte, utils.MaxValueBufferBytes)
		}

		utils.DiskIOWorkers = 15

		utils.IOCPWorkers = 16

		ioWorkers := make([]*storage.DiskIOWorker, utils.DiskIOWorkers)

		for id := range ioWorkers {

			ioWorkers[id] = storage.NewIOWorker(id)

			ioWorkers[id].Start()
		}

		batchDiskIOEvents = make([]storage.DiskIOEventBatch, utils.MaxStoreParts)

		events = make([]storage.DiskIOEvent, utils.MaxStoreParts)

		for i := range batchDiskIOEvents {

			batchDiskIOEvents[i] = storage.DiskIOEventBatch{}
		}

		for i := range events {

			events[i] = storage.DiskIOEvent{}
		}

		waitGroup = &sync.WaitGroup{}

		m.Run()

		for _, worker := range ioWorkers {

			worker.ShutdownNotifications <- true
		}
	}
}

func TestWriteBlobColumnMultipleEncodingFortinetTrafficLog(t *testing.T) {

	store := GetStore("blob-insertion-fortinet-traffic", utils.Log, true, true, encoder, tokenizer)

	messageBytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + "test-data-files" + utils.PathSeparator + "blob-messages-fortinet-traffic")

	messages := strings.Split(string(messageBytes), "\n")

	assertions := assert.New(t)

	assertions.Equal(20000, testBlobIO("123456789^zstd-key^0", messages, store, assertions))

	assertions.Equal(20000, testBlobIO("123456789^snappy-key^0", messages, store, assertions))

}

func TestWriteBlobColumnMultipleEncodingFortinetUTMLog(t *testing.T) {

	store := GetStore("blob-insertion-fortinet-utm", utils.Log, true, true, encoder, tokenizer)

	messageBytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + "test-data-files" + utils.PathSeparator + "blob-messages-fortinet-utm")

	messages := strings.Split(string(messageBytes), "\n")

	assertions := assert.New(t)

	assertions.Equal(20000, testBlobIO("123456789^zstd-key^0", messages, store, assertions))

	assertions.Equal(20000, testBlobIO("123456789^snappy-key^0", messages, store, assertions))

}

func TestWriteBlobColumnMultipleEncodingFortinetLinuxSyslog(t *testing.T) {

	store := GetStore("blob-insertion-syslog", utils.Log, true, true, encoder, tokenizer)

	messageBytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + "test-data-files" + utils.PathSeparator + "blob-messages-linux-syslog")

	messages := strings.Split(string(messageBytes), "\n")

	for i := range messages {

		messages[i] = strings.TrimSuffix(messages[i], "\r")
	}

	assertions := assert.New(t)

	assertions.Equal(16719, testBlobIO("123456789^zstd-key^0", messages, store, assertions))

	assertions.Equal(16719, testBlobIO("123456789^snappy-key^0", messages, store, assertions))

}

// existing encoding and rewriting the key
func TestWriteBlobColumnMultipleEncodingV1(t *testing.T) {

	store := GetStore("blob-insertion-1", utils.Log, true, true, encoder, tokenizer)

	messages := []string{generateRandomString(10)}

	assertions := assert.New(t)

	encoding := GetBlobEncoding(len(messages[0]), 1)

	assertions.Equal(codec.Snappy, encoding)

	assertions.Equal(1, testBlobIO("123456789^encoding-key^0", messages, store, assertions))

	messages = []string{generateRandomString(10000)}

	encoding = GetBlobEncoding(len(messages[0]), 1)

	assertions.Equal(codec.Zstd, encoding)

	assertions.Equal(1, testBlobIO("123456789^encoding-key^0", messages, store, assertions))

	messages = []string{generateRandomString(100)}

	encoding = GetBlobEncoding(len(messages[0]), 1)

	assertions.Equal(codec.Snappy, encoding)

	assertions.Equal(1, testBlobIO("123456789^encoding-key^0", messages, store, assertions))

}

func TestGetBlobEncoding(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(codec.Snappy, GetBlobEncoding(1000, 10))

	assertions.Equal(codec.Zstd, GetBlobEncoding(10000, 10))
}

func TestRemoveStoreVersion1(t *testing.T) {

	pool := utils.NewMemoryPool(10, 10000, false, utils.DefaultBlobPools)

	encoder := codec.NewEncoder(pool)

	store := GetStore("remove-store-version1", utils.PerformanceMetric, true, true, encoder, tokenizer)

	assertions := assert.New(t)

	assertions.NotNil(store)

	store.Close(encoder)

	assertions.True(store.IsClosed())

	RemoveStore(store.GetName())

	value, _ := stores.Get(store.GetName())

	assertions.Nil(value)

	store = GetStore("remove-store-version1", utils.PerformanceMetric, false, true, encoder, tokenizer)

	assertions.NotNil(store)

	assertions.False(store.IsClosed())

}

func TestRemoveStoreVersion2(t *testing.T) {

	key := "1^system.cpu.percent^0"

	expectedValueBytes := []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20}

	store := GetStore("remove-store-version2", utils.PerformanceMetric, true, true, encoder, tokenizer)

	err := store.Put([]byte(key), append(make([]byte, utils.MaxValueBytes), expectedValueBytes...), encoder, tokenizer)

	assert.Nil(t, err)

	err = store.Sync(encoder)

	assert.Nil(t, err)

	go func() {

		readerTokenizer := &utils.Tokenizer{

			Tokens: make([]string, 10),
		}

		bytes := make([]byte, 10000)

		for i := 0; i < 100; i++ {

			store := GetStore("remove-store-version2", utils.PerformanceMetric, false, true, encoder, readerTokenizer)

			found, valueBytes, err := store.Get([]byte(key), bytes, encoder, events[0], waitGroup, readerTokenizer, false)

			assert.Nil(t, err)

			assert.True(t, found)

			assert.Equal(t, expectedValueBytes, valueBytes)

			time.Sleep(time.Millisecond * 10)
		}

	}()

	go func() {

		readerTokenizer := &utils.Tokenizer{

			Tokens: make([]string, 10),
		}

		time.Sleep(time.Millisecond * 2)

		store := GetStore("remove-store-version2", utils.PerformanceMetric, false, true, encoder, readerTokenizer)

		store.Close(encoder)

		RemoveStore(store.GetName())

	}()

	time.Sleep(time.Second * 5)

}

func TestOpenCorruptedStore(t *testing.T) {

	store := GetStore("corrupted-store", utils.PerformanceMetric, true, true, storeEncoder, tokenizer)

	Close()

	_ = os.Remove(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + store.GetName() + utils.PathSeparator + utils.MetadataFile)

	Init()

	store = GetStore("corrupted-store", utils.PerformanceMetric, false, true, storeEncoder, tokenizer)

	assert.NotNil(t, store)

	store.Close(encoder)
}

func TestUpdateStoreCacheMapping(t *testing.T) {

	assertions := assert.New(t)

	store := GetStore("update-store", utils.Mapping, true, true, dataStoreEncoder, tokenizer)

	assertions.NotNil(store)

	UpdateStoreCacheMapping(store.GetName(), 100)

	store.Close(encoder)
}

// Bug:- #24492 store should not be created if it does not exist by the reader

func TestOpenNonExistentStore(t *testing.T) {

	store := GetStore("store-does-not-exist", utils.None, false, true, storeEncoder, tokenizer)

	assert.Nil(t, store)
}

func TestAddAggregationMetric(t *testing.T) {

	assertions := assert.New(t)

	_, contains := verticalAggregations.Get("abc.aggregatable.metric")

	assertions.False(contains)

	UpdateVerticalAggregations("abc.aggregatable.metric", true)

	_, contains = verticalAggregations.Get("abc.aggregatable.metric")

	assertions.True(contains)

	assertions.True(IsAggregationMetric("abc.aggregatable.metric"))

}

func TestAddSearchableColumn(t *testing.T) {

	assertions := assert.New(t)

	contains := searchableColumns.Has("abc.searchable.column")

	assertions.False(contains)

	AddSearchableColumn("abc.searchable.column")

	contains = searchableColumns.Has("abc.searchable.column")

	assertions.True(contains)

	assertions.True(IsSearchableColumn("abc.searchable.column"))

}

func TestIsStoreAvailable(t *testing.T) {

	assertions := assert.New(t)

	assertions.False(IsStoreAvailable("sample-store"))

	store := GetStore("sample-store", utils.PerformanceMetric, true, true, storeEncoder, tokenizer)

	assertions.True(IsStoreAvailable("sample-store"))

	store.Close(storeEncoder)

	err := os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Nil(err)

}

func TestAddFloatingColumn(t *testing.T) {

	assertions := assert.New(t)

	contains := floatingColumns.Has("abc.floating.column")

	assertions.False(contains)

	AddFloatingColumn("abc.floating.column")

	contains = floatingColumns.Has("abc.floating.column")

	assertions.True(contains)

	assertions.True(IsFloatingColumn("abc.floating.column"))

	//MOTADATA-2952
	assertions.True(IsFloatingColumn("ipsla~source.to.destination.avg.latency.ms"))
	assertions.True(IsFloatingColumn("ipsla~source.to.destination.avg.negative.jitter.ms"))
	assertions.True(IsFloatingColumn("ipsla~source.to.destination.avg.positive.jitter.ms"))
	assertions.True(IsFloatingColumn("ipsla~destination.to.source.avg.positive.jitter.ms"))
	assertions.True(IsFloatingColumn("ipsla~destination.to.source.avg.negative.jitter.ms"))
	assertions.True(IsFloatingColumn("ipsla~path.avg.latency"))
}

/*--------------------------Get INT8 Columns-------------------------------------------------------------*/
func TestGetINT8ColumnValues(t *testing.T) {

	assertions := assert.New(t)

	values := []int8{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT8Values(codec.None, values, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex2, dataType, int8Values, int16Values, int32Values, int64Values, float64Values, err := GetINT8ColumnValues(codec.Int8, codec.Int8, bufferBytes[1:], codec.GetEncoding(bufferBytes[0]), "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex2)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int8, dataType)

	assertions.Nil(int16Values)

	assertions.Nil(int32Values)

	assertions.Nil(int64Values)

	assertions.Nil(float64Values)

	assertions.EqualValues(values, int8Values)
}

func TestGetINT8ColumnValuesINT8ToFLOAT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int8{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT8Values(codec.None, values, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex2, dataType, int8Values, int16Values, int32Values, int64Values, float64Values, err := GetINT8ColumnValues(codec.Float64, codec.Int8, bufferBytes[1:], codec.GetEncoding(bufferBytes[0]), "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex2)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Nil(int16Values)

	assertions.Nil(int32Values)

	assertions.Nil(int64Values)

	assertions.Nil(int8Values)

	poolIndex3, expectedValues := encoder.MemoryPool.AcquireFLOAT64Pool(len(values))

	defer encoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex3)

	assertions.EqualValues(codec.INT8ToFLOAT64Values(values, expectedValues), float64Values)

}

func TestGetINT8ColumnValuesINT8ToINT16(t *testing.T) {

	assertions := assert.New(t)

	values := []int8{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT8Values(codec.None, values, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex2, dataType, int8Values, int16Values, int32Values, int64Values, float64Values, err := GetINT8ColumnValues(codec.Int16, codec.Int8, bufferBytes[1:], codec.GetEncoding(bufferBytes[0]), "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT16Pool(poolIndex2)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int16, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int32Values)

	assertions.Nil(int64Values)

	assertions.Nil(int8Values)

	poolIndex3, expectedValues := encoder.MemoryPool.AcquireINT16Pool(len(values))

	defer memoryPool.ReleaseINT16Pool(poolIndex3)

	assertions.EqualValues(codec.INT8ToINT16Values(values, expectedValues), int16Values)

}

func TestGetINT8ColumnValuesINT8ToINT32(t *testing.T) {

	assertions := assert.New(t)

	values := []int8{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT8Values(codec.None, values, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex2, dataType, int8Values, int16Values, int32Values, int64Values, float64Values, err := GetINT8ColumnValues(codec.Int32, codec.Int8, bufferBytes[1:], codec.GetEncoding(bufferBytes[0]), "", "", 0, decoder)

	defer memoryPool.ReleaseINT32Pool(poolIndex2)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int32, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int16Values)

	assertions.Nil(int64Values)

	assertions.Nil(int8Values)

	poolIndex3, expectedValues := encoder.MemoryPool.AcquireINT32Pool(len(values))

	defer encoder.MemoryPool.ReleaseINT32Pool(poolIndex3)

	assertions.EqualValues(codec.INT8ToINT32Values(values, expectedValues), int32Values)

}

func TestGetINT8ColumnValuesINT8ToINT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int8{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT8Values(codec.None, values, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex2, dataType, int8Values, int16Values, int32Values, int64Values, float64Values, err := GetINT8ColumnValues(codec.Int64, codec.Int8, bufferBytes[1:], codec.GetEncoding(bufferBytes[0]), "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex2)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int64, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int16Values)

	assertions.Nil(int32Values)

	assertions.Nil(int8Values)

	poolIndex3, expectedValues := encoder.MemoryPool.AcquireINT64Pool(len(values))

	defer memoryPool.ReleaseINT64Pool(poolIndex3)

	assertions.EqualValues(codec.INT8ToINT64Values(values, expectedValues), int64Values)

}

func TestGetINT8ColumnValuesInvalidDataType(t *testing.T) {

	assertions := assert.New(t)

	values := []int8{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT8Values(codec.None, values, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int8Values, int16Values, int32Values, int64Values, float64Values, err := GetINT8ColumnValues(codec.Invalid, codec.Int8, bufferBytes[1:], codec.GetEncoding(bufferBytes[0]), "", "", 0, decoder)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int16Values)

	assertions.Nil(int32Values)

	assertions.Nil(int8Values)

	assertions.Nil(int64Values)

}

func TestGetINT8ColumnValuesInvalidBytes(t *testing.T) {

	assertions := assert.New(t)

	values := []int8{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT8Values(codec.None, values, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int8Values, int16Values, int32Values, int64Values, float64Values, err := GetINT8ColumnValues(codec.Invalid, codec.Int8, bufferBytes[1:], codec.RLEDictionary, "", "", 0, decoder)

	defer memoryPool.ReleaseINT8Pool(poolIndex)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int16Values)

	assertions.Nil(int32Values)

	assertions.Nil(int8Values)

	assertions.Nil(int64Values)

}

/*--------------------------Get INT16 Columns-------------------------------------------------------------*/

func TestGetINT16ColumnValues(t *testing.T) {

	assertions := assert.New(t)

	values := []int16{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT16Values(codec.None, values, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex2, dataType, int16Values, int32Values, int64Values, float64Values, err := GetINT16ColumnValues(codec.Int16, codec.Int16, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT16Pool(poolIndex2)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int16, dataType)

	assertions.Nil(int32Values)

	assertions.Nil(int64Values)

	assertions.Nil(float64Values)

	assertions.EqualValues(values, int16Values)
}

func TestGetINT16ColumnValuesINT16ToFLOAT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int16{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT16Values(codec.None, values, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex2, dataType, int16Values, int32Values, int64Values, float64Values, err := GetINT16ColumnValues(codec.Float64, codec.Int16, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex2)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Nil(int16Values)

	assertions.Nil(int32Values)

	assertions.Nil(int64Values)

	poolIndex3, expectedValues := encoder.MemoryPool.AcquireFLOAT64Pool(len(values))

	defer encoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex3)

	assertions.NotEqual(-1, poolIndex3)

	assertions.EqualValues(codec.INT16ToFLOAT64Values(values, expectedValues), float64Values)

}

func TestGetINT16ColumnValuesINT16ToINT32(t *testing.T) {

	assertions := assert.New(t)

	values := []int16{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT16Values(codec.None, values, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.NotEqual(-1, poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex2, dataType, int16Values, int32Values, int64Values, float64Values, err := GetINT16ColumnValues(codec.Int32, codec.Int16, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT32Pool(poolIndex2)

	assertions.NotEqual(-1, poolIndex2)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int32, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int16Values)

	assertions.Nil(int64Values)

	poolIndex3, expectedValues := encoder.MemoryPool.AcquireINT32Pool(len(values))

	defer memoryPool.ReleaseINT32Pool(poolIndex3)

	assertions.NotEqual(-1, poolIndex3)

	assertions.EqualValues(codec.INT16ToINT32Values(values, expectedValues), int32Values)

}

func TestGetINT16ColumnValuesINT16ToINT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int16{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT16Values(codec.None, values, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.NotEqual(-1, poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex2, dataType, int16Values, int32Values, int64Values, float64Values, err := GetINT16ColumnValues(codec.Int64, codec.Int16, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex2)

	assertions.NotEqual(-1, poolIndex2)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int64, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int16Values)

	assertions.Nil(int32Values)

	poolIndex3, expectedValues := encoder.MemoryPool.AcquireINT64Pool(len(values))

	defer memoryPool.ReleaseINT64Pool(poolIndex3)

	assertions.NotEqual(-1, poolIndex3)

	assertions.EqualValues(codec.INT16ToINT64Values(values, expectedValues), int64Values)

}

func TestGetINT16ColumnValuesInvalidDataType(t *testing.T) {

	assertions := assert.New(t)

	values := []int16{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT16Values(codec.None, values, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.NotEqual(-1, poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int16Values, int32Values, int64Values, float64Values, err := GetINT16ColumnValues(codec.Invalid, codec.Int16, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int16Values)

	assertions.Nil(int32Values)

	assertions.Nil(int64Values)

}

func TestGetINT16ColumnValuesInvalidBytes(t *testing.T) {

	assertions := assert.New(t)

	values := []int16{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT16Values(codec.None, values, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int16Values, int32Values, int64Values, float64Values, err := GetINT16ColumnValues(codec.Invalid, codec.Int16, codec.RLEDictionary, bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT16Pool(poolIndex)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int16Values)

	assertions.Nil(int32Values)

	assertions.Nil(int64Values)

}

/*--------------------------Get INT24 Columns-------------------------------------------------------------*/

func TestGetINT24ColumnValues(t *testing.T) {

	assertions := assert.New(t)

	values := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int24, 0, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex2, dataType, int32Values, int64Values, float64Values, err := GetINT24ColumnValues(codec.Int24, codec.Int24, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT32Pool(poolIndex2)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int24, dataType)

	assertions.Nil(int64Values)

	assertions.Nil(float64Values)

	assertions.EqualValues(values, int32Values)
}

func TestGetINT24ColumnValuesINT24ToFLOAT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int24, 0, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int32Values, int64Values, float64Values, err := GetINT24ColumnValues(codec.Float64, codec.Int24, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Nil(int32Values)

	assertions.Nil(int64Values)

	poolIndex3, expectedValues := encoder.MemoryPool.AcquireFLOAT64Pool(len(values))

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex3)

	assertions.EqualValues(codec.INT32ToFLOAT64Values(values, expectedValues), float64Values)

}

func TestGetINT24ColumnValuesINT24ToINT32(t *testing.T) {

	assertions := assert.New(t)

	values := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int24, 0, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex2, dataType, int32Values, int64Values, float64Values, err := GetINT24ColumnValues(codec.Int32, codec.Int24, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT32Pool(poolIndex2)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int32, dataType)

	assertions.Nil(int64Values)

	assertions.Nil(float64Values)

	assertions.EqualValues(values, int32Values)

}

func TestGetINT24ColumnValuesINT24ToINT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int24, 0, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex2, dataType, int32Values, int64Values, float64Values, err := GetINT24ColumnValues(codec.Int64, codec.Int24, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex2)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int64, dataType)

	assertions.Nil(int32Values)

	assertions.Nil(float64Values)

	poolIndex3, expectedValues := encoder.MemoryPool.AcquireINT64Pool(len(values))

	defer memoryPool.ReleaseINT64Pool(poolIndex3)

	assertions.EqualValues(codec.INT32ToINT64Values(values, expectedValues), int64Values)

}

func TestGetINT24ColumnValuesInvalidDataType(t *testing.T) {

	assertions := assert.New(t)

	values := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int24, 0, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int32Values, int64Values, float64Values, err := GetINT24ColumnValues(codec.Invalid, codec.Int24, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT32Pool(poolIndex)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int32Values)

	assertions.Nil(int64Values)

}

func TestGetINT24ColumnValuesInvalidBytes(t *testing.T) {

	assertions := assert.New(t)

	values := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int24, 0, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int32Values, int64Values, float64Values, err := GetINT24ColumnValues(codec.Invalid, codec.Int24, codec.RLEDictionary, bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT32Pool(poolIndex)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int32Values)

	assertions.Nil(int64Values)

}

/// ################################################## start from here ############################################################################

/*--------------------------Get INT32 Columns-------------------------------------------------------------*/

func TestGetINT32ColumnValues(t *testing.T) {

	assertions := assert.New(t)

	values := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int32, 0, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int32Values, int64Values, float64Values, err := GetINT32ColumnValues(codec.Int32, codec.Int32, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int32, dataType)

	assertions.Nil(int64Values)

	assertions.Nil(float64Values)

	assertions.EqualValues(values, int32Values)

}

func TestGetINT32ColumnValuesINT32ToFLOAT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int32, 0, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int32Values, int64Values, float64Values, err := GetINT32ColumnValues(codec.Float64, codec.Int32, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Nil(int32Values)

	assertions.Nil(int64Values)

	poolIndex, expectedValues := encoder.MemoryPool.AcquireFLOAT64Pool(len(values))

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.EqualValues(codec.INT32ToFLOAT64Values(values, expectedValues), float64Values)

}

func TestGetINT32ColumnValuesINT32ToINT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int32, 0, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int32Values, int64Values, float64Values, err := GetINT32ColumnValues(codec.Int64, codec.Int32, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int64, dataType)

	assertions.Nil(int32Values)

	assertions.Nil(float64Values)

	poolIndex, expectedValues := encoder.MemoryPool.AcquireINT64Pool(len(values))

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.EqualValues(codec.INT32ToINT64Values(values, expectedValues), int64Values)

}

func TestGetINT32ColumnValuesInvalidDataType(t *testing.T) {

	assertions := assert.New(t)

	values := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int32, 0, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int32Values, int64Values, float64Values, err := GetINT32ColumnValues(codec.Invalid, codec.Int32, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int32Values)

	assertions.Nil(int64Values)

}

func TestGetINT32ColumnValuesInvalidBytes(t *testing.T) {

	assertions := assert.New(t)

	values := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT32Values(values, codec.None, codec.Int32, 0, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int32Values, int64Values, float64Values, err := GetINT32ColumnValues(codec.Invalid, codec.Int32, codec.RLEDictionary, bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT32Pool(poolIndex)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int32Values)

	assertions.Nil(int64Values)

}

/*--------------------------Get INT40 Columns-------------------------------------------------------------*/

func TestGetINT40ColumnValues(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int40, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT40ColumnValues(codec.Int40, codec.Int40, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int40, dataType)

	assertions.Nil(float64Values)

	assertions.EqualValues(values, int64Values)

}

func TestGetINT40ColumnValuesINT40ToFLOAT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int40, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT40ColumnValues(codec.Float64, codec.Int40, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Nil(int64Values)

	poolIndex, expectedValues := encoder.MemoryPool.AcquireFLOAT64Pool(len(values))

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.EqualValues(codec.INT64ToFLOAT64Values(values, expectedValues), float64Values)

}

func TestGetINT40ColumnValuesINT40ToINT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int40, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT40ColumnValues(codec.Int64, codec.Int40, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int64, dataType)

	assertions.Nil(float64Values)

	assertions.Equal(values, int64Values)

}

func TestGetINT40ColumnValuesInvalidDataType(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int40, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT40ColumnValues(codec.Invalid, codec.Int40, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int64Values)

}

func TestGetINT40ColumnValuesInvalidBytes(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int40, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT40ColumnValues(codec.Invalid, codec.Int40, codec.RLEDictionary, bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int64Values)

}

/*--------------------------Get INT48 Columns-------------------------------------------------------------*/

func TestGetINT48ColumnValues(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int48, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT48ColumnValues(codec.Int48, codec.Int48, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int48, dataType)

	assertions.Nil(float64Values)

	assertions.EqualValues(values, int64Values)

}

func TestGetINT48ColumnValuesINT48ToFLOAT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int48, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT48ColumnValues(codec.Float64, codec.Int48, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Nil(int64Values)

	poolIndex, expectedValues := encoder.MemoryPool.AcquireFLOAT64Pool(len(values))

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.EqualValues(codec.INT64ToFLOAT64Values(values, expectedValues), float64Values)

}

func TestGetINT48ColumnValuesINT48ToINT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int48, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT48ColumnValues(codec.Int64, codec.Int48, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int64, dataType)

	assertions.Nil(float64Values)

	assertions.Equal(values, int64Values)

}

func TestGetINT48ColumnValuesInvalidDataType(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int48, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT48ColumnValues(codec.Invalid, codec.Int48, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int64Values)

}

func TestGetINT48ColumnValuesInvalidBytes(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int48, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT48ColumnValues(codec.Invalid, codec.Int48, codec.RLEDictionary, bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int64Values)

}

/*--------------------------Get INT56 Columns-------------------------------------------------------------*/

func TestGetINT56ColumnValues(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int56, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT56ColumnValues(codec.Int56, codec.Int56, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int56, dataType)

	assertions.Nil(float64Values)

	assertions.EqualValues(values, int64Values)

}

func TestGetINT56ColumnValuesINT56ToFLOAT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int56, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT56ColumnValues(codec.Float64, codec.Int56, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Nil(int64Values)

	poolIndex, expectedValues := encoder.MemoryPool.AcquireFLOAT64Pool(len(values))

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.EqualValues(codec.INT64ToFLOAT64Values(values, expectedValues), float64Values)

}

func TestGetINT56ColumnValuesINT56ToINT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int56, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT56ColumnValues(codec.Int64, codec.Int56, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int64, dataType)

	assertions.Nil(float64Values)

	assertions.Equal(values, int64Values)

}

func TestGetINT56ColumnValuesInvalidDataType(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int56, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT56ColumnValues(codec.Invalid, codec.Int56, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int64Values)

}

func TestGetINT56ColumnValuesInvalidBytes(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int56, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT56ColumnValues(codec.Invalid, codec.Int56, codec.RLEDictionary, bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int64Values)

}

/*--------------------------Get INT64 Columns-------------------------------------------------------------*/

func TestGetINT64ColumnValuesINT64ToFLOAT64(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int64, 0)

	defer memoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT64ColumnValues(codec.Float64, codec.Int64, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Nil(int64Values)

	poolIndex, expectedValues := encoder.MemoryPool.AcquireFLOAT64Pool(len(values))

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.EqualValues(codec.INT64ToFLOAT64Values(values, expectedValues), float64Values)

}

func TestGetINT64ColumnValues(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int64, 0)

	defer memoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT64ColumnValues(codec.Int64, codec.Int64, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Int64, dataType)

	assertions.Nil(float64Values)

	assertions.Equal(values, int64Values)

}

func TestGetINT64ColumnValuesInvalidDataType(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int64, 0)

	defer memoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT64ColumnValues(codec.Invalid, codec.Int64, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int64Values)

}

func TestGetINT64ColumnValuesInvalidBytes(t *testing.T) {

	assertions := assert.New(t)

	values := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeINT64Values(values, codec.None, codec.Int64, 0)

	defer memoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, int64Values, float64Values, err := GetINT64ColumnValues(codec.Invalid, codec.Int64, codec.RLEDictionary, bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseINT64Pool(poolIndex)

	assertions.NotNil(err)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

	assertions.Nil(int64Values)

}

// ################################################################ above are needed to ad releasebytepool ############################################################################################

/*--------------------------Get Float8 Columns-------------------------------------------------------------*/

func TestGetFLOAT8ColumnValues(t *testing.T) {

	assertions := assert.New(t)

	values := []float64{1.32, 2.56, 3, 67, 4.55, 5.64, 6.09, 7.54, 8.23, 9.8, 10.09}

	poolIndex, bufferBytes, err := encoder.EncodeFLOAT64Values(values, codec.None, codec.Float8, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, float64Values, err := GetFLOAT8ColumnValues(codec.Float8, codec.Float8, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float8, dataType)

	assertions.Equal(values, float64Values)

}

func TestGetFLOAT8ColumnValuesFLOAT8ToFLOAT16(t *testing.T) {

	assertions := assert.New(t)

	values := []float64{1.32, 2.56, 3, 67, 4.55, 5.64, 6.09, 7.54, 8.23, 9.8, 10.09}

	poolIndex, bufferBytes, err := encoder.EncodeFLOAT64Values(values, codec.None, codec.Float8, 0)

	defer memoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, float64Values, err := GetFLOAT8ColumnValues(codec.Float16, codec.Float8, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float16, dataType)

	assertions.Equal(values, float64Values)

}

func TestGetFLOAT8ColumnValuesFLOAT8ToINT64(t *testing.T) {

	assertions := assert.New(t)

	values := []float64{1, 2, 3, 67, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeFLOAT64Values(values, codec.None, codec.Float8, 0)

	defer memoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, float64Values, err := GetFLOAT8ColumnValues(codec.Int64, codec.Float8, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Equal(values, float64Values)

}

func TestGetFLOAT16ColumnValuesFLOAT16ToINT64(t *testing.T) {

	assertions := assert.New(t)

	values := []float64{1, 2, 3, 67, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeFLOAT64Values(values, codec.None, codec.Float16, 0)

	defer memoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, float64Values, err := GetFLOAT16ColumnValues(codec.Int64, codec.Float16, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer memoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Equal(values, float64Values)

}

func TestGetFLOAT64ColumnValuesFLOAT16ToINT64(t *testing.T) {

	assertions := assert.New(t)

	values := []float64{1, 2, 3, 67, 4, 5, 6, 7, 8, 9, 10}

	poolIndex, bufferBytes, err := encoder.EncodeFLOAT64Values(values, codec.None, codec.Float64, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, float64Values, err := GetFLOAT64ColumnValues(codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Equal(values, float64Values)

}

func TestGetStringColumnValuesFLOAT8Values(t *testing.T) {

	assertions := assert.New(t)

	values := []string{"test1", "test2", "test3", "test4", "test5"}

	poolIndex, bufferBytes, err := encoder.EncodeStringValues(values, codec.None, 0, utils.Empty)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, stringValues, err := GetStringColumnValues(codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseStringPool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.String, dataType)

	assertions.Equal(values, stringValues)

}

func TestGetStringColumnValuesINT8Values(t *testing.T) {

	assertions := assert.New(t)

	values := []string{"test1", "test2", "test3", "test4", "test5"}

	poolIndex, bufferBytes, err := encoder.EncodeStringValues(values, codec.None, 0, utils.Empty)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, stringValues, err := GetStringColumnValues(codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseStringPool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.String, dataType)

	assertions.Equal(values, stringValues)

}

func TestGetFLOAT8ColumnValuesFLOAT8ToFLOAT64(t *testing.T) {

	assertions := assert.New(t)

	values := []float64{1.32, 2.56, 3, 67, 4.55, 5.64, 6.09, 7.54, 8.23, 9.8, 10.09}

	poolIndex, bufferBytes, err := encoder.EncodeFLOAT64Values(values, codec.None, codec.Float8, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, float64Values, err := GetFLOAT8ColumnValues(codec.Float64, codec.Float8, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Equal(values, float64Values)

}

func TestGetFLOAT8ColumnValuesInvalidDataType(t *testing.T) {

	assertions := assert.New(t)

	values := []float64{1.32, 2.56, 3, 67, 4.55, 5.64, 6.09, 7.54, 8.23, 9.8, 10.09}

	poolIndex, bufferBytes, err := encoder.EncodeFLOAT64Values(values, codec.None, codec.Float8, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, float64Values, err := GetFLOAT8ColumnValues(codec.Invalid, codec.Float8, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

}

func TestGetFLOAT8ColumnValuesInvalidBytes(t *testing.T) {

	assertions := assert.New(t)

	values := []float64{1.32, 2.56, 3, 67, 4.55, 5.64, 6.09, 7.54, 8.23, 9.8, 10.09}

	poolIndex, bufferBytes, err := encoder.EncodeFLOAT64Values(values, codec.None, codec.Float8, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, float64Values, err := GetFLOAT8ColumnValues(codec.Invalid, codec.Float8, codec.RLEDictionary, bufferBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

}

/*--------------------------Get Float16 Columns-------------------------------------------------------------*/

func TestGetFLOAT16ColumnValues(t *testing.T) {

	assertions := assert.New(t)

	values := []float64{1.32, 2.56, 3, 67, 4.55, 5.64, 6.09, 7.54, 16.23, 9.16, 10.09}

	poolIndex, bufferBytes, err := encoder.EncodeFLOAT64Values(values, codec.None, codec.Float16, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, float64Values, err := GetFLOAT16ColumnValues(codec.Float16, codec.Float16, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float16, dataType)

	assertions.Equal(values, float64Values)

}

func TestGetFLOAT16ColumnValuesFLOAT16ToFLOAT64(t *testing.T) {

	assertions := assert.New(t)

	values := []float64{1.32, 2.56, 3, 67, 4.55, 5.64, 6.09, 7.54, 16.23, 9.16, 10.09}

	poolIndex, bufferBytes, err := encoder.EncodeFLOAT64Values(values, codec.None, codec.Float16, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, float64Values, err := GetFLOAT16ColumnValues(codec.Float64, codec.Float16, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Equal(values, float64Values)

}

func TestGetFLOAT16ColumnValuesInvalidDataType(t *testing.T) {

	assertions := assert.New(t)

	values := []float64{1.32, 2.56, 3, 67, 4.55, 5.64, 6.09, 7.54, 16.23, 9.16, 10.09}

	poolIndex, bufferBytes, err := encoder.EncodeFLOAT64Values(values, codec.None, codec.Float16, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, float64Values, err := GetFLOAT16ColumnValues(codec.Invalid, codec.Float16, codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

}

func TestGetFLOAT16ColumnValuesInvalidBytes(t *testing.T) {

	assertions := assert.New(t)

	values := []float64{1.32, 2.56, 3, 67, 4.55, 5.64, 6.09, 7.54, 16.23, 9.16, 10.09}

	poolIndex, bufferBytes, err := encoder.EncodeFLOAT64Values(values, codec.None, codec.Float16, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, float64Values, err := GetFLOAT16ColumnValues(codec.Invalid, codec.Float16, codec.RLEDictionary, bufferBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Invalid, dataType)

	assertions.Nil(float64Values)

}

/*--------------------------Get Float64 Columns-------------------------------------------------------------*/

func TestGetFLOAT64ColumnValues(t *testing.T) {

	assertions := assert.New(t)

	values := []float64{1.32, 2.56, 3, 67, 4.55, 5.64, 6.09, 7.54, 64.23, 9.64, 10.09}

	poolIndex, bufferBytes, err := encoder.EncodeFLOAT64Values(values, codec.None, codec.Float64, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, float64Values, err := GetFLOAT64ColumnValues(codec.GetEncoding(bufferBytes[0]), bufferBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Equal(values, float64Values)

}

func TestGetFLOAT64ColumnValuesInvalidBytes(t *testing.T) {

	assertions := assert.New(t)

	values := []float64{1.32, 2.56, 3, 67, 4.55, 5.64, 6.09, 7.54, 64.23, 9.64, 10.09}

	poolIndex, bufferBytes, err := encoder.EncodeFLOAT64Values(values, codec.None, codec.Float64, 0)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, dataType, float64Values, err := GetFLOAT64ColumnValues(codec.RLEDictionary, bufferBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Equal(-1, poolIndex)

	assertions.Equal(codec.Float64, dataType)

	assertions.Nil(float64Values)

}

func TestAppendINT8Value(t *testing.T) {

	assertions := assert.New(t)

	int8Values := []int8{1, 2, 3, 4, 5, 6}

	poolIndex, bufferBytes, err := AppendINT8Value(len(int8Values), 7, codec.None, int8Values, encoder)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, values, err := decoder.DecodeINT8Values(codec.GetEncoding(bufferBytes[utils.MaxValueBytes]), bufferBytes[utils.MaxValueBytes+1:], "", "", 0)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.EqualValues(append(int8Values, 7), values)

	poolIndex, bufferBytes, err = AppendINT8Value(0, 0, codec.None, int8Values, encoder)

	defer decoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, values, err = decoder.DecodeINT8Values(codec.GetEncoding(bufferBytes[utils.MaxValueBytes]), bufferBytes[utils.MaxValueBytes+1:], "", "", 0)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.EqualValues([]int8{0, 1, 2, 3, 4, 5, 6}, values)
}

func TestAppendINT32Value(t *testing.T) {

	assertions := assert.New(t)

	int32Values := []int32{1, 2, 3, 4, 5, 6}

	poolIndex, bufferBytes, err := AppendINT32Value(len(int32Values), 7, codec.None, int32Values, encoder)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, values, err := decoder.DecodeINT32Values(codec.GetEncoding(bufferBytes[utils.MaxValueBytes]), codec.Int32, bufferBytes[utils.MaxValueBytes+1:], "", "", 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.EqualValues(append(int32Values, 7), values)

	poolIndex, bufferBytes, err = AppendINT32Value(0, 0, codec.None, int32Values, encoder)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, values, err = decoder.DecodeINT32Values(codec.GetEncoding(bufferBytes[utils.MaxValueBytes]), codec.Int32, bufferBytes[utils.MaxValueBytes+1:], "", "", 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.EqualValues([]int32{0, 1, 2, 3, 4, 5, 6}, values)
}

func TestAppendStringValue(t *testing.T) {

	assertions := assert.New(t)

	stringValues := []string{"value1"}

	poolIndex, bufferBytes, err := AppendStringValue(len(stringValues), "value2", codec.None, stringValues, encoder, "")

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, values, err := decoder.DecodeStringValues(codec.GetEncoding(bufferBytes[utils.MaxValueBytes]), bufferBytes[utils.MaxValueBytes+1:], "", "", 0)

	defer decoder.MemoryPool.ReleaseStringPool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.EqualValues(append(stringValues, "value2"), values)

	poolIndex, bufferBytes, err = AppendStringValue(0, "value0", codec.None, stringValues, encoder, "")

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	poolIndex, values, err = decoder.DecodeStringValues(codec.GetEncoding(bufferBytes[utils.MaxValueBytes]), bufferBytes[utils.MaxValueBytes+1:], "", "", 0)

	defer decoder.MemoryPool.ReleaseStringPool(poolIndex)

	assertions.Nil(err)

	assertions.NotEqual(-1, poolIndex)

	assertions.EqualValues([]string{"value0", "value1"}, values)
}

func TestAppendValue(t *testing.T) {

	assertions := assert.New(t)

	values := []interface{}{codec.Int8, int64(1), int64(2)}

	values = AppendValue(len(values)-1, 0.2, codec.Int8, codec.Float8, values)

	assertions.EqualValues([]interface{}{codec.Float8, float64(1), float64(2), 0.2}, values)

	values = AppendValue(len(values)-1, 0.3, codec.Float8, codec.Float8, values)

	assertions.EqualValues([]interface{}{codec.Float8, float64(1), float64(2), 0.2, 0.3}, values)

}

func TestAppendTimeTick(t *testing.T) {

	assertions := assert.New(t)

	startTick := utils.UnixToSeconds(time.Now().Unix())

	expectedTicks := []interface{}{startTick, startTick + 1, startTick + 2}

	var ticks []interface{}

	ticks, _ = AppendTimeTick(startTick+1, ticks)

	ticks, _ = AppendTimeTick(startTick+2, ticks)

	ticks, _ = AppendTimeTick(startTick, ticks)

	assertions.EqualValues(expectedTicks, ticks)
}

//Test common functions

func TestGetEventByEventType(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(GetDatastoreType(utils.Log), "Log")

	assertions.Equal(GetDatastoreType(utils.Flow), "Flow")

	assertions.Equal(GetDatastoreType(utils.Trap), "Trap")

	assertions.Equal(GetDatastoreType(utils.MetricPolicy), "MetricPolicy")

	assertions.Equal(GetDatastoreType(utils.Audit), "Audit")

	assertions.Equal(GetDatastoreType(utils.Notification), "User Notification")

	assertions.Equal(GetDatastoreType(utils.EventPolicy), "EventPolicy")

	assertions.Equal(GetDatastoreType(utils.CorrelationMetric), "Correlated Metric")

	assertions.Equal(GetDatastoreType(utils.MetricIndex), "MetricIndex")

	assertions.Equal(GetDatastoreType(utils.CorrelatedMetricIndex), "CorrelatedMetricIndex")

	assertions.Equal(GetDatastoreType(utils.LogIndex), "LogIndex")

	assertions.Equal(GetDatastoreType(utils.FlowIndex), "FlowIndex")

	assertions.Equal(GetDatastoreType(utils.TrapIndex), "TrapIndex")

	assertions.Equal(GetDatastoreType(utils.NotificationIndex), "NotificationIndex")

	assertions.Equal(GetDatastoreType(utils.AuditIndex), "AuditIndex")

	assertions.Equal(GetDatastoreType(utils.PolicyIndex), "PolicyIndex")

	assertions.Equal(GetDatastoreType(10000), "")

	assertions.Equal(GetDatastoreType(utils.Index), "Index")

	assertions.Equal(GetDatastoreType(utils.HealthMetric), "HealthMetric")

	assertions.Equal(GetDatastoreType(utils.HealthMetricIndex), "HealthMetricIndex")

	assertions.Equal(GetDatastoreType(utils.Compliance), "Compliance")

	assertions.Equal(GetDatastoreType(utils.ObjectStatusFlapMetric), "StatusFlapMetric")

	assertions.Equal(GetDatastoreType(utils.ComplianceIndex), "ComplianceIndex")

}

func generateRandomString(length int) string {

	var chars = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0987654321")

	runes := make([]rune, length)

	for i := range runes {

		runes[i] = chars[rand.Intn(len(chars))]

	}

	return string(runes)

}

//Blob function

func TestWriteBlobColumnValues(t *testing.T) {

	assertions := assert.New(t)

	keyBytes := []byte("blob.column")

	store := GetStore("blob-column", utils.Log, true, true, storeEncoder, tokenizer)

	poolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	values := make([]string, 5000)

	for i := 0; i < 5000; i++ {

		values[i] = generateRandomString(1024)
	}

	waitGroup := sync.WaitGroup{}

	event := storage.DiskIOEvent{}

	blobEvent := BlobEvent{}

	blobEvent.KeyBytes = keyBytes

	blobEvent.ValueBytes = valueBytes

	blobEvent.Values = values

	blobEvent.Encoder = encoder

	blobEvent.Decoder = decoder

	blobEvent.Store = store

	blobEvent.Padding = utils.MaxValueBytes

	blobEvent.Tokenizer = tokenizer

	blobEvent.DiskIOEvent = event

	blobEvent.WaitGroup = &waitGroup

	blobEvent.Encoding = codec.None

	headerIndex, headerBytes, err := WriteBlobColumnValues(blobEvent)

	assertions.Nil(err)

	defer encoder.MemoryPool.ReleaseBytePool(headerIndex)

	err = store.Put(keyBytes, headerBytes, encoder, tokenizer)

	assertions.Nil(err)

	found, bytes, err := store.Get(keyBytes, valueBytes, encoder, event, &waitGroup, tokenizer, true)

	assertions.True(found)

	assertions.NotNil(bytes)

	assertions.Nil(err)

	results := make([]string, 0)

	for index := 1; index < len(bytes); index += 13 {

		var decodedValues []string

		blobEvent.ValueBytes = bytes[index : index+13]

		blobEvent.KeyBytes = keyBytes

		poolIndex, decodedValues, err = GetBlobColumnValues(blobEvent)

		assertions.Nil(err)

		results = append(results, decodedValues...)

		encoder.MemoryPool.ReleaseStringPool(poolIndex)
	}

	assertions.Nil(err)

	assertions.Equal(values, results)

	store.Close(encoder)

}

func TestIndexableColumnsV1(t *testing.T) {

	assertions := assert.New(t)

	utils.PublisherNotifications = make(chan utils.MotadataMap, 1_00_000)

	plugin := "dummy-plugin"

	columns := map[string]interface{}{
		"column1": utils.Empty,
		"column2": utils.Empty,
	}

	assertions.False(IsIndexablePlugin(plugin))

	AlterIndexableColumns(plugin, columns, utils.Add)

	assertions.True(IsIndexablePlugin(plugin))

}

func TestGetDatastoreType(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(GetDatastoreType(utils.PolicyResult), "Policy Result")

	assertions.Equal(GetDatastoreType(utils.RunbookWorklog), "Runbook Worklog")
}

func TestGetStoreName(t *testing.T) {

	tick := utils.UnixToSeconds(time.Now().Unix())

	name := "dummystore"

	assert.Equal(t, GetStoreName(tick, name, utils.Empty), utils.SecondsToDate(tick)+utils.HyphenSeparator+name)
}

func TestIsGarbageColumn(t *testing.T) {

	AddGarbageColumn("monitor.id")

	assert.True(t, IsGarbageColumn("monitor.id"))
}

func TestIsNonIndexableColumn(t *testing.T) {

	assert.True(t, IsNonIndexableColumn(utils.Event))
}

func TestHorizontalAggregationView(t *testing.T) {

	utils.Aggregation = false

	assertions := assert.New(t)

	assertions.False(IsAggregationMetric("system.cpu.percent"))

	assertions.False(IsHorizontalAggregationFound("50000-flow"))

	assertions.False(IsHorizontalAggregationExist("50000-flow", "volume.bytes.per.sec"))

	assertions.False(IsHorizontalAggregationExist("50000-flow", "volume.bytes.per.sec"))

	assertions.Equal(GetHorizontalAggregation("50000-flow", nil, nil, nil, "", false), utils.Empty)

	utils.Aggregation = true

	AddHorizontalAggregation("50000-flow", "50000-flow"+utils.AggregationSeparator+"3", nil)
	AddHorizontalAggregation("50000-flow", "50000-flow"+utils.AggregationSeparator+"5", nil)
	AddHorizontalAggregation("50000-flow", "50000-flow"+utils.AggregationSeparator+"10", nil)
	AddHorizontalAggregation("50000-flow", "50000-flow"+utils.AggregationSeparator+"11", nil)

	assertions.Equal(GetHorizontalAggregation("50000-flow", nil, nil, tokenizer, "", false), "50000-flow"+utils.AggregationSeparator+"3")

	RemoveHorizontalAggregation("50000-flow", "50000-flow"+utils.AggregationSeparator+"3")

	assertions.Equal(GetHorizontalAggregation("50000-flow", nil, nil, tokenizer, "", false), "50000-flow"+utils.AggregationSeparator+"5")

}

func TestGetTimeTicks(t *testing.T) {

	bytes := []byte{2, 0, 0, 0, 0, 0, 0, 2, 0, 0, 2}

	decoder := codec.NewDecoder(utils.NewMemoryPool(10, 1, false, utils.DefaultBlobPools))

	_, _, err := GetTimeTicks(bytes, 0, decoder)

	assert.NotNil(t, err)

	decoder = codec.NewDecoder(utils.NewMemoryPool(10, 100, false, utils.DefaultBlobPools))

	_, _, err = GetTimeTicks(bytes, 0, decoder)

	assert.NotNil(t, err)

	bytes = []byte{11, 0, 0, 0, 0, 0, 0, 2, 0, 0, 2}

	_, _, err = GetTimeTicks(bytes, 0, decoder)

	assert.NotNil(t, err)

}

func TestWriteBlobColumnValue(t *testing.T) {

	store, err := storage.OpenOrCreateStore("dummy-testing-write-blob-type-1", utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	store.MarkClosed(encoder)

	_, _, err = writeBlobColumnValue([]byte("testing-ke1"), []byte("value1"), 1, encoder, store, tokenizer, codec.None)

	assert.NotNil(t, err)

	blobEvent := BlobEvent{}

	blobEvent.KeyBytes = []byte("testing-ke1")

	blobEvent.ValueBytes = utils.CheckSumV1Bytes

	blobEvent.Values = []string{""}

	blobEvent.Encoder = encoder

	blobEvent.Store = store

	blobEvent.Padding = 0

	blobEvent.Tokenizer = tokenizer

	blobEvent.DiskIOEvent = events[0]

	blobEvent.WaitGroup = waitGroup

	blobEvent.Encoding = codec.None

	_, _, err = WriteBlobColumnValues(blobEvent)

	assert.NotNil(t, err)

}

func TestGetBlobColumnValues(t *testing.T) {

	bytes := make([]byte, 1000)

	blobEvent := BlobEvent{}

	blobEvent.KeyBytes = []byte("testing-key-1")

	blobEvent.ValueBytes = bytes

	blobEvent.Decoder = decoder

	blobEvent.Encoder = encoder

	blobEvent.Store = nil

	blobEvent.Tokenizer = tokenizer

	blobEvent.DiskIOEvent = events[0]

	blobEvent.WaitGroup = waitGroup

	blobEvent.Encoding = codec.None

	_, _, err := GetBlobColumnValues(blobEvent)

	assert.NotNil(t, err)

	store, err := storage.OpenOrCreateStore("dummy-testing-blob-type-2", utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	_, err = store.PutBlob([]byte("testing-key-1"), []byte("alsdfjlsajfdsa"), tokenizer)

	assert.Nil(t, err)

	blobEvent.Store = store

	_, _, err = GetBlobColumnValues(blobEvent)

	assert.NotNil(t, err)

	store.MarkClosed(encoder)

	_, _, err = GetBlobColumnValues(blobEvent)

	assert.NotNil(t, err)

}

func TestGetColumns(t *testing.T) {

	_, _, _, _, _, _, _, _, err := GetColumns(codec.DataType(200), 200, nil, "", "", 0, decoder)

	assert.NotNil(t, err)

}

func TestGetStringColumnValues(t *testing.T) {

	_, _, _, err := GetStringColumnValues(codec.Snappy, []byte("dummy"), "", "", 4, decoder)

	assert.NotNil(t, err)

}

func TestWriteAggregationConfigs(t *testing.T) {

	os.Remove(configDir + utils.PathSeparator + utils.VerticalAggregations)

	err := os.Mkdir(configDir+utils.PathSeparator+utils.VerticalAggregations, 0777)

	writeAggregationConfigs("key1", nil, "0", true)

	assert.Nil(t, err)

	os.Remove(configDir + utils.PathSeparator + utils.ColumnConfigFile)

	err = os.Mkdir(configDir+utils.PathSeparator+utils.ColumnConfigFile, 0777)

	saveColumns("dummy", "child", nil, utils.Add)

	save("dummyKey", "dummyvalue", cmap.New())

	assert.Nil(t, err)

}

func TestIsHorizontalAggregationExist(t *testing.T) {

	plugin := "plugin-1"

	horizontalAggregations.Set(plugin, nil)

	utils.Aggregation = true

	assert.False(t, IsHorizontalAggregationExist(plugin, "sum"))

	assert.False(t, IsHorizontalAggregationExist("plugin-2", "sum"))

}

func TestGetFLOAT8ColumnValuesV2(t *testing.T) {

	currentDataType := codec.Int16

	preveousDataType := codec.Float8

	key := ""

	storeName := ""

	padding := 0

	values := []float64{12, 34, 12, 1, 8}

	_, bytes, err := encoder.EncodeFLOAT64Values(values, codec.Snappy, codec.Float8, padding)

	assert.Nil(t, err)

	_, _, _, err = GetFLOAT8ColumnValues(currentDataType, preveousDataType, codec.Snappy, bytes[1:], key, storeName, padding, decoder)

	assert.Nil(t, err)

}

func TestStoreClose(t *testing.T) {

	assertion := assert.New(t)

	assertion.True(IsStoreClosed("abc"))
}

// aggregation metadata testcases

func TestUpdateAggregationContexts(t *testing.T) {

	utils.CleanUpStores()

	utils.Create(utils.JobDir)

	assertions := assert.New(t)

	aggregationContexts = make(map[string]bitmap.Bitmap)

	key1 := "key1"

	key2 := "key2"

	positions := bitmap.Bitmap{}

	positions.Set(1)

	positions.Set(2)

	aggregationContexts[key1] = positions

	UpdateAggregationContexts(key1, 3, utils.Add)

	UpdateAggregationContexts(key2, 3, utils.Add)

	UpdateAggregationContexts(key2, 3, utils.Remove)

	UpdateAggregationContexts(key2, 3, utils.Add)

	pendingPositions := GetAggregationContexts()

	assertions.Equal(len(pendingPositions), 2)

	updatedAggregationMetadata := make(map[string]bitmap.Bitmap)

	bytes, err := os.ReadFile(utils.JobDir + utils.PathSeparator + utils.AggregationContexts)

	assertions.Nil(err)

	err = json.Unmarshal(bytes, &updatedAggregationMetadata)

	assertions.Nil(err)

	updatedPositions, found := updatedAggregationMetadata[key1]

	assertions.True(found)

	assertions.Equal(updatedPositions.Count(), 3)

	updatedPositions, found = updatedAggregationMetadata[key2]

	assertions.True(found)

	assertions.Equal(updatedPositions.Count(), 1)

}

func TestWriteAggregationConfigsV2(t *testing.T) {

	key := ""

	writeAggregationConfigs(key, map[string]interface{}{}, "1", false)

	writeAggregationConfigs(key, map[string]interface{}{}, "0", false)

	AddHorizontalAggregation("plugin", "agg", nil)

	AddHorizontalAggregation("plugin", "agg"+utils.PartSeparator, nil)

	GetHorizontalAggregation("plugin", nil, nil, tokenizer, "aa", true)

	GetHorizontalAggregation("plugin", nil, nil, tokenizer, "", true)

	data := GetHorizontalAggregation("plugin", map[string]struct{}{"dummy": {}}, nil, tokenizer, "", false)

	assert.Equal(t, data, "")

}

func TestCorruptedDatastoreReOpen(t *testing.T) {

	storeName := "dummy-datastore-testing-1"

	store := GetStore(storeName, utils.PerformanceMetric, true, true, encoder, tokenizer)

	assertions := assert.New(t)

	assertions.NotNil(store)

	key1Bytes := []byte("testing-key-1")

	key2Bytes := []byte("testing-dummy-key-2")

	key3Bytes := []byte("testing-3")

	key4Bytes := []byte("testing-dummy-4")

	key5Bytes := []byte("testing-dummy-5")

	valueBytes := []byte("testing-value-1")

	store.Put(key1Bytes, valueBytes, encoder, tokenizer) // partition 2

	store.Put(key2Bytes, valueBytes, encoder, tokenizer) // partition 3

	store.Put(key3Bytes, valueBytes, encoder, tokenizer) // partition 4

	store.Put(key4Bytes, valueBytes, encoder, tokenizer) // partition 1

	store.Put(key5Bytes, valueBytes, encoder, tokenizer) // partition 5

	store.Close(encoder)

	RemoveStore(storeName)

	datastoreDir := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + storeName + utils.PathSeparator

	file, err := os.OpenFile(datastoreDir+codec.INTToStringValue(2)+utils.PathSeparator+storage.IdxFile, os.O_RDWR, 0755)

	assertions.Nil(err)

	assertions.NotNil(file)

	_, err = file.WriteAt([]byte{0, 1, 2}, 0)

	assertions.Nil(err)

	file.Close()

	file, err = os.OpenFile(datastoreDir+codec.INTToStringValue(3)+utils.PathSeparator+storage.IdxFile, os.O_RDWR, 0755)

	assertions.Nil(err)

	assertions.NotNil(file)

	_, err = file.WriteAt([]byte{0, 1, 2}, 0)

	assertions.Nil(err)

	file.Close()

	store = GetStore(storeName, utils.None, false, true, encoder, tokenizer)

	assertions.NotNil(store)

	valuesBytes := make([]byte, 1000)

	found, data, err := store.Get(key2Bytes, valuesBytes, encoder, events[0], waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.False(found)

	assertions.Nil(data)

	found, data, err = store.Get(key1Bytes, valuesBytes, encoder, events[0], waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.False(found)

	assertions.Nil(data)

	found, data, err = store.Get(key3Bytes, valuesBytes, encoder, events[0], waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.False(found)

	assertions.Nil(data)

	found, data, err = store.Get(key4Bytes, valuesBytes, encoder, events[0], waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.False(found)

	assertions.Nil(data)

	found, data, err = store.Get(key5Bytes, valuesBytes, encoder, events[0], waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	assertions.NotNil(data)

	store.Close(encoder)

}

func TestCorruptedDatastoreReOpenWithWallFile(t *testing.T) {

	storeName := "dummy-datastore-testing-2"

	store := GetStore(storeName, utils.PerformanceMetric, true, true, encoder, tokenizer)

	assertions := assert.New(t)

	assertions.NotNil(store)

	key1Bytes := []byte("testing-key-122")

	key2Bytes := []byte("testing-dummy-key-2")

	key3Bytes := []byte("testing-3")

	key4Bytes := []byte("testing-dummy-4")

	valueBytes := []byte("testing-value-1")

	store.Put(key1Bytes, valueBytes, encoder, tokenizer) // partition 5

	store.Put(key2Bytes, valueBytes, encoder, tokenizer) // partition 3

	store.Put(key3Bytes, valueBytes, encoder, tokenizer) // partition 2

	store.Put(key4Bytes, valueBytes, encoder, tokenizer) // partition 1

	store.Close(encoder)

	RemoveStore(storeName)

	datastoreDir := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + storeName + utils.PathSeparator

	file, err := os.OpenFile(datastoreDir+codec.INTToStringValue(2)+utils.PathSeparator+storage.IdxFile, os.O_RDWR, 0755)

	assertions.Nil(err)

	assertions.NotNil(file)

	_, err = file.WriteAt([]byte{0, 1, 2}, 0)

	assertions.Nil(err)

	file.Close()

	file, err = os.OpenFile(datastoreDir+codec.INTToStringValue(3)+utils.PathSeparator+storage.IdxFile, os.O_RDWR, 0755)

	assertions.Nil(err)

	assertions.NotNil(file)

	_, err = file.WriteAt([]byte{0, 1, 2}, 0)

	assertions.Nil(err)

	file.Close()

	store = GetStore(storeName, utils.None, false, true, encoder, tokenizer)

	assertions.NotNil(store)

	valuesBytes := make([]byte, 1000)

	found, data, err := store.Get(key2Bytes, valuesBytes, encoder, events[0], waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.False(found)

	assertions.Nil(data)

	found, data, err = store.Get(key1Bytes, valuesBytes, encoder, events[0], waitGroup, tokenizer, true)

	assertions.Nil(err)

	assertions.True(found)

	assertions.NotNil(data)

	found, data, err = store.Get(key3Bytes, valuesBytes, encoder, events[0], waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.False(found)

	assertions.Nil(data)

	found, data, err = store.Get(key4Bytes, valuesBytes, encoder, events[0], waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.False(found)

	assertions.Nil(data)

	store.Close(encoder)

}

func TestCorruptedMetadata(t *testing.T) {

	assertions := assert.New(t)

	storeName := "dummy-datastore-testing-3"

	store := GetStore(storeName, utils.None, true, true, encoder, tokenizer)

	assert.NotNil(t, store)

	key1Bytes := []byte("testing-key-1")

	valueBytes := []byte("testing-value-1")

	key2Bytes := []byte("testing-dummy-key-2")

	store.Put(key1Bytes, valueBytes, encoder, tokenizer)

	store.Put(key2Bytes, valueBytes, encoder, tokenizer)

	store.Close(encoder)

	RemoveStore(storeName)

	datastoreDir := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + storeName + utils.PathSeparator

	file, err := os.OpenFile(datastoreDir+utils.PathSeparator+utils.MetadataFile, os.O_RDWR, 0755)

	assertions.Nil(err)

	assertions.NotNil(file)

	_, err = file.WriteAt([]byte{0, 1, 2}, 0)

	assertions.Nil(err)

	file.Close()

	store = GetStore(storeName, utils.None, false, true, encoder, tokenizer)

	assertions.NotNil(store)

	valuesBytes := make([]byte, 1000)

	found, data, err := store.Get(key2Bytes, valuesBytes, encoder, events[0], waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.False(found)

	assertions.Nil(data)

	found, data, err = store.Get(key1Bytes, valuesBytes, encoder, events[0], waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.False(found)

	assertions.Nil(data)

	saveColumns("indexable.columns", "50001", make(chan string), utils.Add)

	values := cmap.New()

	values.Set("500011", make(chan string))

	save("indexable.columns", "50001", values)

	writeAggregationConfigs("50001", make(chan string), utils.VerticalFormat, false)

	store.Close(encoder)

}

func TestPopulateTempPatchStore(t *testing.T) {

	storeName := "populate-temp-patch-store"

	store := GetStore(storeName, utils.PerformanceMetric, true, true, encoder, tokenizer)

	keyBytes := []byte("1^system.cpu.percent^0")

	expectedValueBytes := []byte{1, 2, 3, 4, 5, 6, 7, 8, 9}

	assertions := assert.New(t)

	err := store.Put(keyBytes, append(make([]byte, utils.MaxValueBytes), expectedValueBytes...), encoder, tokenizer)

	assertions.Nil(err)

	newStore := GetStore(utils.TempPatch+storeName, utils.PerformanceMetric, true, true, encoder, tokenizer)

	err = newStore.Put(keyBytes, append(make([]byte, utils.MaxValueBytes), make([]byte, 100)...), encoder, tokenizer)

	assertions.Nil(err)

	populateStores()

	store.Close(encoder)

	store = GetStore(storeName, utils.PerformanceMetric, true, true, encoder, tokenizer)

	found, valueBytes, err := store.Get(keyBytes, valueBuffers[0], encoder, events[0], waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(expectedValueBytes, valueBytes)

	_, err = os.Stat(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.TempPatch + storeName)

	assertions.True(os.IsNotExist(err))

	store.Close(encoder)

}

func TestPopulateAppliedPatchStore(t *testing.T) {

	storeName := "populate-applied-patch-store"

	store := GetStore(storeName, utils.PerformanceMetric, true, true, encoder, tokenizer)

	keyBytes := []byte("1^system.cpu.percent^0")

	expectedValueBytes := []byte{1, 2, 3, 4, 5, 6, 7, 8, 9}

	assertions := assert.New(t)

	err := store.Put(keyBytes, append(make([]byte, utils.MaxValueBytes), expectedValueBytes...), encoder, tokenizer)

	assertions.Nil(err)

	store.Close(encoder)

	newStore := GetStore(utils.Patch+storeName, utils.PerformanceMetric, true, true, encoder, tokenizer)

	err = newStore.Put(keyBytes, append(make([]byte, utils.MaxValueBytes), make([]byte, 100)...), encoder, tokenizer)

	assertions.Nil(err)

	newStore.Close(encoder)

	populateStores()

	store = GetStore(storeName, utils.PerformanceMetric, true, true, encoder, tokenizer)

	found, valueBytes, err := store.Get(keyBytes, valueBuffers[0], encoder, events[0], waitGroup, tokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotEqual(expectedValueBytes, valueBytes)

	assertions.Equal(make([]byte, 100), valueBytes)

	_, err = os.Stat(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.Patch + storeName)

	assertions.True(os.IsNotExist(err))

}

func TestLoadValidPatchHorizontalAggregationFile(t *testing.T) {

	configDirPath := utils.CurrentDir + utils.PathSeparator + utils.ConfigDir

	_, err := os.Stat(configDirPath)

	expectedContext := utils.MotadataMap{}

	assertions := assert.New(t)

	horizontalAggregationFile := configDirPath + utils.PathSeparator + utils.HorizontalAggregations

	if err != nil {

		_ = os.Mkdir(configDirPath, 0777)

		expectedContext = utils.MotadataMap{

			"key1": "value1",
		}

		bytes, _ := json.Marshal(expectedContext)

		err = os.WriteFile(horizontalAggregationFile, bytes, 0666)

		assertions.Nil(err)

	} else {

		bytes, err := os.ReadFile(horizontalAggregationFile)

		if len(bytes) > 0 {

			err = json.Unmarshal(bytes, &expectedContext)

			assertions.Nil(err)

		} else {

			expectedContext = utils.MotadataMap{

				"key1": "value1",
			}

			bytes, _ := json.Marshal(expectedContext)

			err = os.WriteFile(horizontalAggregationFile, bytes, 0666)

			assertions.Nil(err)
		}

	}

	err = os.WriteFile(configDirPath+utils.PathSeparator+utils.TempPatch+utils.HorizontalAggregations, make([]byte, 100), 0666)

	assertions.Nil(err)

	populateConfigs()

	_, err = os.Stat(configDirPath + utils.PathSeparator + utils.TempPatch + utils.HorizontalAggregations)

	assertions.True(os.IsNotExist(err))

	bytes, err := os.ReadFile(horizontalAggregationFile)

	assertions.Nil(err)

	context := utils.MotadataMap{}

	err = json.Unmarshal(bytes, &context)

	assertions.Nil(err)

	assertions.Equal(expectedContext, context)

}

func TestLoadValidPatchColumnConfigFile(t *testing.T) {

	configDirPath := utils.CurrentDir + utils.PathSeparator + utils.ConfigDir

	_, err := os.Stat(configDirPath)

	expectedContext := utils.MotadataMap{}

	assertions := assert.New(t)

	columnConfigFile := configDirPath + utils.PathSeparator + utils.ColumnConfigFile

	if err != nil {

		_ = os.Mkdir(configDirPath, 0777)

		expectedContext = utils.MotadataMap{

			"key1": "value1",
		}

		bytes, _ := json.Marshal(expectedContext)

		err = os.WriteFile(columnConfigFile, bytes, 0666)

		assertions.Nil(err)

	} else {

		bytes, err := os.ReadFile(columnConfigFile)

		if len(bytes) > 0 {

			err = json.Unmarshal(bytes, &expectedContext)

			assertions.Nil(err)

		} else {

			expectedContext = utils.MotadataMap{

				"key1": "value1",
			}

			bytes, _ := json.Marshal(expectedContext)

			err = os.WriteFile(columnConfigFile, bytes, 0666)

			assertions.Nil(err)
		}

	}

	err = os.WriteFile(configDirPath+utils.PathSeparator+utils.TempPatch+utils.ColumnConfigFile, make([]byte, 100), 0666)

	assertions.Nil(err)

	populateConfigs()

	_, err = os.Stat(configDirPath + utils.PathSeparator + utils.TempPatch + utils.ColumnConfigFile)

	assertions.True(os.IsNotExist(err))

	bytes, err := os.ReadFile(columnConfigFile)

	assertions.Nil(err)

	context := utils.MotadataMap{}

	err = json.Unmarshal(bytes, &context)

	assertions.Nil(err)

	assertions.Equal(expectedContext, context)

}

// put this test case at last
func TestPopulateStore(t *testing.T) {

	Close()

	utils.CleanUpStores()

	dir := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir

	file, err := os.Create(dir)

	assert.Nil(t, err)

	populateStores()

	file.Close()

	bytes, err := utils.ReadLogFile("Datastore", "system")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "shutting down datastore, reason:failed to init datastore")

}

func TestRWLock(t *testing.T) {

	subIterations := 1000

	fields := []string{"field1", "field2", "field3", "field4", "field5", "field6"}

	columns := []string{"column1", "column2", "column3", "column4", "column5", "column6"}

	plugins := []string{"plugin1", "plugin2", "plugin3", "plugin4", "plugin5", "plugin6"}

	aggregations := []string{"aggregation1", "aggregation2", "aggregation3", "aggregation4", "aggregation5", "aggregation6"}

	metrics := []string{"metric1", "metric2", "metric3", "metric4", "metric5", "metric6"}

	keys := []string{"key1", "key2", "key3", "key4", "key5", "key6"}

	blobColumns2 := []string{"blob1", "blob2", "blob3", "blob4", "blob5", "blob6"}

	floatingColumns2 := []string{"float1", "float2", "float3", "float4", "float5", "float6"}

	garbageColumns2 := []string{"garbage1", "garbage2", "garbage3", "garbage4", "garbage5", "garbage6"}

	searchableColumns2 := []string{"search1", "search2", "search3", "search4", "search5", "search6"}

	operations := []string{utils.Remove, utils.Add}

	for k := 0; k < 5; k++ {

		waitGroup := sync.WaitGroup{}

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				IsHorizontalAggregationField(fields[rand.Int()%len(fields)])

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				AddHorizontalAggregationField(fields[rand.Int()%len(fields)])

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				GetColumnEncoder(columns[rand.Int()%len(columns)])

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				AddHorizontalAggregation(plugins[rand.Int()%len(plugins)], aggregations[rand.Int()%len(aggregations)], map[string]interface{}{})

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				UpdateVerticalAggregations(metrics[rand.Int()%len(metrics)], rand.Intn(2) == 0)

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				RemoveHorizontalAggregation(plugins[rand.Int()%len(plugins)], aggregations[rand.Int()%len(aggregations)])

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				GetAggregationContexts()

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				UpdateAggregationContexts(keys[rand.Int()%len(keys)], rand.Uint32()%1000, operations[rand.Int()%len(operations)])

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				IsAggregationContextExists(keys[rand.Int()%len(keys)])

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				AlterIndexableColumns(plugins[rand.Int()%len(plugins)], map[string]interface{}{}, utils.Add)

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				AddGarbageColumn(garbageColumns2[rand.Int()%len(garbageColumns2)])

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				AddSearchableColumn(searchableColumns2[rand.Int()%len(searchableColumns2)])

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				AddBlobColumn(blobColumns2[rand.Int()%len(blobColumns2)])

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				AddFloatingColumn(floatingColumns2[rand.Int()%len(floatingColumns2)])

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				IsHorizontalAggregationExist(plugins[rand.Int()%len(plugins)], aggregations[rand.Int()%len(aggregations)])

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				AddHorizontalAggregation(plugins[rand.Int()%len(plugins)], aggregations[rand.Int()%len(aggregations)], map[string]interface{}{})

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				RemoveHorizontalAggregation(plugins[rand.Int()%len(plugins)], aggregations[rand.Int()%len(aggregations)])

			}

			waitGroup.Done()

		}()

		waitGroup.Add(1)

		go func() {

			for i := 0; i < subIterations; i++ {

				GetHorizontalAggregation(plugins[rand.Int()%len(plugins)], nil, nil, tokenizer, "", false)

			}

			waitGroup.Done()

		}()

		waitGroup.Wait()
	}

}

func TestGetVerticalAggregations(t *testing.T) {

	assertions := assert.New(t)

	verticalAggregations = nil

	assertions.Nil(GetVerticalAggregations())
}

func TestIndexableColumnsV2(t *testing.T) {

	assertions := assert.New(t)

	utils.PublisherNotifications = make(chan utils.MotadataMap, 1_00_000)

	plugin := "dummy.plugin"

	column1 := "dummy.column1"

	column2 := "dummy.column2"

	column3 := "dummy.column3"

	assertions.False(GetIndexableColumns().Contains(plugin))

	AlterIndexableColumns(plugin, map[string]interface{}{
		column1: utils.Empty,
	}, utils.Add)

	assertions.True(GetIndexableColumns().GetMapValue(utils.EventContext).Contains(plugin))

	AlterIndexableColumns(plugin, map[string]interface{}{
		column2: utils.Empty,
	}, utils.Merge)

	assertions.True(GetIndexableColumns().GetMapValue(utils.EventContext).GetMapValue(plugin).Contains(column1))

	assertions.True(GetIndexableColumns().GetMapValue(utils.EventContext).GetMapValue(plugin).Contains(column2))

	AlterIndexableColumns(plugin, map[string]interface{}{
		column3: utils.Empty,
	}, utils.Replace)

	assertions.False(GetIndexableColumns().GetMapValue(utils.EventContext).GetMapValue(plugin).Contains(column1))

	assertions.False(GetIndexableColumns().GetMapValue(utils.EventContext).GetMapValue(plugin).Contains(column2))

	assertions.True(GetIndexableColumns().GetMapValue(utils.EventContext).GetMapValue(plugin).Contains(column3))

	AlterIndexableColumns(plugin, map[string]interface{}{
		column1: utils.Empty,
	}, utils.Merge)

	assertions.True(GetIndexableColumns().GetMapValue(utils.EventContext).GetMapValue(plugin).Contains(column1))
}

func TestWriteBlobColumnValuesWithMaxBlobBytes(t *testing.T) {

	os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions := assert.New(t)

	keyBytes := []byte("blob.column")

	store := GetStore("blob-column-2", utils.Log, true, true, storeEncoder, tokenizer)

	encoder.MemoryPool = utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools)

	poolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	values := make([]string, 5)

	for i := 0; i < 5; i++ {

		values[i] = generateRandomString(1024 * 1024 * 1.5)
	}

	waitGroup := sync.WaitGroup{}

	event := storage.DiskIOEvent{}

	blobEvent := BlobEvent{}

	blobEvent.KeyBytes = keyBytes

	blobEvent.ValueBytes = valueBytes

	blobEvent.Values = values

	blobEvent.Encoder = encoder

	blobEvent.Decoder = decoder

	blobEvent.Store = store

	blobEvent.Padding = utils.MaxValueBytes

	blobEvent.Tokenizer = tokenizer

	blobEvent.DiskIOEvent = event

	blobEvent.WaitGroup = &waitGroup

	blobEvent.Encoding = codec.None

	headerIndex, headerBytes, err := WriteBlobColumnValues(blobEvent)

	assertions.Nil(err)

	defer encoder.MemoryPool.ReleaseBytePool(headerIndex)

	err = store.Put(keyBytes, headerBytes, encoder, tokenizer)

	assertions.Nil(err)

	found, bytes, err := store.Get(keyBytes, valueBytes, encoder, event, &waitGroup, tokenizer, true)

	assertions.True(found)

	assertions.NotNil(bytes)

	assertions.Nil(err)

	results := make([]string, 0)

	for index := 1; index < len(bytes); index += 13 {

		var decodedValues []string

		blobEvent.ValueBytes = bytes[index : index+13]

		blobEvent.KeyBytes = keyBytes

		poolIndex, decodedValues, err = GetBlobColumnValues(blobEvent)

		assertions.Nil(err)

		results = append(results, decodedValues...)

		encoder.MemoryPool.ReleaseStringPool(poolIndex)
	}

	assertions.Nil(err)

	for index := range values {

		values[index] = values[index][:utils.MaxBlobBytes]
	}

	assertions.Equal(values, results)
}

func testBlobIO(keyPrefix string, messages []string, store *storage.Store, assertions *assert.Assertions) int {

	blobEvent := BlobEvent{}

	blobEvent.KeyBytes = []byte(keyPrefix)

	blobEvent.ValueBytes = utils.CheckSumV1Bytes

	blobEvent.Values = messages

	blobEvent.Encoder = encoder

	blobEvent.Decoder = decoder

	blobEvent.Store = store

	blobEvent.Padding = 0

	blobEvent.Tokenizer = tokenizer

	blobEvent.DiskIOEvent = events[0]

	blobEvent.WaitGroup = waitGroup

	blobEvent.Encoding = codec.None

	poolIndex, encodedBytes, err := WriteBlobColumnValues(blobEvent)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	size := 0

	for bufferIndex := 1; bufferIndex < len(encodedBytes); bufferIndex = bufferIndex + 13 {

		blobEvent.ValueBytes = encodedBytes[bufferIndex : bufferIndex+13]

		blobEvent.KeyBytes = []byte(keyPrefix)

		blobEvent.Encoding = codec.GetEncoding(encodedBytes[0])

		blobPoolIndex, blobValues, _ := GetBlobColumnValues(blobEvent)

		decoder.MemoryPool.ReleaseStringPool(blobPoolIndex)

		size += len(blobValues)
	}

	return size

}
