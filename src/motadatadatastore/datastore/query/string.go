/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-05-09  			 Hardik Vala			MOTADATA-4911 Query2.0 First Merge
 */

package query

import (
	"github.com/kelindar/bitmap"
	. "motadatadatastore/codec"
	"motadatadatastore/utils"
	"strings"
)

/* -------------------------------------------------------- VERTICAL --------------------------------------------------- */

func (worker *Worker) evaluateLastValueAggregationFuncString(value string, eventId, executorId int) {

	event := worker.WorkerEvents[eventId]

	values := worker.memoryPools[executorId].GetStringPool(worker.columnPoolIndices[executorId][event.currentIndex])

	if worker.groupElementSize == 0 {

		values[0] = value

		worker.memoryPoolPositions[executorId] = 1

	} else if !worker.externalGrouping {

		worker.evaluateLastValueAggregationFuncStringHelper(executorId, event.groupOrdinal, event.group, values, value)
	} else {

		executor := worker.executors[executorId]

		if worker.externalInstanceGrouping {

			if groups, ok := worker.externalGroupFilterOrdinals[event.group]; ok {

				groups.Iterate(func(group uint32) {

					worker.evaluateLastValueAggregationFuncStringHelper(executorId, executor.externalGroupHashes[group], executor.externalGroupFilters[group], values, value)
				})
			}

		} else {

			utils.Split(event.group, utils.GroupSeparator, worker.tokenizers[2])

			if groups, ok := worker.externalGroupFilterOrdinals[worker.tokenizers[2].Tokens[worker.externalGroupIndex]]; ok {

				externalGroup := utils.Empty

				groups.Iterate(func(group uint32) {

					worker.tokenizers[2].Tokens[worker.externalGroupIndex] = executor.externalGroupFilters[group]

					externalGroup = strings.Join(worker.tokenizers[2].Tokens[:worker.tokenizers[2].Counts], utils.GroupSeparator)

					worker.evaluateLastValueAggregationFuncStringHelper(executorId, utils.GetHash64([]byte(externalGroup)), externalGroup, values, value)
				})
			}
		}
	}

}

func (worker *Worker) evaluateLastValueAggregationFuncStringHelper(executorId int, groupOrdinal uint64, group string, values []string, value string) {

	if position, ok := worker.columnGroups[executorId].Get(groupOrdinal); ok {

		values[position] = value

	} else {

		worker.checkOverflow(executorId, worker.columnGroups[executorId].Len())

		position = worker.memoryPoolPositions[executorId]

		values[position] = value

		worker.columnGroups[executorId].Put(groupOrdinal, position)

		worker.resolvedColumnGroups[position] = group

		worker.memoryPoolPositions[executorId] = position + 1
	}
}

func (worker *Worker) processVerticalStringValues(executorId, eventId, position int, updatePosition bool, column string, values []string, ticks []int32) (int, bool) {

	event := worker.WorkerEvents[eventId]

	if worker.granularity == utils.NotAvailable {

		if worker.lastFunc {

			valid := true

			if worker.timeBoundQuery {

				valid = worker.evaluateLastTick(ticks[len(ticks)-1], eventId, executorId)
			}

			if valid {

				event.currentIndex = worker.setColumnContext(executorId, column+LastSuffix, String, false)

				worker.evaluateLastValueAggregationFuncString(values[len(values)-1], eventId, executorId)
			}
		}

	} else {

		if worker.aggregationFunc == 0 { // means we need raw data...

			if updatePosition {

				updatePosition = false

				worker.memoryPoolPositions[executorId] = position

				position = worker.setVerticalHistoricalTickColumn(eventId, executorId, ticks)
			} else {

				clear(worker.currentPositions)

				for tickIndex, tick := range ticks {

					worker.currentPositions[tick] = append(worker.currentPositions[tick], tickIndex)
				}
			}

			worker.setVerticalHistoricalValueColumnString(eventId, executorId, column, values)
		}
	}

	return position, updatePosition
}

/* -------------------------------------------------------- Drill-Down Function --------------------------------------------------- */

func (worker *Worker) setVerticalHistoricalValueColumnString(eventId, executorId int, column string, values []string) {

	event := worker.WorkerEvents[eventId]

	event.currentIndex = worker.setColumnContext(executorId, column+utils.ValueSuffix, String, false)

	position := worker.memoryPoolPositions[executorId]

	stringValues := worker.memoryPools[executorId].GetStringPool(worker.columnPoolIndices[executorId][event.currentIndex])

	// for blob column we can't update pool position if any chunks are pending for read so need exact position to copy value
	if position+len(values) > len(stringValues) {

		stringValues = worker.memoryPools[executorId].ExpandStringPool(worker.columnPoolIndices[executorId][event.currentIndex], len(stringValues)+utils.OverflowLength)
	}

	for tick, indexes := range worker.currentPositions {

		if tickPositions, ok := worker.positions[tick]; ok {

			for index, tickPosition := range tickPositions {

				stringValues[tickPosition] = values[indexes[index]]
			}
		}
	}
}

/* -------------------------------------------------------- HORIZONTAL --------------------------------------------------- */

func (worker *Worker) processHorizontalStringFunc(executorId, eventId int, values []string, groups []string, column string, dataType DataType, updatePosition bool, position int) (bool, int) {

	if worker.batchSize == 0 {

		worker.batchSize = len(values)
	}

	var groupOrdinals []uint64

	event := worker.WorkerEvents[eventId]

	if event.grouping {

		groupOrdinals = worker.memoryPools[executorId].GetUINT64Pool(worker.groupOrdinalPoolIndex)
	}

	if worker.granularity == utils.NotAvailable {

		if event.grouping && event.condition {

			size, _ := worker.conditionBitmaps[executorId].Max()

			length := int(size + 1)

			iterateAll := false

			// if condition operand array size is 10 and max bit is 10 length is 10 but aggregated operand array size is 8 than need this condition
			if length > len(values) {

				iterateAll = true

				length = len(values)
			}

			if !iterateAll && length >= len(values)-((len(values)*conditionIterateThreshold)/100) {

				iterateAll = true
			}

			if worker.aggregationFunc == Count || worker.aggregationFunc == SumCount || worker.aggregationFunc == MaxCount || worker.aggregationFunc == MaxSumCount ||
				worker.aggregationFunc == MinCount || worker.aggregationFunc == MinSumCount || worker.aggregationFunc == MinMaxCount || worker.aggregationFunc == MinMaxSumCount {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]

							event.groupOrdinal = groupOrdinals[i]

							event.countIndex = event.countAggregationColumnIndex

							worker.evaluateCountAggregationFunc(1, eventId, executorId)

						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]

						event.groupOrdinal = groupOrdinals[i]

						event.countIndex = event.countAggregationColumnIndex

						worker.evaluateCountAggregationFunc(1, eventId, executorId)

					})
				}

			}

			if worker.lastFunc {

				column += utils.KeySeparator + LastFunc

				event.currentIndex = worker.setColumnContext(executorId, column, dataType, false)

				tickPoolIndex := worker.setColumnContext(executorId, column+"^tick", Int64, true)

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]

							event.groupOrdinal = groupOrdinals[i]

							if worker.evaluateHorizontalLastValueTick(tickPoolIndex, eventId, executorId) {

								worker.evaluateLastValueAggregationFuncString(values[i], eventId, executorId)
							}

						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]

						event.groupOrdinal = groupOrdinals[i]

						if worker.evaluateHorizontalLastValueTick(tickPoolIndex, eventId, executorId) {

							worker.evaluateLastValueAggregationFuncString(values[i], eventId, executorId)
						}

					})
				}

			}

		} else if event.grouping && !event.condition {

			length := len(values)

			if len(groups) < length {

				length = len(groups)
			}

			//In case of metric aggregation different combination can occur, but not in event as min , max is not in event but in metric aggregation it is there , so added different scenario
			if worker.aggregationFunc == Count || worker.aggregationFunc == SumCount || worker.aggregationFunc == MaxCount || worker.aggregationFunc == MaxSumCount ||
				worker.aggregationFunc == MinCount || worker.aggregationFunc == MinSumCount || worker.aggregationFunc == MinMaxCount || worker.aggregationFunc == MinMaxSumCount {

				for i := 0; i < length; i++ {

					event.group = groups[i]

					event.groupOrdinal = groupOrdinals[i]

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateCountAggregationFunc(1, eventId, executorId)
				}

			}

			if worker.lastFunc {

				column += utils.KeySeparator + LastFunc

				event.currentIndex = worker.setColumnContext(executorId, column, dataType, false)

				tickPoolIndex := worker.setColumnContext(executorId, column+"^tick", Int64, true)

				for i := 0; i < length; i++ {

					event.group = groups[i]

					event.groupOrdinal = groupOrdinals[i]

					if worker.evaluateHorizontalLastValueTick(tickPoolIndex, eventId, executorId) {

						worker.evaluateLastValueAggregationFuncString(values[i], eventId, executorId)
					}

				}

			}

		} else if !event.grouping && event.condition {

			if worker.aggregationFunc == Count || worker.aggregationFunc == SumCount || worker.aggregationFunc == MaxCount || worker.aggregationFunc == MaxSumCount ||
				worker.aggregationFunc == MinCount || worker.aggregationFunc == MinSumCount || worker.aggregationFunc == MinMaxCount || worker.aggregationFunc == MinMaxSumCount {

				event.countIndex = event.countAggregationColumnIndex

				worker.evaluateCountAggregationFunc(utils.CountConditional(worker.conditionBitmaps[executorId]), eventId, executorId)

			}

			if worker.lastFunc {

				column += utils.KeySeparator + LastFunc

				event.currentIndex = worker.setColumnContext(executorId, column, dataType, false)

				tickPoolIndex := worker.setColumnContext(executorId, column+"^tick", Int64, true)

				if worker.evaluateHorizontalLastValueTick(tickPoolIndex, eventId, executorId) {

					worker.evaluateLastValueAggregationFuncString(utils.LastStringConditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)
				}

			}

		} else if !event.condition && !event.grouping {

			if worker.aggregationFunc == Count || worker.aggregationFunc == SumCount || worker.aggregationFunc == MaxCount || worker.aggregationFunc == MaxSumCount ||
				worker.aggregationFunc == MinCount || worker.aggregationFunc == MinSumCount || worker.aggregationFunc == MinMaxCount || worker.aggregationFunc == MinMaxSumCount {

				event.countIndex = event.countAggregationColumnIndex

				worker.evaluateCountAggregationFunc(int64(len(values)), eventId, executorId)

			}

			if worker.lastFunc {

				column += utils.KeySeparator + LastFunc

				event.currentIndex = worker.setColumnContext(executorId, column, dataType, false)

				tickPoolIndex := worker.setColumnContext(executorId, column+"^tick", Int64, true)

				if worker.evaluateHorizontalLastValueTick(tickPoolIndex, eventId, executorId) {

					worker.evaluateLastValueAggregationFuncString(values[len(values)-1], eventId, executorId)
				}

			}

		}

	} else {

		if worker.aggregationFunc == 0 { // means we need raw data...

			delete(worker.missingColumns, column)

			if !worker.lastFunc && updatePosition {

				updatePosition = false

				worker.memoryPoolPositions[executorId] = position

				if event.condition {

					position = worker.setHorizontalHistoricalTickColumn(executorId, worker.tick, len(values), worker.conditionBitmaps[executorId])
				} else {

					position = worker.setHorizontalHistoricalTickColumn(executorId, worker.tick, len(values), nil)
				}
			}

			if event.condition {

				worker.setHorizontalHistoricalValueColumnStringConditional(eventId, executorId, column, values, worker.conditionBitmaps[executorId])

			} else {

				worker.setHorizontalHistoricalValueColumnString(eventId, executorId, worker.getBatchSize(len(values), executorId, event.condition), column, values)

			}

		} else {

			if event.grouping && event.condition {

				size, _ := worker.conditionBitmaps[executorId].Max()

				length := int(size + 1)

				iterateAll := false

				// if condition operand array size is 10 and max bit is 10 length is 10 but aggregated operand array size is 8 than need this condition
				if length > len(values) {

					iterateAll = true

					length = len(values)
				}

				if !iterateAll && length >= len(values)-((len(values)*conditionIterateThreshold)/100) {

					iterateAll = true
				}

				if worker.aggregationFunc == Count || worker.aggregationFunc == SumCount || worker.aggregationFunc == MaxCount || worker.aggregationFunc == MaxSumCount ||
					worker.aggregationFunc == MinCount || worker.aggregationFunc == MinSumCount || worker.aggregationFunc == MinMaxCount || worker.aggregationFunc == MinMaxSumCount {

					event.countIndex = event.countAggregationColumnIndex

					if iterateAll {

						for i := 0; i < length; i++ {

							if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

								event.group = groups[i]

								event.groupOrdinal = groupOrdinals[i]

								worker.evaluateHorizontalCountHistogramFunc(1, eventId, executorId)

							}
						}
					} else {

						worker.conditionBitmaps[executorId].Range(func(i uint32) {

							event.group = groups[i]

							event.groupOrdinal = groupOrdinals[i]

							worker.evaluateHorizontalCountHistogramFunc(1, eventId, executorId)

						})
					}
				}

			} else if !event.grouping && event.condition {

				if worker.aggregationFunc == Count || worker.aggregationFunc == SumCount || worker.aggregationFunc == MaxCount || worker.aggregationFunc == MaxSumCount ||
					worker.aggregationFunc == MinCount || worker.aggregationFunc == MinSumCount || worker.aggregationFunc == MinMaxCount || worker.aggregationFunc == MinMaxSumCount {

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateHorizontalCountHistogramFunc(utils.CountConditional(worker.conditionBitmaps[executorId]), eventId, executorId)

				}

			} else if !event.condition && !event.grouping {

				if worker.aggregationFunc == Count || worker.aggregationFunc == SumCount || worker.aggregationFunc == MaxCount || worker.aggregationFunc == MaxSumCount ||
					worker.aggregationFunc == MinCount || worker.aggregationFunc == MinSumCount || worker.aggregationFunc == MinMaxCount || worker.aggregationFunc == MinMaxSumCount {

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateHorizontalCountHistogramFunc(int64(len(values)), eventId, executorId)

				}
			}
		}
	}

	return updatePosition, position
}

func (worker *Worker) setHorizontalHistoricalValueColumnString(eventId, executorId, batchSize int, column string, values []string) {

	event := worker.WorkerEvents[eventId]

	event.currentIndex = worker.setColumnContext(executorId, column+utils.ValueSuffix, String, false)

	position := worker.memoryPoolPositions[executorId]

	if position > worker.maxHistoricalRecords {

		worker.queryAbort = true

		return
	}

	stringValues := worker.memoryPools[executorId].GetStringPool(worker.columnPoolIndices[executorId][event.currentIndex])

	if position+batchSize > len(stringValues) {

		stringValues = worker.memoryPools[executorId].ExpandStringPool(worker.columnPoolIndices[executorId][event.currentIndex], len(stringValues)+utils.OverflowLength)
	}

	copy(stringValues[position:], values[:batchSize])
}

func (worker *Worker) setHorizontalHistoricalValueColumnStringConditional(eventId, executorId int, column string, values []string, bitmap bitmap.Bitmap) {

	event := worker.WorkerEvents[eventId]

	event.currentIndex = worker.setColumnContext(executorId, column+utils.ValueSuffix, String, false)

	position := worker.memoryPoolPositions[executorId]

	if position > worker.maxHistoricalRecords {

		worker.queryAbort = true

		return
	}

	stringValues := worker.memoryPools[executorId].GetStringPool(worker.columnPoolIndices[executorId][event.currentIndex])

	size, _ := bitmap.Max()

	length := int(size) + 1

	if worker.batchSize > 0 && length > worker.batchSize {

		length = worker.batchSize
	}

	if position+length > len(stringValues) {

		stringValues = worker.memoryPools[executorId].ExpandStringPool(worker.columnPoolIndices[executorId][event.currentIndex], len(stringValues)+utils.OverflowLength)
	}

	for i := 0; i < length; i++ {
		if bitmap.Contains(uint32(i)) {
			stringValues[position] = values[i]
			position++
		}
	}
}
