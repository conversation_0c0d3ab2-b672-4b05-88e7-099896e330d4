/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-05-09  			 Hardik Vala			MOTADATA-4911 Query2.0 First Merge
* 2025-06-04             <PERSON><PERSON><PERSON>ATA-5780 Called custom MmapAnonymous function
* 2025-06-10             Vedant <PERSON><PERSON><PERSON>-6524 Introduce the constant for the parsing pool
* 2025-06-24			 <PERSON><PERSON>val <PERSON><PERSON>-6639  Added Retention Check In Query Parsing
* 2025-06-23             Vedant <PERSON><PERSON><PERSON>-6370 Mapping operand changes to get the instance type store
 */

package query

import (
	"fmt"
	"github.com/dolthub/swiss"
	"github.com/kamstrup/intmap"
	"github.com/kelindar/bitmap"
	. "motadatadatastore/codec"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"sync"
	"sync/atomic"
	"time"
)

/*----------------------------------------------------------------------------------------------------------------*/

type Executor struct {
	*MemoryPoolManager
	*Parser
	*Distributor
	*ExecutorManager
	Logger
	*EventManager
	*ResponseManager
	*DrillDownManager

	executorId int

	queryEngineType QueryEngineType

	aborted bool

	shutdown bool
}

func NewExecutor(id int, queryEngineType QueryEngineType) *Executor {

	return &Executor{

		executorId: id,

		queryEngineType: queryEngineType,
	}
}

/*----------------------------------------------------------------------------------------------------------------*/

type MemoryPoolManager struct {
	memoryPool *utils.MemoryPool

	incrementalAggregationFuncMemoryPool *utils.MemoryPool

	decoder Decoder // executor wise decoder

	encoder Encoder

	decoders []Decoder

	encoders []Encoder

	poolLength int

	poolSize int
}

func (executor *Executor) initMemoryPoolManager() *MemoryPoolManager {

	poolSize := 40

	poolLength := 1_00_000

	if executor.queryEngineType == Metric {

		poolLength = utils.MetricExecutorPoolLength

	} else if executor.queryEngineType == Log {

		poolLength = utils.LogExecutorPoolLength

	} else if executor.queryEngineType == Flow {

		poolLength = utils.FlowExecutorPoolLength

	} else if executor.queryEngineType == AIOps {

		poolLength = utils.AIOpsExecutorPoolLength

	} else if executor.queryEngineType == DrillDown {

		poolLength = utils.DrillDownExecutorPoolLength
	}

	pool := utils.NewMemoryPool(poolSize, poolLength, true, utils.DefaultBlobPools)

	decoders := make([]Decoder, executor.parsingThreads)

	encoders := make([]Encoder, executor.parsingThreads)

	for i := range encoders {

		parsingPool := utils.NewMemoryPool(utils.MaxStoreParts, poolLength, false, utils.DefaultBlobPools)

		encoders[i] = NewEncoder(parsingPool)

		decoders[i] = NewDecoder(parsingPool)
	}

	return &MemoryPoolManager{

		memoryPool: pool,

		encoder: NewEncoder(pool),

		decoder: NewDecoder(pool),

		poolLength: poolLength,

		poolSize: poolSize,

		encoders: encoders,

		decoders: decoders,

		incrementalAggregationFuncMemoryPool: utils.NewMemoryPool(poolSize, poolLength, true, utils.DefaultBlobPools),
	}
}

/*----------------------------------------------------------------------------------------------------------------*/

type EventManager struct {
	Requests chan utils.MotadataMap

	OverflowedAcks, CleanupAcks []chan int

	AbortRequests, queryResponses chan string

	OverflowNotifications chan string

	AIOpsEngineResponses chan []byte

	ShutdownNotifications chan bool
}

func (executor *Executor) initEventManager(workers []*Worker) *EventManager {

	overflowedAcks := make([]chan int, len(workers))

	cleanupAcks := make([]chan int, len(workers))

	for i := range overflowedAcks {

		overflowedAcks[i] = make(chan int)

		cleanupAcks[i] = make(chan int)
	}

	return &EventManager{

		Requests: make(chan utils.MotadataMap, 1),

		AIOpsEngineResponses: make(chan []byte, 1_00_000),

		OverflowNotifications: make(chan string, len(workers)),

		OverflowedAcks: overflowedAcks,

		CleanupAcks: cleanupAcks,

		queryResponses: make(chan string, len(workers)*100),

		AbortRequests: make(chan string, 1000),

		ShutdownNotifications: make(chan bool, 5),
	}
}

/*----------------------------------------------------------------------------------------------------------------*/

type Logger struct {
	queryLogger utils.Logger

	logger utils.Logger
}

func (executor *Executor) initLogger() Logger {

	return Logger{

		logger: utils.NewLogger(fmt.Sprintf("Query Executor-%v", executor.executorId), "query"),

		queryLogger: utils.NewLogger(fmt.Sprintf("Query Planner-%v", executor.executorId), "query"),
	}
}

/*----------------------------------------------------------------------------------------------------------------*/

type Parser struct {
	entities *intmap.Set[uint64]

	aggregations map[string]int

	stringOrdinalMappings *swiss.Map[int32, []int]

	numericOrdinalMappings *swiss.Map[int32, []int]

	stringMappings []swiss.Map[string, int32]

	numericMappings []swiss.Map[int64, int32]

	dates map[string]map[int32]string

	helperDates map[string]map[int32]string

	request utils.MotadataMap

	columnIndices map[string]int

	conditionOperands map[string]struct{}

	conditionOperandValues utils.MotadataMap

	drillDownFilter utils.MotadataMap

	// internal use to hold int64 value
	int64Buffers [][]byte

	ordinals map[string]struct{}

	externalGroupFilterOrdinals map[string]*CompactedBitmap

	categories utils.MotadataMap

	// runtime goroutines response data structure
	responses []swiss.Map[string, struct{}]

	retentionContext map[utils.DatastoreType][2]int64

	RetentionChangeNotifications chan map[int]map[string]int

	fields map[string]int

	ticks, helperTicks map[int32]map[string]struct{}

	MappingBitmaps, MappingHelperBitmaps, DrillDownMappingBitmaps []*bitmap.Bitmap

	externalGroupFilters []string

	externalGroupHashes []uint64

	helpers []int

	fromDateTime time.Time

	toDateTime time.Time

	externalGroupFilterElementSize int

	probes int

	externalGroupIndex int

	preAggregationTimeInterval int

	topNIncrementalGroupIndex int

	overflowedWorkerId int

	datastoreType utils.DatastoreType

	queryTimestamp int64

	conditionOperand, mappingOperand string

	conditionExpression, helperConditionExpression string

	queryId string

	subQueryId string

	category string

	plugin string

	value string

	innerGroupingField, outerGroupingField string

	timeWindowStartPosition int32

	timeWindowEndPosition int32

	startTick int32

	endTick int32

	granularity int32

	parsingThreads int8

	visualizationResultType int8

	preAggregationJobId int8

	preAggregationQuery bool

	fulltextSearchingView bool

	preFilterQuery bool

	drillDownQuery bool

	timeWindowQuery bool

	timeBoundQuery bool

	instanceQuery bool

	bitmapFilter bool

	externalInstanceGrouping bool

	resultFilter bool

	lookupOrdinal bool

	topNIncrementalQuery bool
}

func (executor *Executor) initParser() *Parser {

	parsingThreads := 10

	stringMappings := make([]swiss.Map[string, int32], parsingThreads)

	numericMappings := make([]swiss.Map[int64, int32], parsingThreads)

	responses := make([]swiss.Map[string, struct{}], parsingThreads)

	int64Buffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength*parsingThreads)

	for i := range stringMappings {

		stringMappings[i] = *swiss.NewMap[string, int32](1000)

		numericMappings[i] = *swiss.NewMap[int64, int32](1000)

		responses[i] = *swiss.NewMap[string, struct{}](1000)
	}

	for i := range int64Buffers {

		int64Buffers[i] = make([]byte, 8)
	}

	return &Parser{

		preAggregationJobId: utils.NotAvailable,

		request: make(utils.MotadataMap),

		stringMappings: stringMappings,

		numericMappings: numericMappings,

		stringOrdinalMappings: swiss.NewMap[int32, []int](1000),

		numericOrdinalMappings: swiss.NewMap[int32, []int](1000),

		columnIndices: make(map[string]int, 10),

		conditionOperands: make(map[string]struct{}, 10),

		externalGroupFilterOrdinals: make(map[string]*CompactedBitmap, 1000),

		externalGroupFilters: make([]string, 10000),

		externalGroupHashes: make([]uint64, 10000),

		drillDownFilter: make(utils.MotadataMap),

		fields: make(map[string]int),

		entities: intmap.NewSet[uint64](1000),

		aggregations: make(map[string]int, 10),

		helpers: make([]int, parsingThreads),

		dates: make(map[string]map[int32]string, parsingThreads),

		helperDates: make(map[string]map[int32]string, parsingThreads),

		MappingBitmaps: make([]*bitmap.Bitmap, parsingThreads),

		MappingHelperBitmaps: make([]*bitmap.Bitmap, parsingThreads),

		DrillDownMappingBitmaps: make([]*bitmap.Bitmap, parsingThreads),

		conditionOperandValues: make(utils.MotadataMap, 4),

		ticks: make(map[int32]map[string]struct{}, 100_00),

		helperTicks: make(map[int32]map[string]struct{}, 100_00),

		ordinals: make(map[string]struct{}, 100),

		parsingThreads: int8(parsingThreads),

		responses: responses,

		categories: make(utils.MotadataMap, 5),

		retentionContext: make(map[utils.DatastoreType][2]int64, 5),

		RetentionChangeNotifications: make(chan map[int]map[string]int, 100),

		int64Buffers: int64Buffers,

		granularity: utils.NotAvailable,

		overflowedWorkerId: utils.NotAvailable,

		externalGroupIndex: utils.NotAvailable,
	}
}

/*----------------------------------------------------------------------------------------------------------------*/

type Distributor struct {
	workers []*Worker

	qualifiedStores []string

	keys []string

	keyIndices []int

	workerPendingEvents *intmap.Map[int, int]

	workerTimeTrackers *intmap.Map[int, int64]

	usedWorkers []int

	workerStates []bool

	availableWorkers []atomic.Bool

	usedWorkerElementSize int

	minWorkerId int

	maxWorkerId int

	qualifiedStoreElementSize int

	currentStoreIndex int

	keyElementSize int

	keyPoolIndex int
}

func (executor *Executor) initDistributor(workers []*Worker, availableWorkers []atomic.Bool) *Distributor {

	minWorkerId := executor.executorId * utils.Workers

	usedWorkers := make([]int, len(workers))

	for i := range usedWorkers {

		usedWorkers[i] = utils.NotAvailable
	}

	return &Distributor{

		qualifiedStores: make([]string, 10_000),

		keyIndices: make([]int, 20_000),

		usedWorkers: usedWorkers,

		workerStates: make([]bool, len(workers)),

		workerPendingEvents: intmap.New[int, int](len(workers)),

		workerTimeTrackers: intmap.New[int, int64](len(workers)),

		workers: workers,

		availableWorkers: availableWorkers,

		minWorkerId: minWorkerId,

		maxWorkerId: (minWorkerId + utils.Workers) - 1,

		keyPoolIndex: utils.NotAvailable,
	}
}

/*----------------------------------------------------------------------------------------------------------------*/

type ExecutorManager struct {
	tokenizers []*utils.Tokenizer

	errors []string

	Columns []string

	ColumnElementSize int

	GroupColumns []string

	valueBufferIndices []int

	KeyBuffers, ValueBuffers [][]byte

	events []storage.DiskIOEvent

	eventBatches [][]storage.DiskIOEventBatch

	waitGroups []sync.WaitGroup

	errorElementSize int

	GroupColumnElementSize int

	categorizedColumnCounts bool
}

func (executor *Executor) initExecutorManager() *ExecutorManager {

	tokenizers := make([]*utils.Tokenizer, executor.parsingThreads)

	for i := range tokenizers {

		tokenizers[i] = &utils.Tokenizer{

			Tokens: make([]string, utils.TokenizerLength),
		}
	}

	valueBufferIndices := make([]int, executor.parsingThreads)

	for j := 1; j < len(valueBufferIndices); j++ {

		valueBufferIndices[j] = UnmappedAnonymousByte
	}

	valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength*int(executor.parsingThreads))

	// make only single condition buffers to support vertical filter, for horizontal filter let parser decide how much buffers are required..
	for i := range valueBuffers[:utils.MaxWorkerEventKeyGroupLength] {

		bytes, err := utils.MmapAnonymous(utils.MaxValueBufferBytes)

		if err != nil {

			executor.logError(fmt.Sprintf("error %v occurred while mapping annonymous buffer for executor %v and index %v", err, executor.executorId, i))

			bytes = make([]byte, utils.MaxValueBufferBytes)
		}

		valueBuffers[i] = bytes
	}

	valueBufferIndices[0] = MappedAnonymousByte

	events := make([]storage.DiskIOEvent, executor.parsingThreads)

	for i := range events {

		events[i] = storage.DiskIOEvent{}
	}

	eventBatches := make([][]storage.DiskIOEventBatch, executor.parsingThreads)

	for i := range eventBatches {

		eventBatches[i] = make([]storage.DiskIOEventBatch, utils.MaxStoreParts)

		for j := 0; j < utils.MaxStoreParts; j++ {

			eventBatches[i][j] = storage.DiskIOEventBatch{}
		}
	}

	return &ExecutorManager{

		errors: make([]string, executor.poolLength),

		GroupColumns: make([]string, executor.poolSize),

		KeyBuffers: make([][]byte, utils.MaxWorkerEventKeyGroupLength*int(executor.parsingThreads)),

		ValueBuffers: valueBuffers,

		valueBufferIndices: valueBufferIndices,

		events: events,

		waitGroups: make([]sync.WaitGroup, executor.parsingThreads),

		Columns: make([]string, executor.poolSize),

		eventBatches: eventBatches,

		tokenizers: tokenizers,

		categorizedColumnCounts: executor.queryEngineType == Log || executor.queryEngineType == Flow || executor.queryEngineType == APM,
	}
}

/*----------------------------------------------------------------------------------------------------------------*/

type ResponseManager struct {
	Groups *intmap.Map[uint64, int] // grouping key with offset position set by worker

	flapStatuses *intmap.Map[uint64, int64]

	ResolvedGroups *swiss.Map[uint64, string]

	missingColumns map[int]struct{}

	shadowCounterIndices *intmap.Set[int]

	ColumnPoolDataTypes, incrementalAggregationPoolDataTypes []DataType

	statisticalFuncs []string

	// int64Values is used in Median calculation
	int64Values []int64

	// float64Values is used in Median calculation
	float64Values []float64

	ColumnPoolIndices, incrementalAggregationPoolIndices []int

	ColumnPoolElementSize, incrementalAggregationFuncPoolElementSize int

	helperPools []int

	pendingEvents int

	drillDownPosition int

	records int

	packerBytePoolIndex, sortedGroupPoolIndex, sortedResolvedGroupPoolIndex int

	packerBytePoolPosition int

	progress float64

	publish bool
}

func (executor *Executor) initResponseManager() *ResponseManager {

	columnPoolIndices := make([]int, executor.poolSize)

	columnPoolDataTypes := make([]DataType, executor.poolSize)

	incrementalAggregationPoolDataTypes := make([]DataType, executor.poolSize)

	incrementalAggregationPoolIndices := make([]int, executor.poolSize)

	for i := 0; i < executor.poolSize; i++ {

		columnPoolIndices[i] = utils.NotAvailable

		columnPoolDataTypes[i] = Invalid

		incrementalAggregationPoolIndices[i] = utils.NotAvailable

		incrementalAggregationPoolDataTypes[i] = Invalid
	}

	helperPools := make([]int, executor.poolSize)

	for i := range helperPools {

		helperPools[i] = utils.NotAvailable
	}

	return &ResponseManager{

		packerBytePoolIndex: utils.NotAvailable,

		sortedGroupPoolIndex: utils.NotAvailable,

		sortedResolvedGroupPoolIndex: utils.NotAvailable,

		ColumnPoolDataTypes: columnPoolDataTypes,

		ColumnPoolIndices: columnPoolIndices,

		incrementalAggregationPoolDataTypes: incrementalAggregationPoolDataTypes,

		incrementalAggregationPoolIndices: incrementalAggregationPoolIndices,

		helperPools: helperPools,

		missingColumns: make(map[int]struct{}, executor.poolSize),

		ResolvedGroups: swiss.NewMap[uint64, string](uint32(executor.poolLength)),

		shadowCounterIndices: intmap.NewSet[int](executor.poolSize),

		statisticalFuncs: make([]string, executor.poolSize),

		int64Values: make([]int64, 9),

		float64Values: make([]float64, 9),
	}
}

/*----------------------------------------------------------------------------------------------------------------*/

type AggregationEvent struct {
	workerPoolIndex, executorPoolIndex, helperPoolIndex int

	columnIndex int

	column string

	groups, records int

	workerId int

	minFunc, maxFunc, sumFunc, countFunc, avgFunc, lastFunc bool

	columnDataType DataType
}

/*----------------------------------------------------------------------------------------------------------------*/

type DrillDownManager struct {
	waitGroup sync.WaitGroup

	drillDownEvent *DrillDownEvent

	qualifiedTicks []int32

	searchEventId string

	timestamp int64

	qualifiedTickPoolIndex, qualifiedTickElementSize int

	tickPosition, pageIndex, pageSize, qualifiedDateIndex int

	incrementalQuery, paginationQuery bool
}

func (executor *Executor) initDrillDownManager() *DrillDownManager {

	return &DrillDownManager{

		qualifiedTickPoolIndex: utils.NotAvailable,

		waitGroup: sync.WaitGroup{},

		drillDownEvent: &DrillDownEvent{},
	}
}

type DrillDownEvent struct {
	EventIds []string

	QualifiedDates []int64

	LastDatePosition, LastTickPosition, DrillDownRecords int

	DrillDownCategories utils.MotadataMap

	BitmapBytes, DrillDownBitmapBytes [][]byte

	DrillDownConditionOperands map[string]struct{}

	DrillDownConditionOperandValues utils.MotadataMap

	DrillDownConditionExpression string

	DrillDownBitmapFilter bool
}
