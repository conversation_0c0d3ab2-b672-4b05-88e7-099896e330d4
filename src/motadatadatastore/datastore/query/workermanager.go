/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-05-09  			 Hardik Vala			MOTADATA-4911 Query2.0 First Merge
 */

package query

import (
	"fmt"
	"github.com/kamstrup/intmap"
	"github.com/kelindar/bitmap"
	"math"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"sync"
)

var maxErrorLimit = 1000 // showing first 1000 errors of worker only

/*----------------------------------------------------------------------------------------------------------------*/

type Worker struct {
	*PoolManager

	*WorkerManager

	*Events

	uniqueId string

	workerId int

	poolLength int

	queryEngineType QueryEngineType

	shutdown bool

	queryAbort bool
}

func NewWorker(workerId int, queryEngineType QueryEngineType) *Worker {

	return &Worker{

		workerId: workerId,

		queryEngineType: queryEngineType,
	}

}

/*----------------------------------------------------------------------------------------------------------------*/

type Events struct {
	events []storage.DiskIOEventBatch

	event storage.DiskIOEvent

	blobEvent datastore.BlobEvent

	Requests chan string

	cleanupNotifications chan int

	ShutdownNotifications chan bool

	waitGroup sync.WaitGroup
}

func (worker *Worker) initEventManager() *Events {

	events := make([]storage.DiskIOEventBatch, utils.MaxStoreParts)

	for i := range events {

		events[i] = storage.DiskIOEventBatch{}
	}

	return &Events{

		waitGroup: sync.WaitGroup{},

		events: events,

		event: storage.DiskIOEvent{},

		blobEvent: datastore.BlobEvent{},

		Requests: make(chan string, 1_000_00),

		ShutdownNotifications: make(chan bool, 5),

		cleanupNotifications: make(chan int, utils.QueryExecutors),
	}
}

/*----------------------------------------------------------------------------------------------------------------*/

type PoolManager struct {
	memoryPools []*utils.MemoryPool

	decoders []Decoder

	encoders []Encoder
}

func (worker *Worker) initPoolManager(executorId, poolElementSize int, executors []*Executor) *PoolManager {

	if worker.queryEngineType == Metric {

		worker.poolLength = utils.MetricWorkerPoolLength

	} else if worker.queryEngineType == Flow {

		worker.poolLength = utils.FlowWorkerPoolLength

	} else if worker.queryEngineType == Log {

		worker.poolLength = utils.LogWorkerPoolLength

	} else if worker.queryEngineType == DrillDown {

		worker.poolLength = int(math.Max(float64(utils.LogWorkerPoolLength), float64(utils.FlowWorkerPoolLength)))

	} else if worker.queryEngineType == AIOps {

		worker.poolLength = utils.MetricWorkerPoolLength
	}

	decoders := make([]Decoder, utils.QueryExecutors)

	encoders := make([]Encoder, utils.QueryExecutors)

	memoryPools := make([]*utils.MemoryPool, utils.QueryExecutors)

	if (worker.workerId+1)%utils.Workers == 0 { // if worker-id is last init all executors data structure

		for i := range executors {

			pool := utils.NewMemoryPool(poolElementSize, worker.poolLength, true, utils.DefaultBlobPools)

			memoryPools[i] = pool

			decoders[i] = NewDecoder(pool)

			encoders[i] = NewEncoder(pool)

		}
	} else {

		pool := utils.NewMemoryPool(poolElementSize, worker.poolLength, true, utils.DefaultBlobPools)

		memoryPools[executorId] = pool

		decoders[executorId] = NewDecoder(pool)

		encoders[executorId] = NewEncoder(pool)
	}

	return &PoolManager{

		memoryPools: memoryPools,

		decoders: decoders,

		encoders: encoders,
	}
}

/*----------------------------------------------------------------------------------------------------------------*/

type WorkerManager struct {
	tokenizers []*utils.Tokenizer

	columnPoolIndices [][]int // stores pool index which contain column values

	valueBuffers, keyBuffers [][]byte

	columnDataTypes [][]DataType // stores column data types

	memoryPoolDataTypes []map[string]DataType //internal use for worker....

	memoryPoolIndices, columns []map[string]int

	conditionOperandValues []utils.MotadataMap

	ordinals []map[string]struct{}

	missingGroupColumns []*intmap.Set[int]

	filters, drillDownFilters []utils.MotadataMap

	positions, currentPositions map[int32][]int

	conditionBitmaps []bitmap.Bitmap //internal use for worker....

	WorkerEvents []*WorkerEvent

	executors []*Executor

	columnGroups []*intmap.Map[uint64, int]

	resolvedColumnGroups []string

	externalGroupFilterOrdinals map[string]*CompactedBitmap

	missingColumns map[string]struct{}

	errors []string

	groups, conditionOperands []string

	memoryPoolPositions, records []int

	conditionExpression string

	compositePlugin, plugin, innerGroupingField, outerGroupingField string

	groupOrdinalPoolIndex int

	errorElementSize int

	groupElementSize, conditionOperandElementSize int

	granularity int

	externalGroupIndex int

	batchSize int

	maxHistoricalRecords int

	fromTimeTick, toTimeTick, startTick, endTick, tick int32

	part uint16

	aggregationFunc AggregationFunc

	avgFunc, countFunc, sumFunc, minFunc, maxFunc, lastFunc, timeBoundQuery, preAggregatedQuery bool

	externalGrouping bool

	externalInstanceGrouping bool

	preAggregationQuery bool

	bitmapFilter bool
}

func (worker *Worker) initWorkerManager(executorId, poolElementSize, bufferElementSize int, executors []*Executor) *WorkerManager {

	valueBufferLength := utils.MaxPoolLength * 8

	if worker.queryEngineType == Log || worker.queryEngineType == DrillDown {

		valueBufferLength *= 2
	}

	keyBuffers := make([][]byte, bufferElementSize)

	valueBuffers := make([][]byte, bufferElementSize)

	memoryPoolDataTypes := make([]map[string]DataType, utils.QueryExecutors)

	memoryPoolIndices := make([]map[string]int, utils.QueryExecutors)

	columns := make([]map[string]int, utils.QueryExecutors)

	columnDataTypes := make([][]DataType, utils.QueryExecutors)

	columnPoolIndices := make([][]int, utils.QueryExecutors)

	columnGroups := make([]*intmap.Map[uint64, int], utils.QueryExecutors)

	conditionOperandValues := make([]utils.MotadataMap, utils.QueryExecutors)

	ordinals := make([]map[string]struct{}, utils.QueryExecutors)

	filters := make([]utils.MotadataMap, utils.QueryExecutors)

	drillDownFilters := make([]utils.MotadataMap, utils.QueryExecutors)

	conditionBitmaps := make([]bitmap.Bitmap, utils.QueryExecutors)

	missingGroupColumns := make([]*intmap.Set[int], utils.QueryExecutors)

	if (worker.workerId+1)%utils.Workers == 0 { // if worker-id is last init all executors data structure

		for i := range executors {

			memoryPoolDataTypes[i] = make(map[string]DataType)

			memoryPoolIndices[i] = make(map[string]int)

			columns[i] = make(map[string]int)

			columnDataTypes[i] = make([]DataType, poolElementSize)

			columnPoolIndices[i] = make([]int, poolElementSize)

			columnGroups[i] = intmap.New[uint64, int](worker.poolLength)

			conditionOperandValues[i] = make(utils.MotadataMap, 4)

			filters[i] = utils.MotadataMap{}

			drillDownFilters[i] = utils.MotadataMap{}

			ordinals[i] = map[string]struct{}{}

			conditionBitmaps[i] = bitmap.Bitmap{0}

			missingGroupColumns[i] = intmap.NewSet[int](16)

		}
	} else {

		memoryPoolDataTypes[executorId] = make(map[string]DataType)

		memoryPoolIndices[executorId] = make(map[string]int)

		columns[executorId] = make(map[string]int)

		columnDataTypes[executorId] = make([]DataType, poolElementSize)

		columnPoolIndices[executorId] = make([]int, poolElementSize)

		columnGroups[executorId] = intmap.New[uint64, int](worker.poolLength)

		conditionOperandValues[executorId] = make(utils.MotadataMap, 4)

		filters[executorId] = utils.MotadataMap{}

		drillDownFilters[executorId] = utils.MotadataMap{}

		ordinals[executorId] = map[string]struct{}{}

		conditionBitmaps[executorId] = bitmap.Bitmap{0}

		missingGroupColumns[executorId] = intmap.NewSet[int](16)
	}

	for i := 0; i < len(valueBuffers); i++ {

		bytes, err := utils.MmapAnonymous(valueBufferLength)

		if err != nil {

			workerLogger.Error(fmt.Sprintf("error %v occurred while mapping annonymous buffer for worker %v and index %v", err, worker.workerId, i))

			bytes = make([]byte, valueBufferLength)
		}

		valueBuffers[i] = bytes
	}

	tokenizers := make([]*utils.Tokenizer, 4)

	for i := range tokenizers {

		tokenizers[i] = &utils.Tokenizer{

			Tokens: make([]string, utils.TokenizerLength),
		}
	}

	return &WorkerManager{

		keyBuffers: keyBuffers,

		valueBuffers: valueBuffers,

		memoryPoolDataTypes: memoryPoolDataTypes,

		memoryPoolIndices: memoryPoolIndices,

		columnDataTypes: columnDataTypes,

		tokenizers: tokenizers,

		memoryPoolPositions: make([]int, utils.QueryExecutors),

		errors: make([]string, maxErrorLimit),

		columnPoolIndices: columnPoolIndices,

		conditionOperandValues: conditionOperandValues,

		columns: columns,

		filters: filters,

		drillDownFilters: drillDownFilters,

		columnGroups: columnGroups,

		resolvedColumnGroups: make([]string, worker.poolLength),

		WorkerEvents: make([]*WorkerEvent, utils.MaxWorkerEvents),

		externalGroupFilterOrdinals: make(map[string]*CompactedBitmap, 1000),

		positions: make(map[int32][]int, 1440),

		currentPositions: make(map[int32][]int, 1440),

		executors: executors,

		ordinals: ordinals,

		conditionBitmaps: conditionBitmaps,

		missingGroupColumns: missingGroupColumns,

		groups: make([]string, 10),

		records: make([]int, utils.QueryExecutors),

		missingColumns: make(map[string]struct{}),

		groupOrdinalPoolIndex: utils.NotAvailable,
	}
}

/*----------------------------------------------------------------------------------------------------------------*/

type WorkerEvent struct {
	keys []string

	storeName string

	group string

	groupOrdinal uint64

	keyElementSize int

	currentIndex int

	countIndex int

	minAggregationColumnIndex, maxAggregationColumnIndex, sumAggregationColumnIndex, countAggregationColumnIndex int

	timeWindowStartTick, timeWindowEndTick int32

	timeWindowQuery bool

	grouping, condition bool
}
