/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-05-09  			 Hardik Vala			MOTADATA-4911 Query2.0 First Merge
* 2025-06-04             <PERSON><PERSON><PERSON>ATA-5780 Called custom Munmap function
 */

package query

import (
	"fmt"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"runtime"
	"strings"
	"time"
)

var (
	workerLogger = utils.NewLogger("Query Worker", "query")

	conditionIterateThreshold = 20
)

/*----------------------------------------------------------------------------------------------------------------*/

// Worker is responsible for processing query tasks assigned by Executors.
// It handles data processing, aggregation, and event management for query execution.
// Workers operate in parallel to efficiently process large datasets.

// Start initializes the Worker and starts its main processing loop.
// It sets up all required components like pool manager, worker manager, and event manager,
// and launches a goroutine to handle incoming events.
//
// Parameters:
//   - executors: Array of Executor instances that will assign tasks to this worker
func (worker *Worker) Start(executors []*Executor) {

	bufferElementSize := utils.MaxWorkerEventKeyGroupLength + 8

	poolElementSize := 24

	executorId := worker.workerId / utils.Workers

	worker.PoolManager = worker.initPoolManager(executorId, poolElementSize, executors)

	worker.WorkerManager = worker.initWorkerManager(executorId, poolElementSize, bufferElementSize, executors)

	worker.Events = worker.initEventManager()

	for i := 0; i < utils.MaxWorkerEvents; i++ {

		worker.WorkerEvents[i] = &WorkerEvent{keys: make([]string, utils.MaxWorkerEventKeys)}
	}

	workerLogger.Info(fmt.Sprintf("query %v started with pool length %v", worker.workerId, worker.poolLength))

	go func() {

		utils.QueryEngineShutdownMutex.Add(1)

		defer utils.QueryEngineShutdownMutex.Done()

		for !worker.shutdown && !utils.GlobalShutdown {

			worker.run()
		}
	}()
}

// run is the main event loop for the Worker.
// It handles various events like timer ticks for memory pool shrinking,
// incoming query requests, cleanup notifications, and shutdown notifications.
// This function runs in a separate goroutine and continues until shutdown is requested.
func (worker *Worker) run() {

	timer := time.NewTicker(time.Second * time.Duration(utils.GetMemoryPoolShrinkTimerSeconds()))

	defer func() {

		if err := recover(); err != nil {

			timer.Stop()

			stackTraceBytes := make([]byte, 1<<20)

			workerLogger.Error(fmt.Sprintf("error %v occurred in worker %v", err, worker.workerId))

			workerLogger.Error(fmt.Sprintf("!!!STACK TRACE for worker %v!!! \n %v", worker.workerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	for {
		select {

		case <-timer.C:

			for id := range worker.memoryPools {

				if worker.memoryPools[id] != nil {

					worker.memoryPools[id].ShrinkPool()
				}
			}

		case event := <-worker.Requests:

			utils.Split(event, utils.KeySeparator, worker.tokenizers[0])

			worker.processEvents(event, worker.tokenizers[0].Tokens[0], StringToINT(worker.tokenizers[0].Tokens[2]), StringToINT(worker.tokenizers[0].Tokens[1]))

		case executorId := <-worker.cleanupNotifications:

			worker.cleanup(executorId)

		case _ = <-worker.ShutdownNotifications:

			for index := range worker.valueBuffers {

				if err := utils.Munmap(worker.valueBuffers[index]); err != nil {

					workerLogger.Warn(fmt.Sprintf("failed to unmap anonymous mapped buffer,reason: %v", err.Error()))
				}
			}

			for i := range worker.memoryPools {

				if worker.memoryPools[i] != nil {

					worker.memoryPools[i].Unmap()
				}
			}

			workerLogger.Info(fmt.Sprintf("query worker %v has been shutdown...", worker.workerId))

			worker.shutdown = true

			return

		}
	}
}

/*----------------------------------------------------------- Helper Functions ---------------------------------------------------------------------*/

// processEvents handles incoming query events from executors.
// It processes the event based on the store type (vertical or horizontal),
// executes the appropriate query processing logic, and notifies the executor when done.
//
// Parameters:
//   - event: The event string containing query information
//   - subQueryId: The ID of the sub-query being processed
//   - executorId: The ID of the executor that sent the event
//   - eventId: The ID of the event being processed
func (worker *Worker) processEvents(event, subQueryId string, executorId, eventId int) {

	defer worker.notifyExecutor(subQueryId, executorId, eventId)

	if utils.DebugEnabled() {

		workerLogger.Debug(fmt.Sprintf("request %v received on query worker %v", event, worker.workerId))
	}

	storeName := worker.WorkerEvents[eventId].storeName

	if storeName != utils.Empty {

		store := datastore.GetStore(storeName, utils.None, false, true, worker.encoders[executorId], worker.tokenizers[2])

		if store != nil {

			worker.uniqueId = subQueryId + utils.GroupSeparator + INTToStringValue(worker.workerId)

			if store.GetDatastoreType() == utils.StaticMetric {

				worker.processVerticalColumnQuery(eventId, executorId, store)

			} else {

				utils.Split(storeName, utils.HyphenSeparator, worker.tokenizers[0])

				if worker.tokenizers[0].Tokens[1] == utils.VerticalFormat {

					worker.processVerticalColumnQuery(eventId, executorId, store)
				} else {

					worker.processHorizontalColumnQuery(eventId, executorId, store)
				}
			}

		} else {

			if utils.DebugEnabled() {

				workerLogger.Debug(fmt.Sprintf("failed to execute query %v for worker %v, reason:store %v does not exist...", event, worker.workerId, storeName))

			}

		}

	}
}

// logError logs an error message and adds it to the worker's error collection.
// It has a maximum limit on the number of errors that can be stored.
//
// Parameters:
//   - error: The error message to log
//   - logError: Whether to also log the error to the logger (in addition to storing it)
func (worker *Worker) logError(error string, logError bool) {

	if logError {

		workerLogger.Error(error)
	}

	if worker.errorElementSize == maxErrorLimit {

		return
	}

	worker.errors[worker.errorElementSize] = error

	worker.errorElementSize++
}

// notifyExecutor informs the executor that the worker has completed processing an event.
// It cleans up resources and sends a notification through the appropriate channel
// based on the query engine type.
//
// Parameters:
//   - subQueryId: The ID of the sub-query being processed
//   - executorId: The ID of the executor to notify
//   - eventId: The ID of the event that was processed
func (worker *Worker) notifyExecutor(subQueryId string, executorId, eventId int) {

	clear(worker.missingColumns)

	if worker.executors[executorId].queryEngineType == DrillDown {

		worker.executors[executorId].waitGroup.Done()

	} else {

		worker.executors[executorId].queryResponses <- subQueryId + utils.GroupSeparator + INTToStringValue(eventId) + utils.GroupSeparator + INTToStringValue(worker.workerId)
	}

	worker.cleanUpConditionContext(executorId, true)
}

// checkOverflow checks if the number of groups exceeds the memory pool capacity.
// If an overflow is detected, it notifies the executor, waits for acknowledgment,
// and then clears resources to make room for new data.
//
// Parameters:
//   - executorId: The ID of the executor associated with this operation
//   - groups: The number of groups being processed
func (worker *Worker) checkOverflow(executorId int, groups int) {

	if groups > worker.memoryPools[executorId].GetPoolLength() {

		if utils.DebugEnabled() {

			workerLogger.Debug(fmt.Sprintf("worker %v grouping limit reached for executor %v , hence notifying overflow to executor..", worker.workerId, executorId))
		}

		worker.executors[executorId].OverflowNotifications <- worker.uniqueId

		// let's wait for the response

		<-worker.executors[executorId].OverflowedAcks[worker.workerId]

		if utils.DebugEnabled() {

			workerLogger.Debug(fmt.Sprintf("worker %v grouping overflow acks received from executor %v", worker.workerId, executorId))
		}

		worker.columnGroups[executorId].Clear()

		clear(worker.resolvedColumnGroups)

		for column, columnIndex := range worker.columns[executorId] {

			if utils.DebugEnabled() {

				workerLogger.Debug(fmt.Sprintf("worker pool %v (data type -> %v) of column was released %v (executor %v)", worker.columnPoolIndices[executorId][columnIndex], worker.columnDataTypes[executorId][columnIndex], column, executorId))
			}

			delete(worker.columns[executorId], column)

			switch worker.columnDataTypes[executorId][columnIndex] {

			case Int64:

				worker.memoryPools[executorId].ReleaseINT64Pool(worker.columnPoolIndices[executorId][columnIndex])

			case Float64:

				worker.memoryPools[executorId].ReleaseFLOAT64Pool(worker.columnPoolIndices[executorId][columnIndex])

			case String:

				worker.memoryPools[executorId].ReleaseStringPool(worker.columnPoolIndices[executorId][columnIndex])

			}

			worker.columnDataTypes[executorId][columnIndex] = Int8

			worker.columnPoolIndices[executorId][columnIndex] = -1
		}

		worker.memoryPoolPositions[executorId] = 0
	}

}

// setColumnContext sets up the context for a column in the worker's memory pool.
// It handles data type conversions and memory allocation for different data types.
// If the column already exists, it may upgrade its data type if needed.
//
// Parameters:
//   - executorId: The ID of the executor associated with this operation
//   - column: The name of the column
//   - dataType: The data type of the column
//   - fixedDataTypeColumn: Whether the column has a fixed data type
//
// Returns:
//   - int: The index of the column in the worker's column array
func (worker *Worker) setColumnContext(executorId int, column string, dataType DataType, fixedDataTypeColumn bool) int {

	found, columnIndex := isColumnExist(column, worker.columns[executorId])

	if fixedDataTypeColumn {

		if !found {

			if worker.columnDataTypes[executorId][columnIndex] < dataType {

				worker.columns[executorId][column] = columnIndex

				poolIndex := -1

				switch {

				case dataType == String:

					poolIndex, _ = worker.memoryPools[executorId].AcquireStringPool(utils.NotAvailable)

				case dataType == Int8:

					poolIndex, _ = worker.memoryPools[executorId].AcquireINT8Pool(utils.NotAvailable)

				case dataType == Int16:

					poolIndex, _ = worker.memoryPools[executorId].AcquireINT16Pool(utils.NotAvailable)

				case dataType == Int24 || dataType == Int32:

					poolIndex, _ = worker.memoryPools[executorId].AcquireINT32Pool(utils.NotAvailable)

				case dataType == Int64:

					poolIndex, _ = worker.memoryPools[executorId].AcquireINT64Pool(utils.NotAvailable)

					worker.memoryPools[executorId].ResetINT64Pool(poolIndex, utils.NotAvailable, utils.DummyINT64Value)
				}

				worker.columnPoolIndices[executorId][columnIndex] = poolIndex

				worker.columnDataTypes[executorId][columnIndex] = dataType

			}
		}
	} else {

		if found {

			if worker.columnDataTypes[executorId][columnIndex] < dataType {

				if worker.columnDataTypes[executorId][columnIndex] == Int64 {

					if dataType >= Float8 && dataType <= Float64 {

						// means we have to convert existing int64 to float64...

						poolIndex, values := worker.memoryPools[executorId].AcquireFLOAT64Pool(len(worker.memoryPools[executorId].GetINT64Pool(worker.columnPoolIndices[executorId][columnIndex])))

						worker.memoryPools[executorId].ResetFLOAT64Pool(poolIndex, len(worker.memoryPools[executorId].GetINT64Pool(worker.columnPoolIndices[executorId][columnIndex])), utils.DummyFLOAT64Value)

						values = INT64ToFLOAT64DummyValues(worker.memoryPools[executorId].GetINT64Pool(worker.columnPoolIndices[executorId][columnIndex]), values)

						worker.memoryPools[executorId].ReleaseINT64Pool(worker.columnPoolIndices[executorId][columnIndex])

						worker.columnDataTypes[executorId][columnIndex] = Float64

						worker.columnPoolIndices[executorId][columnIndex] = poolIndex

					} else if dataType == String {

						// means we have to convert existing int64 to string...

						poolIndex, values := worker.memoryPools[executorId].AcquireStringPool(len(worker.memoryPools[executorId].GetINT64Pool(worker.columnPoolIndices[executorId][columnIndex])))

						values = INT64ToStringDummyValues(worker.memoryPools[executorId].GetINT64Pool(worker.columnPoolIndices[executorId][columnIndex]), values)

						worker.memoryPools[executorId].ReleaseINT64Pool(worker.columnPoolIndices[executorId][columnIndex])

						worker.columnDataTypes[executorId][columnIndex] = String

						worker.columnPoolIndices[executorId][columnIndex] = poolIndex
					}
				} else if worker.columnDataTypes[executorId][columnIndex] == Float64 {

					// means we have to convert existing float64 to string...

					poolIndex, values := worker.memoryPools[executorId].AcquireStringPool(len(worker.memoryPools[executorId].GetFLOAT64Pool(worker.columnPoolIndices[executorId][columnIndex])))

					values = FLOAT64ToStringDummyValues(worker.memoryPools[executorId].GetFLOAT64Pool(worker.columnPoolIndices[executorId][columnIndex]), values)

					worker.memoryPools[executorId].ReleaseFLOAT64Pool(worker.columnPoolIndices[executorId][columnIndex])

					worker.columnDataTypes[executorId][columnIndex] = String

					worker.columnPoolIndices[executorId][columnIndex] = poolIndex
				}
			}

		} else {

			worker.columns[executorId][column] = columnIndex

			if dataType <= Int64 {

				poolIndex, _ := worker.memoryPools[executorId].AcquireINT64Pool(utils.NotAvailable)

				worker.memoryPools[executorId].ResetINT64Pool(poolIndex, utils.NotAvailable, utils.DummyINT64Value)

				worker.columnPoolIndices[executorId][columnIndex] = poolIndex

				worker.columnDataTypes[executorId][columnIndex] = Int64

			} else if dataType >= Float8 && dataType <= Float64 {

				poolIndex, _ := worker.memoryPools[executorId].AcquireFLOAT64Pool(utils.NotAvailable)

				worker.memoryPools[executorId].ResetFLOAT64Pool(poolIndex, utils.NotAvailable, utils.DummyFLOAT64Value)

				worker.columnPoolIndices[executorId][columnIndex] = poolIndex

				worker.columnDataTypes[executorId][columnIndex] = Float64

			} else if dataType == String {

				poolIndex, _ := worker.memoryPools[executorId].AcquireStringPool(utils.NotAvailable)

				worker.columnPoolIndices[executorId][columnIndex] = poolIndex

				worker.columnDataTypes[executorId][columnIndex] = String

			}

		}
	}

	return columnIndex
}

func (worker *Worker) getBatchSize(records, executorId int, condition bool) int {

	length := records

	if condition {

		size, _ := worker.conditionBitmaps[executorId].Max()

		length = int(size + 1)

		// if condition operand array size is 10 and max bit is 10 length is 10 but aggregated operand array size is 8 than need this condition
		if length > records {

			length = records
		}
	}

	if worker.batchSize > 0 && length > worker.batchSize {

		length = worker.batchSize
	}

	return length
}

func (worker *Worker) getAggregationColumnIndices(aggregationFunc AggregationFunc, executorId int, column string, dataType DataType) (int, int, int, int) {

	sumAggregationColumnIndex, countAggregationColumnIndex, maxAggregationColumnIndex, minAggregationColumnIndex := -1, -1, -1, -1

	if aggregationFunc == MinMaxSumCount {

		minAggregationColumnIndex = worker.setColumnContext(executorId, column+MinSuffix, dataType, false)

		maxAggregationColumnIndex = worker.setColumnContext(executorId, column+utils.MaxSuffix, dataType, false)

		sumAggregationColumnIndex = worker.setColumnContext(executorId, column+SumSuffix, dataType, false)

		countAggregationColumnIndex = worker.setColumnContext(executorId, column+CountSuffix, Int64, true)

	} else if aggregationFunc == MinMaxSum {

		minAggregationColumnIndex = worker.setColumnContext(executorId, column+MinSuffix, dataType, false)

		maxAggregationColumnIndex = worker.setColumnContext(executorId, column+utils.MaxSuffix, dataType, false)

		sumAggregationColumnIndex = worker.setColumnContext(executorId, column+SumSuffix, dataType, false)

	} else if aggregationFunc == MinMaxCount {

		minAggregationColumnIndex = worker.setColumnContext(executorId, column+MinSuffix, dataType, false)

		maxAggregationColumnIndex = worker.setColumnContext(executorId, column+utils.MaxSuffix, dataType, false)

		countAggregationColumnIndex = worker.setColumnContext(executorId, column+CountSuffix, Int64, true)

	} else if aggregationFunc == MinMax {

		minAggregationColumnIndex = worker.setColumnContext(executorId, column+MinSuffix, dataType, false)

		maxAggregationColumnIndex = worker.setColumnContext(executorId, column+utils.MaxSuffix, dataType, false)

	} else if aggregationFunc == MinSumCount {

		minAggregationColumnIndex = worker.setColumnContext(executorId, column+MinSuffix, dataType, false)

		sumAggregationColumnIndex = worker.setColumnContext(executorId, column+SumSuffix, dataType, false)

		countAggregationColumnIndex = worker.setColumnContext(executorId, column+CountSuffix, Int64, true)

	} else if aggregationFunc == MinSum {

		minAggregationColumnIndex = worker.setColumnContext(executorId, column+MinSuffix, dataType, false)

		sumAggregationColumnIndex = worker.setColumnContext(executorId, column+SumSuffix, dataType, false)

	} else if aggregationFunc == MinCount {

		minAggregationColumnIndex = worker.setColumnContext(executorId, column+MinSuffix, dataType, false)

		countAggregationColumnIndex = worker.setColumnContext(executorId, column+CountSuffix, Int64, true)

	} else if aggregationFunc == Min {

		minAggregationColumnIndex = worker.setColumnContext(executorId, column+MinSuffix, dataType, false)

	} else if aggregationFunc == MaxSumCount {

		maxAggregationColumnIndex = worker.setColumnContext(executorId, column+utils.MaxSuffix, dataType, false)

		sumAggregationColumnIndex = worker.setColumnContext(executorId, column+SumSuffix, dataType, false)

		countAggregationColumnIndex = worker.setColumnContext(executorId, column+CountSuffix, Int64, true)

	} else if aggregationFunc == MaxSum {

		maxAggregationColumnIndex = worker.setColumnContext(executorId, column+utils.MaxSuffix, dataType, false)

		sumAggregationColumnIndex = worker.setColumnContext(executorId, column+SumSuffix, dataType, false)

	} else if aggregationFunc == MaxCount {

		maxAggregationColumnIndex = worker.setColumnContext(executorId, column+utils.MaxSuffix, dataType, false)

		countAggregationColumnIndex = worker.setColumnContext(executorId, column+CountSuffix, Int64, true)

	} else if aggregationFunc == Max {

		maxAggregationColumnIndex = worker.setColumnContext(executorId, column+utils.MaxSuffix, dataType, false)

	} else if aggregationFunc == SumCount {

		sumAggregationColumnIndex = worker.setColumnContext(executorId, column+SumSuffix, dataType, false)

		countAggregationColumnIndex = worker.setColumnContext(executorId, column+CountSuffix, Int64, true)

	} else if aggregationFunc == Sum {

		sumAggregationColumnIndex = worker.setColumnContext(executorId, column+SumSuffix, dataType, false)

	} else if aggregationFunc == Count {

		countAggregationColumnIndex = worker.setColumnContext(executorId, column+CountSuffix, Int64, true)
	}

	return minAggregationColumnIndex, maxAggregationColumnIndex, sumAggregationColumnIndex, countAggregationColumnIndex
}

func (worker *Worker) appendMissingValues(batchSize, executorId int, event *WorkerEvent, column string) {

	position := worker.memoryPoolPositions[executorId]

	// in case of ordinal column we need to append the dummy ordinal in place of missing data
	if strings.Contains(column, utils.OrdinalSuffix) {

		event.currentIndex = worker.setColumnContext(executorId, column+utils.ValueSuffix, Int64, false)

		values := worker.memoryPools[executorId].GetINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex])

		if position+batchSize > len(values) {

			values = worker.memoryPools[executorId].ExpandINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex], len(values)+utils.OverflowLength)

		}

		for m := 0; m < batchSize; m++ {

			values[position] = storage.DummyStringMappingOrdinal

			position++
		}

	} else {

		//Note  -For missing scenarios, we provide the result only as a string. The reason is that if we rely on the data type, in an incremental query,
		//Column 1 might have an `int` data type initially, but in the next query, if the first position is missing, the worker cannot determine the data type and packs the data as a string.
		//As a result, the first response in the UI is an `int`, while the second is a string, which becomes difficult to handle. The same issue occurs in test cases as well.

		event.currentIndex = worker.setColumnContext(executorId, column+utils.ValueSuffix, String, false)

		values := worker.memoryPools[executorId].GetStringPool(worker.columnPoolIndices[executorId][event.currentIndex])

		if position+batchSize > len(values) {

			values = worker.memoryPools[executorId].ExpandStringPool(worker.columnPoolIndices[executorId][event.currentIndex], len(values)+utils.OverflowLength)

		}

		for m := 0; m < batchSize; m++ {

			values[position] = utils.Empty

			position++
		}
	}
}

func (worker *Worker) setAggregationFunc(aggregation string) {

	if strings.HasSuffix(aggregation, AvgFunc) {

		worker.avgFunc = true

		worker.sumFunc = true

		worker.countFunc = true

	} else if strings.HasSuffix(aggregation, SumFunc) {

		worker.sumFunc = true

	} else if strings.HasSuffix(aggregation, CountFunc) {

		worker.countFunc = true

	} else if strings.HasSuffix(aggregation, MinFunc) {

		worker.minFunc = true

	} else if strings.HasSuffix(aggregation, MaxFunc) {

		worker.maxFunc = true

	} else if strings.HasSuffix(aggregation, LastFunc) {

		worker.lastFunc = true
	}
}

func (worker *Worker) resetWorkerHorizontalAggregationFuncs() {

	worker.lastFunc = false

	worker.maxFunc = false

	worker.minFunc = false

	worker.avgFunc = false

	worker.countFunc = false

	worker.sumFunc = false
}

func getAggregationFunction(min, max, sum, count bool) AggregationFunc {

	var aggregationFunc AggregationFunc

	if min {

		aggregationFunc |= 1 << 3

	}

	if max {

		aggregationFunc |= 1 << 2

	}

	if sum {

		aggregationFunc |= 1 << 1

	}

	if count {

		aggregationFunc |= 1 << 0

	}

	return aggregationFunc
}

func GetSearchEventId(tick, plugin string, part uint16, position int) string {

	return tick + utils.HyphenSeparator + UINT16ToStringValue(part) + utils.HyphenSeparator + INTToStringValue(position) + utils.HyphenSeparator + plugin
}

func isColumnExist(column string, columns map[string]int) (bool, int) {

	if index, exist := columns[column]; exist {

		return true, index
	}

	return false, len(columns)

}

/*----------------------------------------------------------- Cleanup Functions ---------------------------------------------------------------------*/

func (worker *Worker) cleanup(executorId int) {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			workerLogger.Error(fmt.Sprintf("error %v occurred while cleaning worker %v by executor %v", err, worker.workerId, executorId))

			workerLogger.Error(fmt.Sprintf("!!!STACK TRACE for worker %v!!! \n %v", worker.workerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

		worker.executors[executorId].CleanupAcks[worker.workerId] <- executorId
	}()

	if utils.DebugEnabled() {

		workerLogger.Debug(fmt.Sprintf("Cleanup routine called of worker %v by executor %v", worker.workerId, executorId))

		workerLogger.Debug(fmt.Sprintf("groups size %v of worker %v (executor %v)", worker.columnGroups[executorId].Len(), worker.workerId, executorId))
	}

	worker.errorElementSize = 0

	worker.records[executorId] = 0

	worker.groupOrdinalPoolIndex = utils.NotAvailable

	worker.columnGroups[executorId].Clear()

	clear(worker.resolvedColumnGroups)

	worker.memoryPoolPositions[executorId] = 0

	clear(worker.positions)

	clear(worker.currentPositions)

	clear(worker.conditionOperandValues[executorId])

	clear(worker.filters[executorId])

	clear(worker.drillDownFilters[executorId])

	clear(worker.ordinals[executorId])

	clear(worker.missingColumns)

	worker.missingGroupColumns[executorId].Clear()

	for column, columnIndex := range worker.columns[executorId] {

		if utils.DebugEnabled() {

			workerLogger.Debug(fmt.Sprintf("worker pool %v (data type -> %v) of column was released %v (executor %v)", worker.columnPoolIndices[executorId][columnIndex], worker.columnDataTypes[executorId][columnIndex], column, executorId))
		}

		delete(worker.columns[executorId], column)

		switch worker.columnDataTypes[executorId][columnIndex] {

		case Int64:

			worker.memoryPools[executorId].ReleaseINT64Pool(worker.columnPoolIndices[executorId][columnIndex])

		case Float64:

			worker.memoryPools[executorId].ReleaseFLOAT64Pool(worker.columnPoolIndices[executorId][columnIndex])

		case String:

			worker.memoryPools[executorId].ReleaseStringPool(worker.columnPoolIndices[executorId][columnIndex])

		}

		worker.columnDataTypes[executorId][columnIndex] = Int8

		worker.columnPoolIndices[executorId][columnIndex] = -1
	}

	worker.memoryPools[executorId].TestPoolLeak()
}

func (worker *Worker) cleanUpConditionContext(executorId int, cleanupCondition bool) {

	for key := range worker.memoryPoolDataTypes[executorId] {

		dataType := worker.memoryPoolDataTypes[executorId][key]

		if dataType == Float64 {

			worker.memoryPools[executorId].ReleaseFLOAT64Pool(worker.memoryPoolIndices[executorId][key])

		} else if dataType == Int8 {

			worker.memoryPools[executorId].ReleaseINT8Pool(worker.memoryPoolIndices[executorId][key])

		} else if dataType == Int16 {

			worker.memoryPools[executorId].ReleaseINT16Pool(worker.memoryPoolIndices[executorId][key])

		} else if dataType == Int24 || dataType == Int32 {

			worker.memoryPools[executorId].ReleaseINT32Pool(worker.memoryPoolIndices[executorId][key])

		} else if dataType >= Int40 && dataType <= Int64 {

			worker.memoryPools[executorId].ReleaseINT64Pool(worker.memoryPoolIndices[executorId][key])

		} else if dataType == String {

			worker.memoryPools[executorId].ReleaseStringPool(worker.memoryPoolIndices[executorId][key])

		}

		delete(worker.memoryPoolDataTypes[executorId], key)

		delete(worker.memoryPoolIndices[executorId], key)

	}

	if worker.groupOrdinalPoolIndex != utils.NotAvailable {

		worker.memoryPools[executorId].ReleaseUINT64Pool(worker.groupOrdinalPoolIndex)

		worker.groupOrdinalPoolIndex = utils.NotAvailable
	}

	if cleanupCondition {

		if worker.conditionBitmaps[executorId] != nil {

			worker.conditionBitmaps[executorId].Clear()
		}
	}
}

func (worker *Worker) cleanupLargePool(blobPoolIndex, executorId int) int {

	if blobPoolIndex != utils.NotAvailable {

		worker.memoryPools[executorId].ReleaseBytePool(blobPoolIndex)

		blobPoolIndex = utils.NotAvailable
	}

	return blobPoolIndex
}
