/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-05-09  			 Hardik Vala			MOTADATA-4911 Query2.0 First Merge
* 2025-06-25			 Swapnil A. Dave		MOTADATA-6555 Admin role changes for object deprovision
* 2025-06-23             Vedant Dokania         Motadata-6370 Mapping operand changes to get the instance type store
* 2025-06-04             <PERSON><PERSON><PERSON>            MOTADATA-5780 Called custom MmapAnonymous function
 */

/*
 * Package horizontalparser implements the parsing and processing of horizontal queries in the Motadata database.
 *
 * Horizontal queries are time-series queries that retrieve and analyze data across time intervals.
 * This file contains the core functionality for:
 * 1. Parsing query specifications from client requests
 * 2. Evaluating filter conditions to select relevant data
 * 3. Qualifying keys and ticks based on query parameters
 * 4. Resolving mappings and ordinals for efficient data access
 * 5. Processing different types of conditions (equal, range, etc.) for both string and numeric data
 * 6. Building and evaluating complex condition expressions
 * 7. Handling aggregations and grouping of query results
 *
 * The horizontal query processing is optimized for performance with techniques like:
 * - Memory mapping for large data buffers
 * - Parallel processing of query segments
 * - Bitmap-based filtering for fast set operations
 * - Efficient type conversions based on data characteristics
 */

package query

import (
	"encoding/binary"
	"errors"
	"fmt"
	"github.com/kelindar/bitmap"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"reflect"
	"runtime"
	"strings"
	"time"
)

// isHorizontalQuery determines if the query should be processed as a horizontal (time-series) query.
// A horizontal query typically analyzes data across time intervals, while a vertical query
// analyzes data across different entities at a specific point in time.
//
// Parameters:
//   - dataSource: Contains the query specification including filters and data points
//   - filter: The filter field to check for specific conditions
//
// Returns:
//   - true if the query should be processed as a horizontal query, false otherwise
func (executor *Executor) isHorizontalQuery(dataSource utils.MotadataMap, filter string) bool {
	// Extract the data filter from the query specification
	dataFilter := dataSource.GetMapValue(Filters).GetMapValue(DataFilter)

	// If no data filter is specified, default to horizontal query
	if len(dataFilter) == 0 {
		return true
	}

	horizontalQuery := true
	valid := true

	// Check if all data points are valid for horizontal aggregation
	for _, dataPoint := range dataSource.GetMapListValue(DataPoints) {
		// If any data point has an aggregator, it's a horizontal query
		if dataPoint.GetStringValue(Aggregator) != utils.Empty {
			return true
		}

		// Check if the data point is valid for horizontal aggregation
		if !datastore.IsHorizontalAggregationField(executor.plugin + utils.GroupSeparator + dataPoint.GetStringValue(DataPoint)) {
			valid = false
			break
		}
	}

	// If all data points are valid for horizontal aggregation, check filter conditions
	if valid {
		groups := dataFilter.GetMapListValue(Groups)
		valid = false

		// Look for specific filter conditions that would make this a vertical query
		for _, group := range groups {
			if valid {
				break
			}

			if group.GetStringValue(Filter) == include {
				for _, condition := range group.GetMapListValue(Conditions) {
					// Check for an equality condition on the specified filter
					if condition.GetStringValue(Operator) == equal && condition.GetStringValue(Operand) == filter {
						// Store the filter value and mark as vertical query
						executor.value = condition.GetStringValue(utils.Value)
						horizontalQuery = false
						valid = true
						break
					}
				}
			}
		}
	}

	return horizontalQuery
}

func (executor *Executor) parseHorizontalQuery(dataSource utils.MotadataMap, dataPoints []utils.MotadataMap, plugins []interface{}, histogramQuery bool) error {

	if executor.request.Contains(DrillDownQueryContext) {

		executor.logQueryPlan("incremental query received...loading previous drill down query context.")

		if err := executor.loadDrillDownQueryContext(); err != nil {

			return err
		}

		if executor.qualifiedDateIndex == 0 || (executor.request.Contains(PaginationQuery) && executor.request.GetStringValue(PaginationQuery) == utils.Yes) {

			return executor.initDrillDown(false, dataSource)
		}
	}

	timestamp := time.Now().UnixMilli()

	filters := dataSource.GetMapValue(Filters)

	drillDownFilter := filters.GetMapValue(DrillDownFilter)

	dataFilter := filters.GetMapValue(DataFilter)

	resultFilter := filters.GetMapValue(ResultFilter)

	dates := utils.QualifyDates(executor.fromDateTime, executor.toDateTime)

	if len(resultFilter) > 0 {

		executor.resultFilter = true
	}

	entities := dataSource.GetMapValue(Entities)

	if executor.queryEngineType == Metric || executor.queryEngineType == AIOps {

		adminRole := executor.request.GetStringValue(AdminRole) == utils.Yes

		if executor.datastoreType == utils.ObjectStatusMetric {

			adminRole = false
		}

		executor.preAggregationTimeInterval = executor.chooseAggregationTimeInterval(utils.UnixToSeconds(executor.fromDateTime.Unix()), utils.UnixToSeconds(executor.toDateTime.Unix()), false)

		//apply the interval specified in the query if the key is present.
		if executor.preAggregationJobId != utils.NotAvailable && executor.request.Contains(utils.Interval) {

			executor.preAggregationTimeInterval = executor.request.GetIntValue(utils.Interval)
		}

		executor.conditionOperand = datastore.Object

		if !executor.instanceQuery {

			executor.conditionOperand = utils.ObjectId

			executor.mappingOperand = executor.conditionOperand

			// Since the app does not manage unprovisioned objects, each scalar query needs to filter them out, which may impact query performance.
			// Now, the database will filter out objects on every scalar query instead of performing a full scan.
			adminRole = false
		}

		if executor.externalGroupIndex == utils.NotAvailable && executor.GroupColumnElementSize == 1 { // means group by monitor or status flap history

			executor.conditionOperand = utils.ObjectId

			executor.mappingOperand = utils.ObjectId

		} else if executor.externalGroupIndex >= 0 && !executor.externalInstanceGrouping {

			executor.conditionOperand = utils.ObjectId

			executor.mappingOperand = utils.ObjectId
		}

		if executor.conditionOperand == utils.ObjectId && executor.plugin == datastore.NetRouteStatusPlugin {

			executor.conditionOperand = utils.NetRouteId

			executor.mappingOperand = utils.NetRouteId
		}

		executor.qualifyHorizontalTicksPhase1(dates, plugins)

		if len(executor.dates) == 0 {

			return errors.New(fmt.Sprintf(utils.ErrorKeyQualification, utils.ErrorPostingListLookupFailed))
		}

		executor.preFilterQuery = false

		conditionIndex := 0

		executor.MappingBitmaps[conditionIndex] = &bitmap.Bitmap{0}

		if len(executor.ordinals) > 0 { // external grouping bitmaps

			for ordinal := range executor.ordinals {

				executor.MappingBitmaps[conditionIndex].Set(StringToUINT32(ordinal))
			}

		} else {

			filter := false

			// for already filtered objets
			if len(executor.fields) > 0 && len(dataFilter) > 0 {

				filter = true

				if executor.conditionOperand == datastore.Object {

					for key, value := range executor.fields {

						delete(executor.fields, key)

						executor.fields[strings.ReplaceAll(key, utils.KeySeparator, utils.GroupSeparator)] = value
					}
				} else {

					for key, value := range executor.fields {

						delete(executor.fields, key)

						utils.Split(key, utils.KeySeparator, executor.tokenizers[0])

						executor.fields[executor.tokenizers[0].Tokens[0]] = value
					}
				}
			}

			store := datastore.GetStore(executor.mappingOperand+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, false, true, executor.encoder, executor.tokenizers[0])

			if store == nil {

				return errors.New(fmt.Sprintf(utils.ErrorAcquireStore, executor.mappingOperand+utils.HyphenSeparator+datastore.Mappings))
			}

			if filter {

				if executor.conditionOperand == datastore.Object {

					poolIndex, values := executor.memoryPool.AcquireStringPool(len(executor.fields))

					i := 0

					for key := range executor.fields {

						values[i] = key

						i++
					}

					err, mapperPoolIndex, ordinals := store.MapStringValues(poolIndex, executor.encoder, i)

					if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

						executor.memoryPool.ReleaseStringPool(poolIndex)

						return err
					}

					for _, ordinal := range ordinals {

						executor.MappingBitmaps[conditionIndex].Set(uint32(ordinal))
					}

					executor.memoryPool.ReleaseINT32Pool(mapperPoolIndex)

					executor.memoryPool.ReleaseStringPool(poolIndex)

				} else {

					poolIndex, values := executor.memoryPool.AcquireINT64Pool(len(executor.fields))

					i := 0

					for key := range executor.fields {

						values[i] = StringToINT64(key)

						i++
					}

					err, mapperPoolIndex, ordinals := store.MapNumericValues(poolIndex, executor.encoder, i)

					if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

						executor.memoryPool.ReleaseINT64Pool(poolIndex)

						return err
					}

					for _, ordinal := range ordinals {

						executor.MappingBitmaps[conditionIndex].Set(uint32(ordinal))
					}

					executor.memoryPool.ReleaseINT64Pool(poolIndex)

					executor.memoryPool.ReleaseINT32Pool(mapperPoolIndex)
				}

			} else if !adminRole {

				if executor.conditionOperand == datastore.Object {

					err := store.GetAllPrefixMappings(entities, &executor.stringMappings[conditionIndex], executor.tokenizers[1], utils.GroupSeparator)

					if err != nil {

						return err
					}

					executor.stringMappings[conditionIndex].Iter(func(key string, ordinal int32) (stop bool) {

						executor.stringMappings[conditionIndex].Delete(key)

						executor.MappingBitmaps[conditionIndex].Set(uint32(ordinal))

						return stop
					})

					executor.stringMappings[conditionIndex].Clear()

				} else {

					poolIndex, values := executor.memoryPool.AcquireINT64Pool(len(entities))

					i := 0

					for key := range entities {

						values[i] = StringToINT64(key)

						i++
					}

					err, mapperPoolIndex, ordinals := store.MapNumericValues(poolIndex, executor.encoder, i)

					if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

						executor.memoryPool.ReleaseINT64Pool(poolIndex)

						return err
					}

					for _, ordinal := range ordinals {

						executor.MappingBitmaps[conditionIndex].Set(uint32(ordinal))
					}

					executor.memoryPool.ReleaseINT64Pool(poolIndex)

					executor.memoryPool.ReleaseINT32Pool(mapperPoolIndex)
				}
			} else if executor.request[LastQueryStep] != utils.Yes && executor.externalGroupIndex == utils.NotAvailable && executor.category == TopN { //bug-2723

				visualizationProperties := executor.request.GetMapValue(VisualizationProperties)

				if visualizationProperties.Contains(Sparkline) && visualizationProperties.GetStringValue(Sparkline) == utils.Yes {

					if executor.conditionOperand == datastore.Object {

						_ = store.GetAllPrefixMappings(entities, &executor.stringMappings[conditionIndex], executor.tokenizers[1], utils.GroupSeparator)

						executor.stringMappings[conditionIndex].Iter(func(_ string, ordinal int32) (stop bool) {

							executor.entities.Add(utils.GetHash64([]byte(INT32ToStringValue(ordinal))))

							return stop
						})
					} else {

						poolIndex, values := executor.memoryPool.AcquireINT64Pool(len(entities))

						i := 0

						for key := range entities {

							values[i] = StringToINT64(key)

							i++
						}

						err, ordinalPoolIndex, ordinals := store.MapNumericValues(poolIndex, executor.encoder, i)

						executor.memoryPool.ReleaseINT64Pool(poolIndex)

						if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

							executor.memoryPool.ReleaseINT32Pool(ordinalPoolIndex)

							return err
						}

						for _, ordinal := range ordinals {

							executor.entities.Add(utils.GetHash64([]byte(INT32ToStringValue(ordinal))))
						}

						executor.memoryPool.ReleaseINT32Pool(ordinalPoolIndex)
					}
				}
			}
		}

		if len(dataFilter) > 0 || !adminRole {

			executor.bitmapFilter = true

			executor.conditionExpression = utils.Empty

			executor.conditionExpression += contains + startBracket + "$[\"" + executor.conditionOperand + "\"]" + utils.CommaSeparator + datastore.Objects + endBracket
		}

		if executor.GroupColumnElementSize > 0 && executor.externalGroupIndex == utils.NotAvailable {

			executor.lookupOrdinal = true
		}

		aggregatedColumns := map[string]map[string]struct{}{}

		executor.keyPoolIndex, executor.keys = executor.memoryPool.AcquireStringPool(utils.NotAvailable)

		for index, dataPoint := range dataPoints {

			column := dataPoint.GetStringValue(DataPoint)

			aggregator := dataPoint.GetStringValue(Aggregator)

			if statisticalFunc := dataPoint.GetStringValue(StatisticalFunc); statisticalFunc != utils.Empty {

				executor.statisticalFuncs[index] = statisticalFunc

			}

			if aggregator == Sparkline {

				continue
			}

			//Replace the column with shadow counter original value
			if datastore.IsShadowCounter(column) {

				executor.shadowCounterIndices.Add(index)

				column = datastore.FlipShadowCounter(column)
			}

			executor.aggregations[aggregator] += 1

			executor.columnIndices[column+utils.DotSeparator+aggregator] = index

			executor.Columns[executor.ColumnElementSize] = column + utils.KeySeparator + aggregator

			executor.ColumnElementSize++

			if _, ok := aggregatedColumns[column]; !ok {

				aggregatedColumns[column] = make(map[string]struct{})
			}

			for _, plugin := range dataPoint.GetSliceValue(utils.Plugins) {

				key := ToString(plugin) + utils.KeySeparator

				if aggregator == AvgFunc {

					aggregatedColumns[column][key+AvgFunc] = struct{}{}

					delete(aggregatedColumns[column], key+SumFunc)

					delete(aggregatedColumns[column], key+CountFunc)

				} else if aggregator == SumFunc || aggregator == CountFunc {

					if _, ok := aggregatedColumns[column][key+AvgFunc]; !ok {

						aggregatedColumns[column][key+aggregator] = struct{}{}
					}
				} else {

					aggregatedColumns[column][key+aggregator] = struct{}{}
				}
			}

			if executor.externalGroupIndex > 0 {

				executor.conditionExpression = utils.Empty

				executor.conditionExpression += contains + startBracket + "$[\"" + executor.conditionOperand + "\"]" + utils.CommaSeparator + datastore.Objects + endBracket
			}

		}

		executor.qualifyAggregatedProbes()

		for date := range executor.dates {

			tickPoolIndex, ticks := executor.qualifyHorizontalTicksPhase2(date, !executor.preFilterQuery && executor.preAggregationQuery)

			if len(ticks) == 0 {

				if tickPoolIndex != utils.NotAvailable {

					executor.memoryPool.ReleaseINT32Pool(tickPoolIndex)
				}

				continue
			}

			for aggregatedColumn, qualifiedPlugins := range aggregatedColumns {

				poolIndex, columns := executor.memoryPool.AcquireStringPool(len(qualifiedPlugins))

				columnIndex := 0

				for plugin := range qualifiedPlugins {

					columns[columnIndex] = plugin

					columnIndex++
				}

				executor.qualifyHorizontalKeysPhase2(ticks, columns, date+utils.HyphenSeparator+datastore.HorizontalStore+utils.HyphenSeparator+aggregatedColumn+utils.HyphenSeparator+INTToStringValue(executor.preAggregationTimeInterval))

				if strings.HasSuffix(columns[0], LastFunc) {

					executor.qualifyHorizontalKeysPhase2(ticks, columns, date+utils.HyphenSeparator+datastore.HorizontalStore+utils.HyphenSeparator+aggregatedColumn+utils.HyphenSeparator+utils.Garbage+utils.HyphenSeparator+INTToStringValue(executor.preAggregationTimeInterval))
				}

				executor.memoryPool.ReleaseStringPool(poolIndex)
			}

			executor.memoryPool.ReleaseINT32Pool(tickPoolIndex)
		}

	} else {

		if executor.queryEngineType == DrillDown {

			if executor.qualifiedDateIndex != 0 {

				dates = []time.Time{time.UnixMilli(int64(executor.qualifiedDateIndex)).UTC()}

				if utils.QueryPlanLogging {

					executor.logQueryPlan(fmt.Sprintf("incremental date qualified %v", dates[0]))
				}
			} else {

				if utils.QueryPlanLogging {

					executor.logQueryPlan(fmt.Sprintf("%v dates qualified", len(dates)))
				}

				event := executor.drillDownEvent

				event.DrillDownCategories = make(utils.MotadataMap)

				event.DrillDownConditionOperands = make(map[string]struct{})

				event.DrillDownConditionOperandValues = make(utils.MotadataMap)

				event.DrillDownConditionExpression = utils.Empty

				event.DrillDownBitmapFilter = false

				event.LastDatePosition = 0

				event.QualifiedDates = nil

				event.EventIds = nil

				event.DrillDownRecords = 0

				event.LastTickPosition = 0

				event.DrillDownBitmapBytes = make([][]byte, executor.parsingThreads)

				event.BitmapBytes = make([][]byte, executor.parsingThreads)

				for i := range executor.MappingBitmaps {

					event.DrillDownBitmapBytes[i] = []byte{}

					event.BitmapBytes[i] = []byte{}
				}

				for i := len(dates) - 1; i >= 0; i-- {

					event.QualifiedDates = append(event.QualifiedDates, dates[i].UnixMilli())
				}

				if len(dates) > 1 {

					dates = []time.Time{dates[len(dates)-1]}
				}

				if utils.QueryPlanLogging {

					executor.logQueryPlan(fmt.Sprintf("selecting latest date: %v", dates[0]))
				}
			}
		}

		preAggregation := true

		switch executor.datastoreType {

		case utils.CorrelationMetric:

			fallthrough

		case utils.RunbookWorklog:

			fallthrough

		case utils.PolicyResult:

			fallthrough

		case utils.Audit:

			fallthrough

		case utils.Notification:

			fallthrough

		case utils.StatusFlapHistory:

			fallthrough

		case utils.ConfigHistory:

			preAggregation = false
		}

		externalGroupSize := 0

		aggregationColumns := map[string]struct{}{}

		indexableColumns := map[string]struct{}{}

		init := true

		if dataSource.Contains(VisualizationResultBy) {

			groups := dataSource.GetSliceValue(VisualizationResultBy)

			if executor.preAggregationJobId == utils.NotAvailable && executor.datastoreType == utils.Flow && len(groups) > 1 {

				executor.publish = false

				if executor.topNIncrementalQuery {

					init = false

					indexableColumns = executor.qualifyGroups(groups, indexableColumns)

					executor.GroupColumnElementSize = (executor.GroupColumnElementSize - executor.topNIncrementalGroupIndex) + 1

					executor.topNIncrementalGroupIndex--

				} else {

					executor.logQueryPlan(fmt.Sprintf("top incremental query started"))

					executor.topNIncrementalQuery = true

					indexableColumns = executor.qualifyGroups(groups, indexableColumns)

					if histogramQuery {

						executor.topNIncrementalGroupIndex = 0

					} else {

						executor.topNIncrementalGroupIndex = executor.GroupColumnElementSize - 1

						executor.GroupColumnElementSize = 1
					}
				}
			} else {

				indexableColumns = executor.qualifyGroups(groups, indexableColumns)
			}

			// external grouping filters

			// external grouping
			if executor.externalGroupIndex >= 0 { //external grouping

				entity := utils.Empty

				externalGroups := dataSource.GetMapValue(executor.GroupColumns[executor.externalGroupIndex])

				length := uint32(len(externalGroups))

				ordinals := map[string]int{}

				for externalGroup, externalGroupings := range externalGroups {

					externalGroupSize++

					ordinal, found := ordinals[externalGroup]

					if !found {

						ordinal = len(ordinals)

						ordinals[externalGroup] = ordinal

						executor.externalGroupFilters[executor.externalGroupFilterElementSize] = externalGroup

						executor.externalGroupHashes[executor.externalGroupFilterElementSize] = utils.GetHash64([]byte(externalGroup))

						executor.externalGroupFilterElementSize++
					}

					for _, externalGroupEntity := range externalGroupings.([]interface{}) {

						entity = ToString(externalGroupEntity)

						if _, ok := executor.externalGroupFilterOrdinals[entity]; !ok {

							executor.externalGroupFilterOrdinals[entity] = NewCompactedBitmap(length)
						}

						executor.externalGroupFilterOrdinals[entity].Set(uint32(ordinal))
					}
				}

				if externalGroupSize == 0 {

					return errors.New(utils.ErrorGroupingEntitiesRequired)
				}
			}

		} else if executor.category == Grid || executor.category == Chart || executor.category == TopN || executor.category == Map || executor.category == Sankey {

			return errors.New(fmt.Sprintf(utils.ErrorGroupingRequired, executor.category))
		}

		if executor.toDateTime.Unix()-executor.fromDateTime.Unix() <= 300 {

			preAggregation = false
		}

		executor.conditionOperand = utils.EventSource

		if executor.datastoreType == utils.PolicyFlapHistory || executor.datastoreType == utils.MetricPolicy || executor.datastoreType == utils.StatusFlapHistory {

			executor.conditionOperand = utils.ObjectId

		} else if executor.plugin == datastore.NetRouteStatusPlugin {

			executor.conditionOperand = utils.NetRouteId
		}

		executor.mappingOperand = executor.conditionOperand

		if init && drillDownFilter != nil {

			executor.drillDownFilter = drillDownFilter.Clone()
		}

		executor.preAggregationQuery = false

		/*
				Toggle the aggregation query flag to yes in the below cases:
			    1. UI Query and the table in which aggregation view is enabled
			    2. when the query is from the aggregation job but specifies interval (for example: probe the
			       15-minute interval from 5 minute interval view)

		*/
		if (preAggregation && executor.preAggregationJobId == utils.NotAvailable) || (executor.preAggregationJobId != utils.NotAvailable && executor.request.Contains(utils.Interval)) {

			executor.preAggregationQuery = true
		}

		if executor.drillDownQuery {

			executor.preAggregationQuery = false

			executor.fulltextSearchingView = false
		}

		widgetId := int64(0)

		if executor.request.Contains(utils.Id) {

			widgetId = executor.request.GetInt64Value(utils.Id)
		}

		//for aggregation query earlier we are not checking filter , but now we need to check the filter for msg conatins view
		//in case of probing if the job has specified an interval in the query context do not toggle the aggregation query flag.
		if executor.preAggregationJobId != utils.NotAvailable && len(dataFilter) > 0 && !executor.request.Contains(utils.Interval) {

			for _, group := range dataFilter.GetMapListValue(Groups) {

				for _, condition := range group.GetMapListValue(Conditions) {

					operand := condition.GetStringValue(Operand)

					if datastore.IsSearchableColumn(operand) || datastore.IsBlobColumn(operand) {

						preAggregation = false

						executor.preAggregationQuery = false

						executor.bitmapFilter = false
					}
				}
			}
		}

		//check if msg contains view is there after iterating over datafilter in UI query, In step2 query no need to check filter again
		//in case the interval is specified toggle the fulltextsearchingView flag so that the query lands in aggregation table.
		if executor.datastoreType == utils.Log && !executor.drillDownQuery &&
			(executor.preAggregationJobId == utils.NotAvailable || executor.request.Contains(utils.Interval)) {

			if len(dataFilter) > 0 {

				for _, group := range dataFilter.GetMapListValue(Groups) {

					for _, condition := range group.GetMapListValue(Conditions) {

						operand := condition.GetStringValue(Operand)

						if strings.EqualFold(operand, utils.Message) {

							executor.fulltextSearchingView = true

							break
						}
					}

					if executor.fulltextSearchingView {

						break
					}

				}

			} else if len(drillDownFilter) > 0 { // always group by or event source column, so always land to ^ordinal with bitmap filter

				executor.bitmapFilter = true
			}

		}

		executor.preAggregationTimeInterval = executor.chooseAggregationTimeInterval(utils.UnixToSeconds(executor.fromDateTime.Unix()), utils.UnixToSeconds(executor.toDateTime.Unix()), executor.fulltextSearchingView)

		//apply the interval specified in the query if the key is present.
		if executor.preAggregationJobId != utils.NotAvailable && executor.request.Contains(utils.Interval) {

			executor.preAggregationTimeInterval = executor.request.GetIntValue(utils.Interval)
		}

		// check if msg contains view particular widget is aggregated or not
		//in case of interval specified from the query context
		//select the aggregation view specified in the query to serve the result
		if (executor.preAggregationJobId == utils.NotAvailable || executor.request.Contains(utils.Interval)) &&
			executor.datastoreType == utils.Log && executor.fulltextSearchingView {

			aggregation := utils.Empty

			if executor.request.Contains(utils.AggregationView) {

				aggregation = executor.request.GetStringValue(utils.AggregationView)

			} else if datastore.IsHorizontalAggregationFound(executor.plugin) {

				aggregation = datastore.GetHorizontalAggregation(executor.plugin, aggregationColumns, indexableColumns, executor.tokenizers[0], INT64ToStringValue(widgetId), executor.fulltextSearchingView)

				clear(aggregationColumns)

				clear(indexableColumns)
			}

			if aggregation != utils.Empty {

				executor.plugin = aggregation

				executor.preAggregationQuery = true

				filters.Delete(DataFilter)

				dataFilter = nil

			} else {

				if utils.QueryPlanLogging {

					executor.logQueryPlan(fmt.Sprintf(WarningAggregationNotFound, executor.plugin))
				}

				executor.preAggregationQuery = false

				executor.fulltextSearchingView = false

				widgetId = 0
			}

		}

		externalGroupFilters := 0

		// ui query so need to check all aggregation, condition and grouping columns qualified in aggregation or not
		if executor.preAggregationJobId == utils.NotAvailable && !executor.drillDownQuery && !executor.fulltextSearchingView {

			for _, dataPoint := range dataPoints {

				column := dataPoint.GetStringValue(DataPoint)

				aggregationColumns[column] = struct{}{}

				if dataPoint.GetStringValue(Aggregator) == CountFunc {

					if !datastore.IsHorizontalAggregationFound(executor.plugin) {

						if utils.QueryPlanLogging {

							executor.logQueryPlan(fmt.Sprintf("column %v aggregated flag is false, reason : aggregated plugin %v not found, hence setting aggregation flag false", column, executor.plugin))
						}

						executor.preAggregationQuery = false

						break

					} else {

						delete(aggregationColumns, column)
					}

					continue
				}
			}

			if len(dataFilter) > 0 {

				count := 0

				preAggregation, count, indexableColumns = executor.qualifyIndexableColumns(dataFilter, indexableColumns, preAggregation)

				externalGroupFilters += count

				if len(drillDownFilter) > 0 {

					preAggregation, count, indexableColumns = executor.qualifyIndexableColumns(drillDownFilter, indexableColumns, preAggregation)

					externalGroupFilters += count
				}

			} else if len(drillDownFilter) > 0 { // always group by or event source column, so always land to ^ordinal with bitmap filter

				executor.bitmapFilter = true

				preAggregation, externalGroupFilters, indexableColumns = executor.qualifyIndexableColumns(drillDownFilter, indexableColumns, preAggregation)
			}

			if executor.preAggregationQuery {

				aggregation := datastore.GetHorizontalAggregation(executor.plugin, aggregationColumns, indexableColumns, executor.tokenizers[0], INT64ToStringValue(widgetId), executor.fulltextSearchingView)

				if aggregation == utils.Empty {

					if utils.QueryPlanLogging {

						executor.logQueryPlan(fmt.Sprintf(WarningAggregationNotFound, executor.plugin))
					}

					executor.preAggregationQuery = false
				} else {

					executor.plugin = aggregation

					if utils.QueryPlanLogging {

						executor.logQueryPlan(fmt.Sprintf("qualified aggregation is %v", executor.plugin))
					}
				}
			}
		}

		/*
			example case of flow probing for 30 minute
			if 5 minute aggregation view is populated then we can populate the higher aggregation interval view
			from the lesser interval aggregation view in this case 5 minute
			specific aggregation view and interval must be provided to keep data consistency.
		*/
		if executor.preAggregationJobId != utils.NotAvailable && executor.request.Contains(utils.AggregationView) {

			executor.plugin = executor.request.GetStringValue(utils.AggregationView)
		}

		// external grouping with bitmap filter need to pass in filter
		if externalGroupSize > 0 && executor.bitmapFilter && externalGroupFilters == 0 {

			if len(dataFilter) == 0 {

				dataFilter = utils.MotadataMap{}
			}

			var groups []interface{}

			index := 0

			if dataFilter == nil || len(dataFilter) == 0 {

				groups = make([]interface{}, index)

			} else {

				groups = dataFilter.GetListValue(Groups)

				index = len(groups)
			}

			dataFilter[Operator] = and

			dataFilter[Filter] = include

			groups = append(groups, map[string]interface{}{})

			group := groups[index].(map[string]interface{})

			group[Filter] = include

			group[Operator] = and

			conditions := make([]interface{}, 1)

			conditions[0] = map[string]interface{}{}

			index = 0

			qualifiedEntities := dataSource.GetMapValue(Entities)

			items := make([]interface{}, len(qualifiedEntities))

			for entity := range qualifiedEntities {

				items[index] = entity

				index++
			}

			condition := map[string]interface{}{}

			condition[Operand] = executor.conditionOperand

			condition[Operator] = in

			condition[utils.Value] = items

			conditions[0] = condition

			group[Conditions] = conditions

			dataFilter[Groups] = groups

			filters[DataFilter] = dataFilter
		}

		if !executor.preAggregationQuery && executor.plugin == datastore.EventSearchPlugin {

			// for log search plugin key has tick^category^column^part so need to resolve category

			for key, value := range dataSource.GetMapValue(Categories) {

				executor.categories[key] = value
			}

			if len(executor.categories) == 0 { // for exclude filter backend not pass any categories hence look into all filter

				storeName := utils.Plugin + utils.HyphenSeparator + datastore.Mappings

				if datastore.IsStoreAvailable(storeName) {

					if store := datastore.GetStore(storeName, utils.Mapping, false, true, executor.encoder, executor.tokenizers[0]); store != nil {

						store.ListStringMappings(executor.categories)

						executor.categories.Delete(datastore.LogStatPlugin)
					}
				}

				// means we didn't find any plugins from backend side and posting list, and we can't build key without it hence return the query with error
				if len(executor.categories) == 0 {

					return errors.New(fmt.Sprintf(utils.ErrorKeyQualification, "plugin category not found..."))
				}
			}
		}

		if len(dataFilter) > 0 {

			var err error

			if !executor.topNIncrementalQuery || init {

				err = executor.evaluateHorizontalFilterPhase1(dataFilter, dates, preAggregation, executor.MappingBitmaps)
			} else {

				executor.conditionExpression = executor.helperConditionExpression

				for i := range executor.MappingHelperBitmaps {

					if executor.MappingHelperBitmaps[i] != nil && executor.MappingHelperBitmaps[i].Count() > 0 {

						executor.MappingBitmaps[i] = &bitmap.Bitmap{0}

						executor.MappingHelperBitmaps[i].Range(func(ordinal uint32) {

							executor.MappingBitmaps[i].Set(ordinal)
						})
					}
				}

				for date, ticks := range executor.helperDates {

					executor.dates[date] = make(map[int32]string, len(ticks))

					for tick, part := range ticks {

						executor.dates[date][tick] = part
					}
				}

				for tick, parts := range executor.helperTicks {

					executor.ticks[tick] = map[string]struct{}{}

					for part := range parts {

						executor.ticks[tick][part] = struct{}{}
					}
				}
			}

			if len(executor.dates) > 0 {

				executor.preFilterQuery = true

			} else {

				return errors.New(fmt.Sprintf(utils.ErrorFilterKeyQualification, err))
			}

		} else {

			executor.preFilterQuery = false
		}

		if utils.QueryPlanLogging {

			executor.logQueryPlan(fmt.Sprintf("qualified interval is %v", executor.preAggregationTimeInterval))

			executor.logQueryPlan(fmt.Sprintf("aggregation query flag is %v", executor.preAggregationQuery))
		}

		executor.keyPoolIndex, executor.keys = executor.memoryPool.AcquireStringPool(utils.NotAvailable)

		if !executor.preFilterQuery {

			if !executor.topNIncrementalQuery || init {

				executor.qualifyHorizontalTicksPhase1(dates, plugins)

			} else {

				for date, ticks := range executor.helperDates {

					executor.dates[date] = make(map[int32]string, len(ticks))

					for tick, part := range ticks {

						executor.dates[date][tick] = part
					}
				}
			}

			if len(executor.dates) == 0 {

				return errors.New(fmt.Sprintf(utils.ErrorKeyQualification, utils.ErrorPostingListLookupFailed))
			}
		}

		if executor.topNIncrementalQuery && init {

			for date, ticks := range executor.dates {

				executor.helperDates[date] = make(map[int32]string, len(ticks))

				for tick, part := range ticks {

					executor.helperDates[date][tick] = part
				}
			}

			for tick, parts := range executor.ticks {

				executor.helperTicks[tick] = map[string]struct{}{}

				for part := range parts {

					executor.helperTicks[tick][part] = struct{}{}
				}
			}

			if len(executor.conditionExpression) > 0 {

				executor.helperConditionExpression = executor.conditionExpression
			}

			for i := range executor.MappingBitmaps {

				if executor.MappingBitmaps[i] != nil && executor.MappingBitmaps[i].Count() > 0 {

					executor.MappingHelperBitmaps[i] = &bitmap.Bitmap{0}

					executor.MappingBitmaps[i].Range(func(ordinal uint32) {

						executor.MappingHelperBitmaps[i].Set(ordinal)
					})
				}
			}
		}

		if len(drillDownFilter) > 0 {

			condition := executor.conditionExpression

			executor.conditionExpression = utils.Empty

			err := executor.evaluateHorizontalFilterPhase1(drillDownFilter, dates, preAggregation, executor.DrillDownMappingBitmaps)

			if len(executor.dates) == 0 {

				return errors.New(fmt.Sprintf(utils.ErrorFilterKeyQualification, err))
			}

			if len(condition) > 0 {

				condition += andCondition
			}

			condition += executor.conditionExpression

			executor.conditionExpression = utils.Empty

			executor.conditionExpression = condition
		}

		if executor.queryEngineType == DrillDown {

			if err := executor.qualifyDrillDownTicks(utils.UnixMillisToDate(dates[0].UnixMilli()), utils.UnixToSeconds(executor.fromDateTime.Unix()), utils.UnixToSeconds(executor.toDateTime.Unix())); err != nil {

				return err
			}

			for i := range executor.MappingBitmaps {

				if executor.MappingBitmaps[i] != nil {

					executor.drillDownEvent.BitmapBytes[i] = executor.MappingBitmaps[i].ToBytes()
				}
			}

			for i := range executor.DrillDownMappingBitmaps {

				if executor.DrillDownMappingBitmaps[i] != nil {

					executor.drillDownEvent.DrillDownBitmapBytes[i] = executor.DrillDownMappingBitmaps[i].ToBytes()
				}

			}

			executor.drillDownEvent.DrillDownConditionExpression = executor.conditionExpression

			executor.drillDownEvent.DrillDownBitmapFilter = executor.bitmapFilter

			executor.drillDownEvent.DrillDownConditionOperands = executor.conditionOperands

			executor.drillDownEvent.DrillDownCategories = executor.categories

			executor.drillDownEvent.DrillDownConditionOperandValues = executor.conditionOperandValues

			return executor.initDrillDown(executor.qualifiedDateIndex == 0, dataSource)
		}

		poolIndex, columns := executor.memoryPool.AcquireStringPool(utils.NotAvailable)

		defer executor.memoryPool.ReleaseStringPool(poolIndex)

		columnElementSize := 0

		utils.Split(executor.plugin, utils.AggregationSeparator, executor.tokenizers[0])

		plugin := executor.tokenizers[0].Tokens[0]

		uniqueColumns := map[string]struct{}{}

		executor.lookupOrdinal = true

		for index, dataPoint := range dataPoints {

			aggregator := dataPoint.GetStringValue(Aggregator)

			column := dataPoint.GetStringValue(DataPoint)

			if executor.datastoreType == utils.StatusFlapHistory {

				column = getHorizontalStatusFlapColumn(column)
			}

			if datastore.IsIndexableColumn(plugin, column) {

				column += utils.OrdinalSuffix
			}

			executor.columnIndices[column+utils.DotSeparator+aggregator] = index

			if dataPoint.GetStringValue(Aggregator) == utils.Empty {

				if index == 0 {

					executor.Columns[executor.ColumnElementSize] = utils.TimestampKey

					executor.ColumnElementSize++

					executor.Columns[executor.ColumnElementSize] = utils.EventId + utils.ValueSuffix

					executor.ColumnElementSize++

					if executor.datastoreType == utils.StatusFlapHistory || executor.datastoreType == utils.PolicyFlapHistory {

						columns[columnElementSize] = executor.conditionOperand

						columnElementSize++
					}
				}

				executor.Columns[executor.ColumnElementSize] = column + utils.ValueSuffix

				executor.ColumnElementSize++

				columns[columnElementSize] = column

				columnElementSize++

			} else {

				executor.aggregations[aggregator] += 1

				if _, ok := uniqueColumns[column]; ok {

					executor.Columns[executor.ColumnElementSize] = column + utils.KeySeparator + aggregator

					executor.ColumnElementSize++

					continue
				}

				uniqueColumns[column] = struct{}{}

				executor.Columns[executor.ColumnElementSize] = column + utils.KeySeparator + aggregator

				executor.ColumnElementSize++

				columns[columnElementSize] = column

				columnElementSize++

			}
		}

		if columnElementSize > 0 {

			if executor.preAggregationQuery {

				executor.qualifyAggregatedProbes()

				executor.qualifyHorizontalKeysPhase1(executor.Columns[:executor.ColumnElementSize])

			} else {

				executor.probes = columnElementSize

				if executor.datastoreType == utils.StatusFlapHistory || executor.datastoreType == utils.PolicyFlapHistory {

					executor.probes++
				}

				executor.qualifyHorizontalKeysPhase1(columns[:columnElementSize])
			}
		}

	}
	executor.applyGranularity(histogramQuery, timestamp)

	return nil
}

func (executor *Executor) qualifyHorizontalKeysPhase1(columns []string) {

	for date := range executor.dates {

		poolIndex, ticks := executor.qualifyHorizontalTicksPhase2(date, false)

		if len(ticks) == 0 {

			if poolIndex != utils.NotAvailable {

				executor.memoryPool.ReleaseINT32Pool(poolIndex)
			}

			continue
		}

		storeName := date + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + executor.plugin

		if executor.preAggregationQuery {

			storeName += utils.HyphenSeparator + INTToStringValue(executor.preAggregationTimeInterval)
		}

		executor.qualifyHorizontalKeysPhase2(ticks, columns, storeName)

		executor.memoryPool.ReleaseINT32Pool(poolIndex)
	}
}

func (executor *Executor) qualifyHorizontalKeysPhase2(ticks []int32, columns []string, storeName string) {

	if store := datastore.GetStore(storeName, utils.None, false, true, executor.encoder, executor.tokenizers[0]); store != nil {

		countFuncKeys := map[string]struct{}{}

		start := executor.keyElementSize

		key := utils.Empty

		if executor.preAggregationQuery {

			if executor.preFilterQuery {

				for _, tick := range ticks {

					qualifiedTick := INT32ToStringValue(tick)

					clear(countFuncKeys)

					for part := range executor.ticks[tick] {

						for _, column := range columns {

							countFunc := false

							if strings.HasSuffix(column, "^avg") {

								utils.Split(column, utils.KeySeparator, executor.tokenizers[0])

								key = qualifiedTick + utils.KeySeparator + executor.tokenizers[0].Tokens[0] + utils.KeySeparator + SumFunc

								if strings.Contains(part, utils.KeySeparator) {

									utils.Split(part, utils.KeySeparator, executor.tokenizers[0])

									key += utils.KeySeparator + executor.tokenizers[0].Tokens[1]

								} else {

									key += utils.KeySeparator + part
								}

								executor.qualifyDistributionKeys(key, utils.Empty)

								// count

								utils.Split(column, utils.KeySeparator, executor.tokenizers[0])

								key = qualifiedTick + utils.KeySeparator + executor.tokenizers[0].Tokens[0] + utils.KeySeparator + CountFunc

								if executor.categorizedColumnCounts {

									countFunc = true

									key = qualifiedTick + utils.KeySeparator + utils.All + utils.KeySeparator + CountFunc
								}
							} else {

								key = qualifiedTick + utils.KeySeparator + column

								if executor.categorizedColumnCounts {

									utils.Split(column, utils.KeySeparator, executor.tokenizers[0])

									if executor.tokenizers[0].Tokens[1] == CountFunc {

										countFunc = true

										key = qualifiedTick + utils.KeySeparator + utils.All + utils.KeySeparator + executor.tokenizers[0].Tokens[1]
									}
								}
							}

							if strings.Contains(part, utils.KeySeparator) {

								utils.Split(part, utils.KeySeparator, executor.tokenizers[0])

								key += utils.KeySeparator + executor.tokenizers[0].Tokens[1]

							} else {

								key += utils.KeySeparator + part
							}

							if countFunc {

								if _, ok := countFuncKeys[key]; ok {

									continue
								}

								countFuncKeys[key] = struct{}{}
							}

							executor.qualifyDistributionKeys(key, utils.Empty)
						}
					}
				}

			} else {

				if executor.queryEngineType == Metric {

					for _, tick := range ticks {

						qualifiedTick := INT32ToStringValue(tick)

						clear(countFuncKeys)

						utils.Split(columns[0], utils.KeySeparator, executor.tokenizers[1]) // column contains aggregation at last

						for partIndex := 0; partIndex < getParts(utils.GetHash64([]byte(INT32ToStringValue(tick)+utils.KeySeparator+executor.tokenizers[1].Tokens[0])), store); partIndex++ {

							part := INTToStringValue(partIndex)

							for _, column := range columns {

								executor.buildHorizontalPreAggregatedKey(column, part, qualifiedTick, countFuncKeys)
							}
						}
					}

				} else {

					for _, tick := range ticks {

						qualifiedTick := INT32ToStringValue(tick)

						clear(countFuncKeys)

						for index := 0; index < getParts(uint64(tick), store); index++ {

							for _, column := range columns {

								executor.buildHorizontalPreAggregatedKey(column, INTToStringValue(index), qualifiedTick, countFuncKeys)
							}

						}
					}
				}
			}
		} else {

			clear(countFuncKeys)

			if len(executor.categories) > 0 {

				for _, tick := range ticks {

					qualifiedTick := INT32ToStringValue(tick)

					for partIndex := 0; partIndex < getParts(uint64(tick), store); partIndex++ {

						part := INTToStringValue(partIndex)

						for plugin := range executor.categories {

							for _, column := range columns {

								executor.qualifyDistributionKeys(qualifiedTick+utils.KeySeparator+plugin+utils.KeySeparator+column+utils.KeySeparator+part, utils.Empty)
							}

						}
					}
				}
			} else {

				for _, tick := range ticks {

					qualifiedTick := INT32ToStringValue(tick)

					for partIndex := 0; partIndex < getParts(uint64(tick), store); partIndex++ {

						part := INTToStringValue(partIndex)

						for _, column := range columns {

							executor.qualifyDistributionKeys(qualifiedTick+utils.KeySeparator+column+utils.KeySeparator+part, utils.Empty)
						}
					}
				}
			}
		}

		executor.qualifyWorkerEventKeyIndices(start, store, time.Now(), utils.Empty, utils.Empty)
	}
}

func (executor *Executor) buildHorizontalPreAggregatedKey(column, index, qualifiedTick string, countFuncKeys map[string]struct{}) {

	countFunc := false

	key := utils.Empty

	if strings.HasSuffix(column, "^avg") {

		utils.Split(column, utils.KeySeparator, executor.tokenizers[0])

		key = qualifiedTick + utils.KeySeparator + executor.tokenizers[0].Tokens[0] + utils.KeySeparator + SumFunc

		executor.qualifyDistributionKeys(key+utils.KeySeparator+index, utils.Empty)

		key = qualifiedTick + utils.KeySeparator + executor.tokenizers[0].Tokens[0] + utils.KeySeparator + CountFunc

		if executor.categorizedColumnCounts {

			countFunc = true

			key = qualifiedTick + utils.KeySeparator + utils.All + utils.KeySeparator + CountFunc
		}
	} else {

		key = qualifiedTick + utils.KeySeparator + column

		if executor.categorizedColumnCounts {

			utils.Split(column, utils.KeySeparator, executor.tokenizers[0])

			if executor.tokenizers[0].Tokens[1] == CountFunc {

				countFunc = true

				key = qualifiedTick + utils.KeySeparator + utils.All + utils.KeySeparator + executor.tokenizers[0].Tokens[1]
			}
		}
	}

	key = key + utils.KeySeparator + index

	if countFunc {

		if _, ok := countFuncKeys[key]; ok {

			return
		}

		countFuncKeys[key] = struct{}{}
	}

	executor.qualifyDistributionKeys(key, utils.Empty)
}

func (executor *Executor) qualifyHorizontalTicksPhase2(date string, rounding bool) (int, []int32) {

	qualifiedTicks := executor.dates[date]

	if executor.timeWindowQuery {

		baseTick := utils.GetBaseTick(date)

		from := baseTick + executor.timeWindowStartPosition

		end := baseTick + executor.timeWindowEndPosition

		for tick := range qualifiedTicks {

			if !(tick >= from && tick <= end) {

				delete(qualifiedTicks, tick)
			}
		}
	}

	poolIndex, ticks := executor.memoryPool.AcquireINT32Pool(len(qualifiedTicks))

	tickElementSize := 0

	if rounding {

		preAggregationTicks := map[int32]struct{}{}

		for tick := range qualifiedTicks {

			preAggregationTicks[utils.RoundOffSeconds(tick, executor.preAggregationTimeInterval)] = struct{}{}
		}

		for tick := range preAggregationTicks {

			ticks[tickElementSize] = tick

			tickElementSize++
		}

		utils.SortINT32Values(ticks[:tickElementSize])

		startIndex, _ := utils.SearchINT32Value(ticks[:tickElementSize], utils.RoundOffSeconds(utils.UnixToSeconds(executor.fromDateTime.Unix()), executor.preAggregationTimeInterval))

		endIndex, _ := utils.SearchINT32Value(ticks[:tickElementSize], utils.UnixToSeconds(executor.toDateTime.Unix()))

		return poolIndex, ticks[startIndex:endIndex]
	}

	for tick := range qualifiedTicks {

		ticks[tickElementSize] = tick

		tickElementSize++
	}

	utils.SortINT32Values(ticks)

	startIndex := 0

	if executor.preAggregationQuery {

		startIndex, _ = utils.SearchINT32Value(ticks, utils.RoundOffSeconds(utils.UnixToSeconds(executor.fromDateTime.Unix()), executor.preAggregationTimeInterval))

	} else {

		startIndex, _ = utils.SearchINT32Value(ticks, utils.UnixToSeconds(executor.fromDateTime.Unix()))
	}

	endIndex, _ := utils.SearchINT32Value(ticks, utils.UnixToSeconds(executor.toDateTime.Unix()))

	return poolIndex, ticks[startIndex:endIndex]
}

// non-filter posting list tick qualification
func (executor *Executor) qualifyHorizontalTicksPhase1(dates []time.Time, plugins []interface{}) {

	datePoolIndex, qualifiedDates := executor.memoryPool.AcquireStringPool(len(dates))

	defer executor.memoryPool.ReleaseStringPool(datePoolIndex)

	storePoolIndex, stores := executor.memoryPool.AcquireStringPool(len(dates))

	defer executor.memoryPool.ReleaseStringPool(storePoolIndex)

	tickPoolIndex, ticks := executor.memoryPool.AcquireINT32Pool(len(dates))

	defer executor.memoryPool.ReleaseINT32Pool(tickPoolIndex)

	elementSize := 0

	// date wise store qualified
	for index := 0; index < len(dates); index++ {

		date := utils.UnixMillisToDate(dates[index].UnixMilli())

		// dummy posting list
		storeName := date + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + utils.DummyPostingListStoreName + utils.HyphenSeparator + INTToStringValue(int(String))

		if datastore.IsStoreAvailable(storeName) {

			if store := datastore.GetStore(storeName, utils.None, false, true, executor.encoder, executor.tokenizers[0]); store != nil {

				ticks[elementSize] = utils.UnixToSeconds(time.Date(dates[index].Year(), dates[index].Month(), dates[index].Day(), 0, 0, 0, 0, time.UTC).Unix())

				qualifiedDates[elementSize] = date

				stores[elementSize] = storeName

				elementSize++
			}
		}
	}

	if elementSize == 0 {

		return
	}

	i := 0

	// identify how much store qualified in each go-routines
	for index := 0; index < elementSize; index++ {

		executor.helpers[i] += 1

		if i == len(executor.helpers)-1 {

			i = 0
		} else {
			i++
		}
	}

	startIndex := 0

	size := 0

	for j := range executor.helpers {

		if executor.helpers[j] == 0 {

			break
		}

		size++
	}

	responses := make(chan map[string]map[int32]string, size)

	rounding := false

	if executor.queryEngineType != Metric && executor.queryEngineType != AIOps {

		rounding = !executor.preFilterQuery && executor.preAggregationQuery

	}

	for index := range executor.helpers {

		if executor.helpers[index] == 0 {

			break
		}

		go func(ticks []int32, dates []string, stores []string, index int) {

			executor.qualifyHorizontalDates(ticks, dates, stores, index, responses, plugins)

		}(ticks[:elementSize][startIndex:startIndex+executor.helpers[index]], qualifiedDates[:elementSize][startIndex:startIndex+executor.helpers[index]], stores[:elementSize][startIndex:startIndex+executor.helpers[index]], index)

		startIndex += executor.helpers[index]
	}

	for response := range responses {

		size--

		if rounding {

			for date, responseTicks := range response {

				if _, ok := executor.dates[date]; ok {

					qualifiedTicks := executor.dates[date]

					for tick := range responseTicks {

						qualifiedTicks[utils.RoundOffSeconds(tick, executor.preAggregationTimeInterval)] = utils.Empty
					}
				} else {

					for tick := range responseTicks {

						delete(responseTicks, tick)

						responseTicks[utils.RoundOffSeconds(tick, executor.preAggregationTimeInterval)] = utils.Empty
					}

					executor.dates[date] = responseTicks
				}
			}
		} else {

			for date, responseTicks := range response {

				if _, ok := executor.dates[date]; ok {

					qualifiedTicks := executor.dates[date]

					for tick := range responseTicks {

						qualifiedTicks[tick] = utils.Empty
					}
				} else {

					executor.dates[date] = responseTicks
				}
			}
		}

		if size == 0 {

			close(responses)

			break
		}
	}

	return
}

func (executor *Executor) qualifyHorizontalDates(ticks []int32, dates, stores []string, index int, responses chan map[string]map[int32]string, plugins []interface{}) {

	response := map[string]map[int32]string{}

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			executor.logger.Error(fmt.Sprintf("error %v occurred in executor %v dummy event filter posting list", err, executor.executorId))

			executor.logger.Error(fmt.Sprintf("!!!STACK TRACE for executor %v!!! \n %v", executor.executorId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

		responses <- response
	}()

	buffers := make([][]byte, len(plugins))

	// if status flap history query came without filter in future need to change plugin to plugin + ".duration" later
	for pluginIndex, plugin := range plugins {

		buffers[pluginIndex] = []byte(strings.ToLower(ToString(plugin)))
	}

	for i := range stores {

		store := datastore.GetStore(stores[i], utils.None, false, true, executor.encoders[index], executor.tokenizers[index])

		if store == nil {

			continue
		}

		for j := range buffers {

			if _, bytes, err := store.Get(buffers[j], executor.ValueBuffers[index], executor.encoders[index], executor.events[index], &executor.waitGroups[index], executor.tokenizers[index], false); err == nil && bytes != nil && len(bytes) > 0 {

				poolIndex := utils.NotAvailable

				var bitmapBytes []byte

				poolIndex, bitmapBytes, err = executor.decoders[index].DecodeSnappy(bytes)

				if err != nil {

					if poolIndex != utils.NotAvailable {

						executor.decoders[index].MemoryPool.ReleaseBytePool(poolIndex)
					}

					executor.logError(err.Error())

					continue
				}

				searchTokenBitmap := bitmap.FromBytes(bitmapBytes)

				if searchTokenBitmap != nil {

					if _, ok := response[dates[i]]; !ok {

						response[dates[i]] = make(map[int32]string, searchTokenBitmap.Count())
					}

					searchTokenBitmap.Range(func(position uint32) {

						response[dates[i]][ticks[i]+int32(position)] = utils.Empty
					})
				}

				if poolIndex != utils.NotAvailable {

					executor.decoders[index].MemoryPool.ReleaseBytePool(poolIndex)
				}
			}
		}
	}
}

func (executor *Executor) qualifyIndexableColumns(filter utils.MotadataMap, indexableColumns map[string]struct{}, aggregation bool) (bool, int, map[string]struct{}) {

	preAggregation := aggregation

	externalGroupFilters := 0

	for _, group := range filter.GetMapListValue(Groups) {

		for _, condition := range group.GetMapListValue(Conditions) {

			operand := condition.GetStringValue(Operand)

			if datastore.IsSearchableColumn(operand) {

				preAggregation = false

				executor.preAggregationQuery = false

				executor.bitmapFilter = false
			}

			if operand == utils.EventSource || operand == utils.ObjectId {

				externalGroupFilters++
			}

			if executor.preAggregationQuery {

				indexableColumns[operand] = struct{}{}
			}
		}
	}

	return preAggregation, externalGroupFilters, indexableColumns
}

// filter

func (executor *Executor) evaluateHorizontalFilterPhase1(dataFilters utils.MotadataMap, dates []time.Time, preAggregation bool, mappingBitmaps []*bitmap.Bitmap) error {

	t := time.Now().UnixMilli()

	included := dataFilters.GetStringValue(Filter) == include

	size, conditionOROperator := 0, false

	executor.conditionExpression = utils.Empty

	groups := dataFilters.GetMapListValue(Groups)

	var err error

	size, preAggregation, conditionOROperator, err = executor.qualifyConditionExpression(groups, included, dataFilters, mappingBitmaps)

	if err != nil {

		return err
	}

	if size == 0 {

		return errors.New("invalid filter conditions")
	}

	channels := make(chan int, size)

	index := 0

	included = dataFilters.GetStringValue(Filter) == include

	poolIndex, poolIndices := executor.memoryPool.AcquireINTPool(size)

	executor.memoryPool.ResetINTPool(poolIndex, size, -1)

	elementPoolIndex, elements := executor.memoryPool.AcquireINTPool(size)

	executor.memoryPool.ResetINTPool(elementPoolIndex, size, 0)

	defer func(poolIndex, elementPoolIndex int) {

		for i := range executor.responses {

			executor.responses[i].Clear()
		}

		executor.memoryPool.ReleaseINTPool(elementPoolIndex)

		executor.memoryPool.ReleaseINTPool(poolIndex)
	}(poolIndex, elementPoolIndex)

	bitmaps := make([]bitmap.Bitmap, len(groups))

	found := false

	var values []string

	for _, group := range groups {

		excluded := group.GetStringValue(Filter) == exclude

		if !included {

			excluded = !excluded
		}

		for _, condition := range group.GetMapListValue(Conditions) {

			mappingBitmaps[index] = &bitmap.Bitmap{0}

			go func(condition utils.MotadataMap, index int) {

				executor.evaluateHorizontalFilterPhase2(condition, dates, excluded, channels, index, mappingBitmaps)

			}(condition, index)

			index++
		}
	}

	for i := range channels {

		size--

		poolIndices[i], values = executor.memoryPool.AcquireStringPool(utils.NotAvailable)

		elementSize := 0

		executor.responses[i].Iter(func(value string, _ struct{}) (stop bool) {

			found = true

			if elementSize == len(values) {

				values = executor.memoryPool.ExpandStringPool(poolIndices[i], len(values)+executor.poolLength)
			}

			values[elementSize] = value

			elementSize++

			return stop
		})

		elements[i] = elementSize

		if size == 0 {

			if !conditionOROperator {

				for i = range elements {

					if elements[i] == 0 {

						found = false

						break
					}
				}
			}

			close(channels)

			break
		}
	}

	ticks := map[string]int{}

	if found {

		index = 0

		// now qualify final ticks of all filters by ANDing bitmap

		for groupIndex, group := range groups {

			conditions := group.GetMapListValue(Conditions)

			operandValues := make([][]string, len(conditions))

			for conditionIndex := range conditions {

				if poolIndices[index] == utils.NotAvailable {

					continue
				}

				operandValues[conditionIndex] = executor.memoryPool.GetStringPool(poolIndices[index])[:elements[index]]

				index++
			}

			// max length of Values
			maxLength := len(operandValues[0])

			for i := range operandValues {

				if maxLength < len(operandValues[i]) {

					maxLength = len(operandValues[i])
				}
			}

			for i := 0; i < maxLength; i++ {

				for conditionIndex := range operandValues {

					if len(operandValues[conditionIndex]) > i {

						if _, found := ticks[operandValues[conditionIndex][i]]; !found {

							ticks[operandValues[conditionIndex][i]] = len(ticks)
						}
					}

				}
			}

			// nested bit maps is used for each group conditions union and intersection
			nestedConditionBitmaps := make([]bitmap.Bitmap, len(conditions))

			for i := 0; i < len(conditions); i++ {

				nestedConditionBitmaps[i] = bitmap.Bitmap{0}
			}

			//set position in bitmap if filters contains slice value
			for i := 0; i < maxLength; i++ {

				for conditionIndex := range operandValues {

					if len(operandValues[conditionIndex]) > i {

						if value, found := ticks[operandValues[conditionIndex][i]]; found {

							nestedConditionBitmaps[conditionIndex].Set(uint32(value))
						}
					}
				}
			}

			if len(conditions) > 1 {

				// union
				if group.GetStringValue(Operator) == or {

					nestedConditionBitmaps[0].Or(nestedConditionBitmaps[1])

					if len(conditions) == 3 {

						nestedConditionBitmaps[0].Or(nestedConditionBitmaps[2])
					}

				} else { // intersection
					nestedConditionBitmaps[0].And(nestedConditionBitmaps[1])

					if len(conditions) == 3 {

						nestedConditionBitmaps[0].And(nestedConditionBitmaps[2])
					}
				}
			}

			bitmaps[groupIndex] = nestedConditionBitmaps[0]

		}

		if len(groups) > 1 {

			// union
			if dataFilters.GetStringValue(Operator) == or {

				for i := 1; i < len(bitmaps); i++ {

					bitmaps[0].Or(bitmaps[i])
				}
			} else { // intersection
				for i := 1; i < len(bitmaps); i++ {

					bitmaps[0].And(bitmaps[i])
				}
			}
		}

		for key, value := range ticks {

			if !bitmaps[0].Contains(uint32(value)) {

				delete(ticks, key)
			}
		}

		if len(executor.dates) > 0 {

			drillDownDates := map[string]map[int32]string{}

			if executor.preAggregationQuery {

				drillDownTicks := map[int32]map[string]struct{}{}

				for tick := range ticks {

					utils.Split(tick, utils.KeySeparator, executor.tokenizers[0])

					qualifiedTick := StringToINT32(executor.tokenizers[0].Tokens[0])

					if _, ok := drillDownTicks[qualifiedTick]; !ok {

						drillDownTicks[qualifiedTick] = map[string]struct{}{}
					}

					drillDownTicks[qualifiedTick][executor.tokenizers[0].Tokens[1]] = struct{}{}

					date := utils.SecondsToDate(qualifiedTick)

					if _, ok := drillDownDates[date]; !ok {

						drillDownDates[date] = map[int32]string{}
					}

					drillDownDates[date][qualifiedTick] = utils.Empty
				}

				for tick, parts := range executor.ticks {

					if _, ok := drillDownTicks[tick]; !ok {

						delete(executor.ticks, tick)

						continue
					}

					for part := range parts {

						if _, ok := drillDownTicks[tick][part]; !ok {

							delete(parts, part)

							continue
						}
					}
				}

			} else {

				for tick := range ticks {

					utils.Split(tick, utils.KeySeparator, executor.tokenizers[0])

					qualifiedTick := StringToINT32(executor.tokenizers[0].Tokens[0])

					date := utils.SecondsToDate(qualifiedTick)

					if _, ok := drillDownDates[date]; !ok {

						drillDownDates[date] = map[int32]string{}
					}

					drillDownDates[date][qualifiedTick] = utils.Empty
				}

			}

			for date, qualifiedTicks := range executor.dates {

				if _, ok := drillDownDates[date]; !ok {

					delete(executor.dates, date)

					continue
				}

				for tick := range qualifiedTicks {

					if _, ok := drillDownDates[date][tick]; !ok {

						delete(qualifiedTicks, tick)

						continue
					}
				}

				if len(qualifiedTicks) == 0 {

					delete(executor.dates, date)
				}
			}

		} else {

			if executor.preAggregationQuery {

				for tick := range ticks {

					utils.Split(tick, utils.KeySeparator, executor.tokenizers[0])

					qualifiedTick := StringToINT32(executor.tokenizers[0].Tokens[0])

					if _, ok := executor.ticks[qualifiedTick]; !ok {

						executor.ticks[qualifiedTick] = map[string]struct{}{}
					}

					executor.ticks[qualifiedTick][executor.tokenizers[0].Tokens[1]] = struct{}{}

					date := utils.SecondsToDate(qualifiedTick)

					if _, ok := executor.dates[date]; !ok {

						executor.dates[date] = map[int32]string{}
					}

					executor.dates[date][qualifiedTick] = utils.Empty
				}
			} else {

				for tick := range ticks {

					utils.Split(tick, utils.KeySeparator, executor.tokenizers[0])

					qualifiedTick := StringToINT32(executor.tokenizers[0].Tokens[0])

					date := utils.SecondsToDate(qualifiedTick)

					if _, ok := executor.dates[date]; !ok {

						executor.dates[date] = map[int32]string{}
					}

					executor.dates[date][qualifiedTick] = utils.Empty
				}

			}
		}

		for _, i := range poolIndices {

			if i != utils.NotAvailable {

				executor.memoryPool.ReleaseStringPool(i)
			}
		}

		if utils.QueryPlanLogging {

			executor.logQueryPlan(fmt.Sprintf("query planner took %v ms to filter %v ticks", time.Now().UnixMilli()-t, len(ticks)))
		}

		return nil
	} else {

		if utils.QueryPlanLogging {

			executor.logQueryPlan(fmt.Sprintf("query planner took %v ms to filter %v ticks", time.Now().UnixMilli()-t, len(ticks)))
		}

		for _, i := range poolIndices {

			if i != utils.NotAvailable {

				executor.memoryPool.ReleaseStringPool(i)
			}
		}

		return errors.New(utils.ErrorPostingListLookupFailed)
	}
}

// build gval condition
func (executor *Executor) qualifyConditionExpression(groups []utils.MotadataMap, included bool, dataFilters utils.MotadataMap, mappingBitmaps []*bitmap.Bitmap) (int, bool, bool, error) {

	size := 0

	preAggregation := executor.preAggregationQuery

	if included {

		executor.conditionExpression += startBracket
	} else {

		executor.conditionExpression += not + startBracket
	}

	step2Query := executor.request.Contains(LastQueryStep) && executor.request.GetStringValue(LastQueryStep) == utils.No

	conditionOROperator := false

	for groupIndex, group := range groups {

		conditions := group.GetMapListValue(Conditions)

		if !step2Query && len(conditions) > 3 {

			return size, preAggregation, conditionOROperator, errors.New(utils.ErrorInvalidCondition)
		}

		included = group.GetStringValue(Filter) == include

		if included {

			executor.conditionExpression += startBracket
		} else {

			executor.conditionExpression += not + startBracket
		}

		for conditionIndex, condition := range conditions {

			mappingBitmaps[size] = &bitmap.Bitmap{0}

			size++

			operand := condition.GetStringValue(Operand)

			if datastore.IsSearchableColumn(operand) || datastore.IsBlobColumn(operand) {

				preAggregation = false

				executor.bitmapFilter = false

				executor.preAggregationQuery = false
			}

			executor.conditionOperands[operand] = struct{}{}

			if conditionIndex != 0 {

				if group.GetStringValue(Operator) == and {

					executor.conditionExpression += andCondition

				} else {

					conditionOROperator = true

					executor.conditionExpression += orCondition
				}
			}

			executor.conditionExpression += executor.buildConditionExpression(condition)
		}

		executor.conditionExpression += endBracket

		if groupIndex != len(groups)-1 {

			if dataFilters.GetStringValue(Operator) == and {

				executor.conditionExpression += andCondition

			} else {

				conditionOROperator = true

				executor.conditionExpression += orCondition
			}
		}
	}

	executor.conditionExpression += endBracket

	if size > len(executor.decoders) {

		return size, preAggregation, conditionOROperator, errors.New(utils.ErrorInvalidFilter)
	}

	return size, preAggregation, conditionOROperator, nil
}

func (executor *Executor) evaluateHorizontalFilterPhase2(condition utils.MotadataMap, dates []time.Time, excluded bool, channels chan int, index int, mappingBitmaps []*bitmap.Bitmap) {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			executor.logger.Error(fmt.Sprintf("error %v occurred in event filter posting list", err))

			executor.logger.Error(fmt.Sprintf("!!!STACK TRACE !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

		channels <- index
	}()

	operand := condition.GetStringValue(Operand)

	operator := condition.GetStringValue(Operator)

	dataStoreType := datastore.HorizontalStore

	plugin := executor.plugin

	if executor.datastoreType == utils.Log && datastore.IsEventMetadataField(operand) && !executor.fulltextSearchingView {

		plugin = datastore.EventSearchPlugin

		if executor.preAggregationQuery {

			plugin += utils.AggregationSeparator + "0" //default aggregation for event history plugin
		}
	}

	switch operator {

	case lessThan:
		fallthrough
	case lessThanOrEqual:
		fallthrough
	case greaterThan:
		fallthrough
	case greaterThanOrEqual:

		executor.evaluateHorizontalFilterRangeConditionOperator(utils.HyphenSeparator+dataStoreType+utils.HyphenSeparator+operand+utils.HyphenSeparator+plugin+utils.HyphenSeparator+INTToStringValue(int(Int64)),
			[]interface{}{condition.GetStringValue(utils.Value)}, dates, excluded, operator, index, operand, mappingBitmaps)

	case equal:

		if executor.preAggregationQuery {

			executor.evaluateHorizontalFilterEqualConditionStringOperator(utils.HyphenSeparator+dataStoreType+utils.HyphenSeparator+operand+utils.HyphenSeparator+plugin+utils.HyphenSeparator+INTToStringValue(int(String)), []interface{}{condition.GetStringValue(utils.Value)}, dates, index, operand, operator, excluded, mappingBitmaps)

			executor.evaluateHorizontalFilterEqualConditionNumericOperator(utils.HyphenSeparator+dataStoreType+utils.HyphenSeparator+operand+utils.HyphenSeparator+plugin+utils.HyphenSeparator+INTToStringValue(int(Int64)), []interface{}{condition.GetIntValue(utils.Value)}, dates, index, operand, operator, excluded, mappingBitmaps)
		} else {

			if reflect.ValueOf(condition.GetValue(utils.Value)).Kind() == reflect.String {

				executor.evaluateHorizontalFilterEqualConditionStringOperator(utils.HyphenSeparator+dataStoreType+utils.HyphenSeparator+operand+utils.HyphenSeparator+plugin+utils.HyphenSeparator+INTToStringValue(int(String)), []interface{}{condition.GetStringValue(utils.Value)}, dates, index, operand, operator, excluded, mappingBitmaps)

			} else {

				executor.evaluateHorizontalFilterEqualConditionNumericOperator(utils.HyphenSeparator+dataStoreType+utils.HyphenSeparator+operand+utils.HyphenSeparator+plugin+utils.HyphenSeparator+INTToStringValue(int(Int64)), []interface{}{condition.GetIntValue(utils.Value)}, dates, index, operand, operator, excluded, mappingBitmaps)
			}
		}

	case startWith:

		executor.evaluateHorizontalFilterRangeConditionOperator(utils.HyphenSeparator+dataStoreType+utils.HyphenSeparator+operand+utils.HyphenSeparator+plugin+utils.HyphenSeparator+INTToStringValue(int(String)), []interface{}{condition.GetStringValue(utils.Value)}, dates, excluded, operator, index, operand, mappingBitmaps)

	case endWith:

		executor.evaluateHorizontalFilterRangeConditionOperator(utils.HyphenSeparator+dataStoreType+utils.HyphenSeparator+operand+utils.HyphenSeparator+plugin+utils.HyphenSeparator+INTToStringValue(int(String)), []interface{}{condition.GetStringValue(utils.Value)}, dates, excluded, operator, index, operand, mappingBitmaps)

	case contain:

		executor.evaluateHorizontalFilterRangeConditionOperator(utils.HyphenSeparator+dataStoreType+utils.HyphenSeparator+operand+utils.HyphenSeparator+plugin+utils.HyphenSeparator+INTToStringValue(int(String)), []interface{}{condition.GetStringValue(utils.Value)}, dates, excluded, operator, index, operand, mappingBitmaps)

	case contains:

		executor.evaluateHorizontalFilterRangeConditionOperator(utils.HyphenSeparator+dataStoreType+utils.HyphenSeparator+operand+utils.HyphenSeparator+plugin+utils.HyphenSeparator+INTToStringValue(int(String)), condition.GetSliceValue(utils.Value), dates, excluded, operator, index, operand, mappingBitmaps)

	case in:

		values := condition.GetSliceValue(utils.Value)

		executor.evaluateHorizontalFilterEqualConditionStringOperator(utils.HyphenSeparator+dataStoreType+utils.HyphenSeparator+operand+utils.HyphenSeparator+plugin+utils.HyphenSeparator+INTToStringValue(int(String)), values, dates, index, operand, operator, excluded, mappingBitmaps)

		executor.evaluateHorizontalFilterEqualConditionNumericOperator(utils.HyphenSeparator+dataStoreType+utils.HyphenSeparator+operand+utils.HyphenSeparator+plugin+utils.HyphenSeparator+INTToStringValue(int(Int64)), values, dates, index, operand, operator, excluded, mappingBitmaps)

	}
}

func (executor *Executor) evaluateHorizontalFilterEqualConditionStringOperator(storeSuffix string, values []interface{}, dates []time.Time, index int, operand, operator string, excluded bool, mappingBitmaps []*bitmap.Bitmap) {

	if executor.preAggregationQuery {

		storeSuffix += utils.HyphenSeparator + INTToStringValue(executor.preAggregationTimeInterval)
	}

	for i := len(dates) - 1; i >= 0; i-- {

		date := utils.UnixMillisToDate(dates[i].UnixMilli())

		if datastore.IsStoreAvailable(date + storeSuffix) {

			if store := datastore.GetStore(date+storeSuffix, utils.None, false, true, executor.encoders[index], executor.tokenizers[index]); store != nil {

				startTick := utils.UnixToSeconds(time.Date(dates[i].Year(), dates[i].Month(), dates[i].Day(), 0, 0, 0, 0, time.UTC).Unix())

				executor.stringMappings[index].Clear()

				if datastore.IsSearchableColumn(operand) {

					poolIndex, tokens := executor.encoders[index].MemoryPool.AcquireStringPool(utils.NotAvailable)

					startPoolIndex, startIndices := executor.encoders[index].MemoryPool.AcquireINTPool(utils.NotAvailable)

					endPoolIndex, endIndices := executor.encoders[index].MemoryPool.AcquireINTPool(utils.NotAvailable)

					for _, value := range values {

						for _, token := range utils.Tokenize(ToString(value), tokens, startIndices, endIndices) {

							executor.stringMappings[index].Put(token, 0)
						}
					}

					executor.encoders[index].MemoryPool.ReleaseStringPool(poolIndex)

					executor.encoders[index].MemoryPool.ReleaseINTPool(startPoolIndex)

					executor.encoders[index].MemoryPool.ReleaseINTPool(endPoolIndex)

				} else {

					for _, value := range values {

						executor.stringMappings[index].Put(strings.ToLower(ToString(value)), 0)
					}
				}

				keyBuffers, _ := store.GetKeys(&executor.stringMappings[index], &executor.numericMappings[index], excluded, String)

				executor.processHorizontalEqualOperator(keyBuffers, store, index, startTick, String, operand, operator, mappingBitmaps)

				executor.stringMappings[index].Clear()
			}
		}
	}
}

func (executor *Executor) evaluateHorizontalFilterEqualConditionNumericOperator(storeSuffix string, values []interface{}, dates []time.Time, index int, operand, operator string, excluded bool, mappingBitmaps []*bitmap.Bitmap) {

	if executor.preAggregationQuery {

		storeSuffix += utils.HyphenSeparator + INTToStringValue(executor.preAggregationTimeInterval)
	}

	for i := len(dates) - 1; i >= 0; i-- {

		date := utils.UnixMillisToDate(dates[i].UnixMilli())

		if datastore.IsStoreAvailable(date + storeSuffix) {

			if store := datastore.GetStore(date+storeSuffix, utils.None, false, true, executor.encoders[index], executor.tokenizers[index]); store != nil {

				startTick := utils.UnixToSeconds(time.Date(dates[i].Year(), dates[i].Month(), dates[i].Day(), 0, 0, 0, 0, time.UTC).Unix())

				executor.numericMappings[index].Clear()

				executor.numericMappings[index].Clear()

				for _, value := range values {

					executor.numericMappings[index].Put(int64(ToINT(value)), 0)
				}

				keyBuffers, _ := store.GetKeys(&executor.stringMappings[index], &executor.numericMappings[index], excluded, Int64)

				executor.processHorizontalEqualOperator(keyBuffers, store, index, startTick, Int64, operand, operator, mappingBitmaps)

				executor.numericMappings[index].Clear()
			}
		}
	}
}

func (executor *Executor) evaluateHorizontalFilterRangeConditionOperator(storeSuffix string, values []interface{}, dates []time.Time, excluded bool, operator string, index int, operand string, mappingBitmaps []*bitmap.Bitmap) {

	if executor.preAggregationQuery {

		storeSuffix += utils.HyphenSeparator + INTToStringValue(executor.preAggregationTimeInterval)
	}

	var buffers [][]byte

	for i := len(dates) - 1; i >= 0; i-- {

		date := utils.UnixMillisToDate(dates[i].UnixMilli())

		if datastore.IsStoreAvailable(date + storeSuffix) {

			if store := datastore.GetStore(date+storeSuffix, utils.None, false, true, executor.encoders[index], executor.tokenizers[index]); store != nil {

				startTick := utils.UnixToSeconds(time.Date(dates[i].Year(), dates[i].Month(), dates[i].Day(), 0, 0, 0, 0, time.UTC).Unix())

				var err error

				if datastore.IsSearchableColumn(operand) {

					poolIndex, tokens := executor.encoders[index].MemoryPool.AcquireStringPool(utils.NotAvailable)

					startPoolIndex, startIndices := executor.encoders[index].MemoryPool.AcquireINTPool(utils.NotAvailable)

					endPoolIndex, endIndices := executor.encoders[index].MemoryPool.AcquireINTPool(utils.NotAvailable)

					for _, value := range values {

						for _, token := range utils.Tokenize(ToString(value), tokens, startIndices, endIndices) {

							executor.stringMappings[index].Clear()

							if operator == startWith || operator == contains {

								buffers, err = store.GetPrefixKeys([]byte(strings.ToLower(token)), excluded)

							} else if operator == endWith {

								buffers, err = store.GetSuffixKeys([]byte(strings.ToLower(token)), excluded)

							} else if operator == contain {

								buffers, err = store.GetContainKeys([]byte(strings.ToLower(token)), excluded)
							}

							if err != nil {

								executor.logError(err.Error())

								continue
							}

							executor.stringMappings[index].Put(strings.ToLower(token), 0)

							executor.processHorizontalEqualOperator(buffers, store, index, startTick, String, operand, operator, mappingBitmaps)

							executor.stringMappings[index].Clear()
						}
					}

					executor.encoders[index].MemoryPool.ReleaseStringPool(poolIndex)

					executor.encoders[index].MemoryPool.ReleaseINTPool(startPoolIndex)

					executor.encoders[index].MemoryPool.ReleaseINTPool(endPoolIndex)

				} else {

					for k := range values {

						executor.stringMappings[index].Clear()

						executor.numericMappings[index].Clear()

						value := ToString(values[k])

						dataType := String

						switch {

						case operator == startWith:

							fallthrough

						case operator == contains:

							buffers, err = store.GetPrefixKeys([]byte(strings.ToLower(value)), excluded)

						case operator == endWith:

							buffers, err = store.GetSuffixKeys([]byte(strings.ToLower(value)), excluded)

						case operator == contain:

							buffers, err = store.GetContainKeys([]byte(strings.ToLower(value)), excluded)

						case operator == greaterThan:

							dataType = Int64

							buffers, err = store.GetGreaterThanKeys(uint64(StringToINT(value)), false, excluded)

						case operator == greaterThanOrEqual:

							dataType = Int64

							buffers, err = store.GetGreaterThanKeys(uint64(StringToINT(value)), true, excluded)

						case operator == lessThan:

							dataType = Int64

							buffers, err = store.GetLessThanKeys(uint64(StringToINT(value)), false, excluded)

						case operator == lessThanOrEqual:

							dataType = Int64

							buffers, err = store.GetLessThanKeys(uint64(StringToINT(value)), true, excluded)
						}

						if err != nil {

							executor.logError(err.Error())

							continue
						}

						if dataType == String {

							executor.stringMappings[index].Put(strings.ToLower(value), 0)
						} else {

							executor.numericMappings[index].Put(StringToINT64(value), 0)
						}

						executor.processHorizontalEqualOperator(buffers, store, index, startTick, dataType, operand, operator, mappingBitmaps)

						executor.stringMappings[index].Clear()

						executor.numericMappings[index].Clear()
					}
				}
			}
		}
	}
}

func (executor *Executor) processHorizontalEqualOperator(buffers [][]byte, store *storage.Store, index int, startTick int32, dataType DataType, operand, operator string, mappingBitmaps []*bitmap.Bitmap) {

	if len(buffers) > 0 {

		adjustedIndex := index * utils.MaxWorkerEventKeyGroupLength

		if executor.valueBufferIndices[index] == UnmappedAnonymousByte {

			for i := adjustedIndex; i < adjustedIndex+utils.MaxWorkerEventKeyGroupLength; i++ {

				bytes, err := utils.MmapAnonymous(utils.MaxValueBufferBytes)

				if err != nil {

					executor.logError(fmt.Sprintf("error %v occurred while mapping annonymous buffer for executor %v and index %v", err, executor.executorId, i))

					bytes = make([]byte, utils.MaxValueBufferBytes)
				}

				executor.logger.Info(fmt.Sprintf("mapping done for annonymous buffer for index %v", i))

				executor.ValueBuffers[i] = bytes
			}

			executor.valueBufferIndices[index] = MappedAnonymousByte
		}

		i := 0

		for keyIndex, bytes := range buffers {

			executor.KeyBuffers[adjustedIndex+i] = bytes

			if i == utils.MaxWorkerEventKeyGroupLength-1 || keyIndex == len(buffers)-1 {

				i++

				bufferBytes, errs, err := store.GetMultiples(executor.KeyBuffers[adjustedIndex:adjustedIndex+i], executor.ValueBuffers[adjustedIndex:adjustedIndex+i], executor.encoders[index], executor.eventBatches[index], &executor.waitGroups[index], executor.tokenizers[index], false)

				if err != nil {

					i = 0

					executor.logError(err.Error())

					continue
				}

				for k := 0; k < i; k++ {

					// not getting too large error so no need to probe
					if errs[k] != nil || bufferBytes[k] == nil {

						continue
					}

					executor.evaluateHorizontalFilterTicks(bufferBytes[k], index, store, startTick)

				}

				i = 0
			} else {

				i++
			}
		}

		executor.resolveMappings(dataType, index, operand, operator, mappingBitmaps)
	}
}

func (executor *Executor) qualifyAggregatedProbes() {

	count := false

	for aggregation, occurrence := range executor.aggregations {

		if strings.EqualFold(aggregation, AvgFunc) && !count {

			if occurrence > 1 {

				for i := 0; i < occurrence; i++ {

					executor.probes++
				}

				executor.probes++ // count
			} else {

				executor.probes += 2
			}

			count = true

		} else if strings.EqualFold(aggregation, CountFunc) && !count {

			executor.probes += occurrence

			count = true
		} else {

			executor.probes += occurrence
		}
	}
}

func (executor *Executor) resolveMappings(dataType DataType, index int, operand, operator string, mappingBitmaps []*bitmap.Bitmap) {

	if executor.bitmapFilter {
		// resolve mapping and set in condition bitmaps
		if dataType == String {

			poolIndex, values := executor.encoders[index].MemoryPool.AcquireStringPool(executor.stringMappings[index].Count())

			defer executor.encoders[index].MemoryPool.ReleaseStringPool(poolIndex)

			elementSize := 0

			executor.stringMappings[index].Iter(func(val string, _ int32) (stop bool) {

				executor.stringMappings[index].Delete(val)

				values[elementSize] = val

				elementSize++

				return stop
			})

			for _, val := range values {

				executor.resolveStringOrdinals(index, operand, val, operator, mappingBitmaps)
			}

		} else {

			poolIndex, values := executor.encoders[index].MemoryPool.AcquireINT64Pool(executor.numericMappings[index].Count())

			executor.encoders[index].MemoryPool.ResetINT64Pool(poolIndex, len(values), 0)

			defer executor.encoders[index].MemoryPool.ReleaseINT64Pool(poolIndex)

			elementSize := 0

			executor.numericMappings[index].Iter(func(val int64, _ int32) (stop bool) {

				executor.numericMappings[index].Delete(val)

				values[elementSize] = val

				elementSize++

				return stop
			})

			for _, val := range values {

				binary.BigEndian.PutUint64(executor.int64Buffers[index], uint64(val))

				executor.resolveNumericOrdinals(index, operand, ReadBigEndianINT64Value(executor.int64Buffers[index]), operator, mappingBitmaps, false)
			}
		}
	}
}

// resolve mapping and set in condition bitmaps
func (executor *Executor) resolveNumericOrdinals(index int, operand string, value int64, operator string, mappingBitmaps []*bitmap.Bitmap, passOver bool) {

	executor.numericMappings[index].Clear()

	if executor.bitmapFilter || passOver {

		store := datastore.GetStore(operand+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, false, true, executor.encoders[index], executor.tokenizers[index])

		if store == nil {

			return
		}

		switch operator {

		case equal:

			fallthrough

		case in:

			if found, ordinal, _ := store.GetNumericMapping(value, executor.encoders[index]); found {

				executor.numericMappings[index].Delete(value)

				mappingBitmaps[index].Set(uint32(ordinal))
			}

			return

		case greaterThan:

			_ = store.GetGreaterThanMappings(value, false, &executor.numericMappings[index], executor.encoders[index])

		case greaterThanOrEqual:

			_ = store.GetGreaterThanMappings(value, true, &executor.numericMappings[index], executor.encoders[index])

		case lessThan:

			_ = store.GetLessThanMappings(value, false, &executor.numericMappings[index], executor.encoders[index])

		case lessThanOrEqual:

			_ = store.GetLessThanMappings(value, true, &executor.numericMappings[index], executor.encoders[index])
		}

		executor.numericMappings[index].Iter(func(key int64, ordinal int32) (stop bool) {

			executor.numericMappings[index].Delete(key)

			mappingBitmaps[index].Set(uint32(ordinal))

			return stop
		})
	}
}

// resolve mapping and set in condition bitmaps
func (executor *Executor) resolveStringOrdinals(index int, operand, value, operator string, mappingBitmaps []*bitmap.Bitmap) {

	executor.stringMappings[index].Clear()

	store := datastore.GetStore(operand+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, false, true, executor.encoders[index], executor.tokenizers[index])

	if store == nil {

		return
	}

	value = utils.FilterEscapeCharacters1(strings.ToLower(value))

	switch operator {

	case equal:

		fallthrough

	case in:

		_ = store.GetStringEqualMappings(value, &executor.stringMappings[index])

	case startWith:

		_ = store.GetPrefixMappings(value, &executor.stringMappings[index])

	case contain:
		fallthrough
	case contains:

		_ = store.GetContainMappings(value, &executor.stringMappings[index])

	case endWith:

		_ = store.GetSuffixMappings(value, &executor.stringMappings[index])
	}

	executor.stringMappings[index].Iter(func(key string, ordinal int32) (stop bool) {

		executor.stringMappings[index].Delete(key)

		mappingBitmaps[index].Set(uint32(ordinal))

		return stop
	})
}

func (executor *Executor) evaluateHorizontalFilterTicks(bytes []byte, index int, store *storage.Store, startTick int32) {

	if executor.preAggregationQuery {

		poolIndex, eventIds, err := executor.decoders[index].DecodeINT64Values(GetEncoding(bytes[0]), GetDataType(bytes[0]), bytes[1:], string(bytes), store.GetName(), 0)

		if err != nil {

			executor.logError(err.Error())

			if poolIndex != utils.NotAvailable {

				executor.decoders[index].MemoryPool.ReleaseINT64Pool(poolIndex)
			}

			return
		}

		for _, eventId := range eventIds {

			executor.responses[index].Put(utils.SplitAggregationEventId(eventId), struct{}{})
		}

		executor.decoders[index].MemoryPool.ReleaseINT64Pool(poolIndex)

	} else {

		poolIndex, bitmapBytes, err := executor.decoders[index].DecodeSnappy(bytes)

		if err != nil {

			executor.logError(err.Error())

			if poolIndex != utils.NotAvailable {

				executor.decoders[index].MemoryPool.ReleaseBytePool(poolIndex)
			}

			return
		}

		searchTokenBitmap := bitmap.FromBytes(bitmapBytes)

		if searchTokenBitmap != nil {

			searchTokenBitmap.Range(func(position uint32) {

				executor.responses[index].Put(INT32ToStringValue(startTick+int32(position)), struct{}{})
			})
		}

		executor.decoders[index].MemoryPool.ReleaseBytePool(poolIndex)
	}
}

func (executor *Executor) buildConditionExpression(condition utils.MotadataMap) string {

	conditionExpression := startBracket

	operand := condition.GetStringValue(Operand)

	operator := condition.GetStringValue(Operator)

	// means need to change value to ordinal.
	if !executor.bitmapFilter && !datastore.IsSearchableColumn(operand) && !datastore.IsBlobColumn(operand) {

		index := 0

		dataType := String

		poolIndex := utils.NotAvailable

		switch operator {

		case lessThan:
			fallthrough
		case lessThanOrEqual:
			fallthrough
		case greaterThan:
			fallthrough
		case greaterThanOrEqual:

			conditionExpression += contains + startBracket + "$[\"" + operand + "\"]" + utils.CommaSeparator + "$[\"" + operand + utils.DotSeparator + operator + "\"]" + endBracket

			executor.resolveNumericOrdinals(index, operand, condition.GetInt64Value(utils.Value), operator, executor.MappingBitmaps, true)

			conditionOperandValues := utils.MotadataMap{}

			executor.MappingBitmaps[0].Range(func(ordinal uint32) {

				executor.MappingBitmaps[0].Remove(ordinal)

				conditionOperandValues[UINT32ToStringValue(ordinal)] = struct{}{}

			})

			executor.conditionOperandValues[operand+utils.DotSeparator+operator] = conditionOperandValues

		case equal:

			valueType := reflect.ValueOf(condition.GetValue(utils.Value)).Kind()

			if valueType == reflect.String {

				poolIndex, _ = executor.memoryPool.AcquireStringPool(1)

				defer executor.memoryPool.ReleaseStringPool(poolIndex)

				executor.memoryPool.GetStringPool(poolIndex)[0] = utils.FilterEscapeCharacters(condition.GetStringValue(utils.Value))

				conditionExpression += utils.Space + "$[\"" + operand + "\"]" + utils.Space + operator + operator + utils.Space + "\"" + utils.JoinSeparator + "\""

			} else {

				poolIndex, _ = executor.memoryPool.AcquireINT64Pool(1)

				defer executor.memoryPool.ReleaseINT64Pool(poolIndex)

				dataType = Int64

				executor.memoryPool.GetINT64Pool(poolIndex)[0] = int64(ToINT(condition.GetValue(utils.Value)))

				conditionExpression += utils.Space + "$[\"" + operand + "\"]" + utils.Space + operator + operator + utils.Space + utils.JoinSeparator
			}

		case startWith:

			fallthrough

		case endWith:

			fallthrough

		case contain:

			conditionExpression += contains + startBracket + "$[\"" + operand + "\"]" + utils.CommaSeparator + "$[\"" + operand + utils.DotSeparator + operator + "\"]" + endBracket

			executor.resolveStringOrdinals(index, operand, condition.GetStringValue(utils.Value), operator, executor.MappingBitmaps)

			conditionOperandValues := utils.MotadataMap{}

			executor.MappingBitmaps[0].Range(func(ordinal uint32) {

				executor.MappingBitmaps[0].Remove(ordinal)

				conditionOperandValues[UINT32ToStringValue(ordinal)] = struct{}{}

			})

			executor.conditionOperandValues[operand+utils.DotSeparator+operator] = conditionOperandValues

		case in:

			values := condition.GetSliceValue(utils.Value)

			if len(values) == 1 {

				valueType := reflect.ValueOf(values[0]).Kind()

				if valueType == reflect.String {

					poolIndex, _ = executor.memoryPool.AcquireStringPool(1)

					defer executor.memoryPool.ReleaseStringPool(poolIndex)

					executor.memoryPool.GetStringPool(poolIndex)[0] = utils.FilterEscapeCharacters(ToString(values[0]))

					conditionExpression += utils.Space + "$[\"" + operand + "\"]" + utils.Space + equal + equal + utils.Space + "\"" + utils.JoinSeparator + "\""

				} else {

					poolIndex, _ = executor.memoryPool.AcquireINT64Pool(1)

					defer executor.memoryPool.ReleaseINT64Pool(poolIndex)

					dataType = Int64

					executor.memoryPool.GetINT64Pool(poolIndex)[0] = int64(ToINT(values[0]))

					conditionExpression += utils.Space + "$[\"" + operand + "\"]" + utils.Space + equal + equal + utils.Space + utils.JoinSeparator
				}

			} else {

				conditionExpression += contains + startBracket + "$[\"" + operand + "\"]" + utils.CommaSeparator + "$[\"" + operand + utils.DotSeparator + operator + "\"]" + endBracket

				valueType := reflect.ValueOf(values[0]).Kind()

				if valueType == reflect.Float64 || valueType == reflect.Float32 {

					poolIndex, _ = executor.memoryPool.AcquireINT64Pool(len(values))

					defer executor.memoryPool.ReleaseINT64Pool(poolIndex)

					dataType = Int64

					for i := range values {

						executor.memoryPool.GetINT64Pool(poolIndex)[i] = int64(ToINT(values[i]))
					}
				} else {

					poolIndex, _ = executor.memoryPool.AcquireStringPool(len(values))

					defer executor.memoryPool.ReleaseStringPool(poolIndex)

					for i := range values {

						executor.memoryPool.GetStringPool(poolIndex)[i] = utils.FilterEscapeCharacters(ToString(values[i]))
					}
				}
			}
		}

		if operator == equal || operator == in {

			mappingStore := datastore.GetStore(operand+utils.HyphenSeparator+datastore.Mappings, utils.None, false, true, executor.encoder, executor.tokenizers[0])

			if dataType == String {

				_, ordinalPoolIndex, ordinals := mappingStore.MapStringValues(poolIndex, executor.encoder, len(executor.memoryPool.GetStringPool(poolIndex)))

				defer executor.memoryPool.ReleaseINT32Pool(ordinalPoolIndex)

				if len(ordinals) == 1 {

					conditionExpression = strings.ReplaceAll(conditionExpression, utils.JoinSeparator, INT32ToStringValue(ordinals[0]))
				} else {

					// in values

					conditionOperandValues := utils.MotadataMap{}

					for i := range ordinals {

						conditionOperandValues[INT32ToStringValue(ordinals[i])] = struct{}{}
					}

					executor.conditionOperandValues[operand+utils.DotSeparator+operator] = conditionOperandValues

				}
			} else {

				_, ordinalPoolIndex, ordinals := mappingStore.MapNumericValues(poolIndex, executor.encoder, len(executor.memoryPool.GetINT64Pool(poolIndex)))

				defer executor.memoryPool.ReleaseINT32Pool(ordinalPoolIndex)

				if len(ordinals) == 1 {

					conditionExpression = strings.ReplaceAll(conditionExpression, utils.JoinSeparator, INT32ToStringValue(ordinals[0]))
				} else {

					// in values

					conditionOperandValues := utils.MotadataMap{}

					for i := range ordinals {

						conditionOperandValues[INT32ToStringValue(ordinals[i])] = struct{}{}
					}

					executor.conditionOperandValues[operand+utils.DotSeparator+operator] = conditionOperandValues

				}
			}
		}

	} else {

		switch operator {

		case equal:

			conditionExpression += utils.Space + "$[\"" + operand + "\"]" + utils.Space + operator + operator + utils.Space + "\"" + utils.FilterEscapeCharacters(condition.GetStringValue(utils.Value)) + "\""

		case startWith:

			conditionExpression += utils.Space + "startwith" + startBracket + "$[\"" + operand + "\"]" + utils.CommaSeparator + "\"" + utils.FilterEscapeCharacters(condition.GetStringValue(utils.Value)) + "\"" + endBracket

		case endWith:

			conditionExpression += utils.Space + "endwith" + startBracket + "$[\"" + operand + "\"]" + utils.CommaSeparator + "\"" + utils.FilterEscapeCharacters(condition.GetStringValue(utils.Value)) + "\"" + endBracket

		case contain:

			conditionExpression += utils.Space + "contain" + startBracket + "$[\"" + operand + "\"]" + utils.CommaSeparator + "\"" + utils.FilterEscapeCharacters(condition.GetStringValue(utils.Value)) + "\"" + endBracket

		case in:

			conditionExpression += contains + startBracket + "$[\"" + operand + "\"]" + utils.CommaSeparator + "$[\"" + operand + utils.DotSeparator + operator + "\"]" + endBracket

			conditionOperandValues := utils.MotadataMap{}

			for _, value := range condition.GetSliceValue(utils.Value) {

				conditionOperandValues[ToString(value)] = struct{}{}
			}

			executor.conditionOperandValues[operand+utils.DotSeparator+operator] = conditionOperandValues

		}
	}

	conditionExpression += endBracket

	return conditionExpression
}

func (executor *Executor) qualifyGroups(groups []interface{}, indexableColumns map[string]struct{}) map[string]struct{} {

	for _, grouping := range groups {

		executor.GroupColumns[executor.GroupColumnElementSize] = ToString(grouping)

		if executor.GroupColumns[executor.GroupColumnElementSize] == Group || executor.GroupColumns[executor.GroupColumnElementSize] == Tag {

			executor.externalGroupIndex = executor.GroupColumnElementSize

			/*
				in case of event policy we don't have group by group as of now
			*/

			if executor.datastoreType == utils.PolicyFlapHistory || executor.datastoreType == utils.MetricPolicy {

				indexableColumns[utils.ObjectId] = struct{}{}

			} else {

				indexableColumns[utils.EventSource] = struct{}{}
			}
		} else {

			indexableColumns[executor.GroupColumns[executor.GroupColumnElementSize]] = struct{}{}

		}

		executor.GroupColumnElementSize++
	}

	if utils.QueryPlanLogging {

		executor.logQueryPlan(fmt.Sprintf("group by columns are %v", executor.GroupColumns[:executor.GroupColumnElementSize]))
	}

	return indexableColumns
}

func getHorizontalStatusFlapColumn(column string) string {

	if strings.HasSuffix(column, datastore.Duration) {

		column = datastore.Duration
	} else if strings.HasSuffix(column, datastore.ObjectStatusFlapHistory) {

		column = datastore.ObjectStatusFlapHistory
	} else if strings.HasSuffix(column, datastore.Reason) {

		column = datastore.Reason
	} else if strings.Contains(column, datastore.Monitor) || strings.Contains(column, utils.ObjectId) {

		column = utils.ObjectId
	} else if strings.Contains(column, datastore.Instance) {

		column = datastore.Instance
	}

	return column
}
