/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>ata-5190  Migrated constants from datastore package to utils package to match SonarQube Standard
* 2025-03-18             Vedant D. Dokania      Motadata-5200  Status Flap Report Changes  and drilldown event changes
* 2025-03-21			 Dhaval <PERSON>ra			<PERSON>ata-5452  NetRoute Changes For Resolving Query Engine Type
* 2025-04-02			 <PERSON><PERSON>val <PERSON>-4859  Added NetRoute Status Metric Datastore Type
* 2025-05-05			 <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-11  			 <PERSON>ik <PERSON>a			MOTADATA-4911 Sort memory optimize with using int32 instead of int
* 2025-05-14             Vedant Dokania         Motadata -6249  Records length changes
* 2025-06-04             Aashil Shah            MOTADATA-5780 Added IOCP workers and process kill logic
 */

package query

import (
	bytes2 "bytes"
	"encoding/json"
	"fmt"
	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/kelindar/bitmap"
	cp "github.com/otiai10/copy"
	"github.com/pbnjay/memory"
	"github.com/pebbe/zmq4"
	"github.com/shirou/gopsutil/process"
	"github.com/stretchr/testify/assert"
	"golang.org/x/net/context"
	broker2 "motadatadatastore/broker"
	"motadatadatastore/cache"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/datastore/writer"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	_ "net/http/pprof"
	"os"
	"os/exec"
	"path/filepath"
	"reflect"
	"runtime"
	"runtime/debug"
	"strings"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

var (
	executors []*Executor

	workers []*Worker

	executorId = 0

	responses chan []byte

	indexWriters []*writer.IndexWriter

	metricAggregators = make([]*writer.MetricAggregator, utils.MetricAggregators)

	verticalWriters = make([]*writer.VerticalWriter, utils.VerticalWriters)

	staticMetricWriters = make([]*writer.StaticMetricWriter, utils.StaticMetricWriters)

	horizontalWriters = make([]*writer.HorizontalWriter, utils.HorizontalWriters)

	healthMetricWriters = make([]*writer.HorizontalWriter, utils.HealthMetricWriters)

	ioWorkers = make([]*storage.DiskIOWorker, utils.DiskIOWorkers)

	broker = &broker2.Broker{}

	writers = make([]*broker2.DataWriter, utils.DataWriters)

	readers = make([]*writer.DataReader, 2)

	tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	dataAggregators = make([]*broker2.DataAggregator, utils.DataAggregators)

	eventAggregators = make([]*writer.EventAggregator, utils.EventAggregators)

	testDir = filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator

	aiopsDir = filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))) + utils.PathSeparator + "aiops"

	testcaseLogger = utils.NewLogger("Timer", "test")

	configBytes []byte

	executorAllocations map[int]QueryEngineType

	workerAllocations map[int]QueryEngineType

	subscriberSocket *zmq4.Socket

	publisherSocket *zmq4.Socket

	syncFinished sync.WaitGroup

	syncJobShutdownNotifications []chan bool

	encoder = codec.NewEncoder(utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools))
)

const (
	CustomDB = "customDB"

	FlowTable = "flowTable"

	FlowTable1 = "flowTable1"
)

const (
	Today = "today" //start of the day to now

	Last24Hours = "last.24.hours"

	Last12Hours = "last.12.hours"

	Last6Hours = "last.6.hours"

	Last9Hours = "last.9.hours"

	Last1Hour = "last.hour"

	Last5Minutes = "last.5.minutes"

	Last15Minutes = "last.15.minutes"

	Last48Hours = "last.48.hours"

	Last2Days = "last.2.days"

	Last7Days = "last.7.days"

	ThisWeek = "this.week"

	ThisMonth = "this.month"

	Last1Month = "last.1.month"

	LastQuarter = "last.quarter"

	Interface = "interface"
)

const ( //Aggregation Constants

	MinSystemCPUPercent       = "system.cpu.percent^min"
	MaxSystemCPUPercent       = "system.cpu.percent^max"
	SumSystemCPUPercent       = "system.cpu.percent^sum"
	AvgSystemCPUPercent       = "system.cpu.percent^avg"
	CountSystemCPUPercent     = "system.cpu.percent^count"
	LastSystemCPUPercent      = "system.cpu.percent^last"
	SparklineSystemCPUPercent = "system.cpu.percent^sparkline"

	AvgSystemMemoryUsedBytes  = "system.memory.used.bytes1^avg"
	AvgSystemCPUKernelPercent = "system.cpu.kernel.percent^avg"

	SumSystemDiskUsedPercent   = "system.disk.used.percent^sum"
	MaxSystemDiskUsedPercent   = "system.disk.used.percent^max"
	AvgSystemDiskUsedPercent   = "system.disk.used.percent^avg"
	CountSystemDiskUsedPercent = "system.disk.used.percent^count"

	LastSystemOSName = "system.os.name^last"

	MinInterfaceINPackets   = "interface~in.packets^min"
	MaxInterfaceINPackets   = "interface~in.packets^max"
	SumInterfaceINPackets   = "interface~in.packets^sum"
	AvgInterfaceINPackets   = "interface~in.packets^avg"
	CountInterfaceINPackets = "interface~in.packets^count"
	LastInterfaceINPackets  = "interface~in.packets^last"

	SumInterfaceBitsPerSec   = "interface~bits.per.sec^sum"
	AvgInterfaceBitsPerSec   = "interface~bits.per.sec^avg"
	MinInterfaceBitsPerSec   = "interface~bits.per.sec^min"
	MaxInterfaceBitsPerSec   = "interface~bits.per.sec^max"
	CountInterfaceBitsPerSec = "interface~bits.per.sec^count"
	LastInterfaceBitsPerSec  = "interface~bits.per.sec^last"

	LastInterfaceStatus = "interface~status^last"

	LastInterfaceAlias = "interface~alias^last"

	AvgInterfaceOUTPackets  = "interface~out.packets^avg"
	SumInterfaceOUTPackets  = "interface~out.packets^sum"
	LastInterfaceOUTPackets = "interface~out.packets^last"

	MinInterfaceSentOctets     = "interface~sent.octets^min"
	AvgInterfaceSentOctets     = "interface~sent.octets^avg"
	AvgInterfaceReceivedOctets = "interface~received.octets^avg"
	MaxInterfaceReceivedOctets = "interface~received.octets^max"

	MinInterfaceINTrafficUtilization   = "interface~in.traffic.utilization^min"
	MaxInterfaceINTrafficUtilization   = "interface~in.traffic.utilization^max"
	SumInterfaceINTrafficUtilization   = "interface~in.traffic.utilization^sum"
	CountInterfaceINTrafficUtilization = "interface~in.traffic.utilization^count"
	AvgInterfaceINTrafficUtilization   = "interface~in.traffic.utilization^avg"
	LastInterfaceINTrafficUtilization  = "interface~in.traffic.utilization^last"

	MinInterfaceINTrafficUtilizationPercent   = "interface~in.traffic.utilization.percent^min"
	MaxInterfaceINTrafficUtilizationPercent   = "interface~in.traffic.utilization.percent^max"
	SumInterfaceINTrafficUtilizationPercent   = "interface~in.traffic.utilization.percent^sum"
	CountInterfaceINTrafficUtilizationPercent = "interface~in.traffic.utilization.percent^count"
	AvgInterfaceINTrafficUtilizationPercent   = "interface~in.traffic.utilization.percent^avg"
	LastInterfaceINTrafficUtilizationPercent  = "interface~in.traffic.utilization.percent^last"

	MinInterfaceOUTTrafficUtilization   = "interface~out.traffic.utilization^min"
	MaxInterfaceOUTTrafficUtilization   = "interface~out.traffic.utilization^max"
	SumInterfaceOUTTrafficUtilization   = "interface~out.traffic.utilization^sum"
	CountInterfaceOUTTrafficUtilization = "interface~out.traffic.utilization^count"
	AvgInterfaceOUTTrafficUtilization   = "interface~out.traffic.utilization^avg"
	LastInterfaceOUTTrafficUtilization  = "interface~out.traffic.utilization^last"

	MinInterfaceErrorPackets   = "interface~error.packets^min"
	MaxInterfaceErrorPackets   = "interface~error.packets^max"
	SumInterfaceErrorPackets   = "interface~error.packets^sum"
	CountInterfaceErrorPackets = "interface~error.packets^count"
	AvgInterfaceErrorPackets   = "interface~error.packets^avg"
	LastInterfaceErrorPackets  = "interface~error.packets^last"

	MinVLANPorts   = "vlan.ports^min"
	MaxVLANPorts   = "vlan.ports^max"
	SumVLANPorts   = "vlan.ports^sum"
	CountVLANPorts = "vlan.ports^count"
	AvgVLANPorts   = "vlan.ports^avg"
	LastVLANPorts  = "vlan.ports^last"

	MinInterfaces   = "interfaces^min"
	MaxInterfaces   = "interfaces^max"
	SumInterfaces   = "interfaces^sum"
	CountInterfaces = "interfaces^count"
	AvgInterfaces   = "interfaces^avg"
	LastInterfaces  = "interfaces^last"

	MinSystemMemoryCommittedBytes   = "system.memory.committed.bytes^min"
	MaxSystemMemoryCommittedBytes   = "system.memory.committed.bytes^max"
	SumSystemMemoryCommittedBytes   = "system.memory.committed.bytes^sum"
	CountSystemMemoryCommittedBytes = "system.memory.committed.bytes^count"
	AvgSystemMemoryCommittedBytes   = "system.memory.committed.bytes^avg"
	LastSystemMemoryCommittedBytes  = "system.memory.committed.bytes^last"

	MinDummyNumericColumn   = "dummy.numeric.column^min"
	MaxDummyNumericColumn   = "dummy.numeric.column^max"
	SumDummyNumericColumn   = "dummy.numeric.column^sum"
	CountDummyNumericColumn = "dummy.numeric.column^count"
	AvgDummyNumericColumn   = "dummy.numeric.column^avg"

	MinDummyINT8FLOATColumn   = "dummy.int8Float.column^min"
	MaxDummyINT8FLOATColumn   = "dummy.int8Float.column^max"
	SumDummyINT8FLOATColumn   = "dummy.int8Float.column^sum"
	CountDummyINT8FLOATColumn = "dummy.int8Float.column^count"
	AvgDummyINT8FLOATColumn   = "dummy.int8Float.column^avg"

	MinDummyINTFLOATColumn   = "dummy.intFloat.column^min"
	MaxDummyINTFLOATColumn   = "dummy.intFloat.column^max"
	SumDummyINTFLOATColumn   = "dummy.intFloat.column^sum"
	CountDummyINTFLOATColumn = "dummy.intFloat.column^count"
	AvgDummyINTFLOATColumn   = "dummy.intFloat.column^avg"
	LastDummyINTFLOATColumn  = "dummy.intFloat.column^last"

	MinDummyFLOATINTColumn   = "dummy.floatInt.column^min"
	MaxDummyFLOATINTColumn   = "dummy.floatInt.column^max"
	SumDummyFLOATINTColumn   = "dummy.floatInt.column^sum"
	CountDummyFLOATINTColumn = "dummy.floatInt.column^count"
	AvgDummyFLOATINTColumn   = "dummy.floatInt.column^avg"

	MinDummyINT16FLOATColumn   = "dummy.int16Float.column^min"
	MaxDummyINT16FLOATColumn   = "dummy.int16Float.column^max"
	SumDummyINT16FLOATColumn   = "dummy.int16Float.column^sum"
	CountDummyINT16FLOATColumn = "dummy.int16Float.column^count"
	AvgDummyINT16FLOATColumn   = "dummy.int16Float.column^avg"

	MinDummyINT32FLOATColumn   = "dummy.int32Float.column^min"
	MaxDummyINT32FLOATColumn   = "dummy.int32Float.column^max"
	SumDummyINT32FLOATColumn   = "dummy.int32Float.column^sum"
	CountDummyINT32FLOATColumn = "dummy.int32Float.column^count"
	AvgDummyINT32FLOATColumn   = "dummy.int32Float.column^avg"

	MinDummyINT64FLOATColumn   = "dummy.int64Float.column^min"
	MaxDummyINT64FLOATColumn   = "dummy.int64Float.column^max"
	SumDummyINT64FLOATColumn   = "dummy.int64Float.column^sum"
	CountDummyINT64FLOATColumn = "dummy.int64Float.column^count"
	AvgDummyINT64FLOATColumn   = "dummy.int64Float.column^avg"

	LastSystemMemoryUsedBytes  = "system.memory.used.bytes^last"
	MinSystemMemoryUsedBytes   = "system.memory.used.bytes^min"
	CountSystemMemoryUsedBytes = "system.memory.used.bytes^count"

	AvgSystemNetworkINBytes   = "system.network.in.bytes^avg"
	MinSystemNetworkINBytes   = "system.network.in.bytes^min"
	MaxSystemNetworkINBytes   = "system.network.in.bytes^max"
	SumSystemNetworkINBytes   = "system.network.in.bytes^sum"
	CountSystemNetworkINBytes = "system.network.in.bytes^count"

	MaxPingMaxLatency = "ping.max.latency^max"
	SumPingMaxLatency = "ping.max.latency^sum"
	MinPingMaxLatency = "ping.max.latency^min"

	LastDummyINTFLOATColumnCustom = "dummy.intFloat.column.custom^last"

	LastDummyINTStringColumn = "dummy.intString.column^last"

	MessageCount = "message^count"

	// event constant

	MaxLogLevel   = "log.level^max"
	MinLogLevel   = "log.level^min"
	SumLogLevel   = "log.level^sum"
	CountLogLevel = "log.level^count"
	AvgLogLevel   = "log.level^avg"

	MaxProcesses   = "processes^max"
	MinProcesses   = "processes^min"
	SumProcesses   = "processes^sum"
	CountProcesses = "processes^count"
	AvgProcesses   = "processes^avg"

	CountEventSource = "event.source^count"

	MaxBytesSent   = "bytes.sent^max"
	MinBytesSent   = "bytes.sent^min"
	SumBytesSent   = "bytes.sent^sum"
	CountBytesSent = "bytes.sent^count"
	AvgBytesSent   = "bytes.sent^avg"

	SumPolicyValue   = "value^sum"
	CountPolicyValue = "value^count"
	MinPolicyValue   = "value^min"
	MaxPolicyValue   = "value^max"
	AvgPolicyValue   = "value^avg"

	MaxBytesReceived   = "bytes.received^max"
	MinBytesReceived   = "bytes.received^min"
	SumBytesReceived   = "bytes.received^sum"
	CountBytesReceived = "bytes.received^count"
	AvgBytesReceived   = "bytes.received^avg"

	SumLogId   = "log.id^sum"
	CountLogId = "log.id^count"
	AvgLogId   = "log.id^avg"

	SumLogINCount   = "log.in.count^sum"
	CountLogINCount = "log.in.count^count"
	AvgLogINCount   = "log.in.count^avg"

	MaxSourcePort   = "source.port^max"
	MinSourcePort   = "source.port^min"
	SumSourcePort   = "source.port^sum"
	CountSourcePort = "source.port^count"
	AvgSourcePort   = "source.port^avg"

	//trap constants

	CountTrapMessage     = "trap.message^count"
	LastTrapMessage      = "trap.message^last"
	LastTrapVendor       = "trap.vendor^last"
	LastTrapEnterpriseId = "trap.enterprise.id^last"
	LastTrapVersion      = "trap.version^last"

	AvgPackets = "packets^avg"

	CountSeverity = "severity^count"
	CountMessage  = "message^count"

	AvgVolumeBytesPerSec = "volume.bytes.per.sec^avg"
	MinVolumeBytesPerSec = "volume.bytes.per.sec^min"
	MaxVolumeBytesPerSec = "volume.bytes.per.sec^max"
	SumVolumeBytesPerSec = "volume.bytes.per.sec^sum"

	SumVolumeBytes   = "volume.bytes^sum"
	CountVolumeBytes = "volume.bytes^count"
	MinVolumeBytes   = "volume.bytes^min"
	MaxVolumeBytes   = "volume.bytes^max"

	CountVolumeBytesPerSec  = "volume.bytes.per.sec^count"
	AvgDuration             = "duration^avg"
	AvgFlows                = "flows^avg"
	AvgDestinationPort      = "destination.port^avg"
	CountDestinationPort    = "destination.port^count"
	AvgDestinationAS        = "destination.as^avg"
	CountDestinationAS      = "destination.as^count"
	AvgSourceIFIndex        = "source.if.index^avg"
	CountSourceIFIndex      = "source.if.index^count"
	AvgDestinationIFIndex   = "destination.if.index^avg"
	CountDestinationIFIndex = "destination.if.index^count"
	AvgTCPFlags             = "tcp.flags^avg"
	CountTCPFlags           = "tcp.flags^count"
	AvgVolumeBytesPerPacket = "volume.bytes.per.packet^avg"
	AvgPacketsPerSec        = "packets.per.sec^avg"
	AvgVolumeBytes          = "volume.bytes^avg"
	AvgSourceAS             = "source.as^avg"
	CountSourceAS           = "source.as^count"

	//aggregation columns constants

	MinDummyINT8FLOATColumn1   = "interface~dummy.int8Float.column1^min"
	MaxDummyINT8FLOATColumn1   = "interface~dummy.int8Float.column1^max"
	SumDummyINT8FLOATColumn1   = "interface~dummy.int8Float.column1^sum"
	CountDummyINT8FLOATColumn1 = "interface~dummy.int8Float.column1^count"
	AvgDummyINT8FLOATColumn1   = "interface~dummy.int8Float.column1^avg"
	LastDummyINT8FLOATColumn1  = "interface~dummy.int8Float.column1^last"

	MinDummyINT16FLOATColumn1   = "interface~dummy.int16Float.column1^min"
	MaxDummyINT16FLOATColumn1   = "interface~dummy.int16Float.column1^max"
	SumDummyINT16FLOATColumn1   = "interface~dummy.int16Float.column1^sum"
	CountDummyINT16FLOATColumn1 = "interface~dummy.int16Float.column1^count"
	AvgDummyINT16FLOATColumn1   = "interface~dummy.int16Float.column1^avg"

	MinDummyINT32FLOATColumn1   = "interface~dummy.int32Float.column1^min"
	MaxDummyINT32FLOATColumn1   = "interface~dummy.int32Float.column1^max"
	SumDummyINT32FLOATColumn1   = "interface~dummy.int32Float.column1^sum"
	CountDummyINT32FLOATColumn1 = "interface~dummy.int32Float.column1^count"
	AvgDummyINT32FLOATColumn1   = "interface~dummy.int32Float.column1^avg"

	MinDummyINT64FLOATColumn1   = "interface~dummy.int64Float.column1^min"
	MaxDummyINT64FLOATColumn1   = "interface~dummy.int64Float.column1^max"
	SumDummyINT64FLOATColumn1   = "interface~dummy.int64Float.column1^sum"
	CountDummyINT64FLOATColumn1 = "interface~dummy.int64Float.column1^count"
	AvgDummyINT64FLOATColumn1   = "interface~dummy.int64Float.column1^avg"

	SumDummyFLOATColumn1 = "interface~dummy.float.column1^sum"
	AvgDummyFLOATColumn1 = "interface~dummy.float.column1^avg"
)

const (
	_5MinGranularity   = "5 m"
	_8MinGranularity   = "8 m"
	_10MinGranularity  = "10 m"
	_11MinGranularity  = "11 m"
	_15MinGranularity  = "15 m"
	_30MinGranularity  = "30 m"
	_300SecGranularity = "300 s"
	_1DayGranularity   = "1 d"
	_1HourGranularity  = "1 h"
	_6HourGranularity  = "6 h"
	_2SecGranularity   = "2 s"
	_1MonthGranularity = "1 M"
)

const (
	logPlugin1  = "50909-windows.login.audit"
	flowPlugin1 = "21003-flow"

	EventSource          = "event.source"
	BytesSent            = "bytes.sent"
	LogId                = "log.id"
	SourcePort           = "source.port"
	SystemOS             = "system.os"
	BytesReceived        = "bytes.received"
	DestinationIP        = "destination.ip"
	SourceIP             = "source.ip"
	DestinationPort      = "destination.port"
	VolumeBytesPerSec    = "volume.bytes.per.sec"
	VolumeBytesPerPacket = "volume.bytes.per.packet"
	Protocol             = "protocol"
	Processes            = "processes"
	Packets              = "packets"
)

//the function will generate from-time and to-time for different time ranges

// the function will send the query(visualization query) to the executor
func notifyExecutor(query utils.MotadataMap) (map[string][]interface{}, string) {

	result := map[string][]interface{}{}

	var errors string

	context := make(utils.MotadataMap)

	bytes, err := json.Marshal(query)

	if err != nil {

		panic(err)
	}

	context = utils.UnmarshalJson(bytes, context)

	queryEngineType := getQueryEngineType(context)

	category := context.GetStringValue(VisualizationCategory)

	context.GetMapValue(VisualizationDataSources)[utils.Type] = utils.ResolveQueryType(context.GetMapValue(VisualizationDataSources).GetStringValue(utils.Type))

	forecastChart := false

	if category == Forecast || category == Anomaly || category == Baseline {

		if category == Forecast && (context.GetStringValue(visualizationType) == Histogram || context.GetStringValue(visualizationType) == "Area" || context.GetStringValue(visualizationType) == "Line" || context.GetIntValue(visualizationResultType) == int(Normalized)) {

			forecastChart = true

		}
	}

	switch queryEngineType {

	case Metric:

		for i := range executors {

			if executorAllocations[i] == Metric {

				executorId = i

				break
			}
		}

	case Flow:

		for i := range executors {

			if executorAllocations[i] == Flow {

				executorId = i

				break
			}
		}

	case AIOps:

		for i := range executors {

			if executorAllocations[i] == AIOps {

				executorId = i

				break
			}
		}

	case DrillDown:

		for i := range executors {

			if executorAllocations[i] == DrillDown {

				executorId = i

				break
			}
		}

	default:

		for i := range executors {

			if executorAllocations[i] == Log {

				executorId = i

				break
			}
		}
	}

	executors[executorId].Requests <- context

OUTER:
	for {

		select {

		case response := <-utils.PublisherResponses:

			if valid, buffer := utils.CheckProgress(response); valid {

				if !forecastChart {

					result, errors, _, _ = utils.UnpackResponse(buffer, false)

				} else {

					result, errors, _, _ = utils.UnpackResponse(buffer, true)

				}

				break OUTER

			}

		}
	}

	<-utils.Responses

	return result, errors
}

func getQueryEngineType(context utils.MotadataMap) QueryEngineType {

	datastoreType := context.GetMapValue(VisualizationDataSources).GetStringValue(utils.Type)

	category := context.GetStringValue(VisualizationCategory)

	if category == Anomaly || category == Forecast || category == Baseline {

		return AIOps

	} else if context.Contains(utils.DrillDown) && strings.EqualFold(context.GetStringValue(utils.DrillDown), utils.Yes) {

		if datastoreType == "metric" || datastoreType == "availability" || datastoreType == "trap.flap" || datastoreType == "status.metric" || datastoreType == "status.flap.metric" || datastoreType == "netroute.metric" || datastoreType == "netroute.status.metric" {

			return Metric
		}

		return DrillDown

	} else if datastoreType == "metric" || datastoreType == "availability" || datastoreType == "trap.flap" || datastoreType == "status.metric" || datastoreType == "status.flap.metric" || datastoreType == "netroute.metric" || datastoreType == "netroute.status.metric" {

		return Metric

	} else if datastoreType == "flow" {

		return Flow

	}

	return Log
}

// the function will notify the executor when executor panics
func triggerPanic(preAggregation bool) (map[string][]interface{}, string, int, int) {

	if preAggregation {

		executors[executorId].preAggregationJobId = 0

	} else {

		executors[executorId].preAggregationJobId = utils.NotAvailable
	}

	executors[executorId].Requests <- nil

	for {

		select {

		case response := <-utils.PublisherResponses:

			if valid, buffer := utils.CheckProgress(response); valid {

				return utils.UnpackResponse(buffer, false)
			}

		case _ = <-utils.AggregationJobQueryAcks[0]:

			return nil, "error", 0, 0

		}
	}

}

var debugEnv = false

func TestMain(m *testing.M) {

	for _, arg := range os.Args {

		if strings.Contains(arg, "bench") {

			utils.EnvironmentType = utils.DatastoreBenchIntegrationEnvironment

			break
		}
	}

	runtime.GC()

	var err error

	configBytes, err = os.ReadFile((filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	if err != nil {

		panic(err)

	}

	memoryBytes := int64(memory.TotalMemory())

	debug.SetMemoryLimit((memoryBytes * 85) / 100)

	changedParameters := utils.MotadataMap{
		"datastore.store.sync.timer.seconds":           5,
		"datastore.aiops.query.executors":              1,
		"datastore.metric.query.executors":             1,
		"datastore.flow.query.executors":               1,
		"datastore.log.query.executors":                1,
		"datastore.drill.down.query.executors":         1,
		"datastore.query.workers":                      10,
		"datastore.memory.pool.shrink.timer.seconds":   10,
		"datastore.flush.timer.seconds":                5,
		"datastore.vertical.aggregation.timer.seconds": 10,
		"datastore.max.worker.events":                  1,
		"datastore.iocp.workers":                       24,
	}

	utils.SystemBootSequence = utils.Datastore

	if !debugEnv {

		utils.CleanUpStores()

		utils.Create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

		utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

		utils.Create(utils.TempDir)

		utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations)

		utils.Create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.HyphenSeparator + utils.Aggregations)

		utils.Create(utils.TempDir + utils.HyphenSeparator + utils.Aggregations)

	}

	err = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory + utils.PathSeparator)

	if utils.InitConfigs(utils.UpdateConfigs(configBytes, changedParameters)) {

		utils.PublisherNotifications = make(chan utils.MotadataMap, 1_00_000)

		datastore.Init()

		initIndexEngine()

		if !debugEnv {

			//copying stop words
			err = cp.Copy((filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))))+utils.PathSeparator+utils.StopWordFile, utils.CurrentDir+utils.PathSeparator+utils.StopWordFile) //comment this for debugging

			utils.InitDatabases([]string{writer.TestDatabase1, writer.TestDatabase2}, writer.Address, writer.Username, writer.Password) //comment this for debugging

			group := sync.WaitGroup{} //comment this for debugging

			group.Add(2) //comment this for debugging

			writer.PopulateIndexColumns()

			writer.PopulateDefaultColumns()

			if utils.EnvironmentType == utils.DatastoreBenchIntegrationEnvironment {

				enableHorizontalAggregation(logPlugin1)

				enableHorizontalAggregation(flowPlugin1)

			}

			sendAggregationContext()

			go writer.PopulateDatabases(&group, verticalWriters, broker) //comment this for debugging

			go writer.PopulateEventDatabase(&group, broker) //comment this for debugging

			group.Wait()

			completeProbe()

			if utils.EnvironmentType != utils.DatastoreBenchIntegrationEnvironment {

				writer.PopulateEventOverflowDataBase() //comment this for debugging

				completeProbe()

			}

			shutDownChannels()

			doCompaction() //comment this for debugging

			datastore.Close() //comment this for debugging

			//config get altered from populateEventDatabase , so restored the changed config again
			utils.InitConfigs(utils.UpdateConfigs(configBytes, changedParameters)) //comment this for debugging

		} else {

			loadGridVerticalLast()
		}

		utils.LogWorkerPoolLength = 10_000

		utils.FlowWorkerPoolLength = 10_000

		utils.SetLogLevel(0)

		utils.SetTaskLogging(utils.Yes)

		utils.Aggregation = true

		datastore.Init()

		if utils.EnvironmentType != utils.DatastoreBenchIntegrationEnvironment {

			utils.EnvironmentType = utils.DatastoreTestEnvironment
		}

		cache.InitCacheEngine()

		writer.PopulateDefaultColumns()

		workers = make([]*Worker, utils.QueryWorkers)

		executors = make([]*Executor, utils.QueryExecutors)

		responses = make(chan []byte, 1)

		utils.PublisherResponses = responses

		utils.Responses = make(chan string, utils.QueryExecutors)

		executorAllocations = make(map[int]QueryEngineType)

		workerAllocations = make(map[int]QueryEngineType)

		InitQueryEngine()

		initAIOpsEngine()

		statusCode := m.Run()

		if statusCode != 0 {

			utils.BackupFailingTestEnvironment(utils.CurrentDir, filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))), "Query")
		}

		for _, executor := range executors {

			executor.ShutdownNotifications <- true
		}

		for _, worker := range workers {

			worker.ShutdownNotifications <- true
		}

		for _, ioWorker := range ioWorkers {

			ioWorker.ShutdownNotifications <- true
		}

		runtime.GC()
	}

}

func loadGridVerticalLast() {

	jsonBytes, _ := os.ReadFile(filepath.Dir(utils.CurrentDir) + utils.PathSeparator + "grid-vertical-last.json")

	verticalLastExpectedValues := utils.MotadataMap{}

	_ = json.Unmarshal(jsonBytes, &verticalLastExpectedValues)

	for i, values := range verticalLastExpectedValues["c1"].([]interface{}) {

		for _, value := range values.([]interface{}) {

			writer.C1VerticalLastValues[i] = append(writer.C1VerticalLastValues[i], int8(value.(float64)))
		}

	}

	for i, values := range verticalLastExpectedValues["c2"].([]interface{}) {

		for _, value := range values.([]interface{}) {

			writer.C2VerticalLastValues[i] = append(writer.C2VerticalLastValues[i], int16(value.(float64)))
		}

	}

	for i, values := range verticalLastExpectedValues["c3"].([]interface{}) {

		for _, value := range values.([]interface{}) {

			writer.C3VerticalLastValues[i] = append(writer.C3VerticalLastValues[i], int32(value.(float64)))
		}

	}

	for i, values := range verticalLastExpectedValues["c4"].([]interface{}) {

		for _, value := range values.([]interface{}) {

			writer.C4VerticalLastValues[i] = append(writer.C4VerticalLastValues[i], int64(value.(float64)))
		}

	}

	for i, values := range verticalLastExpectedValues["c5"].([]interface{}) {

		for _, value := range values.([]interface{}) {

			writer.C5VerticalLastValues[i] = append(writer.C5VerticalLastValues[i], value.(float64))
		}

	}

	for i, values := range verticalLastExpectedValues["c6"].([]interface{}) {

		for _, value := range values.([]interface{}) {

			writer.C6VerticalLastValues[i] = append(writer.C6VerticalLastValues[i], value.(string))
		}

	}
}

func sendAggregationContext() {

	bytes, err := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.HorizontalAggregations)

	if err != nil {

		panic(err)
	}

	for _, dataWriter := range writers {

		dataWriter.HorizontalAggregationChangeNotifications <- bytes
	}

	time.Sleep(time.Second * 2)
}

func shutDownChannels() {

	broker.ShutdownNotifications <- true

	for _, writer := range writers {

		writer.ShutdownNotifications <- true
	}

	for _, reader := range readers {

		reader.ShutdownNotifications <- true
	}

	for _, verticalWriter := range verticalWriters {

		verticalWriter.ShutdownNotifications <- true
	}

	for _, staticMetricWriter := range staticMetricWriters {

		staticMetricWriter.ShutdownNotifications <- true
	}

	for _, horizontalWriter := range horizontalWriters {

		horizontalWriter.ShutdownNotifications <- true
	}

	for _, healthMetricWriter := range healthMetricWriters {

		healthMetricWriter.ShutdownNotifications <- true
	}

	for _, indexWriter := range indexWriters {

		indexWriter.ShutdownNotifications <- true
	}

	for _, dataAggregator := range dataAggregators {

		dataAggregator.ShutdownNotifications <- true
	}

	for _, eventAggregator := range eventAggregators {

		eventAggregator.ShutdownNotifications <- true
	}

	for _, metricAggregator := range metricAggregators {

		metricAggregator.ShutdownNotifications <- true
	}

	for index := range syncJobShutdownNotifications {

		syncJobShutdownNotifications[index] <- true
	}

	syncFinished.Wait()

}

func initIndexEngine() {

	utils.InitTestConfigs()

	utils.SetDataWriterSyncTimers()

	//disk io worker

	utils.DiskIOWorkers = 300

	ioWorkers = make([]*storage.DiskIOWorker, utils.DiskIOWorkers)

	for id := range ioWorkers {

		ioWorkers[id] = storage.NewIOWorker(id)

		ioWorkers[id].Start()
	}
	//index writers

	indexWriters = make([]*writer.IndexWriter, utils.IndexWriters)

	utils.IndexWriterRequests = make([]chan utils.IndexEvent, len(indexWriters))

	for i := 0; i < len(indexWriters); i++ { //comment this for debugging

		indexWriters[i] = writer.NewIndexWriter(i) //comment this for debugging

		utils.IndexWriterRequests[i] = indexWriters[i].Events //comment this for debugging

		indexWriters[i].Start() //comment this for debugging

	}

	//store sync jobs
	utils.StoreSyncJobAddNotifications = make(chan string, 1000000) //comment this for debugging

	utils.StoreSyncJobRemoveNotifications = make(chan string, 1000000) //comment this for debugging

	syncJobShutdownNotifications = make([]chan bool, utils.StoreSyncJobs)

	for index := range syncJobShutdownNotifications {

		syncJobShutdownNotifications[index] = make(chan bool)

	}

	for i := 0; i < utils.StoreSyncJobs; i++ { //comment this for debugging

		go startSyncJob(utils.StoreSyncJobAddNotifications, syncJobShutdownNotifications[i]) //comment this for debugging

	}

	metricAggregators = make([]*writer.MetricAggregator, utils.MetricAggregators)

	utils.MetricAggregationRequests = make([]chan utils.MetricAggregationRequest, utils.MetricAggregators)

	for id := 0; id < len(metricAggregators); id++ {

		metricAggregators[id] = writer.NewMetricAggregator(id)

		utils.MetricAggregationRequests[id] = metricAggregators[id].Requests

		metricAggregators[id].Start()
	}

	staticMetricWriters = make([]*writer.StaticMetricWriter, utils.StaticMetricWriters)

	for index := 0; index < len(staticMetricWriters); index++ {

		staticMetricWriters[index] = writer.NewStaticMetricWriter(index)

		staticMetricWriters[index].Start()

	}
	//minimum writer required is 25

	verticalWriters = make([]*writer.VerticalWriter, utils.VerticalWriters)

	for index := 0; index < len(verticalWriters); index++ {

		verticalWriters[index] = writer.NewVerticalWriter(index, staticMetricWriters)

		verticalWriters[index].Start()

	}

	horizontalWriters = make([]*writer.HorizontalWriter, utils.HorizontalWriters)

	for i := 0; i < len(horizontalWriters); i++ {

		horizontalWriters[i] = writer.NewHorizontalWriter(i)

		horizontalWriters[i].Start()

	}

	healthMetricWriters = make([]*writer.HorizontalWriter, utils.HealthMetricWriters)

	utils.HorizontalFormatHealthSyncNotifications = make(chan utils.WriterSyncEvent, 10000)

	for i := 0; i < len(healthMetricWriters); i++ {

		healthMetricWriters[i] = writer.NewHorizontalWriter(i)

		healthMetricWriters[i].Start()

	}

	//init broker

	responses = make(chan []byte, 10000)

	utils.PublisherResponses = responses

	utils.HorizontalFormatSyncNotifications = make(chan utils.WriterSyncEvent, 1000000)

	utils.VerticalFormatSyncNotifications = make(chan utils.WriterSyncEvent, 1000000)

	utils.EventAggregationSyncNotifications = make(chan utils.EventAggregationSyncEvent, 1_000_000)

	writers = make([]*broker2.DataWriter, utils.DataWriters)

	dataAggregators = make([]*broker2.DataAggregator, utils.DataAggregators)

	broker = broker2.NewBroker(writers, dataAggregators)

	broker.Start()

	for id := range dataAggregators {

		dataAggregators[id] = broker2.NewDataAggregator(id)

		dataAggregators[id].Start()
	}

	for id := 0; id < len(writers); id++ {

		writers[id] = broker2.NewDataWriter(id, dataAggregators)

		writers[id].Start()

	}

	eventAggregators = make([]*writer.EventAggregator, utils.EventAggregators)

	for id := range eventAggregators {

		eventAggregators[id] = writer.NewEventAggregator(id)

		eventAggregators[id].Start()
	}

	readers = make([]*writer.DataReader, 4)

	readers[0] = writer.NewDataReader(verticalWriters, nil, nil, nil)

	readers[1] = writer.NewDataReader(nil, horizontalWriters, nil, nil)

	readers[2] = writer.NewDataReader(nil, nil, nil, eventAggregators)

	readers[3] = writer.NewDataReader(nil, nil, healthMetricWriters, nil)

	for id := range readers {

		readers[id].Start()
	}

}

func InitQueryEngine() {

	position := 0

	for id := 0; id < utils.MetricQueryWorkers; id++ {

		workers[id] = NewWorker(id, Metric)

		workers[id].Start(executors)

		workerAllocations[id] = Metric

	}

	position += utils.MetricQueryWorkers

	for id := position; id < position+utils.FlowQueryWorkers; id++ {

		workers[id] = NewWorker(id, Flow)

		workers[id].Start(executors)

		workerAllocations[id] = Flow

	}

	position += utils.FlowQueryWorkers

	for id := position; id < position+utils.LogQueryWorkers; id++ {

		workers[id] = NewWorker(id, Log)

		workers[id].Start(executors)

		workerAllocations[id] = Log

	}

	position += utils.LogQueryWorkers

	for id := position; id < position+utils.DrillDownQueryWorkers; id++ {

		workers[id] = NewWorker(id, DrillDown)

		workers[id].Start(executors)

		workerAllocations[id] = DrillDown

	}

	position += utils.DrillDownQueryWorkers

	if utils.AIOpsEngineQueryExecutors > 0 {

		for id := position; id < position+utils.AIOpsEngineQueryWorkers; id++ {

			workers[id] = NewWorker(id, AIOps)

			workers[id].Start(executors)

			workerAllocations[id] = AIOps

		}

		position += utils.AIOpsEngineQueryWorkers
	}

	availableWorkers := make([]atomic.Bool, utils.QueryWorkers)

	position = 0

	for id := 0; id < position+utils.MetricQueryExecutors; id++ {

		executors[id] = NewExecutor(id, Metric)

		executors[id].Start(workers, availableWorkers)

		executorAllocations[id] = Metric
	}

	position += utils.MetricQueryExecutors

	for id := position; id < position+utils.FlowQueryExecutors; id++ {

		executors[id] = NewExecutor(id, Flow)

		executors[id].Start(workers, availableWorkers)

		executorAllocations[id] = Flow
	}

	position += utils.FlowQueryExecutors

	for id := position; id < position+utils.LogQueryExecutors; id++ {

		executors[id] = NewExecutor(id, Log)

		executors[id].Start(workers, availableWorkers)

		executorAllocations[id] = Log
	}

	position += utils.LogQueryExecutors

	for id := position; id < position+utils.DrillDownQueryExecutors; id++ {

		executors[id] = NewExecutor(id, DrillDown)

		executors[id].Start(workers, availableWorkers)

		executorAllocations[id] = DrillDown
	}

	position += utils.DrillDownQueryExecutors

	if utils.AIOpsEngineQueryExecutors > 0 {

		for id := position; id < position+utils.AIOpsEngineQueryExecutors; id++ {

			executors[id] = NewExecutor(id, AIOps)

			executors[id].Start(workers, availableWorkers)

			executorAllocations[id] = AIOps
		}

		position += utils.AIOpsEngineQueryExecutors
	}
}

func initDrillDownQueryEngine() {

	for _, executor := range executors {

		executor.ShutdownNotifications <- true
	}

	for _, worker := range workers {

		worker.ShutdownNotifications <- true
	}

	utils.MaxWorkerEvents = 1

	utils.MetricQueryExecutors = 1

	utils.FlowQueryExecutors = 1

	utils.LogQueryExecutors = 1

	utils.DrillDownQueryExecutors = 1

	utils.AIOpsEngineQueryExecutors = 1

	utils.MetricQueryWorkers = 2

	utils.FlowQueryWorkers = 2

	utils.LogQueryWorkers = 2

	utils.Workers = 2

	utils.DrillDownQueryWorkers = 2

	utils.AIOpsEngineQueryWorkers = 2

	utils.QueryWorkers = utils.MetricQueryWorkers + utils.FlowQueryWorkers + utils.LogQueryWorkers + utils.DrillDownQueryWorkers + utils.AIOpsEngineQueryWorkers

	utils.QueryExecutors = utils.MetricQueryExecutors + utils.FlowQueryExecutors + utils.LogQueryExecutors + utils.DrillDownQueryExecutors + utils.AIOpsEngineQueryExecutors

	workers = make([]*Worker, utils.QueryWorkers)

	executors = make([]*Executor, utils.QueryExecutors)

	workerAllocations = make(map[int]QueryEngineType)

	executorAllocations = make(map[int]QueryEngineType)

	InitQueryEngine()

}

func initAIOpsEventQueryEventReceivers() {

	go func() {

		for {

			bytes, err := subscriberSocket.RecvBytes(0)

			if err == nil {

				if !utils.GlobalShutdown {

					if bytes2.Equal(bytes[:4], HeartBeatBytes) {

						//process heart beat
					} else {

						executorId := codec.ReadINTValue(bytes[4:6])

						executors[executorId].AIOpsEngineResponses <- bytes[6:]
					}

				} else {

					return
				}
			}
		}

	}()

	go func() {

		for {

			_, _ = publisherSocket.SendBytes(<-utils.AIOpsEngineRequests, 0)

		}

	}()

}

func initAIOpsEngine() {

	publisherSocket, _ = zmq4.NewSocket(zmq4.PUSH)

	_ = publisherSocket.Bind("tcp://127.0.0.1:" + utils.GetAIOpsEnginePublisherPort())

	subscriberSocket, _ = zmq4.NewSocket(zmq4.PULL)

	_ = subscriberSocket.Bind("tcp://127.0.0.1:" + utils.GetAIOpsEngineSubscriberPort())

	time.Sleep(time.Second)

	processes, _ := process.Processes()

	for _, process := range processes {

		if processName, err := process.Name(); err == nil && (strings.Contains(processName, utils.AIOpsEngine)) {

			process, err := os.FindProcess(int(process.Pid))

			if err == nil {

				_ = process.Kill() // This works on Windows
			}
		}

	}

	go startAIOpsEngineProcess()

	initAIOpsEventQueryEventReceivers()

}

func startAIOpsEngineProcess() {

	for {

		//request and response pipe are just the prefix
		//id offset for pipes is assumed as the ml executors would be last
		command := exec.Command(aiopsDir+utils.PathSeparator+utils.AIOpsEngine,
			codec.INTToStringValue(0),
			codec.INTToStringValue(utils.AIOpsEngineQueryExecutors),
			utils.GetAIOpsEnginePublisherPort(),
			utils.GetAIOpsEngineSubscriberPort())

		_, err := command.Output()

		fmt.Println(fmt.Sprintf("AI-Engine process exited unexpectedly...%v", err))

	}
}

func doCompaction() {

	thresholdSeconds := utils.IdleStoreDetectionThresholdSeconds

	utils.IdleStoreDetectionThresholdSeconds = 1

	utils.StoreCleanUpJobs = 5

	files, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	if err != nil {

		panic(err)
	}

	group := sync.WaitGroup{}

	group.Add(len(files))

	for index := 0; index < utils.StoreCleanUpJobs; index++ {

		go func(jobId int) {

			encoder := codec.NewEncoder(utils.NewMemoryPool(5, 100_000, true, utils.DefaultBlobPools))

			keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

			valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

			for i := range valueBuffers {

				valueBuffers[i] = make([]byte, utils.MaxValueBufferBytes)

			}

			waitGroup := sync.WaitGroup{}

			files, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

			if err != nil {

				panic(err)
			}

			for _, file := range files {

				if utils.GetFastModN(utils.GetHash64([]byte(file.Name())), utils.StoreCleanUpJobs) == jobId {

					defer group.Done()

					store := datastore.GetStore(file.Name(), utils.None, false, true, encoder, tokenizer)

					if store == nil {

						continue
					}

					if strings.HasSuffix(store.GetName(), utils.HyphenSeparator+utils.Aggregations) || store.GetDatastoreType() == utils.Mapping {

						continue
					}

					_ = store.Sync(encoder)

					for _, partition := range store.GetPartitions() {

						partition.SetLastWriteTimestamp(time.Now().Unix() - 86400)
					}

					store.Cleanup(encoder, keyBuffers, valueBuffers, storage.DiskIOEventBatch{}, storage.DiskIOEvent{}, &waitGroup)

					if store.IsClosed() {

						datastore.RemoveStore(store.GetName())
					}

				}

			}

		}(index)

	}

	group.Wait()

	utils.IdleStoreDetectionThresholdSeconds = thresholdSeconds
}

func startSyncJob(notifications chan string, shutDownNotification chan bool) {

	syncFinished.Add(1)

	defer syncFinished.Done()

	encoder := codec.NewEncoder(utils.NewMemoryPool(5, 100_000, true, utils.DefaultBlobPools))

	stores := make(map[string]struct{})

	timer := time.NewTicker(time.Second * 5)

	for {

		if utils.GlobalShutdown {

			return
		}

		select {

		case store := <-notifications:

			tokens := strings.Split(store, utils.GroupSeparator)

			stores[tokens[0]] = struct{}{}

		case <-timer.C:

			for storeName := range stores {

				if utils.GlobalShutdown {

					return
				}

				store := datastore.GetStore(storeName, utils.None, false, true, encoder, tokenizer)

				if store == nil {

					continue
				}

				err := store.Sync(encoder)

				if err != nil && !strings.Contains(err.Error(), "partition is closed") {

					panic(err)
				}

			}

		case <-shutDownNotification:
			return

		}
	}

}

// In the case of floating point values, we cannot compare the values from clickhouse and motadataDB directly,
//so we are using tolerance for the same

func assertValuesHavingErrorTolerance(expectedValues []float64, values []float64, assertions *assert.Assertions) {

	for index := range expectedValues {

		assertValueHavingErrorTolerance(expectedValues[index], values[index], assertions)

	}
}

func modifyEntities(queryContext utils.MotadataMap) {

	dataSources := queryContext.GetMapValue(VisualizationDataSources)

	dataPoints := dataSources.GetMapListValue(DataPoints)

	entities := dataSources.GetMapValue(Entities)

	plugin := dataSources.GetListValue(utils.Plugins)[0]

	for index := 1; index <= 101; index++ {

		entities[codec.INTToStringValue(index)] = plugin
	}

	for _, datapoint := range dataPoints {

		datapoint[Entities] = entities

		datapoint[utils.Plugins] = []interface{}{codec.ToString(plugin)}
	}

	dataSources[Entities] = entities

	queryContext[VisualizationDataSources] = dataSources
}

func assertValueHavingErrorTolerance(expectedValue float64, value float64, assertions *assert.Assertions) {

	precision := float64(100000)

	if expectedValue > value {

		assertions.True(float64((value/precision)-(expectedValue/precision)) <= 0.3, fmt.Sprintf("clickhouse value :%v , motadataDbValue : %v , difference : %v", expectedValue, value, float32(value-expectedValue)))

	} else {

		assertions.True(float64((value/precision)-(expectedValue/precision)) <= 0.3, fmt.Sprintf("clickhouse value :%v , motadataDbValue : %v , difference : %v", expectedValue, value, float32(expectedValue-value)))

	}
}

func removeDummyFLOAT64ValueSlice(values []interface{}, ticks []interface{}) ([]float64, []int64) {

	var filteredValues []float64

	var filteredTicks []int64

	for index := range values {

		if values[index].(float64) != utils.DummyFLOAT64Value {

			filteredValues = append(filteredValues, values[index].(float64))

			if ticks != nil {

				filteredTicks = append(filteredTicks, ticks[index].(int64))
			}
		}
	}

	return filteredValues, filteredTicks

}

func removeDummyFLOAT64ValueTablev2(table map[string][]interface{}) ([]int64, map[string][]float64) {

	var ticks []int64

	qualifiedTicks := map[int64]struct{}{}

	groups := make(map[string][]float64)

	for key, values := range table {

		if key == "Timestamp" {

			continue
		}

		groups[key], ticks = removeDummyFLOAT64ValueSlice(values, table["Timestamp"])

		for _, tick := range ticks {

			qualifiedTicks[tick] = struct{}{}
		}
	}

	ticks = make([]int64, len(qualifiedTicks))

	i := 0

	for tick := range qualifiedTicks {

		ticks[i] = tick

		i++
	}

	utils.SortINT64Values(ticks)

	return ticks, groups
}

func removeDummyINT64ValueTablev2(table map[string][]interface{}) ([]int64, map[string][]int64) {

	var ticks []int64

	qualifiedTicks := map[int64]struct{}{}

	groups := make(map[string][]int64)

	for key, values := range table {

		if key == "Timestamp" {

			continue
		}

		groups[key], ticks, _, _ = removeDummyINT64ValueSlice(values, table["Timestamp"])

		for _, tick := range ticks {

			qualifiedTicks[tick] = struct{}{}
		}
	}

	ticks = make([]int64, len(qualifiedTicks))

	i := 0

	for tick := range qualifiedTicks {

		ticks[i] = tick

		i++
	}

	utils.SortINT64Values(ticks)

	return ticks, groups
}

func removeDummyINT64ValueSlice(values []interface{}, ticks []interface{}) ([]int64, []int64, int, int) {

	var filteredValues []int64

	var filteredTicks []int64

	startIndex := 0

	endIndex := len(values)

	updated := false

	for index := range values {

		if values[index].(int64) != utils.DummyINT64Value {

			if !updated {

				startIndex = index

				updated = true
			}

			endIndex = index

			filteredValues = append(filteredValues, values[index].(int64))

			if ticks != nil {

				filteredTicks = append(filteredTicks, ticks[index].(int64))
			}
		}
	}

	return filteredValues, filteredTicks, startIndex, endIndex

}

func chooseAggregationInterval(fromTick, toTick int64, queryEngineType QueryEngineType) int64 {

	difference := int((toTick - fromTick) / 1000) //converting millis to seconds

	if queryEngineType == Metric || queryEngineType == AIOps {

		return int64(chooseTimeInterval(utils.AggregationIntervals, difference))
	}

	return int64(chooseTimeInterval(utils.EventAggregationIntervals, difference))

}

func alterAggregationTimeline(queryContext utils.MotadataMap) {

	timeline := queryContext.GetMapValue(VisualizationTimeline)

	queryEngineType := getQueryEngineType(queryContext)

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), queryEngineType)

	if interval == 5 {

		queryContext[VisualizationGranularity] = _5MinGranularity

	} else if interval == 15 {

		queryContext[VisualizationGranularity] = _15MinGranularity

	} else if interval == 30 {

		queryContext[VisualizationGranularity] = _30MinGranularity

	} else if interval == 60 {

		queryContext[VisualizationGranularity] = _1HourGranularity

	} else if interval == 360 {

		queryContext[VisualizationGranularity] = _6HourGranularity

	}

}

func assertAggregationValuesHavingErrorTolerance(expectedValues []float64, values []float64, assertions *assert.Assertions) {

	for index, value := range expectedValues {

		if value > values[index] {

			assertions.True(float32(value-values[index]) <= 0.4, fmt.Sprintf("clickhouse value :%v , motadataDbValue : %v", value, values[index]))

		} else {

			assertions.True(float32(values[index]-value) <= 0.4, fmt.Sprintf("clickhouse value :%v , motadataDbValue : %v", value, values[index]))

		}

	}
}

func filterDummyINT64Values(values []int64) []int64 {

	var filteredValues []int64

	for index := range values {

		if values[index] != utils.DummyINT64Value {

			filteredValues = append(filteredValues, values[index])
		}
	}

	return filteredValues
}

func completeProbe() {

	dirs, _ := os.ReadDir(utils.EventDir)

	var complete bool

	for {

		time.Sleep(time.Second * 2)

		for _, dir := range dirs {

			if dir.IsDir() {

				entries, _ := os.ReadDir(utils.EventDir + utils.PathSeparator + dir.Name())

				for _, entry := range entries {

					if entry.IsDir() {

						files, _ := os.ReadDir(utils.EventDir + utils.PathSeparator + dir.Name() + utils.PathSeparator + entry.Name())

						if len(files) > 0 {

							complete = false

							break

						} else {

							complete = true
						}

					} else {

						files, _ := os.ReadDir(utils.EventDir + utils.PathSeparator + dir.Name())

						if len(files) > 0 {

							complete = false

							break

						} else {

							complete = true
						}

					}

				}

			}
		}

		if complete {

			for aggregation := range datastore.GetVerticalAggregations() {

				entries, _ := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.HyphenSeparator +
					utils.Aggregations + utils.PathSeparator + aggregation)

				if len(entries) > 0 {

					complete = false

					break
				}
			}

			for _, eventAggregator := range eventAggregators {

				if atomic.LoadInt32(&eventAggregator.PendingRequests) > 0 {

					complete = false

					break
				}
			}

			for _, verticalWriter := range verticalWriters {

				if atomic.LoadInt32(&verticalWriter.Pending) > 0 {

					complete = false

					break
				}
			}

			for _, horizontalWriter := range horizontalWriters {

				if atomic.LoadInt32(&horizontalWriter.Pending) > 0 {

					complete = false

					break
				}
			}

			if complete {

				break
			}
		}
	}

	time.Sleep(time.Second * 30)

}

func assertINT64AggregationValues(motadataDBAggregationView map[string][]interface{}, motadataDBTable map[string][]interface{}, assertions *assert.Assertions) {

	_, motadataDBAggregatedTableGroups := removeDummyINT64ValueTablev2(motadataDBAggregationView)

	_, motadataDBRawTableGroups := removeDummyINT64ValueTablev2(motadataDBTable)

	assertions.NotNil(motadataDBRawTableGroups)

	assertions.NotNil(motadataDBAggregatedTableGroups)

	assertions.Equal(len(motadataDBRawTableGroups), len(motadataDBAggregatedTableGroups))

	for key := range motadataDBAggregatedTableGroups {

		assertions.Equal(motadataDBAggregatedTableGroups[key], motadataDBRawTableGroups[key])
	}
}

func assertFLOAT64AggregationValues(motadataDBAggregationView map[string][]interface{}, motadataDBTable map[string][]interface{}, assertions *assert.Assertions) {

	_, motadataDBAggregatedTableGroups := removeDummyFLOAT64ValueTablev2(motadataDBAggregationView)

	_, motadataDBRawTableGroups := removeDummyFLOAT64ValueTablev2(motadataDBTable)

	assertions.NotNil(motadataDBRawTableGroups)

	assertions.NotNil(motadataDBAggregatedTableGroups)

	assertions.Equal(len(motadataDBRawTableGroups), len(motadataDBAggregatedTableGroups))

	for key := range motadataDBAggregatedTableGroups {

		assertAggregationValuesHavingErrorTolerance(motadataDBAggregatedTableGroups[key], motadataDBRawTableGroups[key], assertions)
	}
}

func assertINT64MapListValues(clickHouseValues map[string][]interface{}, motadataDBValues map[string][]interface{}, assertions *assert.Assertions) {

	for key := range clickHouseValues {

		//in case of topn there are scenerio in which two event source has same count
		if motadataDBValues[key] != nil {

			assertions.EqualValues(clickHouseValues[key], motadataDBValues[key])

		}

	}
}

func assertMapListValues(clickHouseValues map[string][]interface{}, motadataDBValues map[string][]interface{}, assertions *assert.Assertions) {

	for key := range clickHouseValues {

		var floatValue bool

		for _, value := range motadataDBValues[key] {

			if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

				floatValue = true

				break

			}
		}

		if floatValue {

			assertValuesHavingErrorTolerance(utils.InterfaceToFLOAT64Values(clickHouseValues[key]), utils.InterfaceToFLOAT64Values(motadataDBValues[key]), assertions)

		} else {

			//in case of topn there are scenerio in which two event source has same count
			if motadataDBValues[key] != nil {

				assertions.EqualValues(clickHouseValues[key], motadataDBValues[key])

			}

		}

	}
}

/*------------------------------------------Histogram Scenarios Start----------------------------------------------------------------*/

/*-------------------------------------------TOP N Scenarios Start-----------------------------------------------------------------*/

func getGranularity(context utils.MotadataMap) int {

	category := context.GetStringValue(VisualizationCategory)

	dataSource := context.GetMapValue(VisualizationDataSources)

	duration := context.GetMapValue(VisualizationTimeline).GetIntValue(datastore.Duration)

	limit := utils.MaxSparklineTicks

	if category == Histogram {

		limit = utils.MaxLineChartTicks

		if strings.HasSuffix(context.GetStringValue(visualizationType), "Bar") {
			limit = utils.MaxBarChartTicks
		}

		if !context.Contains(VisualizationResultBy) {

			limit = limit / len(dataSource.GetMapListValue(DataPoints))
		}
	}

	granularity := 300

	if context.Contains(VisualizationGranularity) {

		visualizationGranularity := context.GetStringValue(VisualizationGranularity)

		if strings.HasSuffix(visualizationGranularity, "s") {

			granularity = codec.StringToINT(strings.Split(visualizationGranularity, "s")[0])

		} else if strings.HasSuffix(visualizationGranularity, "m") {

			granularity = codec.StringToINT(strings.Split(visualizationGranularity, "m")[0]) * 60

		} else if strings.HasSuffix(visualizationGranularity, "h") {

			granularity = codec.StringToINT(strings.Split(visualizationGranularity, "h")[0]) * 3600

		} else if strings.HasSuffix(visualizationGranularity, "d") {

			granularity = codec.StringToINT(strings.Split(visualizationGranularity, "d")[0]) * 86400

		} else if strings.HasSuffix(visualizationGranularity, "M") {

			granularity = codec.StringToINT(strings.Split(visualizationGranularity, "M")[0]) * 86400 * 30
		}

		// in custom timeline, motadata is not giving duration, so everytime granularity is 300,
		//and for 5-years query we can't override 300-second granularity as time-ticks are always 0 as duration is not coming
		timeTicks := duration / granularity

		var index int

		valid := false

		for !valid {

			index++

			if timeTicks <= (limit * index) {

				valid = true

				granularity = granularity * index
			}
		}

	} else {

		// in seconds
		granularity = duration / limit

		if granularity <= 300 {

			granularity = 300

		} else if granularity <= 1800 {

			granularity = 1800

		} else if granularity <= 3600 {

			granularity = 3600

		} else if granularity <= 21600 {

			granularity = 21600
		}

	}

	return granularity
}

func publishTimeTicks(testcaseName string, clickHouseTime int64, motadataDbTime int64) {

	testcaseLogger.Info(testcaseName + " => " + "motadataDBTime : " + codec.ToString(motadataDbTime) + " ms " + " | " + "clickHouseTime : " + codec.ToString(clickHouseTime) + " ms ")
}

func getCurrentFuncName() string {

	pc, _, _, _ := runtime.Caller(1)

	return strings.Split(fmt.Sprintf("%s", runtime.FuncForPC(pc).Name()), ".")[1]
}

func assertINT64ListValues(clickHouseValues []int64, motadataDBValues []int64, assertions *assert.Assertions) {

	for index := range clickHouseValues {

		assertions.EqualValues(clickHouseValues[index], motadataDBValues[index])

	}
}

func prepareClickhouseTopKQueryContext(queryContext utils.MotadataMap, connection clickhouse.Conn, ctx context.Context, tableName string, table map[string]map[string]int, dataFilter string) string {

	var aggregatedColumns, aggregators []string

	var filterQuery, groups1, groups2, groups3, clickHouseQuery1, clickHouseQuery2, clickHouseQuery3 string

	timeline := queryContext.GetMapValue(VisualizationTimeline)

	category := queryContext.GetStringValue(VisualizationCategory)

	queryLimit := utils.MaxFlowTopNSelectionGroups

	queryOrder := "desc"

	if strings.EqualFold(category, TopN) || strings.EqualFold(category, Sankey) {

		queryLimit = queryContext.GetMapValue(VisualizationProperties).GetMapValue(strings.ToLower(queryContext.GetStringValue(visualizationType))).GetMapValue(Sorting).GetIntValue(limit)

		queryOrder = queryContext.GetMapValue(VisualizationProperties).GetMapValue(strings.ToLower(queryContext.GetStringValue(visualizationType))).GetMapValue(Sorting).GetStringValue(order)

	} else if strings.EqualFold(category, Histogram) {

		queryLimit = queryContext.GetMapValue(VisualizationProperties).GetMapValue(strings.ToLower(Histogram)).GetMapValue(Sorting).GetIntValue(limit)

		queryOrder = queryContext.GetMapValue(VisualizationProperties).GetMapValue(strings.ToLower(Histogram)).GetMapValue(Sorting).GetStringValue(order)

	}

	datasource := queryContext.GetMapValue(VisualizationDataSources)

	groupingColumns := datasource.GetSliceValue(VisualizationResultBy)

	dataPoints := datasource.GetMapListValue(DataPoints)

	for _, dataPoint := range dataPoints {

		aggregatedColumns = append(aggregatedColumns, dataPoint.GetStringValue(DataPoint))

		aggregators = append(aggregators, strings.ToLower(dataPoint.GetStringValue(Aggregator)))

	}

	uniqueGrp1 := make(map[string]struct{})

	uniqueGrp2 := make(map[string]struct{})

	uniqueGrp3 := make(map[string]struct{})

	if len(groupingColumns) == 4 {

		groupingColumn1 := strings.ReplaceAll(codec.ToString(groupingColumns[0]), ".", "_")

		groupingColumn2 := strings.ReplaceAll(codec.ToString(groupingColumns[1]), ".", "_")

		groupingColumn3 := strings.ReplaceAll(codec.ToString(groupingColumns[2]), ".", "_")

		clickHouseQuery1 = "select " + "toString( " + groupingColumn1 + " )  from " + tableName + " where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + "" + " group by " + groupingColumn1 + " order by " + aggregators[0] + "(" + strings.ReplaceAll(aggregatedColumns[0], ".", "_") + ")" + queryOrder + "  limit " + codec.ToString(queryLimit)

		clickHouseQuery2 = "select " + "toString( " + groupingColumn1 + " ) , " + "toString( " + groupingColumn2 + " ) from " + tableName + "  where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and " + groupingColumn1 + " in [  placeHolder1 ]" + " group by " + groupingColumn1 + " , " + groupingColumn2 + " order by " + aggregators[0] + "(" + strings.ReplaceAll(aggregatedColumns[0], ".", "_") + ")" + queryOrder + " limit " + codec.ToString(queryLimit)

		clickHouseQuery3 = "select " + " toString( " + groupingColumn1 + " ) , " + " toString( " + groupingColumn2 + " ) " + " , " + " toString( " + groupingColumn3 + " ) from " + tableName + " where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and " + groupingColumn1 + " in [  placeHolder1 ] and " + groupingColumn2 + " in [  placeHolder2 ]" + " group by " + groupingColumn1 + " , " + groupingColumn2 + " , " + groupingColumn3 + " order by " + aggregators[0] + "(" + strings.ReplaceAll(aggregatedColumns[0], ".", "_") + ") " + queryOrder + " limit " + codec.ToString(queryLimit)

		// if data filter is not empty we need to add the same in our query
		if dataFilter != utils.Empty {

			clickHouseQuery1 = "select " + "toString( " + groupingColumn1 + " )  from " + tableName + " where ( timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + ") and ( " + dataFilter + " ) " + " group by " + groupingColumn1 + " order by " + aggregators[0] + "(" + strings.ReplaceAll(aggregatedColumns[0], ".", "_") + ")" + queryOrder + "  limit " + codec.ToString(queryLimit)

			clickHouseQuery2 = "select " + "toString( " + groupingColumn1 + " ) , " + "toString( " + groupingColumn2 + " ) from " + tableName + "  where (timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " ) and ( " + groupingColumn1 + " in [  placeHolder1 ] ) and ( " + dataFilter + " )  group by " + groupingColumn1 + " , " + groupingColumn2 + " order by " + aggregators[0] + "(" + strings.ReplaceAll(aggregatedColumns[0], ".", "_") + ")" + queryOrder + " limit " + codec.ToString(queryLimit)

			clickHouseQuery3 = "select " + " toString( " + groupingColumn1 + " ) , " + " toString( " + groupingColumn2 + " ) " + " , " + " toString( " + groupingColumn3 + " ) from " + tableName + " where ( timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " )  and ( " + groupingColumn1 + " in [  placeHolder1 ] and " + groupingColumn2 + " in [  placeHolder2 ] ) and (" + dataFilter + " ) group by " + groupingColumn1 + " , " + groupingColumn2 + " , " + groupingColumn3 + " order by " + aggregators[0] + "(" + strings.ReplaceAll(aggregatedColumns[0], ".", "_") + ") " + queryOrder + " limit " + codec.ToString(queryLimit)

		}

		rows, err := connection.Query(ctx, clickHouseQuery1)

		if err != nil {

			panic(err)
		}

		for rows.Next() {

			var group string

			err = rows.Scan(&group)

			if _, ok := uniqueGrp1[group]; !ok {

				groups1 += "'" + group + "' ,"

				uniqueGrp1[group] = struct{}{}

			}

		}

		groups1 = groups1[:len(groups1)-1]

		if table[codec.ToString(groupingColumns[0])][writer.MetricDatatype] != datastore.StringColumn {

			groups1 = strings.ReplaceAll(groups1, "'", "")
		}

		clickHouseQuery2 = strings.ReplaceAll(clickHouseQuery2, "placeHolder1", groups1)

		// executing query 2nd time

		rows, err = connection.Query(ctx, clickHouseQuery2)

		if err != nil {

			panic(err)
		}

		groups1 = utils.Empty

		groups2 = utils.Empty

		clear(uniqueGrp1)

		clear(uniqueGrp2)

		clear(uniqueGrp3)

		for rows.Next() {

			var group1 string

			var group2 string

			err = rows.Scan(&group1, &group2)

			if _, ok := uniqueGrp1[group1]; !ok {

				groups1 += "'" + group1 + "' ,"

				uniqueGrp1[group1] = struct{}{}

			}

			if _, ok := uniqueGrp2[group2]; !ok {

				groups2 += "'" + group2 + "' ,"

				uniqueGrp1[group2] = struct{}{}

			}

		}

		groups1 = groups1[:len(groups1)-1]

		groups2 = groups2[:len(groups2)-1]

		if table[codec.ToString(groupingColumns[0])][writer.MetricDatatype] != datastore.StringColumn {

			groups1 = strings.ReplaceAll(groups1, "'", "")
		}

		if table[codec.ToString(groupingColumns[1])][writer.MetricDatatype] != datastore.StringColumn {

			groups2 = strings.ReplaceAll(groups2, "'", "")
		}

		clickHouseQuery3 = strings.ReplaceAll(clickHouseQuery3, "placeHolder1", groups1)

		clickHouseQuery3 = strings.ReplaceAll(clickHouseQuery3, "placeHolder2", groups2)

		//executing query 3rd time

		groups1 = utils.Empty

		groups2 = utils.Empty

		groups3 = utils.Empty

		clear(uniqueGrp1)

		clear(uniqueGrp2)

		clear(uniqueGrp3)

		rows, err = connection.Query(ctx, clickHouseQuery3)

		if err != nil {

			panic(err)
		}

		for rows.Next() {

			var group1 string

			var group2 string

			var group3 string

			err = rows.Scan(&group1, &group2, &group3)

			if _, ok := uniqueGrp1[group1]; !ok {

				groups1 += "'" + group1 + "' ,"

				uniqueGrp1[group1] = struct{}{}

			}

			if _, ok := uniqueGrp2[group2]; !ok {

				groups2 += "'" + group2 + "' ,"

				uniqueGrp2[group2] = struct{}{}

			}

			if _, ok := uniqueGrp3[group3]; !ok {

				groups3 += "'" + group3 + "' ,"

				uniqueGrp3[group3] = struct{}{}

			}

		}

		groups1 = groups1[:len(groups1)-1]

		groups2 = groups2[:len(groups2)-1]

		groups3 = groups3[:len(groups3)-1]

		if table[codec.ToString(groupingColumns[0])][writer.MetricDatatype] != datastore.StringColumn {

			groups1 = strings.ReplaceAll(groups1, "'", "")
		}

		if table[codec.ToString(groupingColumns[1])][writer.MetricDatatype] != datastore.StringColumn {

			groups2 = strings.ReplaceAll(groups2, "'", "")
		}

		if table[codec.ToString(groupingColumns[2])][writer.MetricDatatype] != datastore.StringColumn {

			groups3 = strings.ReplaceAll(groups3, "'", "")
		}

		filterQuery = " ( " + groupingColumn1 + " in [ " + groups1 + "] and " + groupingColumn2 + " in [ " + groups2 + "] and " + groupingColumn3 + " in [ " + groups3 + "] " + " ) "

		if dataFilter != utils.Empty {

			filterQuery = " ( " + groupingColumn1 + " in [ " + groups1 + "] and " + groupingColumn2 + " in [ " + groups2 + "] and " + groupingColumn3 + " in [ " + groups3 + "] " + " ) and ( " + dataFilter + " ) "

		}

	}

	if len(groupingColumns) == 3 {

		groupingColumn1 := strings.ReplaceAll(codec.ToString(groupingColumns[0]), ".", "_")

		groupingColumn2 := strings.ReplaceAll(codec.ToString(groupingColumns[1]), ".", "_")

		clickHouseQuery1 = "select " + "toString( " + groupingColumn1 + " )  from " + tableName + " where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + "" + " group by " + groupingColumn1 + " order by " + aggregators[0] + "(" + strings.ReplaceAll(aggregatedColumns[0], ".", "_") + ") " + queryOrder + " limit " + codec.ToString(queryLimit)

		clickHouseQuery2 = "select " + "toString( " + groupingColumn1 + " ) , " + "toString( " + groupingColumn2 + " ) from " + tableName + "  where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and " + groupingColumn1 + " in [  placeHolder1 ]" + " group by " + groupingColumn1 + " , " + groupingColumn2 + " order by " + aggregators[0] + "(" + strings.ReplaceAll(aggregatedColumns[0], ".", "_") + ") " + queryOrder + "  limit " + codec.ToString(queryLimit)

		if dataFilter != utils.Empty {

			clickHouseQuery1 = "select " + "toString( " + groupingColumn1 + " )  from " + tableName + " where ( timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " ) and ( " + dataFilter + " ) group by " + groupingColumn1 + " order by " + aggregators[0] + "(" + strings.ReplaceAll(aggregatedColumns[0], ".", "_") + ") " + queryOrder + " limit " + codec.ToString(queryLimit)

			clickHouseQuery2 = "select " + "toString( " + groupingColumn1 + " ) , " + "toString( " + groupingColumn2 + " ) from " + tableName + "  where ( timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " )  and ( " + groupingColumn1 + " in [  placeHolder1 ] ) and ( " + dataFilter + " ) group by " + groupingColumn1 + " , " + groupingColumn2 + " " + "  order by " + aggregators[0] + "(" + strings.ReplaceAll(aggregatedColumns[0], ".", "_") + ") " + queryOrder + "  limit " + codec.ToString(queryLimit)

		}

		rows, err := connection.Query(ctx, clickHouseQuery1)

		if err != nil {

			panic(err)
		}

		for rows.Next() {

			var group1 string

			err = rows.Scan(&group1)

			if _, ok := uniqueGrp1[group1]; !ok {

				groups1 += "'" + group1 + "' ,"

				uniqueGrp1[group1] = struct{}{}

			}

		}

		groups1 = groups1[:len(groups1)-1]

		if table[codec.ToString(groupingColumns[0])][writer.MetricDatatype] != datastore.StringColumn {

			groups1 = strings.ReplaceAll(groups1, "'", "")
		}

		clickHouseQuery2 = strings.ReplaceAll(clickHouseQuery2, "placeHolder1", groups1)

		// executing query 2nd time

		rows, err = connection.Query(ctx, clickHouseQuery2)

		if err != nil {

			panic(err)
		}

		groups1 = utils.Empty

		groups2 = utils.Empty

		clear(uniqueGrp1)

		clear(uniqueGrp2)

		for rows.Next() {

			var group1 string

			var group2 string

			err = rows.Scan(&group1, &group2)

			if _, ok := uniqueGrp1[group1]; !ok {

				groups1 += "'" + group1 + "' ,"

				uniqueGrp1[group1] = struct{}{}

			}

			if _, ok := uniqueGrp2[group2]; !ok {

				groups2 += "'" + group2 + "' ,"

				uniqueGrp2[group2] = struct{}{}

			}

		}

		groups1 = groups1[:len(groups1)-1]

		groups2 = groups2[:len(groups2)-1]

		if table[codec.ToString(groupingColumns[0])][writer.MetricDatatype] != datastore.StringColumn {

			groups1 = strings.ReplaceAll(groups1, "'", "")
		}

		if table[codec.ToString(groupingColumns[1])][writer.MetricDatatype] != datastore.StringColumn {

			groups2 = strings.ReplaceAll(groups2, "'", "")
		}

		filterQuery = groupingColumn1 + " in [ " + groups1 + "] and " + groupingColumn2 + " in [ " + groups2 + "]  "

		if dataFilter != utils.Empty {

			filterQuery = "( " + groupingColumn1 + " in [ " + groups1 + "] and " + groupingColumn2 + " in [ " + groups2 + "] )" + "and ( " + dataFilter + " ) "

		}

	}

	if len(groupingColumns) == 2 {

		groupingColumn1 := strings.ReplaceAll(codec.ToString(groupingColumns[0]), ".", "_")

		clickHouseQuery1 = "select " + "toString( " + groupingColumn1 + " )  from " + tableName + " where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + "" + " group by " + groupingColumn1 + " order by " + aggregators[0] + "(" + strings.ReplaceAll(aggregatedColumns[0], ".", "_") + ") " + queryOrder + "  limit " + codec.ToString(queryLimit)

		if dataFilter != utils.Empty {

			clickHouseQuery1 = "select " + "toString( " + groupingColumn1 + " )  from " + tableName + " where ( timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " ) and ( " + dataFilter + " ) group by " + groupingColumn1 + " order by " + aggregators[0] + "(" + strings.ReplaceAll(aggregatedColumns[0], ".", "_") + ") " + queryOrder + "  limit " + codec.ToString(queryLimit)

		}

		rows, err := connection.Query(ctx, clickHouseQuery1)

		if err != nil {

			panic(err)
		}

		for rows.Next() {

			var group1 string

			err = rows.Scan(&group1)

			if _, ok := uniqueGrp1[group1]; !ok {

				groups1 += "'" + group1 + "' ,"

				uniqueGrp1[group1] = struct{}{}

			}

		}

		groups1 = groups1[:len(groups1)-1]

		if table[codec.ToString(groupingColumns[0])][writer.MetricDatatype] != datastore.StringColumn {

			groups1 = strings.ReplaceAll(groups1, "'", "")
		}

		filterQuery = groupingColumn1 + " in [ " + groups1 + "]  "

		if dataFilter != utils.Empty {

			filterQuery = groupingColumn1 + " in [ " + groups1 + "]  and ( " + dataFilter + " ) "

		}

	}

	return filterQuery
}

func enableHorizontalAggregation(plugin string) {

	switch plugin {

	case logPlugin1:

		datastore.AddHorizontalAggregation(logPlugin1, logPlugin1+utils.AggregationSeparator+"0", map[string]interface{}{

			EventSource:     byte(datastore.StringColumn),
			BytesSent:       byte(datastore.IntegerColumn),
			BytesReceived:   byte(datastore.IntegerColumn),
			LogId:           byte(datastore.IntegerColumn),
			writer.LogLevel: byte(datastore.IntegerColumn),
			utils.Type:      float64(utils.Log),
			Processes:       byte(datastore.IntegerColumn),
			utils.IndexableColumns: map[string]interface{}{
				EventSource:         struct{}{},
				utils.EventCategory: struct{}{},
				SourcePort:          struct{}{},
				SystemOS:            struct{}{},
			},
		})

	case flowPlugin1:

		datastore.AddHorizontalAggregation(flowPlugin1, flowPlugin1+utils.AggregationSeparator+"0", map[string]interface{}{

			EventSource:          byte(datastore.StringColumn),
			BytesSent:            byte(datastore.IntegerColumn),
			VolumeBytesPerSec:    byte(datastore.IntegerColumn),
			VolumeBytesPerPacket: byte(datastore.IntegerColumn),
			Packets:              byte(datastore.IntegerColumn),
			utils.Type:           float64(utils.Log),
			utils.IndexableColumns: map[string]interface{}{
				EventSource:     struct{}{},
				SourcePort:      struct{}{},
				DestinationPort: struct{}{},
				SourceIP:        struct{}{},
				DestinationIP:   struct{}{},
				Protocol:        struct{}{},
			},
		})

	}
}

// parser error messages

// grid without group by
func TestGridWithoutGroupByLast1MonthError(t *testing.T) {

	defer cache.Clear()

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "grid-withoutgroupby.json")

	timeline := utils.GetTimeline(Last1Month)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Nil(motadataDBTable)

	assertions.NotNil(errs)

	assertions.EqualValues(fmt.Sprintf(utils.ErrorGroupingRequired, "Grid")+utils.NewLineSeparator, errs)
}

// query without plugins
func TestGridEmptyPluginByLast1MonthError(t *testing.T) {

	defer cache.Clear()

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "grid-emptyplugin-1.json")

	timeline := utils.GetTimeline(Last1Month)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Nil(motadataDBTable)

	assertions.NotNil(errs)

	assertions.EqualValues(utils.ErrorPluginRequired+utils.NewLineSeparator, errs)
}

//panic executor

func TestZ_ExecutorPanicType1(t *testing.T) {

	defer cache.Clear()

	assertions := assert.New(t)

	utils.AggregationJobQueryAcks = make([]chan int, 1)

	utils.AggregationJobQueryAcks[0] = make(chan int)

	motadataDBTable, errs, _, _ := triggerPanic(true)

	assertions.Nil(motadataDBTable)

	assertions.NotNil(errs)
}

func TestZ_ExecutorPanicType2(t *testing.T) {

	defer cache.Clear()

	assertions := assert.New(t)

	motadataDBTable, errs, _, _ := triggerPanic(false)

	assertions.Nil(motadataDBTable)

	assertions.NotNil(errs)
}

func TestExecutorGetErrors(t *testing.T) {

	defer cache.Clear()

	assertions := assert.New(t)

	assertions.Equal(len(executors[0].GetErrors()[:executors[0].errorElementSize]), 0)
}

//
//func TestValidVerticalFilter(t *testing.T) {
//
//	defer cache.Clear()
//
//	parser := Parser{}
//
//	assertions := assert.New(t)
//
//	condition := utils.MotadataMap{}
//
//	condition[Operator] = equal
//
//	source := "system.os"
//
//	condition[utils.Value] = "system.os"
//
//	assertions.True(parser.validVerticalFilter(condition, source, true))
//
//	condition[utils.Value] = " "
//
//	assertions.True(parser.validVerticalFilter(condition, source, false))
//
//	condition[Operator] = startWith
//
//	condition[utils.Value] = "system"
//
//	assertions.True(parser.validVerticalFilter(condition, source, true))
//
//	condition[utils.Value] = "os"
//
//	assertions.True(parser.validVerticalFilter(condition, source, false))
//
//	condition[Operator] = endWith
//
//	condition[utils.Value] = "os"
//
//	assertions.True(parser.validVerticalFilter(condition, source, true))
//
//	condition[utils.Value] = "system"
//
//	assertions.True(parser.validVerticalFilter(condition, source, false))
//
//	condition[Operator] = contain
//
//	condition[utils.Value] = "system"
//
//	assertions.True(parser.validVerticalFilter(condition, source, true))
//
//	condition[utils.Value] = "wrong"
//
//	assertions.True(parser.validVerticalFilter(condition, source, false))
//
//	condition[utils.Value] = 1
//
//	assertions.False(parser.validVerticalFilter(condition, "2", true))
//
//	condition[Operator] = in
//
//	condition[utils.Value] = []interface{}{"system.os"}
//
//	assertions.True(parser.validVerticalFilter(condition, source, true))
//
//	condition[utils.Value] = []interface{}{"system", "os"}
//
//	assertions.True(parser.validVerticalFilter(condition, source, false))
//
//	condition[utils.Value] = []interface{}{1, 2}
//
//	assertions.True(parser.validVerticalFilter(condition, "1", true))
//
//	assertions.True(parser.validVerticalFilter(condition, "3", false))
//
//	condition[Operator] = "wrong operator"
//
//	assertions.False(parser.validVerticalFilter(condition, "", true))
//
//}

func TestErrorToLarge(t *testing.T) {

	defer cache.Clear()

	cache.Clear()

	workersBuffers := make([][][]byte, len(workers))

	for index, worker := range workers {

		workersBuffers[index] = worker.valueBuffers

		worker.valueBuffers = make([][]byte, len(worker.valueBuffers))

		for buffer := range worker.valueBuffers {

			worker.valueBuffers[buffer] = make([]byte, 10)
		}

	}

	defer func() {

		for index, worker := range workers {

			worker.valueBuffers = workersBuffers[index]
		}

	}()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	timeline := utils.GetTimeline(Last24Hours)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-systemcpupercent-error-to-large.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Contains(errs, utils.ErrorTooLarge)

	assertions.NotNil(motadataDBTable)

}

func TestStringColumn(t *testing.T) {

	defer cache.Clear()

	item := StringColumn{}

	item.Len()

	item.values = []string{"1", "2"}

	item.groups = []int32{1, 2}

	item.Swap(0, 1)

	item.Swap(0, 1)

	item.ascendingOrder = true

	assert.True(t, item.Less(0, 1))

	item.ascendingOrder = false

	assert.False(t, item.Less(0, 1))

}

//
//func TestWorkerRun(t *testing.T) {
//
//	defer cache.Clear()
//
//	worker := Worker{}
//
//	worker.Events = make(chan string, 2)
//
//	worker.ShutdownNotifications = make(chan bool, 2)
//
//	go worker.run(0)
//
//	worker.Events <- "demo"
//
//	time.Sleep(time.Second * 2)
//
//	bytes, err := utils.ReadLogFile("Query Worker", "query")
//
//	assert.Nil(t, err)
//
//	assert.Contains(t, string(bytes), "occurred in worker")
//
//	worker.ShutdownNotifications <- true
//
//}
//
//func TestWorkerCleanup(t *testing.T) {
//
//	defer cache.Clear()
//
//	worker := Worker{}
//
//	worker.Events = make(chan string, 2)
//
//	worker.executors = []*Executor{{CleanupAcks: []chan int{make(chan int, 2)}}}
//
//	worker.ShutdownNotifications = make(chan bool, 2)
//
//	worker.cleanup(0)
//
//	bytes, err := utils.ReadLogFile("Query Worker", "query")
//
//	assert.Nil(t, err)
//
//	assert.Contains(t, string(bytes), "occurred while cleaning worker")
//
//}
//
//func TestWorkerCleanupCondition(t *testing.T) {
//
//	defer cache.Clear()
//
//	worker := Worker{}
//
//	worker.memoryPools = []*utils.MemoryPool{utils.NewMemoryPool(1, 10, false, utils.DefaultBlobPools)}
//
//	worker.memoryPoolIndices = []map[string]int{{
//		"float64": 1,
//		"int8":    1,
//		"int16":   1,
//		"int64":   1,
//	}}
//
//	worker.memoryPoolDataTypes = []map[string]codec.DataType{{
//		"float64": codec.Float64,
//		"int8":    codec.Int8,
//		"int16":   codec.Int16,
//		"int64":   codec.Int64,
//	}}
//
//	worker.cleanUpConditionContext(0, false)
//
//	assert.Len(t, worker.memoryPoolIndices[0], 0)
//
//}

func TestSetVerticalHistoricalValueColumnINT8(t *testing.T) {

	defer cache.Clear()

	poollength := utils.MaxPoolLength

	defer func() {
		utils.MaxPoolLength = poollength
	}()

	utils.MaxPoolLength = 20

	eventid := 0

	executorid := 0

	column := "dummy"

	values := make([]int8, 168)

	worker := NewWorker(0, QueryEngineType(0))

	worker.poolLength = 20

	worker.Start(nil)

	defer func() {
		worker.ShutdownNotifications <- true
	}()

	worker.setVerticalHistoricalValueColumnINT8(eventid, executorid, column, values)

	worker.columnDataTypes[executorId][0] = codec.Float64

	values = make([]int8, 20)

	worker.setVerticalHistoricalValueColumnINT8(eventid, executorid, column, values)

	assert.NotEqual(t, worker.columnPoolIndices[executorId][worker.WorkerEvents[eventid].currentIndex], -1)

}

func TestSetVerticalHistoricalValueColumnINT16(t *testing.T) {

	defer cache.Clear()

	poollength := utils.MaxPoolLength

	defer func() {
		utils.MaxPoolLength = poollength
	}()

	utils.MaxPoolLength = 20

	eventid := 0

	executorid := 0

	column := "dummy"

	values := make([]int16, 168)

	worker := NewWorker(0, QueryEngineType(0))

	worker.poolLength = 20

	worker.Start(nil)

	defer func() {
		worker.ShutdownNotifications <- true
	}()

	worker.setVerticalHistoricalValueColumnINT16(eventid, executorid, column, values)

	worker.columnDataTypes[executorId][0] = codec.Float64

	values = make([]int16, 20)

	worker.setVerticalHistoricalValueColumnINT16(eventid, executorid, column, values)

	assert.NotEqual(t, worker.columnPoolIndices[executorId][worker.WorkerEvents[eventid].currentIndex], -1)

}

func TestSetVerticalHistoricalValueColumnINT32(t *testing.T) {

	defer cache.Clear()

	poollength := utils.MaxPoolLength

	defer func() {
		utils.MaxPoolLength = poollength
	}()

	utils.MaxPoolLength = 20

	eventid := 0

	executorid := 0

	column := "dummy"

	values := make([]int32, 168)

	worker := NewWorker(0, QueryEngineType(0))

	worker.poolLength = 20

	worker.Start(nil)

	defer func() {
		worker.ShutdownNotifications <- true
	}()

	worker.setVerticalHistoricalValueColumnINT32(eventid, executorid, column, values)

	worker.columnDataTypes[executorId][0] = codec.Float64

	values = make([]int32, 20)

	worker.setVerticalHistoricalValueColumnINT32(eventid, executorid, column, values)

	assert.NotEqual(t, worker.columnPoolIndices[executorId][worker.WorkerEvents[eventid].currentIndex], -1)

}

func TestSetVerticalHistoricalValueColumnINT64(t *testing.T) {

	defer cache.Clear()

	poollength := utils.MaxPoolLength

	defer func() {
		utils.MaxPoolLength = poollength
	}()

	utils.MaxPoolLength = 20

	eventid := 0

	executorid := 0

	column := "dummy"

	values := make([]int64, 168)

	worker := NewWorker(0, QueryEngineType(0))

	worker.poolLength = 20

	worker.Start(nil)

	defer func() {
		worker.ShutdownNotifications <- true
	}()

	worker.setVerticalHistoricalValueColumnINT64(eventid, executorid, column, values)

	worker.columnDataTypes[executorId][0] = codec.Float64

	values = make([]int64, 20)

	worker.setVerticalHistoricalValueColumnINT64(eventid, executorid, column, values)

	assert.NotEqual(t, worker.columnPoolIndices[executorId][worker.WorkerEvents[eventid].currentIndex], -1)

}

func TestSetVerticalHistoricalValueColumnString(t *testing.T) {

	defer cache.Clear()

	poollength := utils.MaxPoolLength

	defer func() {
		utils.MaxPoolLength = poollength
	}()

	utils.MaxPoolLength = 20

	eventid := 0

	executorid := 0

	column := "dummy"

	values := make([]string, 168)

	worker := NewWorker(0, QueryEngineType(0))

	worker.poolLength = 20

	worker.Start(nil)

	defer func() {
		worker.ShutdownNotifications <- true
	}()

	worker.setVerticalHistoricalValueColumnString(eventid, executorid, column, values)

	worker.columnDataTypes[executorId][0] = codec.Float64

	values = make([]string, 20)

	worker.setVerticalHistoricalValueColumnString(eventid, executorid, column, values)

	assert.NotEqual(t, worker.columnPoolIndices[executorId][worker.WorkerEvents[eventid].currentIndex], -1)

}

func TestInvalidAIOpsVisualizationResultType(t *testing.T) {

	defer cache.Clear()

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "grid-emptyplugin.json")

	timeline := utils.GetTimeline(Last1Month)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationCategory] = Forecast

	queryContext[visualizationResultType] = 3

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Empty(motadataDBTable)

	assertions.Contains(errs, utils.ErrorInvalidVisualizationType)

	queryContext[VisualizationCategory] = Anomaly

	queryContext[visualizationResultType] = 0

	motadataDBTable, errs = notifyExecutor(queryContext)

	assertions.Empty(motadataDBTable)

	assertions.Contains(errs, utils.ErrorInvalidVisualizationType)

	queryContext[VisualizationCategory] = Baseline

	queryContext[visualizationResultType] = 1

	motadataDBTable, errs = notifyExecutor(queryContext)

	assertions.Empty(motadataDBTable)

	assertions.Contains(errs, utils.ErrorInvalidVisualizationType)

	queryContext[VisualizationCategory] = Forecast

	delete(queryContext, visualizationResultType)

	motadataDBTable, errs = notifyExecutor(queryContext)

	assertions.Empty(motadataDBTable)

	assertions.Contains(errs, utils.ErrorInvalidVisualizationType)

}

func TestBenchmarkInsertion(t *testing.T) {

	broker.ShutdownNotifications <- true

	broker.Requests = make(chan []byte, 100000)

	time.Sleep(time.Millisecond * 500)

	valid := atomic.Bool{}

	go func(valid *atomic.Bool) {

		for range <-broker.Requests {

			valid.Store(true)

		}
	}(&valid)

	environmentType := utils.EnvironmentType

	defer func() {

		utils.EnvironmentType = environmentType
	}()

	utils.EnvironmentType = utils.DatastoreBenchIntegrationEnvironment

	wg := &sync.WaitGroup{}

	wg.Add(1)

	writer.PopulateEventDatabase(wg, broker)

	wg.Wait()

	assertions := assert.New(t)

	time.Sleep(time.Second)

	assertions.True(valid.Load())
}

func TestGenerateRandomValueByColumn(t *testing.T) {

	assertions := assert.New(t)

	table := make(map[string]map[string]int)

	table["floating.column"] = map[string]int{

		writer.MetricMaxValue: 100,

		writer.MetricMinValue: 50,

		writer.MetricDatatype: datastore.FloatingColumn,
	}

	value := writer.GenerateRandomValueByColumn("floating.column", table)

	assertions.True(utils.ToFlOAT(value) > 50)

}

func TestSendAIOpsEngineQueryAbortRequest(t *testing.T) {

	executor := NewExecutor(0, Metric)

	assertions := assert.New(t)

	executor.Parser = executor.initParser()

	executor.Logger = executor.initLogger()

	executor.subQueryId = "123"

	executor.sendAIOpsAbortRequest()

	utils.AssertLogMessage(assertions, fmt.Sprintf("Query Planner-%v", 0), "query", fmt.Sprintf("Query abort request sent to AIOps engine for sub query id : %v", executor.subQueryId))
}

func TestStatisticalFunction(t *testing.T) {

	assertions := assert.New(t)

	executor := NewExecutor(0, Metric)

	executor.Parser = executor.initParser()

	executor.MemoryPoolManager = executor.initMemoryPoolManager()

	executor.ExecutorManager = executor.initExecutorManager()

	executor.EventManager = executor.initEventManager(workers)

	executor.ResponseManager = executor.initResponseManager()

	executor.Logger = executor.initLogger()

	executor.int64Values = []int64{10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10}

	executor.float64Values = []float64{10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10}

	executor.evaluateStatisticalFunc(CumulativeSum, codec.Int64)

	executor.evaluateStatisticalFunc(CumulativeSum, codec.Float64)

	executor.evaluateStatisticalFunc(utils.Empty, codec.Int64)

	utils.AssertLogMessage(assertions, "Query Executor-0", "query", "invalid statistical function")

}

func TestBitmapFunction(t *testing.T) {

	assertions := assert.New(t)

	bitmap := NewCompactedBitmap(uint32(2))

	bitmap.Set(uint32(1))

	bitmap.Set(uint32(2))

	assertions.EqualValues(bitmap.count, 2)

	bitmap.Set(uint32(3))

	assertions.EqualValues(bitmap.count, 3)

	count := 0

	bitmap.Iterate(func(i uint32) { count++ })

	assertions.Equal(count, 3)
}

//func TestDrillDownCompleteQuery(t *testing.T) {
//
//	assertions := assert.New(t)
//
//	drillDownWorker := NewDrillDownWorker(NewExecutor(-1, make([]*Worker, utils.QueryWorkers), nil, DrillDown, nil), nil)
//
//	drillDownWorker.executor.context["abc"] = make(chan string)
//
//	err := drillDownWorker.completeQuery()
//
//	assertions.Equal(err.Error(), "incremental query probing failed, reason: query context not encoded")
//}
//
//func TestDrillDownLoadInvalidQueryContext(t *testing.T) {
//
//	assertions := assert.New(t)
//
//	drillDownWorker := NewDrillDownWorker(NewExecutor(-1, make([]*Worker, utils.QueryWorkers), nil, DrillDown, nil), nil)
//
//	drillDownWorker.executor.context["abc"] = make(chan string)
//
//	err := drillDownWorker.loadDrillDownQueryContext(nil)
//
//	assertions.Equal(err.Error(), "drill down query context not received")
//}

//func TestPanicExecutorV1(t *testing.T) {
//
//	tempChannel := utils.PublisherResponses
//
//	utils.PublisherResponses = make(chan []byte, 1000)
//
//	defer func() {
//
//		time.Sleep(time.Second * 2)
//
//	}()
//
//	assertions := assert.New(t)
//
//	maxBufferBytes := utils.MaxValueBufferBytes
//
//	defer func() {
//
//		utils.MaxValueBufferBytes = maxBufferBytes
//
//		utils.PublisherResponses = tempChannel
//	}()
//
//	utils.MaxValueBufferBytes = 0
//
//	id := -1
//
//	workers1 := make([]*Worker, utils.QueryWorkers)
//
//	utils.AggregationJobQueryAcks = []chan int{make(chan int, 1000)}
//
//	for i := range workers1 {
//
//		workers1[i] = NewWorker(i, DrillDown)
//
//		workers1[i].Start(nil)
//	}
//
//	defer func() {
//
//		for i := range workers1 {
//
//			workers1[i].ShutdownNotifications <- true
//		}
//
//	}()
//
//	availableWorkers := make([]atomic.Bool, utils.QueryWorkers)
//
//	position := 0
//
//	for id := 0; id < position+utils.MetricQueryExecutors; id++ {
//
//		executors[id] = NewExecutor(id, Metric)
//
//		executors[id].Start(workers, availableWorkers)
//
//		executorAllocations[id] = Metric
//	}
//
//	position += utils.MetricQueryExecutors
//
//	for id := position; id < position+utils.FlowQueryExecutors; id++ {
//
//		executors[id] = NewExecutor(id, Flow)
//
//		executors[id].Start(workers, availableWorkers)
//
//		executorAllocations[id] = Flow
//	}
//
//	position += utils.FlowQueryExecutors
//
//	for id := position; id < position+utils.LogQueryExecutors; id++ {
//
//		executors[id] = NewExecutor(id, Log)
//
//		executors[id].Start(workers, availableWorkers)
//
//		executorAllocations[id] = Log
//	}
//
//	position += utils.LogQueryExecutors
//
//	for id := position; id < position+utils.DrillDownQueryExecutors; id++ {
//
//		executors[id] = NewExecutor(id, DrillDown)
//
//		executors[id].Start(workers, availableWorkers)
//
//		executorAllocations[id] = DrillDown
//	}
//
//	position += utils.DrillDownQueryExecutors
//
//	if utils.AIOpsEngineQueryExecutors > 0 {
//
//		for id := position; id < position+utils.AIOpsEngineQueryExecutors; id++ {
//
//			executors[id] = NewExecutor(id, AIOps)
//
//			executors[id].Start(workers, availableWorkers)
//
//			executorAllocations[id] = AIOps
//		}
//
//		position += utils.AIOpsEngineQueryExecutors
//	}
//
//	executor := NewExecutor(id, Metric)
//
//	executor.Start(workers1, availableWorkers)
//
//	executor.preAggregationJobId = 0
//
//	executor.ShutdownNotifications <- true
//
//	time.Sleep(time.Second)
//
//	utils.MaxValueBufferBytes = maxBufferBytes
//
//	executor = NewExecutor(id, Metric)
//
//	executor.Start(workers1, availableWorkers)
//
//	defer func() {
//
//		executor.ShutdownNotifications <- true
//	}()
//
//	executor.errors = make([]string, 1000)
//
//	executor.overflowedWorkerId = 0
//
//	executor.OverflowedAcks[0] = make(chan int, 1000)
//
//	executor.workerPendingEvents.Put(0, 1)
//
//	executor.subQueryId = "123"
//
//	executor.queryResponses <- "123" + utils.GroupSeparator + "" + utils.GroupSeparator + "99999"
//
//	time.Sleep(time.Second)
//
//	executor.workerPendingEvents.Put(0, 0)
//
//	executor.queryResponses <- "123" + utils.GroupSeparator + "" + utils.GroupSeparator + "99999"
//
//	time.Sleep(time.Second)
//
//	executor.preAggregationJobId = 0
//
//	executor.queryResponses <- "123" + utils.GroupSeparator + "" + utils.GroupSeparator + "99999"
//
//	time.Sleep(time.Second)
//
//	utils.AssertLogMessage(assertions, "Query Executor", "query", "!!!STACK TRACE for executor")
//
//}

//func TestPanicExecutorV2(t *testing.T) {
//
//	tempChannel := utils.PublisherResponses
//
//	defer func() {
//
//		time.Sleep(time.Second * 2)
//
//	}()
//
//	defer func() {
//
//		utils.PublisherResponses = tempChannel
//	}()
//
//	utils.PublisherResponses = make(chan []byte, 1000)
//
//	assertions := assert.New(t)
//
//	workers1 := make([]*Worker, utils.QueryWorkers)
//
//	utils.AggregationJobQueryAcks = []chan int{make(chan int, 1000)}
//
//	id := -1
//
//	for i := range workers1 {
//
//		workers1[i] = NewWorker(i, DrillDown)
//
//		workers1[i].Start(nil)
//	}
//
//	executor := NewExecutor(id, workers1, nil, 0, nil)
//
//	executor.Start()
//
//	defer func() {
//
//		executor.ShutdownNotifications <- true
//
//		time.Sleep(time.Second * 2)
//
//	}()
//
//	executor.queryEngineType = DrillDown
//
//	executor.drillDownWorker = NewDrillDownWorker(executor, []*DrillDownEvent{{}})
//
//	executor.subQueryId = "1234"
//
//	executor.minWorkerId = 0
//
//	executor.maxWorkerId = 1
//
//	executor.AbortRequests <- "1234"
//
//	time.Sleep(time.Second)
//
//	executor.subQueryId = "123"
//
//	executor.usedWorkerElementSize = 1
//
//	executor.usedWorkers = []int{0}
//
//	executor.queryEngineType = AIOps
//
//	executor.AbortRequests <- "123"
//
//	time.Sleep(time.Second)
//
//	utils.AssertLogMessage(assertions, fmt.Sprintf("Query Planner-%v", id), "query", "query abort received for query id 123")
//}

func compareHistogramTicks(clickHouseDBTicks, motadataDBTicks []int64, assertions *assert.Assertions) {

	if len(clickHouseDBTicks) == len(motadataDBTicks) {

		assertions.EqualValues(clickHouseDBTicks[1:], motadataDBTicks[1:])
	}
}

func compareHistogramValuesFLOAT64(clickHouseDBValues, motadataDBValues []float64, assertions *assert.Assertions) {

	if len(clickHouseDBValues) == len(motadataDBValues) {

		assertValuesHavingErrorTolerance(clickHouseDBValues, motadataDBValues, assertions)

	} else if len(clickHouseDBValues) > len(motadataDBValues) {

		assertValuesHavingErrorTolerance(clickHouseDBValues[1:], motadataDBValues, assertions)

	} else if len(clickHouseDBValues) < len(motadataDBValues) {

		assertValuesHavingErrorTolerance(clickHouseDBValues, motadataDBValues[1:], assertions)

	}
}

func prepareClickhouseStatusFlapResult(durations, objects, ticks []int64, statuses, instances, filters []string) map[string]struct{} {

	var result = make(map[string]struct{})

	resolvedObjects := make([]string, len(statuses))

	if len(instances) > 0 {

		for i := range objects {

			resolvedObjects[i] = codec.ToString(objects[i]) + utils.GroupSeparator + instances[i]
		}
	} else {

		for i := range objects {

			resolvedObjects[i] = codec.ToString(objects[i])
		}
	}

	objectStatuses := make(map[string]string)

	objectPositions := make(map[string]int)

	positionBitmap := bitmap.Bitmap{}

	for i := range statuses {

		object := resolvedObjects[i]

		if status, ok := objectStatuses[object]; !ok {

			objectStatuses[object] = statuses[i]

			objectPositions[object] = i

			positionBitmap.Set(uint32(i))

		} else {

			if status == statuses[i] {

				position, _ := objectPositions[object]

				durations[position] += durations[i]
			} else {

				positionBitmap.Set(uint32(i))

				objectStatuses[object] = statuses[i]

				objectPositions[object] = i
			}

		}

	}

	positionBitmap.Range(func(index uint32) {

		result[codec.ToString(durations[index])+utils.GroupSeparator+statuses[index]+utils.GroupSeparator+codec.ToString(ticks[index])+utils.GroupSeparator+resolvedObjects[index]] = struct{}{}
	})

	if filters != nil {

		for key := range result {

			found := false

			for _, filter := range filters {

				if strings.Contains(key, filter) {

					found = true

					break
				}

			}

			if !found {

				delete(result, key)
			}

		}

	}

	return result
}
