/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*Change Logs
* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-10             Dhaval Bera            MOTADATA-6174  Added Unmap function to release memory-mapped resources
* 2025-06-04             Aashil Shah            MOTADATA-5780 Called custom MmapAnonymous and Munmap functions
 */

package writer

import (
	bytes2 "bytes"
	"errors"
	"fmt"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"runtime"
	"sync"
)

var (
	staticMetricWriterLogger = utils.NewLogger("Static Metric Writer", "writer")
)

type StaticMetricWriter struct {
	encoder Encoder

	tokenizer *utils.Tokenizer

	valueBytes []byte

	waitGroup *sync.WaitGroup

	ioEvent storage.DiskIOEvent

	Events chan utils.MotadataMap

	ShutdownNotifications chan bool

	writerId int

	shutdown bool
}

func NewStaticMetricWriter(writerId int) *StaticMetricWriter {

	bytes, err := utils.MmapAnonymous(utils.MaxValueBufferBytes)

	if err != nil {

		staticMetricWriterLogger.Error(fmt.Sprintf("error %v occurred while mapping annonymous buffer for static metric writer %v", err, writerId))

		bytes = make([]byte, utils.MaxValueBufferBytes)
	}

	return &StaticMetricWriter{

		writerId: writerId,

		encoder: NewEncoder(utils.NewMemoryPool(6, utils.MaxPoolLength, false, utils.DefaultBlobPools)),

		valueBytes: bytes,

		waitGroup: &sync.WaitGroup{},

		ioEvent: storage.DiskIOEvent{},

		ShutdownNotifications: make(chan bool, 5),

		Events: make(chan utils.MotadataMap, 1_00_000),
	}

}

func (writer *StaticMetricWriter) Start() {

	writer.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	go func() {

		utils.WriterEngineShutdownMutex.Add(1)

		for {

			if writer.shutdown || utils.GlobalShutdown {

				break
			}

			writer.processRequest()
		}

		utils.WriterEngineShutdownMutex.Done()
	}()

}

func (writer *StaticMetricWriter) processRequest() {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			staticMetricWriterLogger.Error(fmt.Sprintf("error %v occurred in static metric writer %v ", err, writer.writerId))

			staticMetricWriterLogger.Error(fmt.Sprintf("!!!STACK TRACE for static metric writer %v!!! \n %v", writer.writerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			staticMetricWriterLogger.Error(fmt.Sprintf("static metric writer %v restarted", writer.writerId))

		}
	}()

	for {

		select {

		case event := <-writer.Events:

			err := writer.write(event)

			if err != nil {

				staticMetricWriterLogger.Error(fmt.Sprintf("failed to write the static metric for %v store ,%v key reason %v", event.GetStringValue(utils.Plugin), event.GetStringValue("key"), err))
			}

			writer.encoder.MemoryPool.TestPoolLeak()

		case <-writer.ShutdownNotifications:

			if err := utils.Munmap(writer.valueBytes); err != nil {

				staticMetricWriterLogger.Warn(fmt.Sprintf("failed to unmap anonymous mapped buffer,reason: %v", err.Error()))
			}

			writer.encoder.MemoryPool.Unmap()

			staticMetricWriterLogger.Info(fmt.Sprintf("shutting down static metric writer %v", writer.writerId))

			writer.shutdown = true

			return

		}
	}

}

func (writer *StaticMetricWriter) write(event utils.MotadataMap) error {

	store := datastore.GetStore(event.GetStringValue(utils.Plugin), utils.StaticMetric, true, true, writer.encoder, writer.tokenizer)

	if store == nil {

		return errors.New(fmt.Sprintf(utils.ErrorAcquireStore, event.GetStringValue(utils.Plugin)))
	}

	keyBytes := []byte(event.GetStringValue("key") + datastore.DefaultKeyPart)

	_, bytes, err := store.Get(keyBytes, writer.valueBytes, writer.encoder, writer.ioEvent, writer.waitGroup, writer.tokenizer, false)

	if err != nil {

		store.Delete(keyBytes, writer.encoder, writer.tokenizer)

		staticMetricWriterLogger.Error(fmt.Sprintf(utils.ErrorGetKey, string(keyBytes), store.GetName(), err.Error()) + fmt.Sprintf(MessageWriterId, writer.writerId))

		bytes = nil

	}

	value := event.GetStringValue(Value)

	if len(value)+utils.MaxValueBytes > utils.MaxValueBufferBytes {

		value = value[:utils.MaxValueBufferBytes-utils.MaxValueBytes]

	}

	poolIndex, valueBytes := writer.encoder.MemoryPool.AcquireBytePool(len(value) + utils.MaxValueBytes)

	defer writer.encoder.MemoryPool.ReleaseBytePool(poolIndex)

	copy(valueBytes[utils.MaxValueBytes:], value)

	if bytes == nil || !bytes2.Equal(bytes, valueBytes[utils.MaxValueBytes:]) {

		err = store.Put(keyBytes, valueBytes, writer.encoder, writer.tokenizer)
	}

	return err
}
