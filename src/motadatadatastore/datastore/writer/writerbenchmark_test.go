/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>ata-5190  Migrated constants from datastore package to utils package to match SonarQube Standard
* 2025-05-05			 Swapnil A. Dave		<PERSON>A-6078 renamed the value buffer to readbuffer
* 2025-06-04             A<PERSON>l Shah            MOTADATA-5780 Test Case Refactoring
 */

package writer

import (
	bytes2 "bytes"
	"github.com/dolthub/swiss"
	cp "github.com/otiai10/copy"
	"github.com/stretchr/testify/assert"
	"github.com/valyala/gozstd"
	"math"
	"math/rand"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/utils"
	"os"
	"strings"
	"testing"
	"time"
)

func BenchmarkVerticalWriterFlushINT64Values(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	assertions := assert.New(b)

	writer := NewVerticalWriter(0, configWriters)

	writer.shutdown = true

	writer.Start()

	store := datastore.GetStore("08042024-0-24-vertical.flush.int64", utils.PerformanceMetric, true, true, encoder, tokenizer)

	var err error

	cacheEntries := make([][]interface{}, 2)

	cacheEntries[0] = make([]interface{}, utils.MaxWriterCacheValues)

	cacheEntries[1] = make([]interface{}, utils.MaxWriterCacheValues+1)

	cacheEntries[1][0] = codec.Int64

	tick := utils.UnixToSeconds(time.Now().Unix())

	for i := 1; i < len(cacheEntries[1]); i++ {

		cacheEntries[0][i-1] = tick

		cacheEntries[1][i] = rand.Int63()

		tick += 30
	}

	key := "1000000" + utils.KeySeparator + "akhfbsdakubvaesivbsaeilvubaklvjbievsubrawoeifh" + utils.KeySeparator + "interface~in.packets"

	assertions.Nil(err)

	b.Run("vertical-flush-without-append", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			err = writer.flush(key, store, cacheEntries)

			assertions.Nil(err)

		}

	})

	err = store.Sync(encoder)

	assertions.Nil(err)

	b.Run("vertical-flush-with-append", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			err = writer.flush(key, store, cacheEntries)

			assertions.Nil(err)

		}

	})

}

func BenchmarkVerticalWriterFlushFLOAT64Values(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	assertions := assert.New(b)

	writer := NewVerticalWriter(0, configWriters)

	writer.shutdown = true

	writer.Start()

	store := datastore.GetStore("08042024-0-24-vertical.flush.float64", utils.PerformanceMetric, true, true, encoder, tokenizer)

	var err error

	cacheEntries := make([][]interface{}, 2)

	cacheEntries[0] = make([]interface{}, utils.MaxWriterCacheValues)

	cacheEntries[1] = make([]interface{}, utils.MaxWriterCacheValues+1)

	cacheEntries[1][0] = codec.Float64

	tick := utils.UnixToSeconds(time.Now().Unix())

	for i := 1; i < len(cacheEntries[1]); i++ {

		cacheEntries[0][i-1] = tick

		cacheEntries[1][i] = float64(rand.Int63()) + rand.Float64()

		tick += 30
	}

	key := "1000000" + utils.KeySeparator + "akhfbsdakubvaesivbsaeilvubaklvjbievsubrawoeifh" + utils.KeySeparator + "interface~in.packets"

	assertions.Nil(err)

	b.Run("vertical-flush-without-append", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			err = writer.flush(key, store, cacheEntries)

			assertions.Nil(err)

		}

	})

	err = store.Sync(encoder)

	assertions.Nil(err)

	b.Run("vertical-flush-with-append", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			err = writer.flush(key, store, cacheEntries)

			assertions.Nil(err)

		}

	})

}

func BenchmarkVerticalWriterFlushINT32Values(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	assertions := assert.New(b)

	writer := NewVerticalWriter(0, configWriters)

	writer.shutdown = true

	writer.Start()

	store := datastore.GetStore("08042024-0-24-vertical.flush.int32", utils.PerformanceMetric, true, true, encoder, tokenizer)

	var err error

	cacheEntries := make([][]interface{}, 2)

	cacheEntries[0] = make([]interface{}, utils.MaxWriterCacheValues)

	cacheEntries[1] = make([]interface{}, utils.MaxWriterCacheValues+1)

	cacheEntries[1][0] = codec.Int32

	tick := utils.UnixToSeconds(time.Now().Unix())

	for i := 1; i < len(cacheEntries[1]); i++ {

		cacheEntries[0][i-1] = tick

		cacheEntries[1][i] = int64(rand.Int31())

		tick += 30
	}

	key := "1000000" + utils.KeySeparator + "akhfbsdakubvaesivbsaeilvubaklvjbievsubrawoeifh" + utils.KeySeparator + "interface~in.packets"

	assertions.Nil(err)

	b.Run("vertical-flush-without-append", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			err = writer.flush(key, store, cacheEntries)

			assertions.Nil(err)

		}

	})

	err = store.Sync(encoder)

	assertions.Nil(err)

	b.Run("vertical-flush-with-append", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			err = writer.flush(key, store, cacheEntries)

			assertions.Nil(err)

		}

	})

}

func BenchmarkVerticalWriterProcessBatch100MonitorsScalarTable(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	writer := NewVerticalWriter(0, configWriters)

	writer.shutdown = true

	writer.Start()

	dataBlock := make(map[string][]utils.MotadataMap)

	for i := 0; i < 100; i++ {

		prepareMetricBatch(int32(i), utils.Empty, dataBlock, serverTable)

	}

	buffer := bytes2.Buffer{}

	PackPerformanceMetricDataBlockV1(dataBlock, &buffer)

	batch := utils.MotadataMap{
		utils.Plugin:            "serverPlugin",
		utils.Tick:              time.Now().Unix(),
		datastore.DatastoreType: utils.PerformanceMetric,
		utils.BatchSize:         1,
		utils.Buffer:            buffer.Bytes(),
	}

	b.Run("vertical-process-performance-metric-batch-100-monitors-scalar-table", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			writer.writePerformanceMetric(batch)
		}
	})

}

func BenchmarkVerticalWriterProcessBatch1000MonitorsScalarTable(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	writer := NewVerticalWriter(0, configWriters)

	writer.shutdown = true

	writer.Start()

	dataBlock := make(map[string][]utils.MotadataMap)

	for i := 0; i < 1000; i++ {

		prepareMetricBatch(int32(i), utils.Empty, dataBlock, serverTable)

	}

	buffer := bytes2.Buffer{}

	PackPerformanceMetricDataBlockV1(dataBlock, &buffer)

	batch := utils.MotadataMap{
		utils.Plugin:            "serverPlugin",
		utils.Tick:              time.Now().Unix(),
		datastore.DatastoreType: utils.PerformanceMetric,
		utils.BatchSize:         1,
		utils.Buffer:            buffer.Bytes(),
	}

	b.Run("vertical-process-performance-metric-batch-1000-monitors-scalar-table", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			writer.writePerformanceMetric(batch)
		}
	})

}

func BenchmarkVerticalWriterProcessBatch10000MonitorsScalarTable(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	writer := NewVerticalWriter(0, configWriters)

	writer.shutdown = true

	writer.Start()

	dataBlock := make(map[string][]utils.MotadataMap)

	for i := 0; i < 10000; i++ {

		prepareMetricBatch(int32(i), utils.Empty, dataBlock, serverTable)

	}

	buffer := bytes2.Buffer{}

	PackPerformanceMetricDataBlockV1(dataBlock, &buffer)

	batch := utils.MotadataMap{
		utils.Plugin:            "serverPlugin",
		utils.Tick:              time.Now().Unix(),
		datastore.DatastoreType: utils.PerformanceMetric,
		utils.BatchSize:         1,
		utils.Buffer:            buffer.Bytes(),
	}

	b.Run("vertical-process-performance-metric-batch-10000-monitors-scalar-table", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			writer.writePerformanceMetric(batch)
		}
	})

}

func BenchmarkVerticalWriterProcessBatch100MonitorsInstanceTable(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	writer := NewVerticalWriter(0, configWriters)

	writer.shutdown = true

	writer.Start()

	dataBlock := make(map[string][]utils.MotadataMap)

	instances := []string{"chrome", "process1", "process2", "process3", "process4", "spotify", "chrome2", "chrome3", "chrome4", "chrome5", "chrome6", "firefox", "firefox2"}

	for i := 0; i < 100; i++ {

		for _, instance := range instances {

			prepareMetricBatch(int32(i), instance, dataBlock, interfaceTable)
		}

	}

	buffer := bytes2.Buffer{}

	PackPerformanceMetricDataBlockV1(dataBlock, &buffer)

	batch := utils.MotadataMap{
		utils.Plugin:            "interfacePlugin",
		utils.Tick:              time.Now().Unix(),
		datastore.DatastoreType: utils.PerformanceMetric,
		utils.BatchSize:         1,
		utils.Buffer:            buffer.Bytes(),
	}

	b.Run("vertical-process-performance-metric-batch-100-monitors-instance-table", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			writer.writePerformanceMetric(batch)
		}
	})

}

func BenchmarkVerticalWriterProcessBatch1000MonitorsInstanceTable(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	writer := NewVerticalWriter(0, configWriters)

	writer.shutdown = true

	writer.Start()

	dataBlock := make(map[string][]utils.MotadataMap)

	instances := []string{"chrome", "process1", "process2", "process3", "process4", "spotify", "chrome2", "chrome3", "chrome4", "chrome5", "chrome6", "firefox", "firefox2"}

	for i := 0; i < 1000; i++ {

		for _, instance := range instances {

			prepareMetricBatch(int32(i), instance, dataBlock, interfaceTable)
		}

	}

	buffer := bytes2.Buffer{}

	PackPerformanceMetricDataBlockV1(dataBlock, &buffer)

	batch := utils.MotadataMap{
		utils.Plugin:            "interfacePlugin",
		utils.Tick:              time.Now().Unix(),
		datastore.DatastoreType: utils.PerformanceMetric,
		utils.BatchSize:         1,
		utils.Buffer:            buffer.Bytes(),
	}

	b.Run("vertical-process-performance-metric-batch-1000-monitors-instance-table", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			writer.writePerformanceMetric(batch)
		}
	})

}

func BenchmarkVerticalWriterProcessBatch10000MonitorsInstanceTable(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	writer := NewVerticalWriter(0, configWriters)

	writer.shutdown = true

	writer.Start()

	dataBlock := make(map[string][]utils.MotadataMap)

	instances := []string{"chrome", "process1", "process2", "process3", "process4", "spotify", "chrome2", "chrome3", "chrome4", "chrome5", "chrome6", "firefox", "firefox2"}

	for i := 0; i < 10000; i++ {

		for _, instance := range instances {

			prepareMetricBatch(int32(i), instance, dataBlock, interfaceTable)
		}

	}

	buffer := bytes2.Buffer{}

	PackPerformanceMetricDataBlockV1(dataBlock, &buffer)

	batch := utils.MotadataMap{
		utils.Plugin:            "interfacePlugin",
		utils.Tick:              time.Now().Unix(),
		datastore.DatastoreType: utils.PerformanceMetric,
		utils.BatchSize:         1,
		utils.Buffer:            buffer.Bytes(),
	}

	b.Run("vertical-process-performance-metric-batch-10000-monitors-instance-table", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			writer.writePerformanceMetric(batch)
		}
	})

}

func BenchmarkIndexWriter100INT64IndexTokens(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	indexWriter := NewIndexWriter(0)

	indexWriter.shutdown = true

	indexWriter.Start()

	event := utils.IndexEvent{
		NumericContext: map[int]struct{}{},
		StringContext:  nil,
		StoreType:      utils.Log,
		Plugin:         "plugin",
		Column:         "column",
		Tick:           utils.UnixToSeconds(time.Now().Unix()),
		LookupEvent:    true,
	}

	for i := 0; i < 100; i++ {

		event.NumericContext[i] = struct{}{}
	}

	b.Run("write-index-100-INT64-Tokens", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			indexWriter.writeIndex(event)

		}
	})

}

func BenchmarkIndexWriter1000INT64IndexTokens(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	indexWriter := NewIndexWriter(0)

	indexWriter.shutdown = true

	indexWriter.Start()

	event := utils.IndexEvent{
		NumericContext: map[int]struct{}{},
		StringContext:  nil,
		StoreType:      utils.Log,
		Plugin:         "plugin",
		Column:         "column",
		Tick:           utils.UnixToSeconds(time.Now().Unix()),
		LookupEvent:    true,
	}

	for i := 0; i < 1000; i++ {

		event.NumericContext[i] = struct{}{}
	}

	b.Run("write-index-1000-INT64-Tokens", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			indexWriter.writeIndex(event)

		}
	})

}

func BenchmarkIndexWriter10000INT64IndexTokens(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	indexWriter := NewIndexWriter(0)

	indexWriter.shutdown = true

	indexWriter.Start()

	event := utils.IndexEvent{
		NumericContext: map[int]struct{}{},
		StringContext:  nil,
		StoreType:      utils.Log,
		Plugin:         "plugin",
		Column:         "column",
		Tick:           utils.UnixToSeconds(time.Now().Unix()),
		LookupEvent:    true,
	}

	for i := 0; i < 10000; i++ {

		event.NumericContext[i] = struct{}{}
	}

	b.Run("write-index-10000-INT64-Tokens", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			indexWriter.writeIndex(event)

		}
	})

}

func BenchmarkIndexWriter100STRINGIndexTokens(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	indexWriter := NewIndexWriter(0)

	indexWriter.shutdown = true

	indexWriter.Start()

	event := utils.IndexEvent{
		NumericContext: nil,
		StringContext:  map[string]struct{}{},
		StoreType:      utils.Log,
		Plugin:         "plugin",
		Column:         "column",
		Tick:           utils.UnixToSeconds(time.Now().Unix()),
		LookupEvent:    true,
	}

	for i := 0; i < 100; i++ {

		value := codec.INTToStringValue(i)

		event.StringContext[value+"#######"+value] = struct{}{}
	}

	b.Run("write-index-100-STRING-Tokens", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			indexWriter.writeIndex(event)

		}
	})

}

func BenchmarkIndexWriter1000STRINGIndexTokens(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	indexWriter := NewIndexWriter(0)

	indexWriter.shutdown = true

	indexWriter.Start()

	event := utils.IndexEvent{
		NumericContext: nil,
		StringContext:  map[string]struct{}{},
		StoreType:      utils.Log,
		Plugin:         "plugin",
		Column:         "column",
		Tick:           utils.UnixToSeconds(time.Now().Unix()),
		LookupEvent:    true,
	}

	for i := 0; i < 1000; i++ {

		value := codec.INTToStringValue(i)

		event.StringContext[value+"#######"+value] = struct{}{}
	}

	b.Run("write-index-1000-STRING-Tokens", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			indexWriter.writeIndex(event)

		}
	})

}

func BenchmarkIndexWriter10000STRINGIndexTokens(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	indexWriter := NewIndexWriter(0)

	indexWriter.shutdown = true

	indexWriter.Start()

	event := utils.IndexEvent{
		NumericContext: nil,
		StringContext:  map[string]struct{}{},
		StoreType:      utils.Log,
		Plugin:         "plugin",
		Column:         "column",
		Tick:           utils.UnixToSeconds(time.Now().Unix()),
		LookupEvent:    true,
	}

	for i := 0; i < 10000; i++ {

		value := codec.INTToStringValue(i)

		event.StringContext[value+"#######"+value] = struct{}{}
	}

	b.Run("write-index-10000-STRING-Tokens", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			indexWriter.writeIndex(event)

		}
	})

}

func BenchmarkIndexWriter100STRINGIndexMessagesBlobColumn(b *testing.B) {

	assertions := assert.New(b)

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	indexWriter := NewIndexWriter(0)

	indexWriter.shutdown = true

	indexWriter.Start()

	event := utils.IndexEvent{
		NumericContext: nil,
		StringContext:  map[string]struct{}{},
		StoreType:      utils.Log,
		Plugin:         utils.Plugin,
		Column:         utils.Message,
		Tick:           utils.UnixToSeconds(time.Now().Unix()),
		LookupEvent:    true,
	}

	datastore.AddBlobColumn(utils.Message)

	bytes, err := os.ReadFile(benchmarkDataFilePath + utils.PathSeparator + blobColumnLogFile)

	assertions.Nil(err)

	values := strings.Split(string(bytes), utils.NewLineSeparator)

	for i := 0; i < 100; i++ {

		event.StringContext[values[i]] = struct{}{}
	}

	b.Run("write-index-100-STRING-Tokens-blob-column", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			indexWriter.writeIndex(event)

		}
	})

}

func BenchmarkIndexWriter500STRINGIndexMessagesBlobColumn(b *testing.B) {

	assertions := assert.New(b)

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	indexWriter := NewIndexWriter(0)

	indexWriter.shutdown = true

	indexWriter.Start()

	event := utils.IndexEvent{
		NumericContext: nil,
		StringContext:  map[string]struct{}{},
		StoreType:      utils.Log,
		Plugin:         utils.Plugin,
		Column:         utils.Message,
		Tick:           utils.UnixToSeconds(time.Now().Unix()),
		LookupEvent:    true,
	}

	datastore.AddBlobColumn(utils.Message)

	bytes, err := os.ReadFile(benchmarkDataFilePath + utils.PathSeparator + blobColumnLogFile)

	assertions.Nil(err)

	values := strings.Split(string(bytes), utils.NewLineSeparator)

	for i := 0; i < 500; i++ {

		event.StringContext[values[i]] = struct{}{}
	}

	b.Run("write-index-500-STRING-Tokens-blob-column", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			indexWriter.writeIndex(event)

		}
	})

}

func BenchmarkIndexWriter1000STRINGIndexMessagesBlobColumn(b *testing.B) {

	assertions := assert.New(b)

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	indexWriter := NewIndexWriter(0)

	indexWriter.shutdown = true

	indexWriter.Start()

	event := utils.IndexEvent{
		NumericContext: nil,
		StringContext:  map[string]struct{}{},
		StoreType:      utils.Log,
		Plugin:         utils.Plugin,
		Column:         utils.Message,
		Tick:           utils.UnixToSeconds(time.Now().Unix()),
		LookupEvent:    true,
	}

	datastore.AddBlobColumn(utils.Message)

	bytes, err := os.ReadFile(benchmarkDataFilePath + utils.PathSeparator + blobColumnLogFile)

	assertions.Nil(err)

	values := strings.Split(string(bytes), utils.NewLineSeparator)

	for i := 0; i < 1000; i++ {

		event.StringContext[values[i]] = struct{}{}
	}

	b.Run("write-index-1000-STRING-Tokens-blob-column", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			indexWriter.writeIndex(event)

		}
	})

}

func BenchmarkIndexWriter3000STRINGIndexMessagesBlobColumn(b *testing.B) {

	assertions := assert.New(b)

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	indexWriter := NewIndexWriter(0)

	indexWriter.shutdown = true

	indexWriter.Start()

	event := utils.IndexEvent{
		NumericContext: nil,
		StringContext:  map[string]struct{}{},
		StoreType:      utils.Log,
		Plugin:         utils.Plugin,
		Column:         utils.Message,
		Tick:           utils.UnixToSeconds(time.Now().Unix()),
		LookupEvent:    true,
	}

	datastore.AddBlobColumn(utils.Message)

	bytes, err := os.ReadFile(benchmarkDataFilePath + utils.PathSeparator + blobColumnLogFile)

	assertions.Nil(err)

	values := strings.Split(string(bytes), utils.NewLineSeparator)

	for i := range values {

		event.StringContext[values[i]] = struct{}{}
	}

	b.Run("write-index-3000-STRING-Tokens-blob-column", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			indexWriter.writeIndex(event)

		}
	})

}

func BenchmarkIndexWriterDummyPostingList(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	indexWriter := NewIndexWriter(0)

	indexWriter.shutdown = true

	indexWriter.Start()

	event := utils.IndexEvent{
		NumericContext: nil,
		StringContext:  nil,
		StoreType:      utils.Log,
		Plugin:         utils.Plugin,
		Tick:           utils.UnixToSeconds(time.Now().Unix()),
		LookupEvent:    false,
	}

	b.Run("write-non-filter-index", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			indexWriter.writeIndex(event)

		}
	})

}

func BenchmarkHorizontalWriterWriteField100EPS(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	horizontalWriter := NewHorizontalWriter(0)

	horizontalWriter.shutdown = true

	horizontalWriter.Start()

	horizontalWriter.overflowPoolLength = utils.OverflowLength

	horizontalWriter.batchSize = 100

	horizontalWriter.tick = utils.UnixToSeconds(time.Now().Unix())

	horizontalWriter.plugin = "500000-flow"

	datastore.AlterIndexableColumns(horizontalWriter.plugin, map[string]interface{}{

		EventSource:     struct{}{},
		SourceCity:      struct{}{},
		DestinationCity: struct{}{},
		Application:     struct{}{},
	}, utils.Add)

	for i := 0; i < horizontalWriter.batchSize; i++ {

		for k := 0; k < stringFieldElementSize; k++ {

			column := stringColumnPrefix + codec.INTToStringValue(k)

			horizontalWriter.stringFields[column] = append(horizontalWriter.stringFields[column], codec.INTToStringValue(i))
		}

		horizontalWriter.stringFields[EventSource] = append(horizontalWriter.stringFields[EventSource], stringValues[EventSource][rand.Intn(len(stringValues[EventSource]))])

		horizontalWriter.stringFields[SourceCity] = append(horizontalWriter.stringFields[SourceCity], stringValues[SourceCity][rand.Intn(len(stringValues[SourceCity]))])

		horizontalWriter.stringFields[DestinationCity] = append(horizontalWriter.stringFields[DestinationCity], stringValues[DestinationCity][rand.Intn(len(stringValues[DestinationCity]))])

		horizontalWriter.stringFields[Application] = append(horizontalWriter.stringFields[Application], stringValues[Application][rand.Intn(len(stringValues[Application]))])

		for k := 0; k < int32FieldElementSize; k++ {

			column := int32ColumnPrefix + codec.INTToStringValue(k)

			value := math.MaxInt16 + int32(i)

			horizontalWriter.int32Fields[column] = append(horizontalWriter.int32Fields[column], value)

			if val, ok := horizontalWriter.int32Fields[column+utils.MaxSuffix]; ok && val[0] < value {

				horizontalWriter.int32Fields[column+utils.MaxSuffix][0] = value
			} else {

				horizontalWriter.int32Fields[column+utils.MaxSuffix] = append(horizontalWriter.int32Fields[column+utils.MaxSuffix], value)
			}
		}

		for k := 0; k < int64FieldElementSize; k++ {

			column := int64ColumnPrefix + codec.INTToStringValue(k)

			value := math.MaxInt32 + int64(i)

			horizontalWriter.int64Fields[column] = append(horizontalWriter.int64Fields[column], value)

			if val, ok := horizontalWriter.int64Fields[column+utils.MaxSuffix]; ok && val[0] < value {

				horizontalWriter.int64Fields[column+utils.MaxSuffix][0] = value
			} else {

				horizontalWriter.int64Fields[column+utils.MaxSuffix] = append(horizontalWriter.int64Fields[column+utils.MaxSuffix], value)
			}
		}
	}

	b.Run("write-field-100EPS", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			horizontalWriter.writeField(EventSource, horizontalWriter.plugin)

		}
	})

}

func BenchmarkHorizontalWriterWriteField1000EPS(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	horizontalWriter := NewHorizontalWriter(0)

	horizontalWriter.shutdown = true

	horizontalWriter.Start()

	horizontalWriter.overflowPoolLength = utils.OverflowLength

	horizontalWriter.batchSize = 1000

	horizontalWriter.tick = utils.UnixToSeconds(time.Now().Unix())

	horizontalWriter.plugin = "500000-flow"

	datastore.AlterIndexableColumns(horizontalWriter.plugin, map[string]interface{}{

		EventSource:     struct{}{},
		SourceCity:      struct{}{},
		DestinationCity: struct{}{},
		Application:     struct{}{},
	}, utils.Add)

	for i := 0; i < horizontalWriter.batchSize; i++ {

		for k := 0; k < stringFieldElementSize; k++ {

			column := stringColumnPrefix + codec.INTToStringValue(k)

			horizontalWriter.stringFields[column] = append(horizontalWriter.stringFields[column], codec.INTToStringValue(i))
		}

		horizontalWriter.stringFields[EventSource] = append(horizontalWriter.stringFields[EventSource], stringValues[EventSource][rand.Intn(len(stringValues[EventSource]))])

		horizontalWriter.stringFields[SourceCity] = append(horizontalWriter.stringFields[SourceCity], stringValues[SourceCity][rand.Intn(len(stringValues[SourceCity]))])

		horizontalWriter.stringFields[DestinationCity] = append(horizontalWriter.stringFields[DestinationCity], stringValues[DestinationCity][rand.Intn(len(stringValues[DestinationCity]))])

		horizontalWriter.stringFields[Application] = append(horizontalWriter.stringFields[Application], stringValues[Application][rand.Intn(len(stringValues[Application]))])

		for k := 0; k < int32FieldElementSize; k++ {

			column := int32ColumnPrefix + codec.INTToStringValue(k)

			value := math.MaxInt16 + int32(i)

			horizontalWriter.int32Fields[column] = append(horizontalWriter.int32Fields[column], value)

			if val, ok := horizontalWriter.int32Fields[column+utils.MaxSuffix]; ok && val[0] < value {

				horizontalWriter.int32Fields[column+utils.MaxSuffix][0] = value
			} else {

				horizontalWriter.int32Fields[column+utils.MaxSuffix] = append(horizontalWriter.int32Fields[column+utils.MaxSuffix], value)
			}
		}

		for k := 0; k < int64FieldElementSize; k++ {

			column := int64ColumnPrefix + codec.INTToStringValue(k)

			value := math.MaxInt32 + int64(i)

			horizontalWriter.int64Fields[column] = append(horizontalWriter.int64Fields[column], value)

			if val, ok := horizontalWriter.int64Fields[column+utils.MaxSuffix]; ok && val[0] < value {

				horizontalWriter.int64Fields[column+utils.MaxSuffix][0] = value
			} else {

				horizontalWriter.int64Fields[column+utils.MaxSuffix] = append(horizontalWriter.int64Fields[column+utils.MaxSuffix], value)
			}
		}
	}

	b.Run("write-field-1000EPS", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			horizontalWriter.writeField(EventSource, horizontalWriter.plugin)

		}
	})

}

func BenchmarkHorizontalWriterWriteField5000EPS(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	horizontalWriter := NewHorizontalWriter(0)

	horizontalWriter.shutdown = true

	horizontalWriter.Start()

	horizontalWriter.overflowPoolLength = utils.OverflowLength

	horizontalWriter.batchSize = 5000

	horizontalWriter.tick = utils.UnixToSeconds(time.Now().Unix())

	horizontalWriter.plugin = "500000-flow"

	datastore.AlterIndexableColumns(horizontalWriter.plugin, map[string]interface{}{

		EventSource:     struct{}{},
		SourceCity:      struct{}{},
		DestinationCity: struct{}{},
		Application:     struct{}{},
	}, utils.Add)

	for i := 0; i < horizontalWriter.batchSize; i++ {

		for k := 0; k < stringFieldElementSize; k++ {

			column := stringColumnPrefix + codec.INTToStringValue(k)

			horizontalWriter.stringFields[column] = append(horizontalWriter.stringFields[column], codec.INTToStringValue(i))
		}

		horizontalWriter.stringFields[EventSource] = append(horizontalWriter.stringFields[EventSource], stringValues[EventSource][rand.Intn(len(stringValues[EventSource]))])

		horizontalWriter.stringFields[SourceCity] = append(horizontalWriter.stringFields[SourceCity], stringValues[SourceCity][rand.Intn(len(stringValues[SourceCity]))])

		horizontalWriter.stringFields[DestinationCity] = append(horizontalWriter.stringFields[DestinationCity], stringValues[DestinationCity][rand.Intn(len(stringValues[DestinationCity]))])

		horizontalWriter.stringFields[Application] = append(horizontalWriter.stringFields[Application], stringValues[Application][rand.Intn(len(stringValues[Application]))])

		for k := 0; k < int32FieldElementSize; k++ {

			column := int32ColumnPrefix + codec.INTToStringValue(k)

			value := math.MaxInt16 + int32(i)

			horizontalWriter.int32Fields[column] = append(horizontalWriter.int32Fields[column], value)

			if val, ok := horizontalWriter.int32Fields[column+utils.MaxSuffix]; ok && val[0] < value {

				horizontalWriter.int32Fields[column+utils.MaxSuffix][0] = value
			} else {

				horizontalWriter.int32Fields[column+utils.MaxSuffix] = append(horizontalWriter.int32Fields[column+utils.MaxSuffix], value)
			}
		}

		for k := 0; k < int64FieldElementSize; k++ {

			column := int64ColumnPrefix + codec.INTToStringValue(k)

			value := math.MaxInt32 + int64(i)

			horizontalWriter.int64Fields[column] = append(horizontalWriter.int64Fields[column], value)

			if val, ok := horizontalWriter.int64Fields[column+utils.MaxSuffix]; ok && val[0] < value {

				horizontalWriter.int64Fields[column+utils.MaxSuffix][0] = value
			} else {

				horizontalWriter.int64Fields[column+utils.MaxSuffix] = append(horizontalWriter.int64Fields[column+utils.MaxSuffix], value)
			}
		}
	}

	b.Run("write-field-5000EPS", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			horizontalWriter.writeField(EventSource, horizontalWriter.plugin)

		}
	})

}

func BenchmarkHorizontalWriterWriteField10000EPS(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	horizontalWriter := NewHorizontalWriter(0)

	horizontalWriter.shutdown = true

	horizontalWriter.Start()

	horizontalWriter.overflowPoolLength = utils.OverflowLength

	horizontalWriter.batchSize = 10000

	horizontalWriter.tick = utils.UnixToSeconds(time.Now().Unix())

	horizontalWriter.plugin = "500000-flow"

	datastore.AlterIndexableColumns(horizontalWriter.plugin, map[string]interface{}{

		EventSource:     struct{}{},
		SourceCity:      struct{}{},
		DestinationCity: struct{}{},
		Application:     struct{}{},
	}, utils.Add)

	for i := 0; i < horizontalWriter.batchSize; i++ {

		for k := 0; k < stringFieldElementSize; k++ {

			column := stringColumnPrefix + codec.INTToStringValue(k)

			horizontalWriter.stringFields[column] = append(horizontalWriter.stringFields[column], codec.INTToStringValue(i))
		}

		horizontalWriter.stringFields[EventSource] = append(horizontalWriter.stringFields[EventSource], stringValues[EventSource][rand.Intn(len(stringValues[EventSource]))])

		horizontalWriter.stringFields[SourceCity] = append(horizontalWriter.stringFields[SourceCity], stringValues[SourceCity][rand.Intn(len(stringValues[SourceCity]))])

		horizontalWriter.stringFields[DestinationCity] = append(horizontalWriter.stringFields[DestinationCity], stringValues[DestinationCity][rand.Intn(len(stringValues[DestinationCity]))])

		horizontalWriter.stringFields[Application] = append(horizontalWriter.stringFields[Application], stringValues[Application][rand.Intn(len(stringValues[Application]))])

		for k := 0; k < int32FieldElementSize; k++ {

			column := int32ColumnPrefix + codec.INTToStringValue(k)

			value := math.MaxInt16 + int32(i)

			horizontalWriter.int32Fields[column] = append(horizontalWriter.int32Fields[column], value)

			if val, ok := horizontalWriter.int32Fields[column+utils.MaxSuffix]; ok && val[0] < value {

				horizontalWriter.int32Fields[column+utils.MaxSuffix][0] = value
			} else {

				horizontalWriter.int32Fields[column+utils.MaxSuffix] = append(horizontalWriter.int32Fields[column+utils.MaxSuffix], value)
			}
		}

		for k := 0; k < int64FieldElementSize; k++ {

			column := int64ColumnPrefix + codec.INTToStringValue(k)

			value := math.MaxInt32 + int64(i)

			horizontalWriter.int64Fields[column] = append(horizontalWriter.int64Fields[column], value)

			if val, ok := horizontalWriter.int64Fields[column+utils.MaxSuffix]; ok && val[0] < value {

				horizontalWriter.int64Fields[column+utils.MaxSuffix][0] = value
			} else {

				horizontalWriter.int64Fields[column+utils.MaxSuffix] = append(horizontalWriter.int64Fields[column+utils.MaxSuffix], value)
			}
		}
	}

	b.Run("write-field-10000EPS", func(b *testing.B) {

		for i := 0; i < b.N; i++ {

			horizontalWriter.writeField(EventSource, horizontalWriter.plugin)

		}
	})

}

func BenchmarkMetricAggregatorWriteWALINT8(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int8)

	for i := 0; i < utils.MaxWriterCacheValues; i++ {

		entries[1] = append(entries[1], int64(i))

		if i < utils.MaxWriterCacheValues-1 {

			entries[0] = append(entries[0], tick+1)
		}
	}

	plugin := "20-server"

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	b.Run("metric-aggregation-write-wal-int8", func(b *testing.B) {

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	})

}

func BenchmarkMetricAggregatorWriteWALINT16(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int16)

	for i := math.MaxInt8; i < math.MaxInt8+utils.MaxWriterCacheValues; i++ {

		entries[1] = append(entries[1], int64(i))

		if i < math.MaxInt8+utils.MaxWriterCacheValues-1 {

			entries[0] = append(entries[0], tick+1)
		}
	}

	plugin := "20-server"

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	b.Run("metric-aggregation-write-wal-int16", func(b *testing.B) {

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	})

}

func BenchmarkMetricAggregatorWriteWALINT32(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int32)

	for i := math.MaxInt16; i < math.MaxInt16+utils.MaxWriterCacheValues; i++ {

		entries[1] = append(entries[1], int64(i))

		if i < math.MaxInt16+utils.MaxWriterCacheValues-1 {

			entries[0] = append(entries[0], tick+1)
		}
	}

	plugin := "20-server"

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	b.Run("metric-aggregation-write-wal-int32", func(b *testing.B) {

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	})

}

func BenchmarkMetricAggregatorWriteWALINT64(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int64)

	for i := math.MaxInt32; i < math.MaxInt32+utils.MaxWriterCacheValues; i++ {

		entries[1] = append(entries[1], int64(i))

		if i < math.MaxInt32+utils.MaxWriterCacheValues-1 {

			entries[0] = append(entries[0], tick+1)
		}
	}

	plugin := "20-server"

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	b.Run("metric-aggregation-write-wal-int64", func(b *testing.B) {

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	})

}

func BenchmarkMetricAggregatorWriteWALFLOAT64(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Float64)

	for i := math.MaxInt32; i < math.MaxInt32+utils.MaxWriterCacheValues; i++ {

		entries[1] = append(entries[1], float64(i))

		if i < math.MaxInt32+utils.MaxWriterCacheValues-1 {

			entries[0] = append(entries[0], tick+1)
		}
	}

	plugin := "20-server"

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	b.Run("metric-aggregation-write-wal-float64", func(b *testing.B) {

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	})

}

func BenchmarkMetricAggregatorMergeWALINT8100MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int8)

	entries[1] = append(entries[1], int64(1))

	plugin := "20-server"

	for i := 0; i < 100; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int8-100-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT16100MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int16)

	entries[1] = append(entries[1], int64(math.MaxInt8+1))

	plugin := "20-server"

	for i := 0; i < 100; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int16-100-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT32100MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int32)

	entries[1] = append(entries[1], int64(math.MaxInt16+1))

	plugin := "20-server"

	for i := 0; i < 100; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int32-100-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT64100MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int64)

	entries[1] = append(entries[1], int64(math.MaxInt32+1))

	plugin := "20-server"

	for i := 0; i < 100; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int64-100-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALFLOAT64100MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Float64)

	entries[1] = append(entries[1], float64(math.MaxInt32+1))

	plugin := "20-server"

	for i := 0; i < 100; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-float64-100-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT81000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int8)

	entries[1] = append(entries[1], int64(1))

	plugin := "20-server"

	for i := 0; i < 1000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int8-1000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT161000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int16)

	entries[1] = append(entries[1], int64(math.MaxInt8+1))

	plugin := "20-server"

	for i := 0; i < 1000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int16-1000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT321000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int32)

	entries[1] = append(entries[1], int64(math.MaxInt16+1))

	plugin := "20-server"

	for i := 0; i < 1000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int32-1000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT641000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int64)

	entries[1] = append(entries[1], int64(math.MaxInt32+1))

	plugin := "20-server"

	for i := 0; i < 1000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int64-1000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALFLOAT641000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Float64)

	entries[1] = append(entries[1], float64(math.MaxInt32+1))

	plugin := "20-server"

	for i := 0; i < 1000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-float64-1000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT82000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int8)

	entries[1] = append(entries[1], int64(1))

	plugin := "20-server"

	for i := 0; i < 2000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int8-2000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT162000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int16)

	entries[1] = append(entries[1], int64(math.MaxInt8+1))

	plugin := "20-server"

	for i := 0; i < 2000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int16-2000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT322000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int32)

	entries[1] = append(entries[1], int64(math.MaxInt16+1))

	plugin := "20-server"

	for i := 0; i < 2000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int32-2000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT642000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int64)

	entries[1] = append(entries[1], int64(math.MaxInt32+1))

	plugin := "20-server"

	for i := 0; i < 2000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int64-2000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALFLOAT642000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Float64)

	entries[1] = append(entries[1], float64(math.MaxInt32+1))

	plugin := "20-server"

	for i := 0; i < 2000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-float64-2000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT85000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int8)

	entries[1] = append(entries[1], int64(1))

	plugin := "20-server"

	for i := 0; i < 5000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int8-5000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT165000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int16)

	entries[1] = append(entries[1], int64(math.MaxInt8+1))

	plugin := "20-server"

	for i := 0; i < 5000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int16-5000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT325000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int32)

	entries[1] = append(entries[1], int64(math.MaxInt16+1))

	plugin := "20-server"

	for i := 0; i < 5000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int32-5000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT645000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int64)

	entries[1] = append(entries[1], int64(math.MaxInt32+1))

	plugin := "20-server"

	for i := 0; i < 5000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int64-5000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALFLOAT645000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Float64)

	entries[1] = append(entries[1], float64(math.MaxInt32+1))

	plugin := "20-server"

	for i := 0; i < 5000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-float64-5000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT8000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int8)

	entries[1] = append(entries[1], int64(1))

	plugin := "20-server"

	for i := 0; i < 8000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int8-8000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT168000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int16)

	entries[1] = append(entries[1], int64(math.MaxInt8+1))

	plugin := "20-server"

	for i := 0; i < 8000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int16-8000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT328000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int32)

	entries[1] = append(entries[1], int64(math.MaxInt16+1))

	plugin := "20-server"

	for i := 0; i < 8000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int32-8000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT648000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int64)

	entries[1] = append(entries[1], int64(math.MaxInt32+1))

	plugin := "20-server"

	for i := 0; i < 8000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int64-8000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALFLOAT648000MonitorsScalarMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Float64)

	entries[1] = append(entries[1], float64(math.MaxInt32+1))

	plugin := "20-server"

	for i := 0; i < 8000; i++ {

		key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, "")

		metricAggregator.writeWAL(utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		})
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-float64-8000-monitors-scalar-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT8100MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int8)

	entries[1] = append(entries[1], int64(1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 100; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int8-100-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT16100MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int16)

	entries[1] = append(entries[1], int64(math.MaxInt8+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 100; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int16-100-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT32100MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int32)

	entries[1] = append(entries[1], int64(math.MaxInt16+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 100; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int32-100-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT64100MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int64)

	entries[1] = append(entries[1], int64(math.MaxInt32+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 100; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int64-100-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALFLOAT64100MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Float64)

	entries[1] = append(entries[1], float64(math.MaxInt32+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 100; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-float64-100-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT8200MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int8)

	entries[1] = append(entries[1], int64(1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 200; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int8-200-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT16200MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int16)

	entries[1] = append(entries[1], int64(math.MaxInt8+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 200; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int16-200-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT32200MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int32)

	entries[1] = append(entries[1], int64(math.MaxInt16+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 200; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int32-200-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT64200MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int64)

	entries[1] = append(entries[1], int64(math.MaxInt32+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 200; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int64-200-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALFLOAT64200MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Float64)

	entries[1] = append(entries[1], float64(math.MaxInt32+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 200; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-float64-200-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT8300MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int8)

	entries[1] = append(entries[1], int64(1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 300; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int8-300-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT16300MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int16)

	entries[1] = append(entries[1], int64(math.MaxInt8+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 300; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int16-300-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMetricAggregatorMergeWALINT32300MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int32)

	entries[1] = append(entries[1], int64(math.MaxInt16+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 300; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int32-300-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALINT64300MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int64)

	entries[1] = append(entries[1], int64(math.MaxInt32+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 300; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int64-300-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALFLOAT64300MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Float64)

	entries[1] = append(entries[1], float64(math.MaxInt32+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 300; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-float64-300-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALINT8400MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int8)

	entries[1] = append(entries[1], int64(1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 400; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int8-400-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALINT16400MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int16)

	entries[1] = append(entries[1], int64(math.MaxInt8+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 400; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int16-400-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALINT32400MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int32)

	entries[1] = append(entries[1], int64(math.MaxInt16+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 400; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int32-400-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALINT64400MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int64)

	entries[1] = append(entries[1], int64(math.MaxInt32+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 400; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int64-400-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALFLOAT64400MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Float64)

	entries[1] = append(entries[1], float64(math.MaxInt32+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 400; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-float64-400-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALINT8500MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int8)

	entries[1] = append(entries[1], int64(1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 500; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int8-500-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALINT16500MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int16)

	entries[1] = append(entries[1], int64(math.MaxInt8+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 500; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int16-500-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALINT32500MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int32)

	entries[1] = append(entries[1], int64(math.MaxInt16+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 500; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int32-500-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALINT64500MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int64)

	entries[1] = append(entries[1], int64(math.MaxInt32+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 500; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int64-500-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALFLOAT64500MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Float64)

	entries[1] = append(entries[1], float64(math.MaxInt32+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 500; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-float64-500-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALINT8600MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int8)

	entries[1] = append(entries[1], int64(1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 600; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int8-600-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALINT16600MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int16)

	entries[1] = append(entries[1], int64(math.MaxInt8+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 600; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int16-600-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALINT32600MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int32)

	entries[1] = append(entries[1], int64(math.MaxInt16+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 600; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int32-600-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALINT64600MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Int64)

	entries[1] = append(entries[1], int64(math.MaxInt32+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 600; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-int64-600-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

func BenchmarkMergeMetricAggregationWALFLOAT64600MonitorsInstanceMetric(b *testing.B) {

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	metricAggregator := NewMetricAggregator(0)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(entries[1], codec.Float64)

	entries[1] = append(entries[1], float64(math.MaxInt32+1))

	plugin := "20-server"

	instances := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"}

	for i := 0; i < 600; i++ {

		for _, instance := range instances {

			key := datastore.GetMetricKey(codec.INTToStringValue(i), metric, instance)

			metricAggregator.writeWAL(utils.MetricAggregationRequest{

				Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
			})
		}
	}

	syncMetric := utils.Empty

	for syncMetric = range metricAggregator.files {

		break
	}

	b.Run("metric-aggregation-merge-wal-float64-600-monitors-10-instance-metric", func(b *testing.B) {

		metricAggregator.sync(syncMetric, false)

	})

}

var flowView1Table = map[string]interface{}{

	VolumeBytesPerSec:        float64(datastore.IntegerColumn),
	SentDiscardedPackets:     float64(datastore.IntegerColumn),
	ReceivedDiscardedPackets: float64(datastore.IntegerColumn),
	utils.VolumeBytes:        float64(datastore.IntegerColumn),
	datastore.Duration:       float64(datastore.IntegerColumn),
	VolumeBytesPerPacket:     float64(datastore.IntegerColumn),
	Packets:                  float64(datastore.IntegerColumn),
	Flows:                    float64(datastore.IntegerColumn),
	PacketsPerSec:            float64(datastore.IntegerColumn),
	TCPFlags:                 float64(datastore.IntegerColumn),
	utils.Type:               float64(utils.Flow),
	utils.IndexableColumns: map[string]interface{}{
		SourceIP:           struct{}{},
		DestinationIP:      struct{}{},
		SourcePort:         struct{}{},
		DestinationPort:    struct{}{},
		DestinationCountry: struct{}{},
		SourceCity:         struct{}{},
		DestinationCity:    struct{}{},
		EventSource:        struct{}{},
	},
}

func BenchmarkAggregateEventData1000EPSFlowView1(b *testing.B) {

	assertions := assert.New(b)

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	_ = os.MkdirAll(utils.EventDir, 0755)

	_ = os.MkdirAll(utils.AggregationDir, 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.Aggregations, 0755)

	tick := codec.INT64ToStringValue(utils.RoundOffUnixSeconds(time.Now().Unix(), 30))

	fileName := prepareFile(tick, "500000-flow@@@0", codec.INTToStringValue(int(utils.Flow)), "30", 1000, flowView1Table)

	utils.Split(fileName, utils.SpecialSeparator, tokenizer)

	qualifiedTime := time.UnixMilli(1000 * codec.StringToINT64(tokenizer.Tokens[0])).UTC()

	eventAggregator := NewEventAggregator(0)

	eventAggregator.shutdown = true

	eventAggregator.Start()

	plugin := "500000-flow"

	path := codec.INT64ToStringValue(time.Date(qualifiedTime.Year(), qualifiedTime.Month(),
		qualifiedTime.Day(), 0, 0, 0, 0, time.UTC).Unix()) + utils.PathSeparator

	_ = os.MkdirAll(workingDir+path, 0755)

	path += fileName

	event := utils.MotadataMap{

		utils.Plugin:            plugin,
		utils.Tick:              codec.StringToINT64(tick),
		datastore.DatastoreType: utils.Flow,
		utils.File:              path,
	}

	var err error

	utils.EventAggregationSyncNotifications = make(chan utils.EventAggregationSyncEvent, 100_000)

	b.Run("aggregate-event-data-1000EPS-flow-view1", func(b *testing.B) {

		err = cp.Copy(tempDir+fileName, workingDir+path)

		err = eventAggregator.aggregate(event)

		assertions.Nil(err)
	})

}

func BenchmarkAggregateEventData2000EPSFlowView1(b *testing.B) {

	batchSize := 2000

	assertions := assert.New(b)

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	_ = os.MkdirAll(utils.EventDir, 0755)

	_ = os.MkdirAll(utils.AggregationDir, 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.Aggregations, 0755)

	tick := codec.INT64ToStringValue(utils.RoundOffUnixSeconds(time.Now().Unix(), 30))

	fileName := prepareFile(tick, "500000-flow@@@0", codec.INTToStringValue(int(utils.Flow)), "30", batchSize, flowView1Table)

	utils.Split(fileName, utils.SpecialSeparator, tokenizer)

	qualifiedTime := time.UnixMilli(1000 * codec.StringToINT64(tokenizer.Tokens[0])).UTC()

	eventAggregator := NewEventAggregator(0)

	eventAggregator.shutdown = true

	eventAggregator.Start()

	plugin := "500000-flow"

	path := codec.INT64ToStringValue(time.Date(qualifiedTime.Year(), qualifiedTime.Month(),
		qualifiedTime.Day(), 0, 0, 0, 0, time.UTC).Unix()) + utils.PathSeparator

	_ = os.MkdirAll(workingDir+path, 0755)

	path += fileName

	event := utils.MotadataMap{

		utils.Plugin:            plugin,
		utils.Tick:              codec.StringToINT64(tick),
		datastore.DatastoreType: utils.Flow,
		utils.File:              path,
	}

	var err error

	utils.EventAggregationSyncNotifications = make(chan utils.EventAggregationSyncEvent, 100_000)

	b.Run("aggregate-event-data-2000EPS-flow-view1", func(b *testing.B) {

		err = cp.Copy(tempDir+fileName, workingDir+path)

		err = eventAggregator.aggregate(event)

		assertions.Nil(err)
	})

}

func BenchmarkAggregateEventData3000EPSFlowView1(b *testing.B) {

	batchSize := 3000

	assertions := assert.New(b)

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	_ = os.MkdirAll(utils.EventDir, 0755)

	_ = os.MkdirAll(utils.AggregationDir, 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.Aggregations, 0755)

	tick := codec.INT64ToStringValue(utils.RoundOffUnixSeconds(time.Now().Unix(), 30))

	fileName := prepareFile(tick, "500000-flow@@@0", codec.INTToStringValue(int(utils.Flow)), "30", batchSize, flowView1Table)

	utils.Split(fileName, utils.SpecialSeparator, tokenizer)

	qualifiedTime := time.UnixMilli(1000 * codec.StringToINT64(tokenizer.Tokens[0])).UTC()

	eventAggregator := NewEventAggregator(0)

	eventAggregator.shutdown = true

	eventAggregator.Start()

	plugin := "500000-flow"

	path := codec.INT64ToStringValue(time.Date(qualifiedTime.Year(), qualifiedTime.Month(),
		qualifiedTime.Day(), 0, 0, 0, 0, time.UTC).Unix()) + utils.PathSeparator

	_ = os.MkdirAll(workingDir+path, 0755)

	path += fileName

	event := utils.MotadataMap{

		utils.Plugin:            plugin,
		utils.Tick:              codec.StringToINT64(tick),
		datastore.DatastoreType: utils.Flow,
		utils.File:              path,
	}

	var err error

	utils.EventAggregationSyncNotifications = make(chan utils.EventAggregationSyncEvent, 100_000)

	b.Run("aggregate-event-data-3000EPS-flow-view1", func(b *testing.B) {

		err = cp.Copy(tempDir+fileName, workingDir+path)

		err = eventAggregator.aggregate(event)

		assertions.Nil(err)
	})

}

func BenchmarkAggregateEventData4000EPSFlowView1(b *testing.B) {

	batchSize := 4000

	assertions := assert.New(b)

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	_ = os.MkdirAll(utils.EventDir, 0755)

	_ = os.MkdirAll(utils.AggregationDir, 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.Aggregations, 0755)

	tick := codec.INT64ToStringValue(utils.RoundOffUnixSeconds(time.Now().Unix(), 30))

	fileName := prepareFile(tick, "500000-flow@@@0", codec.INTToStringValue(int(utils.Flow)), "30", batchSize, flowView1Table)

	utils.Split(fileName, utils.SpecialSeparator, tokenizer)

	qualifiedTime := time.UnixMilli(1000 * codec.StringToINT64(tokenizer.Tokens[0])).UTC()

	eventAggregator := NewEventAggregator(0)

	eventAggregator.shutdown = true

	eventAggregator.Start()

	plugin := "500000-flow"

	path := codec.INT64ToStringValue(time.Date(qualifiedTime.Year(), qualifiedTime.Month(),
		qualifiedTime.Day(), 0, 0, 0, 0, time.UTC).Unix()) + utils.PathSeparator

	_ = os.MkdirAll(workingDir+path, 0755)

	path += fileName

	event := utils.MotadataMap{

		utils.Plugin:            plugin,
		utils.Tick:              codec.StringToINT64(tick),
		datastore.DatastoreType: utils.Flow,
		utils.File:              path,
	}

	var err error

	utils.EventAggregationSyncNotifications = make(chan utils.EventAggregationSyncEvent, 100_000)

	b.Run("aggregate-event-data-4000EPS-flow-view1", func(b *testing.B) {

		err = cp.Copy(tempDir+fileName, workingDir+path)

		err = eventAggregator.aggregate(event)

		assertions.Nil(err)
	})

}

func BenchmarkAggregateEventData5000EPSFlowView1(b *testing.B) {

	batchSize := 5000

	assertions := assert.New(b)

	datastore.Close()

	utils.CleanUpStores()

	datastore.Init()

	_ = os.MkdirAll(utils.EventDir, 0755)

	_ = os.MkdirAll(utils.AggregationDir, 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.HyphenSeparator+utils.Aggregations, 0755)

	tick := codec.INT64ToStringValue(utils.RoundOffUnixSeconds(time.Now().Unix(), 30))

	fileName := prepareFile(tick, "500000-flow@@@0", codec.INTToStringValue(int(utils.Flow)), "30", batchSize, flowView1Table)

	utils.Split(fileName, utils.SpecialSeparator, tokenizer)

	qualifiedTime := time.UnixMilli(1000 * codec.StringToINT64(tokenizer.Tokens[0])).UTC()

	eventAggregator := NewEventAggregator(0)

	eventAggregator.shutdown = true

	eventAggregator.Start()

	plugin := "500000-flow"

	path := codec.INT64ToStringValue(time.Date(qualifiedTime.Year(), qualifiedTime.Month(),
		qualifiedTime.Day(), 0, 0, 0, 0, time.UTC).Unix()) + utils.PathSeparator

	_ = os.MkdirAll(workingDir+path, 0755)

	path += fileName

	event := utils.MotadataMap{

		utils.Plugin:            plugin,
		utils.Tick:              codec.StringToINT64(tick),
		datastore.DatastoreType: utils.Flow,
		utils.File:              path,
	}

	var err error

	utils.EventAggregationSyncNotifications = make(chan utils.EventAggregationSyncEvent, 100_000)

	b.Run("aggregate-event-data-5000EPS-flow-view1", func(b *testing.B) {

		err = cp.Copy(tempDir+fileName, workingDir+path)

		err = eventAggregator.aggregate(event)

		assertions.Nil(err)
	})

}

var tempDir = utils.AggregationDir + utils.PathSeparator

var DummyOrdinal = codec.INTToStringValue(utils.NotAvailable)

func prepareFile(tick, view, storeType, interval string, batchSize int, table map[string]interface{}) string {

	key := tick + utils.SpecialSeparator + view + utils.SpecialSeparator + storeType + utils.SpecialSeparator + interval

	ordinalsByValue := map[string]int32{}

	valueByOrdinals := map[int32]string{}

	stringFields := map[string][]string{}

	int64Fields := map[string][]int64{}

	var aggregationColumns []string

	var indexableColumns []string

	dataTypes := map[string]codec.DataType{}

	aggregations := swiss.NewMap[string, int64](uint32(utils.OverflowLength))

	for column, dataType := range table {

		if column == utils.Type || column == utils.IndexableColumns {

			continue
		}

		aggregationColumns = append(aggregationColumns, column)

		if dataType.(float64) == float64(datastore.IntegerColumn) {

			dataTypes[column] = codec.Int64

			for i := 0; i < batchSize; i++ {

				int64Fields[column] = append(int64Fields[column], rand.Int63())
			}

		} else {

			dataTypes[column] = codec.String

			for i := 0; i < batchSize; i++ {

				stringFields[column] = append(stringFields[column], codec.INTToStringValue(int(rand.Int63())))
			}
		}

	}

	for column := range table[utils.IndexableColumns].(map[string]interface{}) {

		dataTypes[column] = codec.String

		indexableColumns = append(indexableColumns, column)

		for i := 0; i < batchSize; i++ {

			index := rand.Intn(len(EventStringColumnValues[column]))

			stringFields[column] = append(stringFields[column], EventStringColumnValues[column][index])
		}
	}

	columnOrdinals := make([]string, len(aggregationColumns))

	for i := range aggregationColumns {

		columnOrdinals[i] = codec.INTToStringValue(i)
	}

	group := utils.Empty

	ordinal := int32(utils.NotAvailable)

	ok := false

	for index := 0; index < batchSize; index++ {

		group = utils.Empty

		// iterate over indexable columns and create group by concatenating values
		for columnIndex, column := range indexableColumns {

			if dataTypes[column] == codec.String {

				group += stringFields[column][index]

			} else if int64Fields[column][index] != utils.DummyINT64Value {

				group += utils.INT64ToStringValue(int64Fields[column][index])
			} else {

				group += utils.Empty
			}

			if columnIndex != len(indexableColumns)-1 {

				group += utils.GroupSeparator
			}
		}

		// generate ordinal for new group
		if ordinal, ok = ordinalsByValue[group]; !ok {

			ordinal = int32(len(ordinalsByValue))

			ordinalsByValue[group] = ordinal

			valueByOrdinals[ordinal] = group

		}

		count := false

		// calculate sum and count for current index record
		for columnIndex, column := range aggregationColumns {

			if dataTypes[column] == codec.Int64 {

				if int64Fields[column][index] != utils.DummyINT64Value {

					metric := codec.INT32ToStringValue(ordinal) + utils.KeySeparator + columnOrdinals[columnIndex] + utils.KeySeparator

					value, found := aggregations.Get(metric + utils.Sum)

					if !found {

						value = int64Fields[column][index]

					} else if value != math.MaxInt64 {

						// check for overflow value and if sum overflows than keep max int64 value

						updatedValue := value + int64Fields[column][index]

						if int64Fields[column][index] < 0 || updatedValue >= 0 {

							value = updatedValue

						} else {

							value = math.MaxInt64
						}
					}

					aggregations.Put(metric+utils.Sum, value)

					count = true
				}

			} else {

				count = true
			}
		}

		if count {

			metric := codec.INT32ToStringValue(ordinal) + utils.KeySeparator + DummyOrdinal + utils.KeySeparator + utils.Count

			value, found := aggregations.Get(metric)

			if found {

				value += 1
			} else {

				value = 1
			}

			aggregations.Put(metric, value)
		}

		// if groups count exceeds overflow length then flush data in file and move
		if len(ordinalsByValue) == utils.MaxDataAggregationGroups { // overflow

		}
	}

	buffer := bytes2.NewBuffer(make([]byte, 1024*1024))

	buffer.Reset()

	if len(valueByOrdinals) == 0 { // data already dumped after overflow of grouping

		return utils.Empty
	}

	codec.EncodeINT32Value(0, buffer) //indexabale columns length

	for _, column := range indexableColumns {

		codec.EncodeINT16Value(int16(len(column)), buffer)

		buffer.WriteString(column)

		buffer.WriteByte(byte(dataTypes[column]))
	}

	codec.EncodeINT32ValueAt(int32(buffer.Len())-4, 0, buffer)

	position := buffer.Len()

	codec.EncodeINT32Value(0, buffer) //aggregated columns length

	for _, column := range aggregationColumns {

		codec.EncodeINT16Value(int16(len(column)), buffer)

		buffer.WriteString(column)

		buffer.WriteByte(byte(dataTypes[column]))
	}

	codec.EncodeINT32ValueAt(int32(buffer.Len()-position)-4, position, buffer)

	position = buffer.Len()

	codec.EncodeINT32Value(0, buffer) //ordinals

	for ord, value := range valueByOrdinals {

		codec.EncodeINT32Value(ord, buffer)

		codec.EncodeINT16Value(int16(len(value)), buffer)

		buffer.WriteString(value)

	}

	codec.EncodeINT32ValueAt(int32(buffer.Len()-position)-4, position, buffer)

	aggregations.Iter(func(column string, value int64) (stop bool) {

		codec.EncodeINT16Value(int16(len(column)), buffer)

		buffer.WriteString(column)

		codec.EncodeINT64Value(value, buffer)

		return stop
	})

	buffer.Write(utils.EOTBytes)

	utils.Split(key, utils.SpecialSeparator, tokenizer)

	fileName := key + utils.SpecialSeparator + codec.INTToStringValue(utils.GetFastModN(utils.GetHash64([]byte(tokenizer.Tokens[0]+tokenizer.Tokens[1])), utils.EventAggregators)) + utils.SpecialSeparator + utils.INT64ToStringValue(time.Now().UnixNano())

	file, err := os.OpenFile(tempDir+fileName, os.O_CREATE|os.O_RDWR|os.O_APPEND, 0666)

	if err != nil {

		return utils.Empty
	}

	if _, err = file.Write(gozstd.Compress(nil, buffer.Bytes())); err != nil {

		_ = file.Close()

		_ = os.Remove(file.Name())

		return utils.Empty
	}

	_ = file.Close()

	return fileName
}

const stringFieldElementSize = 20
const int32FieldElementSize = 10
const int64FieldElementSize = 10
const stringColumnPrefix = "string-"
const int32ColumnPrefix = "int32-"
const int64ColumnPrefix = "int64-"

var stringValues = map[string][]string{

	EventSource:         {"**********", "**********", "**********", "**********", "**********", "**********", "**********"},
	Protocol:            {"tcp", "udp", "snmp", "icmp"},
	Application:         {"chrome", "spotify", "firefox", "brave", "safari", "edge", "internet-explorer"},
	SourceCity:          {"kansas", "ahmedabad", "mumbai", "new york", "pripyat", "lisbon", "ontario", "delhi", "gandhinagar"},
	DestinationCity:     {"hong-kong", "kanpur", "mumbai", "new york", "las vegas", "lisbon", "ontario", "delhi", "gandhinagar"},
	SystemOSName:        {"windows", "linux", "ubuntu", "centos", "macos"},
	SystemOsVersion:     {"1.0", "1.2", "1.3", "1.4", "1.5", "1.6", "1.7", "1.8"},
	SystemOsRelease:     {"1.0/windows", "1.2/linux", "1.3/1.4", "1.4/1.6", "1.5/macos", "1.6/centos", "1.7/ubuntu", "1.8/safari"},
	InterfaceLastChange: {"change1", "change2", "change3", "change4", "change5", "change6", "change7", "change8", "change9"},
}

var serverTable = map[string]map[string]int{

	SystemCPUPercent: {
		MetricDatatype: datastore.FloatingColumn,
		MetricMinValue: 0,
		MetricMaxValue: 100,
	},
	SystemCPUCores: {
		MetricDatatype: datastore.IntegerColumn,
		MetricMinValue: 0,
		MetricMaxValue: math.MaxInt16,
	},
	SystemOSName: {
		MetricDatatype: datastore.StringColumn,
	},
	SystemOsVersion: {
		MetricDatatype: datastore.StringColumn,
	},
	SystemOsRelease: {
		MetricDatatype: datastore.StringColumn,
	},
	SystemMemoryUsedBytes: {
		MetricDatatype: datastore.IntegerColumn,
		MetricMinValue: 0,
		MetricMaxValue: math.MaxInt16,
	},
	SystemDiskUsedPercent: {
		MetricDatatype: datastore.FloatingColumn,
		MetricMinValue: 0,
		MetricMaxValue: 100,
	},
	SystemMemoryCommittedBytes: {
		MetricDatatype: datastore.IntegerColumn,
		MetricMinValue: 8 * 1024 * 1024 * 1024,
		MetricMaxValue: 256 * 1024 * 1024 * 1024,
	},
	SystemNetworkINBytes: {
		MetricDatatype: datastore.IntegerColumn,
		MetricMinValue: math.MaxInt16,
		MetricMaxValue: math.MaxInt64,
	},
	VLANPorts: {
		MetricDatatype: datastore.IntegerColumn,
		MetricMinValue: 0,
		MetricMaxValue: math.MaxInt8,
	},
	Interfaces: {
		MetricDatatype: datastore.IntegerColumn,
		MetricMinValue: 0,
		MetricMaxValue: 1000,
	},
}

var interfaceTable = map[string]map[string]int{

	InterfaceINPackets: {
		MetricDatatype: datastore.IntegerColumn,
		MetricMinValue: math.MaxInt32 + 1,
		MetricMaxValue: codec.MaxInt40,
	},
	InterfaceOUTPackets: {
		MetricDatatype: datastore.IntegerColumn,
		MetricMinValue: math.MaxInt32 + 1,
		MetricMaxValue: codec.MaxInt40,
	},
	InterfaceSentOctets: {
		MetricDatatype: datastore.IntegerColumn,
		MetricMinValue: math.MaxInt32 + 1,
		MetricMaxValue: codec.MaxInt40,
	},
	InterfaceReceivedOctets: {
		MetricDatatype: datastore.IntegerColumn,
		MetricMinValue: math.MaxInt32 + 1,
		MetricMaxValue: codec.MaxInt40,
	},
	InterfaceINTrafficUtilization: {
		MetricDatatype: datastore.IntegerColumn,
		MetricMinValue: math.MaxInt32 + 1,
		MetricMaxValue: codec.MaxInt40,
	},
	InterfaceOUTTrafficUtilization: {
		MetricDatatype: datastore.IntegerColumn,
		MetricMinValue: math.MaxInt16 + 1,
		MetricMaxValue: math.MaxInt32,
	},
	InterfaceErrorPackets: {
		MetricDatatype: datastore.IntegerColumn,
		MetricMinValue: math.MaxInt8 + 1,
		MetricMaxValue: math.MaxInt16,
	},
	InterfaceBandwidth: {
		MetricDatatype: datastore.FloatingColumn,
		MetricMinValue: 0,
		MetricMaxValue: math.MaxInt8,
	},
	InterfaceSentBytes: {
		MetricDatatype: datastore.IntegerColumn,
		MetricMinValue: 0,
		MetricMaxValue: math.MaxInt8,
	},
	InterfaceLastChange: {
		MetricDatatype: datastore.StringColumn,
	},
	InterfaceInstanceName: {
		MetricDatatype: datastore.StringColumn,
		_Instance:      1,
	},
	Interface: {
		MetricDatatype: datastore.StringColumn,
		_Instance:      1,
	},
}

func prepareMetricBatch(objectId int32, instance string, batch map[string][]utils.MotadataMap, table map[string]map[string]int) {

	for column := range table {

		metadata := table[column]

		row := utils.MotadataMap{}

		if metadata[MetricDatatype] == datastore.StringColumn {

			row[Datatype] = codec.String

			if _, found := metadata[_Instance]; found && metadata[_Instance] == 1 {

				row[Value] = instance

			} else {

				row[Value] = stringValues[column][rand.Intn(len(stringValues[column]))]

			}

		} else {

			/*

				ordinals according to vertical writer
			*/

			if metadata[MetricDatatype] == datastore.IntegerColumn {

				row[Datatype] = datastore.IntegerColumn

			} else {

				row[Datatype] = datastore.FloatingColumn

			}

			row[Value] = generateRandomMetricValueByColumn(column, table)
		}

		row[ObjectId] = objectId

		row[Metric] = column

		row[_Instance] = instance

		key := codec.ToString(objectId)

		if instance != utils.Empty {

			key = key + utils.GroupSeparator + instance
		}
		batch[key] = append(batch[key], row)
	}

}

func generateRandomMetricValueByColumn(column string, table map[string]map[string]int) interface{} {

	metadata := table[column]

	if metadata[MetricDatatype] == datastore.StringColumn {

		index := rand.Intn(len(stringValues[column]))

		return stringValues[column][index]

	}
	var value interface{}

	maxValue := metadata[MetricMaxValue]

	minValue := metadata[MetricMinValue]

	dataType := metadata[MetricDatatype]

	value = int64(rand.Intn(maxValue-minValue+1) + minValue)

	if dataType == datastore.FloatingColumn {

		value = float64(value.(int64)) + rand.Float64()

		if value.(float64) > float64(maxValue) {

			value = float64(maxValue)
		}
	}

	return value
}
