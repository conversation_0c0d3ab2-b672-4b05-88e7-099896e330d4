/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
	When we execute the query for the last week or last month os whichever timeline which has a
	larger time frame, for that query we have to open so many keys and get data. Rather than opening all keys,
	why not for time intervals we write data and for that time frame only one key needs to open so that our IO, time will be reduced.
	and performance will be increased.So We write aggregated data ahead of time.

	Metric aggregators are workers who collect data and write it after it has been aggregated. And here we write only metric data.

	A metric aggregator is an online aggregator, which means that data is aggregated as it arrives,
	rather than after it is written. The metric aggregator receives metric data entries from the vertical writer
	and then aggregates the data. In metric aggregation, we only support monitor and instance in group by.

	- Aggregation data is written in a horizontal format.

	for metric aggregation store is based on metric name rather than plugin name, and plugin name is inserted in key,

	Example

	metric := "system.cpu.percent"

	store name becomes "12012024-1-system.cpu.percent-5" for 5 minute interval

	we have three intervals (5, 15, 30) for aggregation.

	Note:
		- If an error occurs while aggregating data for a specific tick, the aggregator sends a request to the aggregation job, which writes data for that tick.
		- For metric aggregation, the store is based on metric name rather than plugin name, and the plugin name is inserted in the key.
*/

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 Aashil Shah			Motadata-5190  Migrated constants from datastore to utils to match SonarQube Standard
* 2025-03-21			 Dhaval Bera			Motadata-5452  Changed "object.id" with "netroute.id" for NetRoute Status Plugin
* 2025-04-02			 Dhaval Bera			Motadata-4859  Added NetRoute Status Metric Aggregation Datastore Type
* 2025-04-09			 Dhaval Bera			Motadata-4859  Refactoring "fmt.Sprintf" to update go version to 1.24
* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-10             Dhaval Bera            MOTADATA-6174  Added Unmap function to release memory-mapped resources
* 2025-05-26			 Aashil Shah			Motadata-6275  Made Metric Aggregator Pool Size configurable
* 2025-06-23             Dhaval Bera            MOTADATA-6642 Refactoring condition check
* 2025-06-23             Vedant Dokania         Motadata-6370 Mapping operand changes to get the instance type store
* 2025-06-04             Aashil Shah            MOTADATA-5780 Called custom MmapAnonymous and Munmap functions
 */

package writer

import (
	bytes2 "bytes"
	"errors"
	"fmt"
	"github.com/dolthub/swiss"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"runtime"
	"strings"
	"sync"
	"time"
)

var (
	metricAggregatorDir = utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.HyphenSeparator + utils.Aggregations + utils.PathSeparator
)

type MetricAggregator struct {
	waitGroup *sync.WaitGroup

	memoryPool *utils.MemoryPool

	objects *swiss.Map[string, struct{}] //stores qualified objects

	int64Fields *swiss.Map[string, []int64] //used during wal file reading

	float64Fields *swiss.Map[string, []float64] //used during wal file reading

	stringFields *swiss.Map[string, string] //used during wal file reading

	stringMappings, numericMappings *swiss.Map[int32, []int]

	encoder Encoder

	decoder Decoder

	logger utils.Logger

	event storage.DiskIOEvent

	tokenizers []*utils.Tokenizer

	events []storage.DiskIOEventBatch

	dataTypes map[string]DataType //aggregation wise datatype

	Requests chan utils.MetricAggregationRequest

	ShutdownNotifications chan bool

	txnEntries map[uint64]utils.TxnEntry

	timers map[string]int64 // recorded the first entry time tick

	records, stats, poolIndices map[string]int //aggregation wise pool indices

	partitions map[string]struct{}

	files map[string]*os.File

	keyBuffers, valueBuffers [][]byte

	bufferBytes, txnBufferBytes, int32Bytes []byte

	aggregatorId, txnOffset, txnPartition int

	//flag to trigger the probing job for a particular tick
	pendingProbe, shutdown bool
}

func NewMetricAggregator(id int) *MetricAggregator {

	pool := utils.NewMemoryPool(utils.MetricAggregatorPoolSize, utils.MaxPoolLength, false, utils.DefaultBlobPools)

	return &MetricAggregator{

		Requests: make(chan utils.MetricAggregationRequest, 5_00_00),

		aggregatorId: id,

		logger: utils.NewLogger(fmt.Sprintf("Metric Aggregator-%v", id), "aggregator"),

		timers: make(map[string]int64, 10),

		keyBuffers: make([][]byte, 8),

		valueBuffers: make([][]byte, 8),

		memoryPool: pool,

		encoder: NewEncoder(pool),

		decoder: NewDecoder(pool),

		events: make([]storage.DiskIOEventBatch, utils.MaxStoreParts),

		event: storage.DiskIOEvent{},

		records: make(map[string]int, 100),

		dataTypes: make(map[string]DataType, 10),

		poolIndices: make(map[string]int, 10),

		partitions: make(map[string]struct{}, 20),

		files: make(map[string]*os.File, 20),

		ShutdownNotifications: make(chan bool, 5),

		waitGroup: &sync.WaitGroup{},

		tokenizers: make([]*utils.Tokenizer, 2),

		stats: make(map[string]int, 100),

		txnPartition: utils.NotAvailable,

		int32Bytes: make([]byte, 4),

		txnEntries: make(map[uint64]utils.TxnEntry, 50),

		txnOffset: 4,
	}
}

func (aggregator *MetricAggregator) Start() {

	aggregator.stringMappings = swiss.NewMap[int32, []int](1000)

	aggregator.numericMappings = swiss.NewMap[int32, []int](1000)

	for i := range aggregator.valueBuffers {

		bytes, err := utils.MmapAnonymous(utils.MaxValueBufferBytes)

		if err != nil {

			aggregator.logger.Error(fmt.Sprintf("error %v occurred while mapping annonymous buffer for metric aggregator %v and index %v", err, aggregator.aggregatorId, i))

			bytes = make([]byte, utils.MaxValueBufferBytes)
		}

		aggregator.valueBuffers[i] = bytes
	}

	txnBufferBytes, err := utils.MmapAnonymous(utils.DataWriterTxnBufferBytes)

	if err != nil {

		aggregator.logger.Error(fmt.Sprintf("error %v occurred while mapping annonymous txn buffer for metric aggregator %v", err, aggregator.aggregatorId))

		txnBufferBytes = make([]byte, utils.DataWriterTxnBufferBytes)
	}

	aggregator.txnBufferBytes = txnBufferBytes

	for i := range aggregator.events {

		aggregator.events[i] = storage.DiskIOEventBatch{}
	}

	for i := range aggregator.tokenizers {

		aggregator.tokenizers[i] = &utils.Tokenizer{

			Tokens: make([]string, utils.TokenizerLength),
		}
	}

	bytes, err := utils.MmapAnonymous(utils.DataWriterValueBufferBytes)

	if err != nil {

		bytes = make([]byte, utils.DataWriterValueBufferBytes)
	}

	aggregator.bufferBytes = bytes

	go func() {

		utils.AggregatorEngineShutdownMutex.Add(1)

		for {

			if aggregator.shutdown || utils.GlobalShutdown {

				break
			}

			aggregator.start(true)
		}

		utils.AggregatorEngineShutdownMutex.Done()

	}()
}

/*
	flow of metric aggregator ->

	writer ------> aggregator (single value process at a time written in wal)

	Metric Aggregator Note :- Data is written in a temporary wal file written in a specific manner
	after a certain periodic timer or the overflow length we read the data from the wal file and aggregate them together.
*/

func (aggregator *MetricAggregator) start(init bool) {

	timer := time.NewTicker(time.Second * time.Duration(utils.VerticalAggregationSyncTimerSeconds))

	defer func() {

		if err := recover(); err != nil {

			timer.Stop()

			stackTraceBytes := make([]byte, 1<<20)

			aggregator.logger.Error(fmt.Sprintf("error %v occurred", err))

			aggregator.logger.Error(fmt.Sprintf("!!!STACK TRACE for metric aggregator !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			aggregator.logger.Error(fmt.Sprintf("restarting metric aggregator - %v", aggregator.aggregatorId))

		}
	}()

	/*
		This check is here for writing data that were unprocessed where previously datastore was running and the data was unprocessed when shutting down.
	*/
	if init && aggregator.aggregatorId == 0 {

		if aggregations := datastore.GetVerticalAggregations(); len(aggregations) > 0 {

			for metric := range aggregations {

				dirs, err := os.ReadDir(metricAggregatorDir + metric)

				if err != nil {

					continue
				}

				for _, dir := range dirs {

					if dir.IsDir() {

						continue
					}

					utils.Split(dir.Name(), utils.HyphenSeparator, aggregator.tokenizers[0])

					aggregator.tokenizers[0].Tokens[0] = metric

					key := strings.Join(aggregator.tokenizers[0].Tokens[:aggregator.tokenizers[0].Counts], utils.HyphenSeparator)

					utils.Split(dir.Name(), utils.HyphenSeparator, aggregator.tokenizers[0])

					txn := aggregator.tokenizers[0].Tokens[0] + utils.HyphenSeparator + "0"

					for i := 1; i < aggregator.tokenizers[0].Counts; i++ {

						txn += utils.HyphenSeparator + aggregator.tokenizers[0].Tokens[i]
					}

					aggregator.files[key], err = os.OpenFile(metricAggregatorDir+metric+utils.PathSeparator+key, os.O_CREATE|os.O_RDWR|os.O_APPEND, 0666)

					if err != nil {

						aggregator.logger.Error(fmt.Sprintf("error %v occurred while open file for metric %v and key %v", err, metric, key))

						delete(aggregator.files, key)

						continue
					}

					aggregator.sync(key, true)
				}
			}
		}
	}

	syncIntervalSeconds := int64(utils.VerticalAggregationSyncTimerSeconds)

	for {

		select {

		// at time interval we sync the data until then we only collect the for aggregation
		case <-timer.C:

			for metric, timestamp := range aggregator.timers {

				if time.Now().Unix()-timestamp >= syncIntervalSeconds {

					aggregator.sync(metric, true)
				}
			}

			// aggregation event comes from the vertical writer
		case event := <-aggregator.Requests: //notification received from writer for writing the wal file

			aggregator.writeWAL(event)

		case <-aggregator.ShutdownNotifications:

			aggregator.logger.Info(fmt.Sprintf("shutting down metric aggregator %v", aggregator.aggregatorId))

			for metric := range aggregator.timers {

				aggregator.sync(metric, true)
			}

			for index := range aggregator.valueBuffers {

				if err := utils.Munmap(aggregator.valueBuffers[index]); err != nil {

					aggregator.logger.Warn(fmt.Sprintf("failed to unmap anonymous mapped buffer,reason: %v", err.Error()))
				}
			}

			if err := utils.Munmap(aggregator.txnBufferBytes); err != nil {

				aggregator.logger.Warn(fmt.Sprintf("failed to unmap anonymous mapped txn buffer,reason: %v", err.Error()))
			}

			if err := utils.Munmap(aggregator.bufferBytes); err != nil {

				aggregator.logger.Warn(fmt.Sprintf("failed to unmap anonymous mapped buffer bytes,reason: %v", err.Error()))
			}

			aggregator.memoryPool.Unmap()

			aggregator.logger.Info(fmt.Sprintf("shutdown completed for metric aggregator %v", aggregator.aggregatorId))

			aggregator.shutdown = true

			return
		}
	}
}

// Before writing the aggregated data directly, we collect the data for the time being, aggregate it, and write it.
func (aggregator *MetricAggregator) writeWAL(event utils.MetricAggregationRequest) {

	/*
		wal files are written on a lifetime store under name of the metric Before writing the aggregated data directly, we collect the data for the time being, aggregate it, and write it.ic being aggregated

		wal file structure :- file name -> transaction.id-tick(rounded according to interval)-interval-plugin

		contents :- object/group (key) -> value in the highest form of their data-type
		(int8, int16, int24, int32, int64 -> int64)  (float8, float16, float32, float64 -> float64)
		(string to string)
	*/

	ticks := event.Entries[0]

	cacheValues := event.Entries[1]

	dataType := cacheValues[0].(DataType)

	cacheValues = cacheValues[1:]

	utils.Split(event.Key, utils.KeySeparator, aggregator.tokenizers[0])

	keyBytes := []byte(strings.Join(aggregator.tokenizers[0].Tokens[:aggregator.tokenizers[0].Counts-1], utils.GroupSeparator))

	if _, ok := aggregator.partitions[event.Column]; !ok {

		err := os.Mkdir(metricAggregatorDir+event.Column, 0755)

		if err != nil && !os.IsExist(err) {

			aggregator.logger.Error(fmt.Sprintf("error %v occurred while creating partition for metric %v", err, event.Column))

			return
		}

		aggregator.partitions[event.Column] = struct{}{}
	}

	roundedTickPoolIndex, roundedTicks := aggregator.encoder.MemoryPool.AcquireINT32Pool(utils.NotAvailable)

	positionPoolIndex, positions := aggregator.encoder.MemoryPool.AcquireINTPool(utils.NotAvailable)

	defer aggregator.encoder.MemoryPool.ReleaseINTPool(positionPoolIndex)

	defer aggregator.encoder.MemoryPool.ReleaseINT32Pool(roundedTickPoolIndex)

	elementSize, secondTick := 0, int64(0)

	for index, tick := range ticks {

		secondTick = utils.SecondsToUnix(tick.(int32))

		key := event.Plugin + utils.AggregationSeparator + event.Column + utils.KeySeparator + INT64ToStringValue(utils.GetBaseTickv1(secondTick)) + utils.KeySeparator + INTToStringValue(utils.AggregationIntervals[0]) + utils.KeySeparator + utils.VerticalFormat

		roundedTick := utils.RoundOffSeconds(tick.(int32), utils.AggregationIntervals[0])

		/*
		   metric aggregator will write only one interval wal file, and then it will derive all the interval aggregation from base wal file

		   For example intervals of 5, 15, and 30 minutes.
		   The base interval is 5 minutes, and all other intervals (15 and 30 minutes) must be multiples of this base interval.
		   We will write ticks only for the 5-minute interval, storing raw data at this frequency in the WAL file.
		   During synchronization, we will aggregate data from the 5-minute intervals to create the 15-minute and 30-minute intervals.
		   This design reduces real-time processing by deferring aggregation to sync time, optimizing storage and minimizing writes.
		*/

		if datastore.IsAggregationContextExists(key) {

			for _, interval := range utils.AggregationIntervals {

				utils.ManagerNotifications <- utils.MotadataMap{

					utils.Interval: interval,
					utils.Plugin:   event.Plugin,
					utils.Tick:     utils.SecondsToUnix(utils.RoundOffSeconds(tick.(int32), interval)),
					utils.EventContext: utils.MotadataMap{
						utils.Type:   StringToINT(event.StoreType),
						event.Column: float64(dataType),
					},
					utils.DatastoreFormat: utils.VerticalFormat,
					utils.OperationType:   utils.Recover,
				}
			}

			continue

		}

		if index == 0 || roundedTick > roundedTicks[elementSize-1] {

			roundedTicks[elementSize] = roundedTick

			positions[elementSize] = index

			elementSize++

		}
	}

	positions[elementSize] = len(ticks)

	for index, roundedTick := range roundedTicks[:elementSize] {

		values := cacheValues[positions[index]:positions[index+1]]

		bytes := make([]byte, 0)

		if dataType <= Int64 {

			dataType = Int64

			bytes = append(bytes, byte(dataType))

			bytes = aggregator.writeINT64Values(values, bytes)

		} else if dataType < String {

			dataType = Float64

			bytes = append(bytes, byte(dataType))

			bytes = aggregator.writeFLOAT64Values(values, bytes)

		} else {

			dataType = String

			bytes = append(bytes, byte(dataType))

			bytes = aggregator.writeStringValues(values, bytes)

		}

		bytes = append(bytes, utils.EOTBytes...)

		event.Key = event.Column + utils.HyphenSeparator + INT32ToStringValue(roundedTick) + utils.HyphenSeparator + INTToStringValue(utils.AggregationIntervals[0]) + utils.HyphenSeparator + event.Plugin + utils.HyphenSeparator + event.StoreType

		var err error

		//When one key exceeds the overflow length of values or when data exceeds the size of the buffer, we sync the data.
		// 8 - valueBuffer offset (checksum 4 byte, 4 byte value buffer length)
		// 4 - keyBytes length, 4 - overall entries length
		if aggregator.records[event.Key]+len(values) >= utils.OverflowLength || aggregator.stats[event.Key]+len(bytes)+8+len(keyBytes)+4+4 >= len(aggregator.bufferBytes) {

			aggregator.sync(event.Key, false)
		}

		if _, ok := aggregator.files[event.Key]; !ok {

			aggregator.files[event.Key], err = os.OpenFile(metricAggregatorDir+event.Column+utils.PathSeparator+event.Key, os.O_CREATE|os.O_RDWR|os.O_APPEND, 0666)

			if err != nil {

				aggregator.logger.Fatal(fmt.Sprintf("failed to open file for %v ; reason : %v", event.Key, err))

				continue
			}
		}

		offset := 4

		WriteINT32Value(int32(len(keyBytes)), offset, aggregator.bufferBytes)

		offset += 4

		copy(aggregator.bufferBytes[offset:], keyBytes)

		offset += len(keyBytes)

		WriteINT32Value(int32(len(bytes)), offset, aggregator.bufferBytes)

		offset += 4

		copy(aggregator.bufferBytes[offset:], bytes)

		offset += len(bytes)

		WriteINT32Value(int32(offset-4), 0, aggregator.bufferBytes)

		if _, err = aggregator.files[event.Key].Write(aggregator.bufferBytes[:offset]); err != nil {

			_ = aggregator.files[event.Key].Close()

			_ = os.Remove(aggregator.files[event.Key].Name())

			delete(aggregator.files, event.Key)

			delete(aggregator.records, event.Key)

			delete(aggregator.stats, event.Key)

			delete(aggregator.timers, event.Key)

			aggregator.logger.Fatal(fmt.Sprintf("%v file rollbacked ; reason : %v", event.Key, err))

			continue
		}

		if _, ok := aggregator.timers[event.Key]; !ok {

			aggregator.timers[event.Key] = time.Now().Unix()
		}

		if _, ok := aggregator.records[event.Key]; !ok {

			aggregator.records[event.Key] = 0
		}

		if _, ok := aggregator.stats[event.Key]; !ok {

			aggregator.stats[event.Key] = 0
		}

		aggregator.records[event.Key] = aggregator.records[event.Key] + len(values)

		aggregator.stats[event.Key] += len(bytes) + len(keyBytes) + 4
	}

}

func (aggregator *MetricAggregator) sync(metric string, testPoolLeak bool) {

	/*
		after the periodic timer is triggered we start reading wal files for the particular metric and
		start aggregating the data in memory and will be dumping them.
	*/

	records := 0

	t := time.Now().UnixMilli()

	interval := utils.Empty

	plugin := utils.Empty

	tick := utils.Empty

	file, ok := aggregator.files[metric]

	if utils.EnvironmentType != utils.DatastoreBenchUnitEnvironment {

		delete(aggregator.timers, metric) //delete the timers

		delete(aggregator.records, metric) //reset the records

		delete(aggregator.files, metric)

		delete(aggregator.stats, metric)

	}

	if ok {

		defer func(walFile *os.File) {

			if utils.EnvironmentType != utils.DatastoreBenchUnitEnvironment {

				_ = walFile.Close()

				_ = os.Remove(walFile.Name())

			}

		}(file)

		stat, err := file.Stat()

		var bytes []byte

		if int(stat.Size()) >= len(aggregator.bufferBytes) {

			bytes = make([]byte, stat.Size(), stat.Size())

			_, err = file.ReadAt(bytes, 0)

			if err != nil {

				aggregator.logger.Error(fmt.Sprintf("failed to read wal file %v, reason : %v", file.Name(), err))

				return
			}

			bytes = bytes[:stat.Size()]

		} else {

			length := 0

			length, err = file.ReadAt(aggregator.bufferBytes, 0)

			if err != nil && !strings.EqualFold(err.Error(), "EOF") {

				aggregator.logger.Error(fmt.Sprintf("failed to read wal file %v, reason : %v", file.Name(), err))

				return
			}

			bytes = aggregator.bufferBytes[:length]
		}

		utils.Split(metric, utils.HyphenSeparator, aggregator.tokenizers[0])

		tick = aggregator.tokenizers[0].Tokens[aggregator.tokenizers[0].Counts-5]

		interval = aggregator.tokenizers[0].Tokens[aggregator.tokenizers[0].Counts-4]

		plugin = aggregator.tokenizers[0].Tokens[aggregator.tokenizers[0].Counts-3] + utils.HyphenSeparator + aggregator.tokenizers[0].Tokens[aggregator.tokenizers[0].Counts-2]

		datastoreType := StringToINT(aggregator.tokenizers[0].Tokens[aggregator.tokenizers[0].Counts-1])

		metric = aggregator.tokenizers[0].Tokens[0]

		for i := range utils.AggregationIntervals {

			aggregator.cleanup(testPoolLeak)

			records = 0

			aggregator.int64Fields = swiss.NewMap[string, []int64](2000)

			aggregator.float64Fields = swiss.NewMap[string, []float64](2000)

			aggregator.stringFields = swiss.NewMap[string, string](2000)

			aggregator.objects = swiss.NewMap[string, struct{}](2000)

			err = aggregator.readWAL(bytes)

			if err != nil {

				aggregator.logger.Error(fmt.Sprintf("failed to read wal file %v , reason : %v", file.Name(), err))

				continue
			}

			records += aggregator.objects.Count()

			tick = INT32ToStringValue(utils.RoundOffSeconds(StringToINT32(tick), utils.AggregationIntervals[i]))

			interval = INTToStringValue(utils.AggregationIntervals[i])

			storeName := utils.SecondsToDate(StringToINT32(tick)) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + INTToStringValue(utils.AggregationIntervals[i])

			instance := strings.Contains(metric, utils.InstanceSeparator)

			key := plugin + utils.AggregationSeparator + metric + utils.KeySeparator + INT64ToStringValue(utils.GetBaseTickv1(utils.SecondsToUnix(StringToINT32(tick)))) + utils.KeySeparator + interval + utils.KeySeparator + utils.VerticalFormat

			// if aggregation job is writing particular interval and at the same time metric aggregator also try to write the same interval,
			// then metric aggregator will leave that interval to aggregation job.
			if datastore.IsAggregationContextPositionExists(key, utils.GetPosition(utils.SecondsToUnix(StringToINT32(tick)), utils.AggregationIntervals[i])) {

				dataType := datastore.StringColumn

				if aggregator.int64Fields.Count() > 0 || aggregator.float64Fields.Count() > 0 {

					dataType = datastore.IntegerColumn

				}

				utils.ManagerNotifications <- utils.MotadataMap{

					utils.Interval: interval,
					utils.Plugin:   plugin,
					utils.Tick:     utils.SecondsToUnix(StringToINT32(tick)),
					utils.EventContext: utils.MotadataMap{
						utils.Type: datastoreType,
						metric:     float64(dataType),
					},
					utils.DatastoreFormat: utils.VerticalFormat,
					utils.OperationType:   utils.Recover,
				}

				continue
			}

			if aggregator.int64Fields.Count() > 0 || aggregator.float64Fields.Count() > 0 {

				aggregator.merge(plugin, tick, interval, storeName, metric, datastoreType, true, instance)

			}

			if aggregator.stringFields.Count() > 0 {

				storeName = utils.SecondsToDate(StringToINT32(tick)) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + utils.Garbage + utils.HyphenSeparator + interval

				aggregator.merge(plugin, tick, interval, storeName, metric, datastoreType, false, instance)
			}
		}
	}

	if utils.DebugEnabled() {

		aggregator.logger.Debug(fmt.Sprintf("aggregator took %v ms to sync %v records of metric %v for tick %v, plugin %v and interval second %v", time.Now().UnixMilli()-t, records, metric, tick, plugin, interval))
	}
}

// We read the key value from the temporary .wal file and fill the int64fields, stringfields, and float64fields.
func (aggregator *MetricAggregator) readWAL(bufferBytes []byte) (err error) {

	defer func() {

		if r := recover(); r != nil {

			err = r.(error)
		}
	}()

	if bufferBytes == nil || len(bufferBytes) == 0 {

		return errors.New("failed to read file")
	}

	position := 0

	length := 0

	for position < len(bufferBytes) {

		length = int(ReadINT32Value(bufferBytes[position : position+4]))

		position += 4

		bytes := bufferBytes[position : position+length]

		position += length

		if !bytes2.Equal(bytes[len(bytes)-3:], utils.EOTBytes) {

			break
		}

		offset := 0

		length = int(ReadINT32Value(bytes[offset : offset+4]))

		offset += 4

		keyBytes := string(bytes[offset : offset+length])

		offset += length

		length = int(ReadINT32Value(bytes[offset : offset+4]))

		offset += 4

		valueBytes := bytes[offset : offset+length]

		offset += length

		valueBytes = valueBytes[:len(valueBytes)-3]

		dataType := DataType(valueBytes[0])

		elements := ReadINTValue(valueBytes[1:3])

		buffer := bytes2.NewBuffer(valueBytes[3:])

		for buffer.Len() != 0 {

			if dataType <= Int64 {

				values, _ := aggregator.int64Fields.Get(keyBytes)

				for i := 0; i < elements; i++ {

					values = append(values, ReadINT64Value(buffer.Next(8)))
				}

				aggregator.int64Fields.Put(keyBytes, values)

			} else if dataType < String {

				values, _ := aggregator.float64Fields.Get(keyBytes)

				for i := 0; i < elements; i++ {

					values = append(values, ReadFLOAT64Value(buffer.Next(8)))
				}

				aggregator.float64Fields.Put(keyBytes, values)

			} else {

				for i := 0; i < elements; i++ {

					//For numeric fields, we put all the data; however, for string fields, we put only the most recently arrived data.
					aggregator.stringFields.Put(keyBytes, string(buffer.Next(ReadINTValue(buffer.Next(2)))))
				}
			}
		}

		aggregator.objects.Put(keyBytes, struct{}{})
	}

	return nil
}

func (aggregator *MetricAggregator) cleanup(testPoolLeak bool) {

	aggregator.int64Fields = nil

	aggregator.float64Fields = nil

	aggregator.stringFields = nil

	aggregator.objects = nil

	aggregator.pendingProbe = false

	aggregator.cleanupPool(testPoolLeak)

	aggregator.cleanupTxn()
}

func (aggregator *MetricAggregator) cleanupTxn() {

	aggregator.txnOffset, aggregator.txnPartition = 4, -1

	clear(aggregator.txnEntries)
}

/*
While writing the aggregated data, we can see a scenario where for that interval already some data is written, so we have to aggregate the data after merging the old and new data.
*/
func (aggregator *MetricAggregator) merge(plugin, tick, interval, storeName, metric string, datastoreType int, numericProbe, instance bool) {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			aggregator.logger.Error(fmt.Sprintf("error %v occurred while merge metric aggregation for metric %v , plugin %v and interval %v", err, metric, plugin, interval))

			aggregator.logger.Error(fmt.Sprintf("!!!STACK TRACE for metric aggregator !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			aggregator.pendingProbe = true
		}

		if aggregator.pendingProbe {

			dataType := datastore.IntegerColumn

			if !numericProbe {

				dataType = datastore.StringColumn

			}

			/*
				due to error occurred for some reason we send this notification to aggregation job manager
				which then will aggregate the data for this specific tick
			*/
			utils.ManagerNotifications <- utils.MotadataMap{

				utils.Interval: StringToINT(interval),
				utils.Plugin:   plugin,
				utils.Tick:     utils.SecondsToUnix(StringToINT32(tick)),
				utils.EventContext: utils.MotadataMap{
					utils.Type: datastoreType,
					metric:     float64(dataType),
				},
				utils.DatastoreFormat: utils.VerticalFormat,
				utils.OperationType:   utils.Recover,
			}
		}
	}()

	resolvedDataStoreType := utils.MetricAggregation

	if utils.DatastoreType(datastoreType) == utils.NetRouteStatusMetric {

		resolvedDataStoreType = utils.NetRouteStatusMetricAggregation

	}

	err, objectPoolIndex := aggregator.processMerge(plugin, tick, storeName, numericProbe, instance, resolvedDataStoreType)

	if len(err) > 0 {

		aggregator.logger.Error(err)

		if objectPoolIndex != utils.NotAvailable {

			aggregator.encoder.MemoryPool.ReleaseStringPool(objectPoolIndex)
		}
	}
}

// this is internal use for merge method
func (aggregator *MetricAggregator) processMerge(plugin, tick, storeName string, numericProbe, instance bool, resolvedDatastoreType utils.DatastoreType) (string, int) {

	if store := datastore.GetStore(storeName, resolvedDatastoreType, true, true, aggregator.encoder, aggregator.tokenizers[1]); store != nil {

		var err error

		defer aggregator.cleanupTxn()

		parts := int(store.GetMaxPart(utils.GetHash64([]byte(tick + utils.KeySeparator + plugin))))

		exist := false

		probes := map[string]struct{}{} //stores current part objects

		groupByColumn := datastore.Object

		instanceType := utils.Empty

		if instance {

			utils.Split(storeName, utils.HyphenSeparator, aggregator.tokenizers[0])

			if aggregator.tokenizers[0].Counts > 3 {

				utils.Split(aggregator.tokenizers[0].Tokens[2], utils.InstanceSeparator, aggregator.tokenizers[0])

				instanceType = aggregator.tokenizers[0].Tokens[0]

			} else {

				return "instance type not found", utils.NotAvailable
			}

		}

		for part := 0; part <= parts; part++ {

			aggregator.cleanupTxn()

			aggregator.txnPartition = store.GetPartition([]byte(tick), aggregator.tokenizers[1])

			var mappingStore *storage.Store

			if instance {

				mappingStore = datastore.GetStore(instanceType+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, aggregator.encoder, aggregator.tokenizers[1])

				aggregator.keyBuffers[0] = []byte(tick + utils.KeySeparator + plugin + utils.KeySeparator + datastore.Object + utils.OrdinalSuffix + utils.KeySeparator + INTToStringValue(part))
			} else {

				groupByColumn = utils.ObjectId

				if plugin == datastore.NetRouteStatusPlugin {

					groupByColumn = utils.NetRouteId
				}

				mappingStore = datastore.GetStore(groupByColumn+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, aggregator.encoder, aggregator.tokenizers[1])

				aggregator.keyBuffers[0] = []byte(tick + utils.KeySeparator + plugin + utils.KeySeparator + groupByColumn + utils.OrdinalSuffix + utils.KeySeparator + INTToStringValue(part))
			}

			if mappingStore == nil {

				return fmt.Sprintf(utils.ErrorAcquireStore, groupByColumn), utils.NotAvailable
			}

			found, valueBytes, _ := store.Get(aggregator.keyBuffers[0], aggregator.valueBuffers[0], aggregator.encoder, aggregator.event, aggregator.waitGroup, aggregator.tokenizers[1], true)

			if found && len(valueBytes) > 0 {

				exist = true

				ordinalPoolIndex := 0

				var ordinals []int32

				//resolve ordinals of existing objects
				ordinalPoolIndex, ordinals, err = aggregator.decoder.DecodeINT32Values(GetEncoding(valueBytes[0]), Int32, valueBytes[1:], string(aggregator.keyBuffers[0]), storeName, 0)

				if err != nil {

					return fmt.Sprintf("error %v occurred while decode the ordinals for key %v , plugin %v , tick %v and part %v", err, string(aggregator.keyBuffers[0]), plugin, tick, part), utils.NotAvailable
				}

				mapperPoolIndex := 0

				objectElementSize := len(ordinals)

				var dataType DataType

				err, dataType, mapperPoolIndex = mappingStore.ResolveMapping(ordinalPoolIndex, aggregator.encoder, aggregator.stringMappings, aggregator.numericMappings, objectElementSize)

				if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

					return fmt.Sprintf("error %v occurred while resolve object ordinals for key %v , plugin %v , tick %v and part %v", err, string(aggregator.keyBuffers[0]), plugin, tick, part), utils.NotAvailable
				}

				qualifiedObjects := map[int]string{}

				objectPoolIndex, objects := aggregator.encoder.MemoryPool.AcquireStringPool(objectElementSize)

				if dataType == String {

					for i, value := range aggregator.encoder.MemoryPool.GetStringPool(mapperPoolIndex)[:objectElementSize] {

						objects[i] = value

						if aggregator.objects.Has(objects[i]) {

							qualifiedObjects[i] = objects[i]

							probes[objects[i]] = struct{}{}
						}
					}

					aggregator.encoder.MemoryPool.ReleaseStringPool(mapperPoolIndex)

				} else {

					for i, value := range aggregator.encoder.MemoryPool.GetINT64Pool(mapperPoolIndex)[:objectElementSize] {

						objects[i] = INT64ToStringValue(value)

						if aggregator.objects.Has(objects[i]) {

							qualifiedObjects[i] = objects[i]

							probes[objects[i]] = struct{}{}
						}
					}

					aggregator.encoder.MemoryPool.ReleaseINT64Pool(mapperPoolIndex)
				}

				aggregator.encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

				// means no changes in current part
				if len(qualifiedObjects) == 0 {

					aggregator.encoder.MemoryPool.ReleaseStringPool(objectPoolIndex)

					continue
				}

				if numericProbe {

					// means numeric aggregations
					for i, aggregation := range utils.VerticalAggregationFuncs {

						aggregator.keyBuffers[i] = []byte(tick + utils.KeySeparator + plugin + utils.KeySeparator + aggregation + utils.KeySeparator + INTToStringValue(part))
					}

					var valueBuffers [][]byte

					var errs []error

					valueBuffers, errs, err = store.GetMultiples(aggregator.keyBuffers[:len(utils.VerticalAggregationFuncs)], aggregator.valueBuffers[:len(utils.VerticalAggregationFuncs)], aggregator.encoder, aggregator.events, aggregator.waitGroup, aggregator.tokenizers[1], true)

					if err != nil {

						return fmt.Sprintf("error %v occurred while doing io of numeric aggregation for key %v , tick %v and part %v", err, plugin, tick, part), objectPoolIndex
					}

					for k := 0; k < len(utils.VerticalAggregationFuncs); k++ {

						if errs[k] != nil || (valueBuffers[k] == nil || len(valueBuffers[k]) == 0) {

							aggregator.pendingProbe = true

							if errs[k] != nil {

								return fmt.Sprintf(utils.ErrorDiskIO, errs[k], plugin, tick, part), objectPoolIndex

							} else {

								return fmt.Sprintf(utils.ErrorDecodeKeyValues, string(aggregator.keyBuffers[k]), plugin, tick, part), objectPoolIndex
							}
						}

						dataType = GetDataType(valueBuffers[k][0])

						index := -1

						if dataType == Float8 || dataType == Float16 || dataType == Float64 {

							if dataType == Float8 {

								index, _, err = aggregator.decoder.DecodeFLOAT8Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

							} else if dataType == Float16 {

								index, _, err = aggregator.decoder.DecodeFLOAT16Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

							} else if dataType == Float64 {

								index, _, err = aggregator.decoder.DecodeFLOAT64Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)
							}

							if err != nil {

								return fmt.Sprintf(utils.ErrorDecodeDatatype, err, dataType, string(aggregator.keyBuffers[k]), plugin, tick, part), objectPoolIndex
							}

							if utils.VerticalAggregationFuncs[k] == utils.Sum {

								err = aggregator.doSumAggregationFLOAT(index, qualifiedObjects, aggregator.keyBuffers[k])

							} else if utils.VerticalAggregationFuncs[k] == utils.Min {

								err = aggregator.doMinAggregationFLOAT(index, qualifiedObjects, aggregator.keyBuffers[k])

							} else if utils.VerticalAggregationFuncs[k] == utils.Max {

								err = aggregator.doMaxAggregationFLOAT(index, qualifiedObjects, aggregator.keyBuffers[k])

							} else if utils.VerticalAggregationFuncs[k] == utils.Last {

								err = aggregator.doLastAggregationFLOAT(index, qualifiedObjects, aggregator.keyBuffers[k])
							}

							if err != nil {

								return fmt.Sprintf(utils.ErrorEncodeDatatype, err, dataType, string(aggregator.keyBuffers[k]), plugin, tick, part), objectPoolIndex
							}
						} else {

							poolIndex := 0

							var values []int64

							if dataType == Int8 {

								var int8Values []int8

								poolIndex, int8Values, err = aggregator.decoder.DecodeINT8Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

								if err != nil {

									return fmt.Sprintf(utils.ErrorDecodeDatatype, err, dataType, string(aggregator.keyBuffers[k]), plugin, tick, part), objectPoolIndex
								}

								index, values = aggregator.encoder.MemoryPool.AcquireINT64Pool(len(int8Values))

								aggregator.encoder.MemoryPool.ResetINT64Pool(index, len(values), 0)

								INT8ToINT64Values(int8Values, values)

								aggregator.encoder.MemoryPool.ReleaseINT8Pool(poolIndex)

							} else if dataType == Int16 {

								var int16Values []int16

								poolIndex, int16Values, err = aggregator.decoder.DecodeINT16Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

								if err != nil {

									return fmt.Sprintf(utils.ErrorDecodeDatatype, err, dataType, string(aggregator.keyBuffers[k]), plugin, tick, part), objectPoolIndex
								}

								index, values = aggregator.encoder.MemoryPool.AcquireINT64Pool(len(int16Values))

								aggregator.encoder.MemoryPool.ResetINT64Pool(index, len(values), 0)

								INT16ToINT64Values(int16Values, values)

								aggregator.encoder.MemoryPool.ReleaseINT16Pool(poolIndex)

							} else if dataType == Int24 || dataType == Int32 {

								var int32Values []int32

								poolIndex, int32Values, err = aggregator.decoder.DecodeINT32Values(GetEncoding(valueBuffers[k][0]), dataType, valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

								if err != nil {

									return fmt.Sprintf(utils.ErrorDecodeDatatype, err, dataType, string(aggregator.keyBuffers[k]), plugin, tick, part), objectPoolIndex
								}

								index, values = aggregator.encoder.MemoryPool.AcquireINT64Pool(len(int32Values))

								aggregator.encoder.MemoryPool.ResetINT64Pool(index, len(values), 0)

								INT32ToINT64Values(int32Values, values)

								aggregator.encoder.MemoryPool.ReleaseINT32Pool(poolIndex)
							} else {

								index, values, err = aggregator.decoder.DecodeINT64Values(GetEncoding(valueBuffers[k][0]), dataType, valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

								if err != nil {

									return fmt.Sprintf(utils.ErrorDecodeDatatype, err, dataType, string(aggregator.keyBuffers[k]), plugin, tick, part), objectPoolIndex
								}
							}

							if utils.VerticalAggregationFuncs[k] == utils.Sum {

								err = aggregator.doSumAggregationINT(index, qualifiedObjects, aggregator.keyBuffers[k])

							} else if utils.VerticalAggregationFuncs[k] == utils.Min {

								err = aggregator.doMinAggregationINT(index, qualifiedObjects, aggregator.keyBuffers[k])

							} else if utils.VerticalAggregationFuncs[k] == utils.Max {

								err = aggregator.doMaxAggregationINT(index, qualifiedObjects, aggregator.keyBuffers[k])

							} else if utils.VerticalAggregationFuncs[k] == utils.Count {

								err = aggregator.doCountAggregation(index, qualifiedObjects, aggregator.keyBuffers[k])

							} else if utils.VerticalAggregationFuncs[k] == utils.Last {

								err = aggregator.doLastAggregationINT(index, qualifiedObjects, aggregator.keyBuffers[k])
							}

							if err != nil {

								return fmt.Sprintf(utils.ErrorDecodeDatatype, err, dataType, string(aggregator.keyBuffers[k]), plugin, tick, part), objectPoolIndex
							}
						}
					}
				} else {

					// for string values we only do one aggregation ( last )

					aggregator.keyBuffers[0] = []byte(tick + utils.KeySeparator + plugin + utils.KeySeparator + utils.VerticalStringAggregationFuncs[0] + utils.KeySeparator + INTToStringValue(part))

					found, valueBytes, err = store.Get(aggregator.keyBuffers[0], aggregator.valueBuffers[0], aggregator.encoder, aggregator.event, aggregator.waitGroup, aggregator.tokenizers[1], true)

					blobPoolIndex := utils.NotAvailable

					var blobBytes []byte

					if err != nil {

						if strings.Contains(err.Error(), utils.ErrorTooLarge) {

							blobPoolIndex, blobBytes = aggregator.decoder.MemoryPool.AcquireBlobPool(utils.NotAvailable)

							found, valueBytes, err = store.Get(aggregator.keyBuffers[0], blobBytes, aggregator.encoder, aggregator.event, aggregator.waitGroup, aggregator.tokenizers[1], true)

						}
					}

					if err != nil {

						if blobPoolIndex != utils.NotAvailable {

							aggregator.decoder.MemoryPool.ReleaseBytePool(blobPoolIndex)
						}

						return fmt.Sprintf("error %v occurred while doing io of string aggregation for key %v , tick %v and part %v", err, plugin, tick, part), objectPoolIndex
					}

					index := -1

					index, _, err = aggregator.decoder.DecodeStringValues(GetEncoding(valueBytes[0]), valueBytes[1:], string(aggregator.keyBuffers[0]), store.GetName(), 0)

					if blobPoolIndex != utils.NotAvailable {

						aggregator.decoder.MemoryPool.ReleaseBytePool(blobPoolIndex)
					}

					if err != nil {

						return fmt.Sprintf(utils.ErrorDecodeValues, string(aggregator.keyBuffers[0]), store.GetName(), err), objectPoolIndex
					}

					if err = aggregator.doLastAggregationString(index, objectElementSize, qualifiedObjects, aggregator.keyBuffers[0]); err != nil {

						return fmt.Sprintf(utils.ErrorEncodeDatatype, err, String, string(aggregator.keyBuffers[0]), plugin, tick, part), objectPoolIndex
					}
				}

				aggregator.commit(store, part, tick)

				aggregator.encoder.MemoryPool.ReleaseStringPool(objectPoolIndex)
			}
		}

		if numericProbe {

			for key := range probes {

				aggregator.int64Fields.Delete(key)

				aggregator.float64Fields.Delete(key)

				aggregator.objects.Delete(key)
			}
		} else {

			for key := range probes {

				aggregator.stringFields.Delete(key)

				aggregator.objects.Delete(key)
			}
		}

		//Here, we see that some data remains after merging, which is new, so we have to append rather than merge.
		if aggregator.objects.Count() > 0 {

			aggregator.cleanupPool(false)

			aggregator.append(store, tick, plugin, exist, numericProbe, instance)
		}
	}

	return utils.Empty, utils.NotAvailable
}

/*
When new data arrives, we must append new data.
*/
func (aggregator *MetricAggregator) append(store *storage.Store, tick, plugin string, exist, numericProbe, instance bool) {

	groupByColumn := utils.ObjectId

	if plugin == datastore.NetRouteStatusPlugin {

		groupByColumn = utils.NetRouteId
	}

	existingPart := int(store.GetMaxPart(utils.GetHash64([]byte(tick + utils.KeySeparator + plugin))))

	part := existingPart

	aggregations := utils.VerticalAggregationFuncs

	aggregator.txnPartition = store.GetPartition([]byte(tick), aggregator.tokenizers[0])

	defer aggregator.cleanupTxn()

	if numericProbe {

		if aggregator.int64Fields.Count() > 0 || aggregator.float64Fields.Count() > 0 {

			// migrating the data type to the highest one if there are two datatype entries for single object
			aggregator.int64ToFloat64Values()

			batchSize := aggregator.int64Fields.Count() + aggregator.float64Fields.Count()

			objects := swiss.NewMap[string, int](uint32(batchSize))

			if aggregator.int64Fields.Count() > 0 {

				aggregator.int64Fields.Iter(func(key string, _ []int64) (stop bool) {

					objects.Put(key, objects.Count())

					return stop
				})

			} else {

				aggregator.float64Fields.Iter(func(key string, _ []float64) (stop bool) {

					objects.Put(key, objects.Count())

					return stop
				})

			}

			// Here, we check whether there is any data in the current part for the key.
			err, objectElementSize, _, overflowed, valueBuffers := aggregator.probe(batchSize, tick, part, store, aggregations, plugin, exist, instance)

			if err != nil {

				return
			}

			//If an overflow occurs, we do not need to see the current key; instead, we simply write data into a new part.
			if overflowed {
				//we increase the part by one

				if _, ok := aggregator.poolIndices[datastore.Object+utils.KeySeparator+utils.Last]; ok {

					if instance {

						aggregator.memoryPool.ReleaseINT32Pool(aggregator.poolIndices[datastore.Object+utils.KeySeparator+utils.Last])

						delete(aggregator.poolIndices, datastore.Object+utils.KeySeparator+utils.Last)

						delete(aggregator.dataTypes, datastore.Object+utils.KeySeparator+utils.Last)
					}
				}

				if _, ok := aggregator.poolIndices[groupByColumn+utils.KeySeparator+utils.Last]; ok {

					aggregator.memoryPool.ReleaseINT32Pool(aggregator.poolIndices[groupByColumn+utils.KeySeparator+utils.Last])

					delete(aggregator.poolIndices, groupByColumn+utils.KeySeparator+utils.Last)

					delete(aggregator.dataTypes, groupByColumn+utils.KeySeparator+utils.Last)
				}

				part++

				objectElementSize = 0
			}

			if aggregator.int64Fields.Count() > 0 {

				for k, aggregation := range aggregations {

					var int64Values []int64

					aggregator.poolIndices[aggregation], int64Values = aggregator.encoder.MemoryPool.AcquireINT64Pool(aggregator.int64Fields.Count())

					aggregator.encoder.MemoryPool.ResetINT64Pool(aggregator.poolIndices[aggregation], len(int64Values), 0)

					aggregator.int64Fields.Iter(func(key string, values []int64) (stop bool) {

						index, ok := objects.Get(key)

						if !ok {

							return stop
						}

						if k == 0 {

							if instance {

								aggregator.encoder.MemoryPool.GetStringPool(aggregator.poolIndices[datastore.Object])[index] = key

								utils.Split(key, utils.GroupSeparator, aggregator.tokenizers[0])

								aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[groupByColumn])[index] = StringToINT64(aggregator.tokenizers[0].Tokens[0])

							} else {

								aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[groupByColumn])[index] = StringToINT64(key)
							}
						}

						if aggregation == utils.Sum {

							int64Values[index] = utils.SumINT64(values)

						} else if aggregation == utils.Max {

							int64Values[index] = utils.MaxINT64(values)

						} else if aggregation == utils.Min {

							int64Values[index] = utils.MinINT64(values)

						} else if aggregation == utils.Count {

							int64Values[index] = int64(len(values))

						} else if aggregation == utils.Last {

							int64Values[index] = values[len(values)-1]
						}

						return stop
					})

					currentDataType := GetMaxDataTypeINT64Values(int64Values)

					updated := true

					//This is for when new data can be added without overflowing.
					if objectElementSize > 0 && !overflowed {

						previousDataType := GetDataType(valueBuffers[k][0])

						updated = false

						if previousDataType == Float8 || previousDataType == Float16 || previousDataType == Float64 {

							index := -1

							poolIndex, values := aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(len(int64Values))

							aggregator.encoder.MemoryPool.ResetFLOAT64Pool(poolIndex, len(values), 0)

							INT64ToFLOAT64Values(int64Values, values)

							aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

							delete(aggregator.poolIndices, aggregation)

							aggregator.dataTypes[aggregation] = Float64

							size := len(values)

							if previousDataType == Float8 {

								index, _, err = aggregator.decoder.DecodeFLOAT8Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

							} else if previousDataType == Float16 {

								index, _, err = aggregator.decoder.DecodeFLOAT16Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

							} else if previousDataType == Float64 {

								index, _, err = aggregator.decoder.DecodeFLOAT64Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)
							}

							if err != nil {

								aggregator.memoryPool.ReleaseFLOAT64Pool(poolIndex)

								aggregator.pendingProbe = true

								aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

								return
							}

							tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(objectElementSize + len(values))

							size = len(tempValues)

							aggregator.encoder.MemoryPool.ResetFLOAT64Pool(tempPoolIndex, objectElementSize+len(values), 0)

							copy(tempValues, aggregator.encoder.MemoryPool.GetFLOAT64Pool(index)[:objectElementSize])

							copy(tempValues[objectElementSize:], values)

							aggregator.poolIndices[aggregation] = tempPoolIndex

							aggregator.memoryPool.ReleaseFLOAT64Pool(poolIndex)

							aggregator.memoryPool.ReleaseFLOAT64Pool(index)

							if !aggregator.writeAggregationValues(store, []byte(tick+utils.KeySeparator+plugin+utils.KeySeparator+aggregation+utils.KeySeparator+INTToStringValue(part)), GetMaxDataTypeFLOAT64Values(tempValues), aggregator.poolIndices[aggregation], size, aggregation) {

								return
							}

							continue

						} else if previousDataType != currentDataType {

							size := len(int64Values)

							err, size = aggregator.toINT64Values(valueBuffers, k, store, previousDataType, currentDataType, aggregation, plugin, objectElementSize)

							if err != nil {

								return
							}

							if err = aggregator.write(nil, nil, aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[aggregation])[:size], aggregator.keyBuffers[k], GetMaxDataTypeINT64Values(aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[aggregation])[:size]), aggregation); err != nil {

								aggregator.pendingProbe = true

								aggregator.logger.Error(fmt.Sprintf("error %v occurred while encoding %v datatype key %v , key %v", err, currentDataType, string(aggregator.keyBuffers[k]), plugin))

								return
							}
						} else {

							poolIndex := -1

							size := 0

							if currentDataType == Int8 {

								var decodedValues []int8

								poolIndex, decodedValues, err = aggregator.decoder.DecodeINT8Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

								if err != nil {

									aggregator.pendingProbe = true

									aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

									return
								}

								index, values := aggregator.encoder.MemoryPool.AcquireINT8Pool(objectElementSize + len(int64Values))

								aggregator.encoder.MemoryPool.ResetINT8Pool(index, len(values), 0)

								copy(values, decodedValues[:objectElementSize])

								tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT8Pool(len(int64Values))

								aggregator.encoder.MemoryPool.ResetINT8Pool(tempPoolIndex, len(tempValues), 0)

								INT64ToINT8Values(int64Values, tempValues)

								copy(values[objectElementSize:], tempValues)

								aggregator.encoder.MemoryPool.ReleaseINT8Pool(tempPoolIndex)

								aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

								aggregator.encoder.MemoryPool.ReleaseINT8Pool(poolIndex)

								aggregator.poolIndices[aggregation] = index

								size = len(values)

							} else if currentDataType == Int16 {

								var decodedValues []int16

								poolIndex, decodedValues, err = aggregator.decoder.DecodeINT16Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

								if err != nil {

									aggregator.pendingProbe = true

									aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

									return
								}

								index, values := aggregator.encoder.MemoryPool.AcquireINT16Pool(objectElementSize + len(int64Values))

								aggregator.encoder.MemoryPool.ResetINT16Pool(index, len(values), 0)

								copy(values, decodedValues[:objectElementSize])

								tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT16Pool(len(int64Values))

								aggregator.encoder.MemoryPool.ResetINT16Pool(tempPoolIndex, len(tempValues), 0)

								INT64ToINT16Values(int64Values, tempValues)

								copy(values[objectElementSize:], tempValues)

								aggregator.encoder.MemoryPool.ReleaseINT16Pool(tempPoolIndex)

								aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

								aggregator.encoder.MemoryPool.ReleaseINT16Pool(poolIndex)

								aggregator.poolIndices[aggregation] = index

								size = len(values)

							} else if currentDataType == Int24 || currentDataType == Int32 {

								var decodedValues []int32

								poolIndex, decodedValues, err = aggregator.decoder.DecodeINT32Values(GetEncoding(valueBuffers[k][0]), currentDataType, valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

								if err != nil {

									aggregator.pendingProbe = true

									aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

									return
								}

								valuePoolIndex, values := aggregator.encoder.MemoryPool.AcquireINT32Pool(objectElementSize + len(int64Values))

								aggregator.encoder.MemoryPool.ResetINT32Pool(valuePoolIndex, len(values), 0)

								copy(values, decodedValues[:objectElementSize])

								tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT32Pool(len(int64Values))

								aggregator.encoder.MemoryPool.ResetINT32Pool(tempPoolIndex, len(tempValues), 0)

								INT64ToINT32Values(int64Values, tempValues)

								copy(values[objectElementSize:], tempValues)

								aggregator.encoder.MemoryPool.ReleaseINT32Pool(tempPoolIndex)

								aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

								aggregator.encoder.MemoryPool.ReleaseINT32Pool(poolIndex)

								aggregator.poolIndices[aggregation] = valuePoolIndex

								size = len(values)
							} else {

								var decodedValues []int64

								poolIndex, decodedValues, err = aggregator.decoder.DecodeINT64Values(GetEncoding(valueBuffers[k][0]), currentDataType, valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

								if err != nil {

									aggregator.pendingProbe = true

									aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

									return
								}

								index, values := aggregator.encoder.MemoryPool.AcquireINT64Pool(objectElementSize + len(int64Values))

								aggregator.encoder.MemoryPool.ResetINT64Pool(index, len(values), 0)

								copy(values, decodedValues[:objectElementSize])

								copy(values[objectElementSize:], int64Values)

								aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

								aggregator.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

								aggregator.poolIndices[aggregation] = index

								size = len(values)
							}

							aggregator.dataTypes[aggregation] = currentDataType

							if !aggregator.writeAggregationValues(store, []byte(tick+utils.KeySeparator+plugin+utils.KeySeparator+aggregation+utils.KeySeparator+INTToStringValue(part)), currentDataType, aggregator.poolIndices[aggregation], size, aggregation) {

								return
							}
						}
					}

					//This occurs when data cannot be written in the existing part and must be written in a new part.
					if updated {

						if currentDataType == Int8 {

							index, values := aggregator.encoder.MemoryPool.AcquireINT8Pool(len(int64Values))

							aggregator.encoder.MemoryPool.ResetINT8Pool(index, len(int64Values), 0)

							INT64ToINT8Values(int64Values, values)

							aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

							aggregator.poolIndices[aggregation] = index

						} else if currentDataType == Int16 {

							index, values := aggregator.encoder.MemoryPool.AcquireINT16Pool(len(int64Values))

							aggregator.encoder.MemoryPool.ResetINT16Pool(index, len(int64Values), 0)

							INT64ToINT16Values(int64Values, values)

							aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

							aggregator.poolIndices[aggregation] = index

						} else if currentDataType == Int24 || currentDataType == Int32 {

							valuePoolIndex, values := aggregator.encoder.MemoryPool.AcquireINT32Pool(len(int64Values))

							aggregator.encoder.MemoryPool.ResetINT32Pool(valuePoolIndex, len(int64Values), 0)

							INT64ToINT32Values(int64Values, values)

							aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

							aggregator.poolIndices[aggregation] = valuePoolIndex
						}

						aggregator.dataTypes[aggregation] = currentDataType

						if !aggregator.writeAggregationValues(store, []byte(tick+utils.KeySeparator+plugin+utils.KeySeparator+aggregation+utils.KeySeparator+INTToStringValue(part)), currentDataType, aggregator.poolIndices[aggregation], len(int64Values), aggregation) {

							return
						}
					}
				}

			} else if aggregator.float64Fields.Count() > 0 {

				var int64Values []int64

				var float64Values []float64

				poolIndex := 0

				for k, aggregation := range aggregations {

					if aggregation == utils.Count {

						aggregator.poolIndices[aggregation], int64Values = aggregator.encoder.MemoryPool.AcquireINT64Pool(aggregator.float64Fields.Count())

						aggregator.encoder.MemoryPool.ResetINT64Pool(aggregator.poolIndices[aggregation], len(int64Values), 0)

					} else {

						aggregator.poolIndices[aggregation], float64Values = aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(aggregator.float64Fields.Count())

						aggregator.encoder.MemoryPool.ResetFLOAT64Pool(aggregator.poolIndices[aggregation], len(float64Values), 0)

					}

					aggregator.float64Fields.Iter(func(key string, values []float64) (stop bool) {

						index, ok := objects.Get(key)

						if !ok {

							return stop
						}

						if k == 0 {

							if instance {

								aggregator.encoder.MemoryPool.GetStringPool(aggregator.poolIndices[datastore.Object])[index] = key

								utils.Split(key, utils.GroupSeparator, aggregator.tokenizers[0])

								aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[groupByColumn])[index] = StringToINT64(aggregator.tokenizers[0].Tokens[0])
							} else {

								aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[groupByColumn])[index] = StringToINT64(key)
							}
						}
						if aggregation == utils.Sum {

							float64Values[index] = utils.SumFLOAT64(values)

						} else if aggregation == utils.Max {

							float64Values[index] = utils.MaxFLOAT64(values)

						} else if aggregation == utils.Min {

							float64Values[index] = utils.MinFLOAT64(values)

						} else if aggregation == utils.Count {

							int64Values[index] = int64(len(values))

						} else if aggregation == utils.Last {

							float64Values[index] = values[len(values)-1]
						}

						return stop
					})

					currentDataType := Invalid

					updated := true

					if aggregation == utils.Count {

						if objectElementSize > 0 && !overflowed {

							updated = false

							previousDataType := GetDataType(valueBuffers[k][0])

							currentDataType = GetMaxDataTypeINT64Values(int64Values)

							size := len(int64Values)

							if previousDataType == currentDataType {

								if currentDataType == Int8 {

									var decodedValues []int8

									poolIndex, decodedValues, err = aggregator.decoder.DecodeINT8Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

									if err != nil {

										aggregator.pendingProbe = true

										aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

										return
									}

									index, values := aggregator.encoder.MemoryPool.AcquireINT8Pool(objectElementSize + len(int64Values))

									aggregator.encoder.MemoryPool.ResetINT8Pool(index, len(values), 0)

									copy(values, decodedValues[:objectElementSize])

									tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT8Pool(len(int64Values))

									aggregator.encoder.MemoryPool.ResetINT8Pool(tempPoolIndex, len(tempValues), 0)

									INT64ToINT8Values(int64Values, tempValues)

									copy(values[objectElementSize:], tempValues)

									aggregator.encoder.MemoryPool.ReleaseINT8Pool(tempPoolIndex)

									aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

									aggregator.encoder.MemoryPool.ReleaseINT8Pool(poolIndex)

									aggregator.poolIndices[aggregation] = index

									size = len(values)

								} else if currentDataType == Int16 {

									var decodedValues []int16

									poolIndex, decodedValues, err = aggregator.decoder.DecodeINT16Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

									if err != nil {

										aggregator.pendingProbe = true

										aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

										return
									}

									index, values := aggregator.encoder.MemoryPool.AcquireINT16Pool(objectElementSize + len(int64Values))

									aggregator.encoder.MemoryPool.ResetINT16Pool(index, len(values), 0)

									copy(values, decodedValues[:objectElementSize])

									tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT16Pool(len(int64Values))

									aggregator.encoder.MemoryPool.ResetINT16Pool(tempPoolIndex, len(tempValues), 0)

									INT64ToINT16Values(int64Values, tempValues)

									copy(values[objectElementSize:], tempValues)

									aggregator.encoder.MemoryPool.ReleaseINT16Pool(tempPoolIndex)

									aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

									aggregator.encoder.MemoryPool.ReleaseINT16Pool(poolIndex)

									aggregator.poolIndices[aggregation] = index

									size = len(values)

								} else if currentDataType == Int24 || currentDataType == Int32 {

									var decodedValues []int32

									poolIndex, decodedValues, err = aggregator.decoder.DecodeINT32Values(GetEncoding(valueBuffers[k][0]), currentDataType, valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

									if err != nil {

										aggregator.pendingProbe = true

										aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

										return
									}

									valuePoolIndex, values := aggregator.encoder.MemoryPool.AcquireINT32Pool(objectElementSize + len(int64Values))

									aggregator.encoder.MemoryPool.ResetINT32Pool(valuePoolIndex, len(values), 0)

									copy(values, decodedValues[:objectElementSize])

									tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT32Pool(len(int64Values))

									aggregator.encoder.MemoryPool.ResetINT32Pool(tempPoolIndex, len(tempValues), 0)

									INT64ToINT32Values(int64Values, tempValues)

									copy(values[objectElementSize:], tempValues)

									aggregator.encoder.MemoryPool.ReleaseINT32Pool(tempPoolIndex)

									aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

									aggregator.encoder.MemoryPool.ReleaseINT32Pool(poolIndex)

									aggregator.poolIndices[aggregation] = valuePoolIndex

									size = len(values)

								} else {

									var decodedValues []int64

									poolIndex, decodedValues, err = aggregator.decoder.DecodeINT64Values(GetEncoding(valueBuffers[k][0]), currentDataType, valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

									if err != nil {

										aggregator.pendingProbe = true

										aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

										return
									}

									index, values := aggregator.encoder.MemoryPool.AcquireINT64Pool(objectElementSize + len(int64Values))

									aggregator.encoder.MemoryPool.ResetINT64Pool(index, len(values), 0)

									copy(values, decodedValues[:objectElementSize])

									copy(values[objectElementSize:], int64Values)

									aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

									aggregator.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

									aggregator.poolIndices[aggregation] = index

									size = len(values)
								}

								aggregator.dataTypes[aggregation] = currentDataType

								if !aggregator.writeAggregationValues(store, []byte(tick+utils.KeySeparator+plugin+utils.KeySeparator+aggregation+utils.KeySeparator+INTToStringValue(part)), currentDataType, aggregator.poolIndices[aggregation], size, aggregation) {

									return
								}

							} else {

								err, size = aggregator.toINT64Values(valueBuffers, k, store, previousDataType, currentDataType, aggregation, plugin, objectElementSize)

								if err != nil {

									return
								}

								if err = aggregator.write(nil, nil, aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[aggregation])[:size], aggregator.keyBuffers[k], GetMaxDataTypeINT64Values(aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[aggregation])), aggregation); err != nil {

									aggregator.pendingProbe = true

									aggregator.logger.Error(fmt.Sprintf("error %v occurred while encoding %v datatype key %v , key %v", err, currentDataType, string(aggregator.keyBuffers[k]), plugin))

									return
								}
							}
						}

						if updated {

							currentDataType = GetMaxDataTypeINT64Values(int64Values)

							aggregator.dataTypes[aggregation] = currentDataType

							if currentDataType == Int8 {

								index, values := aggregator.encoder.MemoryPool.AcquireINT8Pool(len(int64Values))

								aggregator.encoder.MemoryPool.ResetINT8Pool(index, len(int64Values), 0)

								INT64ToINT8Values(int64Values, values)

								aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

								aggregator.poolIndices[aggregation] = index

							} else if currentDataType == Int16 {

								index, values := aggregator.encoder.MemoryPool.AcquireINT16Pool(len(int64Values))

								aggregator.encoder.MemoryPool.ResetINT16Pool(index, len(int64Values), 0)

								INT64ToINT16Values(int64Values, values)

								aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

								aggregator.poolIndices[aggregation] = index

							} else if currentDataType == Int24 || currentDataType == Int32 {

								valuePoolIndex, values := aggregator.encoder.MemoryPool.AcquireINT32Pool(len(int64Values))

								aggregator.encoder.MemoryPool.ResetINT32Pool(valuePoolIndex, len(int64Values), 0)

								INT64ToINT32Values(int64Values, values)

								aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

								aggregator.poolIndices[aggregation] = valuePoolIndex
							}

							if !aggregator.writeAggregationValues(store, []byte(tick+utils.KeySeparator+plugin+utils.KeySeparator+aggregation+utils.KeySeparator+INTToStringValue(part)), currentDataType, aggregator.poolIndices[aggregation], len(int64Values), aggregation) {

								return
							}
						}

					} else {

						currentDataType = GetMaxDataTypeFLOAT64Values(float64Values)

						if objectElementSize > 0 && !overflowed {

							updated = false

							previousDataType := GetDataType(valueBuffers[k][0])

							if previousDataType == currentDataType || (previousDataType == Float8 || previousDataType == Float16 || previousDataType == Float64) {

								index := -1

								var decodedValues []float64

								if previousDataType == Float8 {

									index, decodedValues, err = aggregator.decoder.DecodeFLOAT8Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

								} else if previousDataType == Float16 {

									index, decodedValues, err = aggregator.decoder.DecodeFLOAT16Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

								} else if previousDataType == Float64 {

									index, decodedValues, err = aggregator.decoder.DecodeFLOAT64Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)
								}

								if err != nil {

									aggregator.memoryPool.ReleaseFLOAT64Pool(aggregator.poolIndices[aggregation])

									aggregator.pendingProbe = true

									aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

									return
								}

								tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(objectElementSize + len(float64Values))

								aggregator.encoder.MemoryPool.ResetFLOAT64Pool(tempPoolIndex, len(tempValues), 0)

								copy(tempValues, decodedValues[:objectElementSize])

								copy(tempValues[objectElementSize:], float64Values)

								aggregator.memoryPool.ReleaseFLOAT64Pool(aggregator.poolIndices[aggregation])

								aggregator.memoryPool.ReleaseFLOAT64Pool(index)

								aggregator.poolIndices[aggregation] = tempPoolIndex

								currentDataType = GetMaxDataTypeFLOAT64Values(decodedValues)

								aggregator.dataTypes[aggregation] = currentDataType

								if !aggregator.writeAggregationValues(store, []byte(tick+utils.KeySeparator+plugin+utils.KeySeparator+aggregation+utils.KeySeparator+INTToStringValue(part)), currentDataType, aggregator.poolIndices[aggregation], len(tempValues), aggregation) {

									return
								}
							} else {

								// previous was int and now getting float

								index := -1

								var values []float64

								size := 0

								if previousDataType == Int8 {

									var decodedValues []int8

									index, decodedValues, err = aggregator.decoder.DecodeINT8Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

									if err != nil {

										aggregator.pendingProbe = true

										aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

										return
									}

									poolIndex, values = aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(len(float64Values) + objectElementSize)

									aggregator.encoder.MemoryPool.ResetFLOAT64Pool(poolIndex, len(values), 0)

									tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(objectElementSize)

									aggregator.encoder.MemoryPool.ResetFLOAT64Pool(tempPoolIndex, objectElementSize, 0)

									INT8ToFLOAT64Values(decodedValues[:objectElementSize], tempValues)

									copy(values, tempValues)

									copy(values[objectElementSize:], float64Values)

									aggregator.encoder.MemoryPool.ReleaseFLOAT64Pool(tempPoolIndex)

									size = len(values)

									aggregator.encoder.MemoryPool.ReleaseFLOAT64Pool(aggregator.poolIndices[aggregation])

									aggregator.poolIndices[aggregation] = poolIndex

									aggregator.encoder.MemoryPool.ReleaseINT8Pool(index)

								} else if previousDataType == Int16 {

									var decodedValues []int16

									index, decodedValues, err = aggregator.decoder.DecodeINT16Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

									if err != nil {

										aggregator.pendingProbe = true

										aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

										return
									}

									poolIndex, values = aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(len(float64Values) + objectElementSize)

									aggregator.encoder.MemoryPool.ResetFLOAT64Pool(poolIndex, len(values), 0)

									tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(objectElementSize)

									aggregator.encoder.MemoryPool.ResetFLOAT64Pool(tempPoolIndex, objectElementSize, 0)

									INT16ToFLOAT64Values(decodedValues[:objectElementSize], tempValues)

									copy(values, tempValues)

									copy(values[objectElementSize:], float64Values)

									aggregator.encoder.MemoryPool.ReleaseFLOAT64Pool(tempPoolIndex)

									size = len(values)

									aggregator.encoder.MemoryPool.ReleaseFLOAT64Pool(aggregator.poolIndices[aggregation])

									aggregator.poolIndices[aggregation] = poolIndex

									aggregator.encoder.MemoryPool.ReleaseINT16Pool(index)

								} else if previousDataType == Int24 || previousDataType == Int32 {

									var decodedValues []int32

									index, decodedValues, err = aggregator.decoder.DecodeINT32Values(GetEncoding(valueBuffers[k][0]), previousDataType, valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

									if err != nil {

										aggregator.pendingProbe = true

										aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

										return
									}

									poolIndex, values = aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(len(float64Values) + objectElementSize)

									aggregator.encoder.MemoryPool.ResetFLOAT64Pool(poolIndex, len(values), 0)

									tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(objectElementSize)

									aggregator.encoder.MemoryPool.ResetFLOAT64Pool(tempPoolIndex, objectElementSize, 0)

									INT32ToFLOAT64Values(decodedValues[:objectElementSize], tempValues)

									copy(values, tempValues)

									copy(values[objectElementSize:], float64Values)

									aggregator.encoder.MemoryPool.ReleaseFLOAT64Pool(tempPoolIndex)

									size = len(values)

									aggregator.encoder.MemoryPool.ReleaseFLOAT64Pool(aggregator.poolIndices[aggregation])

									aggregator.poolIndices[aggregation] = poolIndex

									aggregator.encoder.MemoryPool.ReleaseINT32Pool(index)

								} else {

									var decodedValues []int64

									index, decodedValues, err = aggregator.decoder.DecodeINT64Values(GetEncoding(valueBuffers[k][0]), previousDataType, valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

									if err != nil {

										aggregator.pendingProbe = true

										aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

										return
									}

									poolIndex, values = aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(len(float64Values) + objectElementSize)

									aggregator.encoder.MemoryPool.ResetFLOAT64Pool(poolIndex, len(values), 0)

									tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(objectElementSize)

									aggregator.encoder.MemoryPool.ResetFLOAT64Pool(tempPoolIndex, objectElementSize, 0)

									INT64ToFLOAT64Values(decodedValues[:objectElementSize], tempValues)

									copy(values, tempValues)

									copy(values[objectElementSize:], float64Values)

									aggregator.encoder.MemoryPool.ReleaseFLOAT64Pool(tempPoolIndex)

									size = len(values)

									aggregator.encoder.MemoryPool.ReleaseFLOAT64Pool(aggregator.poolIndices[aggregation])

									aggregator.poolIndices[aggregation] = poolIndex

									aggregator.encoder.MemoryPool.ReleaseINT64Pool(index)
								}

								currentDataType = GetMaxDataTypeFLOAT64Values(aggregator.encoder.MemoryPool.GetFLOAT64Pool(aggregator.poolIndices[aggregation]))

								aggregator.dataTypes[aggregation] = currentDataType

								if !aggregator.writeAggregationValues(store, []byte(tick+utils.KeySeparator+plugin+utils.KeySeparator+aggregation+utils.KeySeparator+INTToStringValue(part)), currentDataType, aggregator.poolIndices[aggregation], size, aggregation) {

									return
								}
							}

						}

						if updated {

							aggregator.dataTypes[aggregation] = currentDataType

							if !aggregator.writeAggregationValues(store, []byte(tick+utils.KeySeparator+plugin+utils.KeySeparator+aggregation+utils.KeySeparator+INTToStringValue(part)), currentDataType, aggregator.poolIndices[aggregation], len(float64Values), aggregation) {

								return
							}
						}
					}
				}
			}

			aggregator.writeGroups(objects, tick, part, store, instance, plugin, objectElementSize)

			if aggregator.pendingProbe {

				return
			}

			if !aggregator.commit(store, part, tick) {

				return
			}

			if existingPart != part { //if previous parts do not match the current part we need to increase the multipart value

				err = store.AddMultipartKey(utils.GetHash64([]byte(tick+utils.KeySeparator+plugin)), uint16(1))

				if err != nil {

					aggregator.logger.Error(fmt.Sprintf("error %v occurred while updating multipart key", err))
				}
			}
		}
	} else { //similar to numeric version

		aggregations = utils.VerticalStringAggregationFuncs

		if aggregator.stringFields.Count() > 0 {

			objects := swiss.NewMap[string, int](uint32(aggregator.stringFields.Count()))

			aggregator.stringFields.Iter(func(key string, _ string) (stop bool) {

				objects.Put(key, objects.Count())

				return stop
			})

			err, objectElementSize, blobPoolIndex, overflowed, valueBuffers := aggregator.probe(aggregator.stringFields.Count(), tick, part, store, aggregations, plugin, exist, instance)

			if err != nil {

				if blobPoolIndex != utils.NotAvailable {

					aggregator.encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

					blobPoolIndex = utils.NotAvailable
				}

				return
			}

			if overflowed {

				if _, ok := aggregator.poolIndices[datastore.Object+utils.KeySeparator+utils.Last]; ok {

					if instance {

						aggregator.memoryPool.ReleaseINT32Pool(aggregator.poolIndices[datastore.Object+utils.KeySeparator+utils.Last])

						delete(aggregator.poolIndices, datastore.Object+utils.KeySeparator+utils.Last)

						delete(aggregator.dataTypes, datastore.Object+utils.KeySeparator+utils.Last)
					}
				}

				if _, ok := aggregator.poolIndices[groupByColumn+utils.KeySeparator+utils.Last]; ok {

					aggregator.memoryPool.ReleaseINT32Pool(aggregator.poolIndices[groupByColumn+utils.KeySeparator+utils.Last])

					delete(aggregator.poolIndices, groupByColumn+utils.KeySeparator+utils.Last)

					delete(aggregator.dataTypes, groupByColumn+utils.KeySeparator+utils.Last)
				}

				part++

				objectElementSize = 0
			}

			for k, aggregation := range aggregations {

				var stringValues []string

				aggregator.dataTypes[aggregation] = String

				aggregator.poolIndices[aggregation], stringValues = aggregator.encoder.MemoryPool.AcquireStringPool(aggregator.stringFields.Count())

				objects.Iter(func(key string, index int) (stop bool) {

					if k == 0 {

						if instance {

							aggregator.encoder.MemoryPool.GetStringPool(aggregator.poolIndices[datastore.Object])[index] = key

							utils.Split(key, utils.GroupSeparator, aggregator.tokenizers[0])

							aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[groupByColumn])[index] = StringToINT64(aggregator.tokenizers[0].Tokens[0])

						} else {

							aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[groupByColumn])[index] = StringToINT64(key)
						}
					}

					stringValues[index], _ = aggregator.stringFields.Get(key)

					return stop
				})

				dataType := String

				aggregator.dataTypes[aggregation] = dataType

				size := len(stringValues)

				if objectElementSize > 0 && !overflowed {

					index := -1

					var values []string

					index, values, err = aggregator.decoder.DecodeStringValues(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

					if err != nil {

						if blobPoolIndex != utils.NotAvailable {

							aggregator.encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

							blobPoolIndex = utils.NotAvailable
						}

						aggregator.pendingProbe = true

						aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeDatatype, err, dataType, string(aggregator.keyBuffers[k]), plugin, tick, part))

						return
					}

					tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireStringPool(objectElementSize + len(stringValues))

					size = len(tempValues)

					copy(tempValues, values[:objectElementSize])

					copy(tempValues[objectElementSize:], stringValues)

					aggregator.encoder.MemoryPool.ReleaseStringPool(aggregator.poolIndices[aggregation])

					aggregator.encoder.MemoryPool.ReleaseStringPool(index)

					aggregator.poolIndices[aggregation] = tempPoolIndex
				}

				if !aggregator.writeAggregationValues(store, []byte(tick+utils.KeySeparator+plugin+utils.KeySeparator+aggregation+utils.KeySeparator+INTToStringValue(part)), dataType, aggregator.poolIndices[aggregation], size, aggregation) {

					if blobPoolIndex != utils.NotAvailable {

						aggregator.encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

						blobPoolIndex = utils.NotAvailable
					}

					return
				}
			}

			if blobPoolIndex != utils.NotAvailable {

				aggregator.encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

				blobPoolIndex = utils.NotAvailable
			}

			aggregator.writeGroups(objects, tick, part, store, instance, plugin, objectElementSize)

			if aggregator.pendingProbe {

				return
			}

			if !aggregator.commit(store, part, tick) {

				return
			}

			if existingPart != part {

				err = store.AddMultipartKey(utils.GetHash64([]byte(tick+utils.KeySeparator+plugin)), uint16(1))

				if err != nil {

					aggregator.logger.Error(fmt.Sprintf("error %v occurred while updating multipart key", err))
				}
			}
		}
	}
}

/*
With aggregated data, we write two keys for  objects and objects.id.
where objects is id###instance where object.id has only id

this two keys represents the grouping, we only support monitor id and instance in grouping
*/
func (aggregator *MetricAggregator) writeGroups(objects *swiss.Map[string, int], tick string, part int, store *storage.Store, instance bool, plugin string, objectElementSize int) {

	groupByColumn := utils.ObjectId

	if plugin == datastore.NetRouteStatusPlugin {

		groupByColumn = utils.NetRouteId
	}

	poolIndex := utils.NotAvailable

	var err error

	size := objects.Count()

	var mappingStore *storage.Store

	var instanceType string

	if instance {

		utils.Split(store.GetName(), utils.HyphenSeparator, aggregator.tokenizers[0])

		if aggregator.tokenizers[0].Counts > 3 {

			utils.Split(aggregator.tokenizers[0].Tokens[2], utils.InstanceSeparator, aggregator.tokenizers[0])

			instanceType = aggregator.tokenizers[0].Tokens[0]

		} else {

			aggregator.pendingProbe = true

			return
		}

		mappingStore = datastore.GetStore(instanceType+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, aggregator.encoder, aggregator.tokenizers[1])

		if mappingStore == nil {

			aggregator.pendingProbe = true

			return
		}

		err, poolIndex, _ = mappingStore.MapStringValues(aggregator.poolIndices[datastore.Object], aggregator.encoder, objects.Count())

		if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

			aggregator.pendingProbe = true

			aggregator.logger.Error(fmt.Sprintf("error %v occurred while writing ordinals for tick %v and part %v", err, tick, part))

			return
		}

		if ordinalPoolIndex, ok := aggregator.poolIndices[datastore.Object+utils.KeySeparator+utils.Last]; ok && ordinalPoolIndex != utils.NotAvailable {

			tempPoolIndex, tempValues := aggregator.memoryPool.AcquireINT32Pool(objectElementSize + objects.Count())

			aggregator.memoryPool.ResetINT32Pool(tempPoolIndex, len(tempValues), 0)

			copy(tempValues, aggregator.memoryPool.GetINT32Pool(aggregator.poolIndices[datastore.Object+utils.KeySeparator+utils.Last]))

			copy(tempValues[objectElementSize:], aggregator.memoryPool.GetINT32Pool(poolIndex))

			aggregator.memoryPool.ReleaseINT32Pool(poolIndex)

			size = len(tempValues)

			poolIndex = tempPoolIndex

			defer aggregator.memoryPool.ReleaseINT32Pool(ordinalPoolIndex)

			delete(aggregator.dataTypes, datastore.Object+utils.KeySeparator+utils.Last)

			delete(aggregator.poolIndices, datastore.Object+utils.KeySeparator+utils.Last)
		}

		if !aggregator.writeAggregationValues(store, []byte(tick+utils.KeySeparator+plugin+utils.KeySeparator+datastore.Object+utils.OrdinalSuffix+utils.KeySeparator+INTToStringValue(part)), Int32, poolIndex, size, utils.Empty) {

			aggregator.encoder.MemoryPool.ReleaseINT32Pool(poolIndex)

			return
		}

		aggregator.encoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	}

	mappingStore = datastore.GetStore(groupByColumn+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, aggregator.encoder, aggregator.tokenizers[1])

	if mappingStore == nil {

		aggregator.pendingProbe = true

		return
	}

	err, poolIndex, _ = mappingStore.MapNumericValues(aggregator.poolIndices[groupByColumn], aggregator.encoder, objects.Count())

	if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

		aggregator.pendingProbe = true

		aggregator.logger.Error(fmt.Sprintf("error %v occurred while writing ordinals for tick %v and part %v", err, tick, part))

		return
	}

	size = objects.Count()

	if ordinalPoolIndex, ok := aggregator.poolIndices[groupByColumn+utils.KeySeparator+utils.Last]; ok && ordinalPoolIndex != utils.NotAvailable {

		tempPoolIndex, tempValues := aggregator.memoryPool.AcquireINT32Pool(objectElementSize + objects.Count())

		aggregator.memoryPool.ResetINT32Pool(tempPoolIndex, len(tempValues), 0)

		copy(tempValues, aggregator.memoryPool.GetINT32Pool(aggregator.poolIndices[groupByColumn+utils.KeySeparator+utils.Last]))

		copy(tempValues[objectElementSize:], aggregator.memoryPool.GetINT32Pool(poolIndex))

		aggregator.memoryPool.ReleaseINT32Pool(poolIndex)

		size = len(tempValues)

		poolIndex = tempPoolIndex

		defer aggregator.memoryPool.ReleaseINT32Pool(ordinalPoolIndex)

		delete(aggregator.dataTypes, groupByColumn+utils.KeySeparator+utils.Last)

		delete(aggregator.poolIndices, groupByColumn+utils.KeySeparator+utils.Last)
	}

	defer aggregator.encoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	if !aggregator.writeAggregationValues(store, []byte(tick+utils.KeySeparator+plugin+utils.KeySeparator+groupByColumn+utils.OrdinalSuffix+utils.KeySeparator+INTToStringValue(part)), Int32, poolIndex, size, utils.Empty) {

		return
	}
}

// This will write after encoding the provided element array.
func (aggregator *MetricAggregator) writeAggregationValues(store *storage.Store, keyBytes []byte, dataType DataType, poolIndex, size int, aggregation string) bool {

	bytePoolIndex := utils.NotAvailable

	var bufferBytes []byte

	var err error

	switch {

	case dataType == String:

		bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeStringValues(aggregator.encoder.MemoryPool.GetStringPool(poolIndex)[:size], None, utils.MaxValueBytes, string(keyBytes))

	case dataType == Float8 || dataType == Float16 || dataType == Float64:

		bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeFLOAT64Values(aggregator.encoder.MemoryPool.GetFLOAT64Pool(poolIndex)[:size], None, dataType, utils.MaxValueBytes)

	case dataType == Int8:

		bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT8Values(None, aggregator.encoder.MemoryPool.GetINT8Pool(poolIndex)[:size], utils.MaxValueBytes)

	case dataType == Int16:

		bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT16Values(None, aggregator.encoder.MemoryPool.GetINT16Pool(poolIndex)[:size], utils.MaxValueBytes)

	case dataType == Int24 || dataType == Int32:

		bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT32Values(aggregator.encoder.MemoryPool.GetINT32Pool(poolIndex)[:size], None, dataType, GetDataTypeBits(dataType), utils.MaxValueBytes)

	case dataType == Int40 || dataType == Int48 || dataType == Int56 || dataType == Int64:

		if aggregation == utils.Sum && dataType == Int64 { //for sum aggregation we convert the int64 data-types to float64 to avoid overflow

			tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(size)

			aggregator.encoder.MemoryPool.ResetFLOAT64Pool(tempPoolIndex, size, 0)

			INT64ToFLOAT64Values(aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex)[:size], tempValues)

			bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeFLOAT64Values(tempValues, None, Float64, utils.MaxValueBytes)

			aggregator.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			aggregator.dataTypes[aggregation] = Float64

			aggregator.poolIndices[aggregation] = tempPoolIndex

		} else {

			bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT64Values(aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex)[:size], None, dataType, utils.MaxValueBytes)
		}

	}

	if err != nil {

		if bytePoolIndex != utils.NotAvailable {

			aggregator.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)
		}

		aggregator.pendingProbe = true

		aggregator.logger.Error(fmt.Sprintf("error %v occurred while encoding %v datatype key %v for store %v", err, dataType, string(keyBytes), store.GetName()))

		return false
	}

	defer aggregator.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

	err = aggregator.writeTxn(keyBytes, bufferBytes)

	if err != nil {

		aggregator.pendingProbe = true

		aggregator.logger.Error(fmt.Sprintf("error %v occurred while writing transaction of  %v datatype key %v for store %v", err, dataType, string(keyBytes), store.GetName()))

		return false
	}

	return true
}

func (aggregator *MetricAggregator) write(stringValues []string, float64Values []float64, int64Values []int64, keyBytes []byte, dataType DataType, aggregation string) error {

	bytePoolIndex := utils.NotAvailable

	var bufferBytes []byte

	var err error

	switch {

	case dataType == String:

		bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeStringValues(stringValues, None, utils.MaxValueBytes, string(keyBytes))

	case dataType == Float8 || dataType == Float16 || dataType == Float64:

		bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeFLOAT64Values(float64Values, None, dataType, utils.MaxValueBytes)

	case dataType == Int8:

		tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT8Pool(len(int64Values))

		aggregator.encoder.MemoryPool.ResetINT8Pool(tempPoolIndex, len(tempValues), 0)

		INT64ToINT8Values(int64Values, tempValues)

		bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT8Values(None, tempValues, utils.MaxValueBytes)

		aggregator.encoder.MemoryPool.ReleaseINT8Pool(tempPoolIndex)

	case dataType == Int16:

		tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT16Pool(len(int64Values))

		aggregator.encoder.MemoryPool.ResetINT16Pool(tempPoolIndex, len(tempValues), 0)

		INT64ToINT16Values(int64Values, tempValues)

		bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT16Values(None, tempValues, utils.MaxValueBytes)

		aggregator.encoder.MemoryPool.ReleaseINT16Pool(tempPoolIndex)

	case dataType == Int24 || dataType == Int32:

		tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT32Pool(len(int64Values))

		aggregator.encoder.MemoryPool.ResetINT32Pool(tempPoolIndex, len(tempValues), 0)

		INT64ToINT32Values(int64Values, tempValues)

		bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT32Values(tempValues, None, dataType, GetDataTypeBits(dataType), utils.MaxValueBytes)

		aggregator.encoder.MemoryPool.ReleaseINT32Pool(tempPoolIndex)

	case dataType == Int40 || dataType == Int48 || dataType == Int56 || dataType == Int64:

		if aggregation == utils.Sum && dataType == Int64 {

			tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(len(int64Values))

			aggregator.encoder.MemoryPool.ResetFLOAT64Pool(tempPoolIndex, len(tempValues), 0)

			INT64ToFLOAT64Values(int64Values, tempValues)

			bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeFLOAT64Values(tempValues, None, Float64, utils.MaxValueBytes)

			aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

			aggregator.dataTypes[aggregation] = Float64

			aggregator.poolIndices[aggregation] = tempPoolIndex

		} else {

			bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT64Values(int64Values, None, dataType, utils.MaxValueBytes)
		}

	}

	if err != nil {

		if bytePoolIndex != utils.NotAvailable {

			aggregator.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)
		}

		return err
	}

	defer aggregator.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

	return aggregator.writeTxn(keyBytes, bufferBytes)
}

// The probe is used to determine whether the current key contains data or not, and if it does, whether it overflows after appending new data.
func (aggregator *MetricAggregator) probe(batchSize int, tick string, part int, store *storage.Store, aggregations []string, plugin string, exist, instance bool) (error, int, int, bool, [][]byte) {

	groupByColumn := ObjectId

	if plugin == datastore.NetRouteStatusPlugin {

		groupByColumn = utils.NetRouteId
	}

	objectElementSize := 0

	ordinalPoolIndex := utils.NotAvailable

	instanceOrdinalPoolIndex := utils.NotAvailable

	if exist {

		size := 1

		aggregator.keyBuffers[0] = []byte(tick + utils.KeySeparator + plugin + utils.KeySeparator + groupByColumn + utils.OrdinalSuffix + utils.KeySeparator + INTToStringValue(part))

		if instance {

			size++

			aggregator.keyBuffers[1] = []byte(tick + utils.KeySeparator + plugin + utils.KeySeparator + datastore.Object + utils.OrdinalSuffix + utils.KeySeparator + INTToStringValue(part))
		}

		valueBuffers, errs, err := store.GetMultiples(aggregator.keyBuffers[:size], aggregator.valueBuffers[:size], aggregator.encoder, aggregator.events, aggregator.waitGroup, aggregator.tokenizers[1], true)

		if err != nil {

			aggregator.pendingProbe = true

			aggregator.logger.Error(fmt.Sprintf(utils.ErrorDiskIO, err, plugin, tick, part))

			return err, objectElementSize, -1, false, nil
		}

		for i := range valueBuffers {

			if valueBuffers[i] != nil && len(valueBuffers[i]) > 0 {

				dataType := GetDataType(valueBuffers[i][0])

				if dataType == Int32 {

					var ordinals []int32

					if i == 0 {

						ordinalPoolIndex, ordinals, _ = aggregator.decoder.DecodeINT32Values(GetEncoding(valueBuffers[i][0]), Int32, valueBuffers[i][1:], string(aggregator.keyBuffers[0]), store.GetName(), 0)

						if ordinalPoolIndex != utils.NotAvailable {

							objectElementSize += len(ordinals)
						}
					} else {

						instanceOrdinalPoolIndex, _, _ = aggregator.decoder.DecodeINT32Values(GetEncoding(valueBuffers[i][0]), Int32, valueBuffers[i][1:], string(aggregator.keyBuffers[0]), store.GetName(), 0)
					}
				}
			} else {

				aggregator.pendingProbe = true

				aggregator.logger.Error(fmt.Sprintf(utils.ErrorDiskIO, errs[i], plugin, tick, part))

				return errs[i], objectElementSize, -1, false, nil
			}
		}

		if instance {

			aggregator.poolIndices[datastore.Object+utils.KeySeparator+utils.Last] = instanceOrdinalPoolIndex

			aggregator.dataTypes[datastore.Object+utils.KeySeparator+utils.Last] = Int32
		}

		aggregator.poolIndices[groupByColumn+utils.KeySeparator+utils.Last] = ordinalPoolIndex

		aggregator.dataTypes[groupByColumn+utils.KeySeparator+utils.Last] = Int32
	}

	overflowed := false
	//check overflowed
	if objectElementSize+batchSize > utils.OverflowLength {

		overflowed = true
	}

	var err error

	// no need to check for parting as we flush once it reach to overflowed length in append

	aggregator.dataTypes[groupByColumn] = Int64

	if instance {

		aggregator.dataTypes[datastore.Object] = String

		aggregator.poolIndices[datastore.Object], _ = aggregator.encoder.MemoryPool.AcquireStringPool(batchSize)
	}

	aggregator.poolIndices[groupByColumn], _ = aggregator.encoder.MemoryPool.AcquireINT64Pool(batchSize)

	aggregator.encoder.MemoryPool.ResetINT64Pool(aggregator.poolIndices[groupByColumn], batchSize, 0)

	var valueBuffers [][]byte

	var errs []error

	blobPoolIndex := utils.NotAvailable

	if objectElementSize > 0 && !overflowed { // means merge existing data with new one

		k := 0

		for _, aggregation := range aggregations {

			aggregator.keyBuffers[k] = []byte(tick + utils.KeySeparator + plugin + utils.KeySeparator + aggregation + utils.KeySeparator + INTToStringValue(part))

			k++
		}

		aggregator.keyBuffers[k] = []byte(tick + utils.KeySeparator + plugin + utils.KeySeparator + groupByColumn + utils.OrdinalSuffix + utils.KeySeparator + INTToStringValue(part))

		k++

		if instance {

			aggregator.keyBuffers[k] = []byte(tick + utils.KeySeparator + plugin + utils.KeySeparator + datastore.Object + utils.OrdinalSuffix + utils.KeySeparator + INTToStringValue(part))

			k++
		}

		valueBuffers, errs, err = store.GetMultiples(aggregator.keyBuffers[:k], aggregator.valueBuffers[:k], aggregator.encoder, aggregator.events, aggregator.waitGroup, aggregator.tokenizers[1], true)

		if err != nil {

			aggregator.pendingProbe = true

			aggregator.logger.Error(fmt.Sprintf(utils.ErrorDiskIO, err, plugin, tick, part))

			return err, objectElementSize, -1, overflowed, nil
		}

		for k = range errs {

			if errs[k] != nil || (valueBuffers[k] == nil || len(valueBuffers[k]) == 0) {

				if errs[k] != nil {

					// assuming only one string aggregation at a time
					if strings.Contains(errs[k].Error(), utils.ErrorTooLarge) {

						var blobBytes []byte

						found := false

						blobPoolIndex, blobBytes = aggregator.decoder.MemoryPool.AcquireBlobPool(utils.NotAvailable)

						found, valueBuffers[k], err = store.Get(aggregator.keyBuffers[k], blobBytes, aggregator.encoder, aggregator.event, aggregator.waitGroup, aggregator.tokenizers[1], true)

						if found || err == nil {

							continue
						}
					}

					aggregator.logger.Error(fmt.Sprintf(utils.ErrorDiskIO, errs[k], plugin, tick, part))

					return errs[k], objectElementSize, blobPoolIndex, overflowed, nil

				} else {

					aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeKeyValues, string(aggregator.keyBuffers[k]), plugin, tick, part))

					return errors.New(fmt.Sprintf(utils.ErrorDecodeKeyValues, string(aggregator.keyBuffers[k]), plugin, tick, part)), objectElementSize, blobPoolIndex, overflowed, nil
				}
			}
		}
	}

	return nil, objectElementSize, blobPoolIndex, overflowed, valueBuffers
}

func (aggregator *MetricAggregator) doSumAggregationFLOAT(index int, objects map[int]string, keyBytes []byte) error {

	values := aggregator.decoder.MemoryPool.GetFLOAT64Pool(index)

	defer aggregator.decoder.MemoryPool.ReleaseFLOAT64Pool(index)

	if aggregator.int64Fields.Count() > 0 {

		for i := range values {

			if object, ok := objects[i]; ok && aggregator.int64Fields.Has(object) {

				int64Values, _ := aggregator.int64Fields.Get(objects[i])

				values[i] += INT64ToFLOAT64Value(utils.SumINT64(int64Values))
			}
		}
	}

	if aggregator.float64Fields.Count() > 0 {

		for i := range values {

			if object, ok := objects[i]; ok && aggregator.float64Fields.Has(object) {

				float64Values, _ := aggregator.float64Fields.Get(objects[i])

				values[i] += utils.SumFLOAT64(float64Values)
			}
		}
	}

	return aggregator.write(nil, values, nil, keyBytes, GetMaxDataTypeFLOAT64Values(values), utils.Sum)

}

func (aggregator *MetricAggregator) doMinAggregationFLOAT(index int, objects map[int]string, keyBytes []byte) error {

	values := aggregator.decoder.MemoryPool.GetFLOAT64Pool(index)

	defer aggregator.decoder.MemoryPool.ReleaseFLOAT64Pool(index)

	if aggregator.int64Fields.Count() > 0 {

		for i := range values {

			if object, ok := objects[i]; ok && aggregator.int64Fields.Has(object) {

				int64Values, _ := aggregator.int64Fields.Get(objects[i])

				min := INT64ToFLOAT64Value(utils.MinINT64(int64Values))

				if min < values[i] {

					values[i] = min
				}
			}
		}
	}

	if aggregator.float64Fields.Count() > 0 {

		for i := range values {

			if object, ok := objects[i]; ok && aggregator.float64Fields.Has(object) {

				float64Values, _ := aggregator.float64Fields.Get(objects[i])

				min := utils.MinFLOAT64(float64Values)

				if min < values[i] {

					values[i] = min
				}
			}
		}
	}

	return aggregator.write(nil, values, nil, keyBytes, GetMaxDataTypeFLOAT64Values(values), utils.Min)
}

func (aggregator *MetricAggregator) doMaxAggregationFLOAT(index int, objects map[int]string, keyBytes []byte) error {

	values := aggregator.decoder.MemoryPool.GetFLOAT64Pool(index)

	defer aggregator.decoder.MemoryPool.ReleaseFLOAT64Pool(index)

	if aggregator.int64Fields.Count() > 0 {

		for i := range values {

			if object, ok := objects[i]; ok && aggregator.int64Fields.Has(object) {

				int64Values, _ := aggregator.int64Fields.Get(objects[i])

				max := INT64ToFLOAT64Value(utils.MaxINT64(int64Values))

				if max > values[i] {

					values[i] = max
				}
			}
		}
	}

	if aggregator.float64Fields.Count() > 0 {

		for i := range values {

			if object, ok := objects[i]; ok && aggregator.float64Fields.Has(object) {

				float64Values, _ := aggregator.float64Fields.Get(objects[i])

				max := utils.MaxFLOAT64(float64Values)

				if max > values[i] {

					values[i] = max
				}
			}
		}
	}

	return aggregator.write(nil, values, nil, keyBytes, GetMaxDataTypeFLOAT64Values(values), utils.Max)
}

func (aggregator *MetricAggregator) doLastAggregationFLOAT(index int, objects map[int]string, keyBytes []byte) error {

	values := aggregator.decoder.MemoryPool.GetFLOAT64Pool(index)

	defer aggregator.decoder.MemoryPool.ReleaseFLOAT64Pool(index)

	if aggregator.int64Fields.Count() > 0 {

		for i := range values {

			if object, ok := objects[i]; ok && aggregator.int64Fields.Has(object) {

				int64Values, _ := aggregator.int64Fields.Get(objects[i])

				values[i] = INT64ToFLOAT64Value(int64Values[len(int64Values)-1])
			}
		}
	}

	if aggregator.float64Fields.Count() > 0 {

		for i := range values {

			if object, ok := objects[i]; ok && aggregator.float64Fields.Has(object) {

				float64Values, _ := aggregator.float64Fields.Get(objects[i])

				values[i] = float64Values[len(float64Values)-1]
			}
		}
	}

	return aggregator.write(nil, values, nil, keyBytes, GetMaxDataTypeFLOAT64Values(values), utils.Last)
}

func (aggregator *MetricAggregator) doSumAggregationINT(index int, objects map[int]string, keyBytes []byte) error {

	values := aggregator.decoder.MemoryPool.GetINT64Pool(index)

	size := len(values)

	defer aggregator.decoder.MemoryPool.ReleaseINT64Pool(index)

	dataTypeChanged := false

	if aggregator.float64Fields.Count() > 0 {

		for i := range values {

			if _, ok := objects[i]; ok {

				dataTypeChanged = true

				break
			}
		}
	}

	currentIndex := index

	if !dataTypeChanged && aggregator.int64Fields.Count() > 0 {

		tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT64Pool(size)

		aggregator.encoder.MemoryPool.ResetINT64Pool(tempPoolIndex, size, 0)

		defer aggregator.encoder.MemoryPool.ReleaseINT64Pool(tempPoolIndex)

		copy(tempValues, values)

		for i := range values {

			if object, ok := objects[i]; ok && aggregator.int64Fields.Has(object) {

				int64Values, _ := aggregator.int64Fields.Get(object)

				values[i] += utils.SumINT64(int64Values)

				if GetDataTypeINT(int(values[i])) > Int56 {

					dataTypeChanged = true

					currentIndex = tempPoolIndex

					break
				}
			}
		}
	}

	if dataTypeChanged {

		poolIndex, float64Values := aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(size)

		aggregator.encoder.MemoryPool.ResetFLOAT64Pool(poolIndex, size, 0)

		INT64ToFLOAT64Values(aggregator.encoder.MemoryPool.GetINT64Pool(currentIndex), float64Values)

		return aggregator.doSumAggregationFLOAT(poolIndex, objects, keyBytes)
	}

	return aggregator.write(nil, nil, values, keyBytes, GetMaxDataTypeINT64Values(values), utils.Sum)
}

func (aggregator *MetricAggregator) doMinAggregationINT(index int, objects map[int]string, keyBytes []byte) error {

	values := aggregator.decoder.MemoryPool.GetINT64Pool(index)

	defer aggregator.decoder.MemoryPool.ReleaseINT64Pool(index)

	dataTypeChanged := false

	if aggregator.float64Fields.Count() > 0 {

		for i := range values {

			if _, ok := objects[i]; ok {

				dataTypeChanged = true

				break
			}
		}

		if dataTypeChanged {

			poolIndex, float64Values := aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(len(values))

			aggregator.encoder.MemoryPool.ResetFLOAT64Pool(poolIndex, len(float64Values), 0)

			INT64ToFLOAT64Values(values, float64Values)

			return aggregator.doMinAggregationFLOAT(poolIndex, objects, keyBytes)
		}
	}

	if !dataTypeChanged {

		if aggregator.int64Fields.Count() > 0 {

			for i := range values {

				if object, ok := objects[i]; ok && aggregator.int64Fields.Has(object) {

					int64Values, _ := aggregator.int64Fields.Get(object)

					min := utils.MinINT64(int64Values)

					if min < values[i] {

						values[i] = min
					}
				}
			}
		}

		return aggregator.write(nil, nil, values, keyBytes, GetMaxDataTypeINT64Values(values), utils.Min)
	}

	return nil
}

func (aggregator *MetricAggregator) doMaxAggregationINT(index int, objects map[int]string, keyBytes []byte) error {

	values := aggregator.decoder.MemoryPool.GetINT64Pool(index)

	defer aggregator.decoder.MemoryPool.ReleaseINT64Pool(index)

	dataTypeChanged := false

	if aggregator.float64Fields.Count() > 0 {

		for i := range values {

			if _, ok := objects[i]; ok {

				dataTypeChanged = true

				break
			}
		}

		if dataTypeChanged {

			poolIndex, float64Values := aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(len(values))

			aggregator.encoder.MemoryPool.ResetFLOAT64Pool(poolIndex, len(float64Values), 0)

			INT64ToFLOAT64Values(values, float64Values)

			return aggregator.doMaxAggregationFLOAT(poolIndex, objects, keyBytes)
		}
	}

	if !dataTypeChanged {

		if aggregator.int64Fields.Count() > 0 {

			for i := range values {

				if object, ok := objects[i]; ok && aggregator.int64Fields.Has(object) {

					int64Values, _ := aggregator.int64Fields.Get(object)

					max := utils.MaxINT64(int64Values)

					if max > values[i] {

						values[i] = max
					}
				}
			}
		}

		return aggregator.write(nil, nil, values, keyBytes, GetMaxDataTypeINT64Values(values), utils.Max)
	}

	return nil
}

func (aggregator *MetricAggregator) doLastAggregationINT(index int, objects map[int]string, keyBytes []byte) error {

	values := aggregator.decoder.MemoryPool.GetINT64Pool(index)

	defer aggregator.decoder.MemoryPool.ReleaseINT64Pool(index)

	dataTypeChanged := false

	if aggregator.float64Fields.Count() > 0 {

		for i := range values {

			if _, ok := objects[i]; ok {

				dataTypeChanged = true

				break
			}
		}

		if dataTypeChanged {

			poolIndex, float64Values := aggregator.encoder.MemoryPool.AcquireFLOAT64Pool(len(values))

			aggregator.encoder.MemoryPool.ResetFLOAT64Pool(poolIndex, len(float64Values), 0)

			INT64ToFLOAT64Values(values, float64Values)

			return aggregator.doLastAggregationFLOAT(poolIndex, objects, keyBytes)
		}
	}

	if !dataTypeChanged {

		if aggregator.int64Fields.Count() > 0 {

			for i := range values {

				if object, ok := objects[i]; ok && aggregator.int64Fields.Has(object) {

					int64Values, _ := aggregator.int64Fields.Get(object)

					values[i] = int64Values[len(int64Values)-1]
				}
			}
		}

		return aggregator.write(nil, nil, values, keyBytes, GetMaxDataTypeINT64Values(values), utils.Last)
	}

	return nil
}

func (aggregator *MetricAggregator) doCountAggregation(index int, objects map[int]string, keyBytes []byte) error {

	values := aggregator.decoder.MemoryPool.GetINT64Pool(index)

	defer aggregator.decoder.MemoryPool.ReleaseINT64Pool(index)

	if aggregator.int64Fields.Count() > 0 {

		for i := range values {

			if object, ok := objects[i]; ok && aggregator.int64Fields.Has(object) {

				int64Values, _ := aggregator.int64Fields.Get(objects[i])

				values[i] += int64(len(int64Values))
			}
		}
	}

	if aggregator.float64Fields.Count() > 0 {

		for i := range values {

			if object, ok := objects[i]; ok && aggregator.float64Fields.Has(object) {

				float64Values, _ := aggregator.float64Fields.Get(objects[i])

				values[i] += int64(len(float64Values))
			}
		}
	}

	return aggregator.write(nil, nil, values, keyBytes, GetMaxDataTypeINT64Values(values), utils.Count)
}

func (aggregator *MetricAggregator) doLastAggregationString(index, objectElementSize int, objects map[int]string, keyBytes []byte) error {

	values := aggregator.decoder.MemoryPool.GetStringPool(index)[:objectElementSize]

	defer aggregator.decoder.MemoryPool.ReleaseStringPool(index)

	for i := range values {

		if _, ok := objects[i]; ok && aggregator.stringFields.Has(objects[i]) {

			values[i], _ = aggregator.stringFields.Get(objects[i])
		}
	}

	return aggregator.write(values, nil, nil, keyBytes, String, utils.Last)
}

func (aggregator *MetricAggregator) int64ToFloat64Values() {

	if aggregator.int64Fields.Count() > 0 && aggregator.float64Fields.Count() > 0 {

		aggregator.int64Fields.Iter(func(key string, values []int64) (stop bool) {

			aggregator.int64Fields.Delete(key)

			float64Values, _ := aggregator.float64Fields.Get(key)

			for i := range values {

				float64Values = append(float64Values, INT64ToFLOAT64Value(values[i]))
			}

			aggregator.float64Fields.Put(key, float64Values)

			return stop
		})
	}
}

func (aggregator *MetricAggregator) toINT64Values(valueBuffers [][]byte, k int, store *storage.Store, previousDataType, currentDataType DataType, aggregation, metric string, objectElementSize int) (error, int) {

	int64Values := aggregator.memoryPool.GetINT64Pool(aggregator.poolIndices[aggregation])

	size := 0

	if previousDataType == Int8 {

		poolIndex, decodedValues, err := aggregator.decoder.DecodeINT8Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

		if err != nil {

			aggregator.pendingProbe = true

			aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeMetricDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), metric))

			return err, size
		}

		index, values := aggregator.encoder.MemoryPool.AcquireINT64Pool(objectElementSize + len(int64Values))

		aggregator.encoder.MemoryPool.ResetINT64Pool(index, len(values), 0)

		tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT64Pool(objectElementSize)

		aggregator.encoder.MemoryPool.ResetINT64Pool(tempPoolIndex, objectElementSize, 0)

		INT8ToINT64Values(decodedValues, tempValues)

		copy(values, tempValues)

		copy(values[objectElementSize:], int64Values)

		aggregator.encoder.MemoryPool.ReleaseINT64Pool(tempPoolIndex)

		size = len(values)

		aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

		aggregator.poolIndices[aggregation] = index

		aggregator.encoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	} else if previousDataType == Int16 {

		poolIndex, decodedValues, err := aggregator.decoder.DecodeINT16Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

		if err != nil {

			aggregator.pendingProbe = true

			aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeMetricDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), metric))

			return err, size
		}

		index, values := aggregator.encoder.MemoryPool.AcquireINT64Pool(objectElementSize + len(int64Values))

		aggregator.encoder.MemoryPool.ResetINT64Pool(index, len(values), 0)

		tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT64Pool(objectElementSize)

		aggregator.encoder.MemoryPool.ResetINT64Pool(tempPoolIndex, objectElementSize, 0)

		INT16ToINT64Values(decodedValues, tempValues)

		copy(values, tempValues)

		copy(values[objectElementSize:], int64Values)

		aggregator.encoder.MemoryPool.ReleaseINT64Pool(tempPoolIndex)

		size = len(values)

		aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

		aggregator.poolIndices[aggregation] = index

		aggregator.encoder.MemoryPool.ReleaseINT16Pool(poolIndex)

	} else if previousDataType == Int24 || previousDataType == Int32 {

		poolIndex, decodedValues, err := aggregator.decoder.DecodeINT32Values(GetEncoding(valueBuffers[k][0]), previousDataType, valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

		if err != nil {

			aggregator.pendingProbe = true

			aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeMetricDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), metric))

			return err, size
		}

		index, values := aggregator.encoder.MemoryPool.AcquireINT64Pool(objectElementSize + len(int64Values))

		aggregator.encoder.MemoryPool.ResetINT64Pool(index, len(values), 0)

		tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT64Pool(objectElementSize)

		aggregator.encoder.MemoryPool.ResetINT64Pool(tempPoolIndex, objectElementSize, 0)

		INT32ToINT64Values(decodedValues, tempValues)

		copy(values, tempValues)

		copy(values[objectElementSize:], int64Values)

		aggregator.encoder.MemoryPool.ReleaseINT64Pool(tempPoolIndex)

		size = len(values)

		aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

		aggregator.poolIndices[aggregation] = index

		aggregator.encoder.MemoryPool.ReleaseINT32Pool(poolIndex)
	} else {

		poolIndex, decodedValues, err := aggregator.decoder.DecodeINT64Values(GetEncoding(valueBuffers[k][0]), previousDataType, valueBuffers[k][1:], string(aggregator.keyBuffers[k]), store.GetName(), 0)

		if err != nil {

			aggregator.pendingProbe = true

			aggregator.logger.Error(fmt.Sprintf(utils.ErrorDecodeMetricDatatype, err, currentDataType, string(aggregator.keyBuffers[k]), metric))

			return err, size
		}

		index, values := aggregator.encoder.MemoryPool.AcquireINT64Pool(objectElementSize + len(int64Values))

		copy(values, decodedValues[:objectElementSize])

		copy(values[objectElementSize:], int64Values)

		size = len(values)

		aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregation])

		aggregator.poolIndices[aggregation] = index

		aggregator.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)
	}

	aggregator.dataTypes[aggregation] = Int64

	return nil, size
}

// cleanup aggregation wise pool indices...
func (aggregator *MetricAggregator) cleanupPool(testPoolLeak bool) {

	for key, poolIndex := range aggregator.poolIndices {

		delete(aggregator.poolIndices, key)

		if dataType, found := aggregator.dataTypes[key]; found {

			delete(aggregator.dataTypes, key)

			switch {

			case dataType == Int8:

				aggregator.encoder.MemoryPool.ReleaseINT8Pool(poolIndex)

			case dataType == Int16:

				aggregator.encoder.MemoryPool.ReleaseINT16Pool(poolIndex)

			case dataType == Int24 || dataType == Int32:

				aggregator.encoder.MemoryPool.ReleaseINT32Pool(poolIndex)

			case dataType == Int40 || dataType == Int48 || dataType == Int56 || dataType == Int64:

				aggregator.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			case dataType == Float8 || dataType == Float16 || dataType == Float64:

				aggregator.encoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

			case dataType == String:

				aggregator.encoder.MemoryPool.ReleaseStringPool(poolIndex)
			}
		}
	}

	if testPoolLeak {

		aggregator.memoryPool.TestPoolLeak()
	}
}

func (aggregator *MetricAggregator) writeFLOAT64Values(values []interface{}, bufferBytes []byte) []byte {

	poolIndex, bytes := aggregator.encoder.WriteINT16Value(int16(len(values)), 0)

	bufferBytes = append(bufferBytes, bytes...)

	aggregator.encoder.MemoryPool.ReleaseBytePool(poolIndex)

	for _, value := range values {

		poolIndex, bytes = aggregator.encoder.WriteFLOAT64Value(value.(float64), 0)

		bufferBytes = append(bufferBytes, bytes...)

		aggregator.encoder.MemoryPool.ReleaseBytePool(poolIndex)
	}

	return bufferBytes

}

func (aggregator *MetricAggregator) writeINT64Values(values []interface{}, bufferBytes []byte) []byte {

	poolIndex, bytes := aggregator.encoder.WriteINT16Value(int16(len(values)), 0)

	bufferBytes = append(bufferBytes, bytes...)

	aggregator.encoder.MemoryPool.ReleaseBytePool(poolIndex)

	for _, value := range values {

		poolIndex, bytes = aggregator.encoder.WriteINT64Value(value.(int64), 0)

		bufferBytes = append(bufferBytes, bytes...)

		aggregator.encoder.MemoryPool.ReleaseBytePool(poolIndex)
	}

	return bufferBytes
}

func (aggregator *MetricAggregator) writeStringValues(values []interface{}, bufferBytes []byte) []byte {

	poolIndex, bytes := aggregator.encoder.WriteINT16Value(int16(len(values)), 0)

	bufferBytes = append(bufferBytes, bytes...)

	aggregator.encoder.MemoryPool.ReleaseBytePool(poolIndex)

	for _, value := range values {

		poolIndex, bytes = aggregator.encoder.WriteINT16Value(int16(len(value.(string))), 0)

		bufferBytes = append(bufferBytes, bytes...)

		aggregator.encoder.MemoryPool.ReleaseBytePool(poolIndex)

		bufferBytes = append(bufferBytes, value.(string)...)

	}

	return bufferBytes

}

// txn

func (aggregator *MetricAggregator) writeTxn(keyBytes []byte, bufferBytes []byte) (err error) {

	if size := len(bufferBytes) + 4 + len(utils.EOTBytes) + len(keyBytes) + aggregator.txnOffset; size > len(aggregator.txnBufferBytes) {

		aggregator.logger.Info(fmt.Sprintf("remapping annonymous txn buffers with current length %v and required size %v", len(aggregator.txnBufferBytes), size))

		aggregator.txnBufferBytes = utils.RemapBytes(aggregator.txnBufferBytes, size)
	}

	copy(bufferBytes, utils.CheckSumV1Bytes) // add checksum

	WriteINT32Value(int32(len(bufferBytes)-utils.MaxValueBytes), 4, bufferBytes) //add length

	WriteINT32Value(int32(len(keyBytes)), 0, aggregator.int32Bytes) //calculate length of key bytes

	copy(aggregator.txnBufferBytes[aggregator.txnOffset:], aggregator.int32Bytes) //add key length bytes

	aggregator.txnOffset += 4

	copy(aggregator.txnBufferBytes[aggregator.txnOffset:], keyBytes)

	aggregator.txnOffset += len(keyBytes)

	offset := aggregator.txnOffset

	copy(aggregator.txnBufferBytes[aggregator.txnOffset:], bufferBytes)

	aggregator.txnOffset += len(bufferBytes)

	aggregator.txnEntries[utils.GetHash64(keyBytes)] = utils.TxnEntry{Length: len(bufferBytes), Offset: offset}

	return
}

func (aggregator *MetricAggregator) commit(store *storage.Store, part int, tick string) bool {

	copy(aggregator.txnBufferBytes[aggregator.txnOffset:], utils.EOTBytes)

	aggregator.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(aggregator.txnOffset-4), 0, aggregator.txnBufferBytes)

	err := store.CommitTxn(aggregator.txnBufferBytes[:aggregator.txnOffset], aggregator.txnEntries, aggregator.encoder, aggregator.txnPartition)

	aggregator.cleanupTxn()

	if err != nil {

		aggregator.logger.Error(fmt.Sprintf("error %v occurred while committing txn for store %v, part %v and tick %v", err, store.GetName(), part, tick))

		return false
	}

	return true

}
