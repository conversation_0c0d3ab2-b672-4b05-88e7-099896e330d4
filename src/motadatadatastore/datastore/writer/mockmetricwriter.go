/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-10			 <PERSON><PERSON>val <PERSON>ra			Motadata-4913  Altered modulo operator with new modulo function
* 2025-03-05			 <PERSON><PERSON><PERSON>-5190  Migrated constants from datastore to utils to match SonarQube Standard
* 2025-03-05             Hardik Vala            Motadata-5451  Status Flap Report Vertical Testcases
* 2025-03-21			 <PERSON><PERSON>val <PERSON>-5452  Added Insertion For NetRoute
* 2025-04-02			 D<PERSON>val <PERSON>-4859  Added NetRoute Status Metric Datastore Type
* 2025-05-05			 Swapnil <PERSON><PERSON>A-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-06-04             Aashil Shah            MOTADATA-5780 Made Clickhouse Address Dynamic for windows build
 */

// NOSONAR
package writer

import (
	bytes2 "bytes"
	"context"
	"encoding/binary"
	"encoding/json"
	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
	"github.com/golang/snappy"
	"math"
	"math/rand"
	broker2 "motadatadatastore/broker"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"reflect"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

var (
	flowBatch1          driver.Batch
	auditBatch1         driver.Batch
	trapBatch1          driver.Batch
	policyDurationBatch driver.Batch
	eventPolicyBatch1   driver.Batch
)

const (
	Metric                = "metric"
	Value                 = "value"
	_Instance             = "instance"
	Datatype              = "datatype"
	ObjectId              = "object.id"
	Interface             = "interface"
	InterfaceInstanceName = "interface~instance.name"

	//groups

	ServerGroup = "server"

	NetworkGroup = "network"

	//tag

	Tag1 = "tag1"

	Tag2 = "tag2"

	TrapFlapPrefix = "500003-trap.flap###"

	//metric metadata

	MetricDatatype = "metric.datatype"
	MetricMaxValue = "metric.max.value"
	MetricMinValue = "metric.min.value"
)

const ( //clickhouse constants

	TestDatabase1 = "dbTest"

	TestDatabase2 = "customDB"

	Username = "default"

	Password = "TRACEorg@2010"

	CreateScalarTableQuery = "CREATE TABLE IF NOT EXISTS scalar (monitor_id Int32 CODEC(ZSTD(1)),plugin_id String CODEC(ZSTD(1)),group String CODEC(ZSTD(1)),tag String CODEC(ZSTD(1)),system_cpu_percent Float64 CODEC(ZSTD(1)),system_disk_used_percent Float64 CODEC(ZSTD(1)),system_memory_used_bytes Int64 CODEC(ZSTD(1)),system_network_in_bytes Int64 CODEC(ZSTD(1)), system_os_name String CODEC(ZSTD(1)),vlan_ports Int64 CODEC(ZSTD(1)),interfaces Int64 CODEC(ZSTD(1)),system_memory_committed_bytes Int64 CODEC(ZSTD(1)),ping_max_latency Int64 CODEC(ZSTD(1)),timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,plugin_id ,group,tag,system_cpu_percent,system_disk_used_percent,system_memory_used_bytes,system_network_in_bytes, system_os_name,vlan_ports,interfaces,system_memory_committed_bytes,ping_max_latency,timestamp)"

	ScalarBatchQuery = "Insert into scalar (monitor_id ,plugin_id ,group, tag ,system_cpu_percent,system_disk_used_percent,system_memory_used_bytes , system_network_in_bytes , system_os_name,vlan_ports,interfaces,system_memory_committed_bytes,ping_max_latency, timestamp)"

	CreateConfigHistoryQuery = "CREATE TABLE IF NOT EXISTS configHistory ( event_source String CODEC(ZSTD(1)), event String CODEC(ZSTD(1)),  config_operation String CODEC(ZSTD(1)), config_operation_status String CODEC(ZSTD(1)), config_operation_output String CODEC(ZSTD(1)), config_operation_message String CODEC(ZSTD(1)), config_operation_user_name String CODEC(ZSTD(1)), timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp))) order by ( event_source ,config_operation , config_operation_status , config_operation_output , config_operation_message, config_operation_user_name )"

	ConfigHistoryBatchQuery = " Insert into configHistory ( event_source  , event ,  config_operation , config_operation_status , config_operation_output , config_operation_message , config_operation_user_name , timestamp ) "

	CreateComplianceQuery = "CREATE TABLE IF NOT EXISTS compliance ( audit_policy_id Int64  CODEC(ZSTD(1)), compliance_percent Int64  CODEC(ZSTD(1)), timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp))) order by ( timestamp )"

	ComplianceBatchQuery = " Insert into compliance ( audit_policy_id  , compliance_percent , timestamp ) "

	CreateInstanceTableQuery = "CREATE TABLE IF NOT EXISTS instance (monitor_id Int32,plugin_id  String CODEC(ZSTD(1)),instance String CODEC(ZSTD(1)),instance_type String CODEC(ZSTD(1)),group String CODEC(ZSTD(1)) ,tag String CODEC(ZSTD(1)) ,in_packets Int64 CODEC(ZSTD(1)) ,out_packets Int64 CODEC(ZSTD(1)) ,sent_octets Int64 CODEC(ZSTD(1)) ,received_octets Int64 CODEC(ZSTD(1)) , last_change String CODEC(ZSTD(1)) ,operational_status String CODEC(ZSTD(1)) ,admin_status String CODEC(ZSTD(1)) ,status String CODEC(ZSTD(1)) ,alias String CODEC(ZSTD(1)) ,error_packets Int64 CODEC(ZSTD(1)) , in_traffic_utilization Int64 CODEC(ZSTD(1)) , out_traffic_utilization Int64 CODEC(ZSTD(1)) ,bytes_per_sec Int64 CODEC(ZSTD(1)) , in_traffic_utilization_percent Float64 CODEC(ZSTD(1)) , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,plugin_id,instance,instance_type,group,tag,in_packets,out_packets,sent_octets,received_octets, last_change,operational_status,admin_status,status,alias,error_packets, in_traffic_utilization , out_traffic_utilization, in_traffic_utilization_percent,timestamp)"

	InstanceBatchQuery = "Insert into instance (monitor_id,plugin_id, instance , instance_type,group, tag ,in_packets ,out_packets ,sent_octets ,received_octets , last_change ,operational_status ,admin_status, status,alias, error_packets , in_traffic_utilization  , out_traffic_utilization ,bytes_per_sec, in_traffic_utilization_percent ,timestamp)"

	CreateScalarMetricDummyTableQuery = "CREATE TABLE IF NOT EXISTS dummyTable (monitor_id Int32 CODEC(ZSTD(1)) ,plugin_id String CODEC(ZSTD(1)) ,dummy_numeric_column Float64 CODEC(ZSTD(1)) ,timestamp Int64  CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,plugin_id,dummy_numeric_column,timestamp)"

	CustomBatchQuery = "Insert into dummyTable (monitor_id ,plugin_id,dummy_numeric_column, timestamp )"

	CreateScalarMetricDummyTableQuery1 = "CREATE TABLE IF NOT EXISTS dummyTable1 (monitor_id Int32 CODEC(ZSTD(1)) ,plugin_id String CODEC(ZSTD(1)) ,dummy_numeric_column Float64 CODEC(ZSTD(1)) ,timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,plugin_id,dummy_numeric_column,timestamp)"

	CustomBatchQuery1 = "Insert into dummyTable1 (monitor_id ,plugin_id,dummy_numeric_column, timestamp)"

	CreateScalarMetricDummyTable2Query = "CREATE TABLE IF NOT EXISTS dummyTable2 (monitor_id Int32 CODEC(ZSTD(1)) ,plugin_id String CODEC(ZSTD(1)) ,dummy_numeric_column2 String CODEC(ZSTD(1)),timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,plugin_id,dummy_numeric_column2 ,timestamp)"

	CustomBatchQuery2 = "Insert into dummyTable2 (monitor_id , plugin_id, dummy_numeric_column2, timestamp)"

	CreateDummyTable3Query = "CREATE TABLE IF NOT EXISTS dummyTable3 (monitor_id Int32 CODEC(ZSTD(1)) ,plugin_id String CODEC(ZSTD(1)) ,dummy_numeric_column3 String CODEC(ZSTD(1)) ,timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,plugin_id,dummy_numeric_column3,timestamp)"

	CustomBatchQuery3 = "Insert into dummyTable3 (monitor_id , plugin_id, dummy_numeric_column3, timestamp)"

	CreateDummyINT8FLOATTableQuery = "CREATE TABLE IF NOT EXISTS dummyInt8FloatTable (monitor_id Int32 CODEC(ZSTD(1)) ,plugin_id String CODEC(ZSTD(1)) ,dummy_int8Float_column Float64 CODEC(ZSTD(1)) ,timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,plugin_id,dummy_int8Float_column,timestamp)"

	CustomINT8FLOATBatchQuery = "Insert into dummyInt8FloatTable (monitor_id , plugin_id, dummy_int8Float_column, timestamp)"

	CreateDummyINT16FLOATTableQuery = "CREATE TABLE IF NOT EXISTS dummyInt16FloatTable (monitor_id Int32 CODEC(ZSTD(1)) ,plugin_id String CODEC(ZSTD(1)) ,dummy_int16Float_column Float64 CODEC(ZSTD(1)) ,timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by ( monitor_id,plugin_id,dummy_int16Float_column,timestamp)"

	CustomINT16FLOATBatchQuery = "Insert into dummyInt16FloatTable (monitor_id , plugin_id, dummy_int16Float_column, timestamp)"

	CreateDummyINT32FLOATTableQuery = "CREATE TABLE IF NOT EXISTS dummyInt32FloatTable (monitor_id Int32 CODEC(ZSTD(1)) ,plugin_id String CODEC(ZSTD(1)) ,dummy_int32Float_column Float64 CODEC(ZSTD(1)) ,timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,plugin_id,dummy_int32Float_column,timestamp)"

	CustomINT32FLOATBatchQuery = "Insert into dummyInt32FloatTable (monitor_id , plugin_id, dummy_int32Float_column, timestamp)"

	CreateDummyINT64FLOATTableQuery = "CREATE TABLE IF NOT EXISTS dummyInt64FloatTable (monitor_id Int32  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) ,dummy_int64Float_column  Float64 CODEC(ZSTD(1)) ,timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by ( monitor_id,plugin_id,dummy_int64Float_column,timestamp)"

	CustomINT64FLOATBatchQuery = "Insert into dummyInt64FloatTable (monitor_id , plugin_id, dummy_int64Float_column, timestamp)"

	CreateDummyINTFLOATTableQuery = "CREATE TABLE IF NOT EXISTS dummyIntFloatTable (monitor_id Int32 CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) ,dummy_intFloat_column  Float64 CODEC(ZSTD(1)) ,timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by ( monitor_id,plugin_id,dummy_intFloat_column,timestamp)"

	CustomINTFLOATBatchQuery = "Insert into dummyIntFloatTable (monitor_id , plugin_id, dummy_intFloat_column, timestamp)"

	CreateDummyFLOATINTTableQuery = "CREATE TABLE IF NOT EXISTS dummyFloatIntTable (monitor_id Int32  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) ,dummy_floatInt_column  Float64 CODEC(ZSTD(1)) ,timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,plugin_id,dummy_floatInt_column,timestamp)"

	CustomFLOATINTBatchQuery = "Insert into dummyFloatIntTable (monitor_id , plugin_id, dummy_floatInt_column, timestamp)"

	CreateDummyINTFLOATTableQueryCustom = "CREATE TABLE IF NOT EXISTS dummyIntFloatTableCustom (monitor_id Int32  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) ,dummy_intFloat_column_custom  Float64 CODEC(ZSTD(1)) ,timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,plugin_id,dummy_intFloat_column_custom,timestamp)"

	CustomINTFLOATBatchQueryCustom = "Insert into dummyIntFloatTableCustom (monitor_id , plugin_id, dummy_intFloat_column_custom, timestamp)"

	CreateDummyINTStringTableQueryCustom = "CREATE TABLE IF NOT EXISTS dummyIntStringTable (monitor_id Int32  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) ,dummy_intString_column String  CODEC(ZSTD(1)) ,timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,plugin_id,dummy_intString_column,timestamp)"

	CustomINTStringBatchQueryCustom = "Insert into dummyIntStringTable (monitor_id , plugin_id, dummy_intString_column, timestamp)"

	CreateLogTable1Query = "CREATE TABLE IF NOT EXISTS logTable1( event_source String  CODEC(ZSTD(1)) ,group String  CODEC(ZSTD(1)) , tag String  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) ,log_level Int64  CODEC(ZSTD(1)) ,log_id Int64  CODEC(ZSTD(1)) ,processes Int64  CODEC(ZSTD(1)) , message_id Int64  CODEC(ZSTD(1)) ,system_os String  CODEC(ZSTD(1)) ,log_message String  CODEC(ZSTD(1)) ,level_description String  CODEC(ZSTD(1)) ,bytes_sent Int64  CODEC(ZSTD(1)) ,bytes_received Int64  CODEC(ZSTD(1)) ,log_in_count Int64  CODEC(ZSTD(1)) ,source_port Int64  CODEC(ZSTD(1)) , uagid Int64   CODEC(ZSTD(1)) , message String  CODEC(ZSTD(1)) ,event_category String  CODEC(ZSTD(1)) , event_source_type String  CODEC(ZSTD(1)) , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (event_source,group,tag,plugin_id,log_level,log_id,processes,system_os,log_message,level_description,bytes_sent,bytes_received,log_in_count,source_port ,uagid, timestamp,message,event_category,event_source_type)"

	InsertLogTable1BatchQuery = "Insert into logTable1( event_source,group,tag,plugin_id,log_level,log_id,processes,message_id,system_os,log_message,level_description,bytes_sent,bytes_received,log_in_count,source_port, uagid,message,event_category,event_source_type,timestamp)"

	CreateLogTable5Query = "CREATE TABLE IF NOT EXISTS logTable5( event_source String  CODEC(ZSTD(1)) ,group String  CODEC(ZSTD(1)) , tag String  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) ,log_level Int64  CODEC(ZSTD(1)) ,log_id Int64  CODEC(ZSTD(1)) ,processes Int64  CODEC(ZSTD(1)) , message_id Int64  CODEC(ZSTD(1)) ,system_os String  CODEC(ZSTD(1)) ,log_message String  CODEC(ZSTD(1)) ,level_description String  CODEC(ZSTD(1)) ,bytes_sent Int64  CODEC(ZSTD(1)) ,bytes_received Int64  CODEC(ZSTD(1)) ,log_in_count Int64  CODEC(ZSTD(1)) ,source_port Int64  CODEC(ZSTD(1)) , uagid Int64  CODEC(ZSTD(1))  , message String  CODEC(ZSTD(1)) ,event_category String  CODEC(ZSTD(1)) , event_source_type String  CODEC(ZSTD(1)) , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (event_source,group,tag,plugin_id,log_level,log_id,processes,system_os,log_message,level_description,bytes_sent,bytes_received,log_in_count,source_port ,uagid, timestamp,message,event_category,event_source_type)"

	InsertLogTable5BatchQuery = "Insert into logTable5( event_source,group,tag,plugin_id,log_level,log_id,processes,message_id,system_os,log_message,level_description,bytes_sent,bytes_received,log_in_count,source_port, uagid,message,event_category,event_source_type,timestamp)"

	CreateLogTable2Query = "CREATE TABLE IF NOT EXISTS logTable2( event_source String  CODEC(ZSTD(1)) ,group String  CODEC(ZSTD(1)) , tag String  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) ,log_level Int64  CODEC(ZSTD(1)) ,processes Int64  CODEC(ZSTD(1)) , system_os String  CODEC(ZSTD(1)) ,log_message String  CODEC(ZSTD(1)) ,level_description String  CODEC(ZSTD(1)) ,bytes_sent Int64  CODEC(ZSTD(1)) ,bytes_received Int64  CODEC(ZSTD(1)) ,source_port Int64  CODEC(ZSTD(1)) , uagid Int64  CODEC(ZSTD(1))  ,message String  CODEC(ZSTD(1)) ,event_category String  CODEC(ZSTD(1)) ,event_source_type String  CODEC(ZSTD(1)) , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (event_source,group,tag,plugin_id,log_level,processes,system_os,log_message,level_description,bytes_sent,bytes_received,source_port ,uagid,message, timestamp,event_category,event_source_type)"

	InsertLogTable2BatchQuery = "Insert into logTable2( event_source,group,tag,plugin_id,log_level,processes, system_os,log_message,level_description,bytes_sent,bytes_received,source_port, uagid, message,event_category,event_source_type, timestamp)"

	CreateLogTable3Query = "CREATE TABLE IF NOT EXISTS logTable3( event_source String  CODEC(ZSTD(1)) ,group String  CODEC(ZSTD(1)) , tag String  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) ,log_level Int64  CODEC(ZSTD(1)) ,processes Int64, system_os String  CODEC(ZSTD(1)) ,log_message String  CODEC(ZSTD(1)) ,level_description String  CODEC(ZSTD(1)) ,bytes_sent Int64  CODEC(ZSTD(1)) ,bytes_received Int64  CODEC(ZSTD(1)) ,source_port Int64  CODEC(ZSTD(1)) , uagid Int64  CODEC(ZSTD(1)) , message String  CODEC(ZSTD(1)) ,event_category String  CODEC(ZSTD(1)) ,event_source_type String  CODEC(ZSTD(1)) , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (event_source,group,tag,plugin_id,log_level,processes,system_os,log_message,level_description,bytes_sent,bytes_received,source_port ,uagid, timestamp,event_category,event_source_type)"

	InsertLogTable3BatchQuery = "Insert into logTable3( event_source,group,tag,plugin_id,log_level,processes, system_os,log_message,level_description,bytes_sent,bytes_received,source_port, uagid,message,event_category, event_source_type,timestamp)"

	CreateLogTable4Query = "CREATE TABLE IF NOT EXISTS logTable4( event_source String  CODEC(ZSTD(1)) ,group String  CODEC(ZSTD(1)) , tag String  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) ,log_level Int64  CODEC(ZSTD(1)) ,log_id Int64  CODEC(ZSTD(1)) ,processes Int64  CODEC(ZSTD(1)) , message_id Int64  CODEC(ZSTD(1)) ,system_os String  CODEC(ZSTD(1)) ,log_message String  CODEC(ZSTD(1)) ,level_description String  CODEC(ZSTD(1)) ,bytes_sent Int64  CODEC(ZSTD(1)) ,bytes_received Int64  CODEC(ZSTD(1)) ,log_in_count Int64  CODEC(ZSTD(1)) ,source_port Int64  CODEC(ZSTD(1)) , uagid Int64   CODEC(ZSTD(1)) , message String  CODEC(ZSTD(1)) ,event_category String  CODEC(ZSTD(1)) ,event_source_type String  CODEC(ZSTD(1)) ,timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (event_source,group,tag,plugin_id,log_level,log_id,processes,system_os,log_message,level_description,bytes_sent,bytes_received,log_in_count,source_port ,uagid,message, timestamp,event_category,event_source_type)"

	InsertLogTable4BatchQuery = "Insert into logTable4( event_source,group,tag,plugin_id,log_level,log_id,processes,message_id,system_os,log_message,level_description,bytes_sent,bytes_received,log_in_count,source_port, uagid, message,event_category, event_source_type ,timestamp)"

	CreateFlowTableQuery = "CREATE TABLE IF NOT EXISTS flowTable( event_source String  CODEC(ZSTD(1)) ,group String  CODEC(ZSTD(1)) , tag String  CODEC(ZSTD(1)) , volume_bytes_per_sec Int64  CODEC(ZSTD(1)) ,duration Int64  CODEC(ZSTD(1)) ,flows Int64  CODEC(ZSTD(1)) ,destination_port Int64  CODEC(ZSTD(1)) ,destination_as Int64  CODEC(ZSTD(1)) ,source_if_index Int64  CODEC(ZSTD(1)) ,destination_if_index Int64  CODEC(ZSTD(1)) ,tcp_flags Int64  CODEC(ZSTD(1)) ,volume_bytes_per_packet Int64  CODEC(ZSTD(1)) ,packets Int64  CODEC(ZSTD(1)) ,source_port Int64  CODEC(ZSTD(1)) ,packets_per_sec Int64  CODEC(ZSTD(1)) ,destination_ip_as Int64  CODEC(ZSTD(1)) ,volume_bytes Int64  CODEC(ZSTD(1)) ,source_as Int64  CODEC(ZSTD(1)) , sent_discarded_packets Int64  CODEC(ZSTD(1)) , received_discarded_packets Int64  CODEC(ZSTD(1)) ,source_city String  CODEC(ZSTD(1)) ,destination_ip String  CODEC(ZSTD(1)) ,peer_destination String  CODEC(ZSTD(1)) ,source_isp String  CODEC(ZSTD(1)) ,source_domain String  CODEC(ZSTD(1)) ,source_asn String  CODEC(ZSTD(1)) ,source_country String  CODEC(ZSTD(1)) ,protocol String  CODEC(ZSTD(1)) ,source_threat String  CODEC(ZSTD(1)) ,destination_country String  CODEC(ZSTD(1)) ,destination_city String  CODEC(ZSTD(1)) ,application String  CODEC(ZSTD(1)) ,tos String  CODEC(ZSTD(1)) ,destination_isp String  CODEC(ZSTD(1)) ,destination_domain String  CODEC(ZSTD(1)) ,destination_threat String  CODEC(ZSTD(1)) ,source_ip String  CODEC(ZSTD(1)) ,application_protocol String  CODEC(ZSTD(1)) ,peer_source String  CODEC(ZSTD(1)) ,destination_asn String  CODEC(ZSTD(1)) , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp))) order by (event_source,group,tag,volume_bytes_per_sec,duration,flows,destination_port,destination_as,source_if_index,destination_if_index,tcp_flags,volume_bytes_per_packet,packets,source_port,packets_per_sec,destination_ip_as,volume_bytes,source_as, sent_discarded_packets, received_discarded_packets, source_city,destination_ip,peer_destination,source_isp,source_domain,source_asn,source_country,protocol,source_threat,destination_country,destination_city,application,tos,destination_isp,destination_domain,destination_threat,source_ip,application_protocol,peer_source,destination_asn,timestamp)"

	InsertFlowTableBatchQuery = "Insert into flowTable(event_source,group,tag,volume_bytes_per_sec,duration,flows,destination_port,destination_as,source_if_index,destination_if_index,tcp_flags,volume_bytes_per_packet,packets,source_port,packets_per_sec,destination_ip_as,volume_bytes,source_as, sent_discarded_packets, received_discarded_packets, source_city,destination_ip,peer_destination,source_isp,source_domain,source_asn,source_country,protocol,source_threat,destination_country,destination_city,application,tos,destination_isp,destination_domain,destination_threat,source_ip,application_protocol,peer_source,destination_asn, timestamp)"

	CreateFlowTableQuery2 = "CREATE TABLE IF NOT EXISTS flowTable2( event_source String  CODEC(ZSTD(1)) ,group String  CODEC(ZSTD(1)) , tag String  CODEC(ZSTD(1)) , volume_bytes_per_sec Int64  CODEC(ZSTD(1)) ,duration Int64  CODEC(ZSTD(1)) ,flows Int64  CODEC(ZSTD(1)) ,destination_port Int64  CODEC(ZSTD(1)) ,destination_as Int64  CODEC(ZSTD(1)) ,source_if_index Int64  CODEC(ZSTD(1)) ,destination_if_index Int64  CODEC(ZSTD(1)) ,tcp_flags Int64  CODEC(ZSTD(1)) ,volume_bytes_per_packet Int64  CODEC(ZSTD(1)) ,packets Int64  CODEC(ZSTD(1)) ,source_port Int64  CODEC(ZSTD(1)) ,packets_per_sec Int64  CODEC(ZSTD(1)) ,destination_ip_as Int64  CODEC(ZSTD(1)) ,volume_bytes Int64  CODEC(ZSTD(1)) ,source_as Int64  CODEC(ZSTD(1)) , sent_discarded_packets Int64  CODEC(ZSTD(1)) , received_discarded_packets Int64  CODEC(ZSTD(1)) ,source_city String  CODEC(ZSTD(1)) ,destination_ip String  CODEC(ZSTD(1)) ,peer_destination String  CODEC(ZSTD(1)) ,source_isp String  CODEC(ZSTD(1)) ,source_domain String  CODEC(ZSTD(1)) ,source_asn String  CODEC(ZSTD(1)) ,source_country String  CODEC(ZSTD(1)) ,protocol String  CODEC(ZSTD(1)) ,source_threat String  CODEC(ZSTD(1)) ,destination_country String  CODEC(ZSTD(1)) ,destination_city String  CODEC(ZSTD(1)) ,application String  CODEC(ZSTD(1)) ,tos String  CODEC(ZSTD(1)) ,destination_isp String  CODEC(ZSTD(1)) ,destination_domain String  CODEC(ZSTD(1)) ,destination_threat String  CODEC(ZSTD(1)) ,source_ip String  CODEC(ZSTD(1)) ,application_protocol String  CODEC(ZSTD(1)) ,peer_source String  CODEC(ZSTD(1)) ,destination_asn String  CODEC(ZSTD(1)) , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp))) order by (event_source,group,tag,volume_bytes_per_sec,duration,flows,destination_port,destination_as,source_if_index,destination_if_index,tcp_flags,volume_bytes_per_packet,packets,source_port,packets_per_sec,destination_ip_as,volume_bytes,source_as, sent_discarded_packets, received_discarded_packets, source_city,destination_ip,peer_destination,source_isp,source_domain,source_asn,source_country,protocol,source_threat,destination_country,destination_city,application,tos,destination_isp,destination_domain,destination_threat,source_ip,application_protocol,peer_source,destination_asn,timestamp)"

	InsertFlowTableBatchQuery2 = "Insert into flowTable2(event_source,group,tag,volume_bytes_per_sec,duration,flows,destination_port,destination_as,source_if_index,destination_if_index,tcp_flags,volume_bytes_per_packet,packets,source_port,packets_per_sec,destination_ip_as,volume_bytes,source_as, sent_discarded_packets, received_discarded_packets, source_city,destination_ip,peer_destination,source_isp,source_domain,source_asn,source_country,protocol,source_threat,destination_country,destination_city,application,tos,destination_isp,destination_domain,destination_threat,source_ip,application_protocol,peer_source,destination_asn, timestamp)"

	CreateStatusDurationInstanceMetricTableQuery = "CREATE TABLE IF NOT EXISTS statusTable( monitor_id Int32  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) , status String  CODEC(ZSTD(1)) , reason String  CODEC(ZSTD(1)) ,instance String  CODEC(ZSTD(1)) ,instance_type String  CODEC(ZSTD(1)) , duration Int32  CODEC(ZSTD(1)) ,  timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,status,reason,instance,instance_type,duration, timestamp)"

	InsertStatusDurationInstanceMetricBatchQuery = "Insert into statusTable( monitor_id ,plugin_id , status , reason ,instance ,instance_type , duration  , timestamp )"

	CreateStatusDurationHorizontalTableQuery = "CREATE TABLE IF NOT EXISTS statusHorizontalTable( monitor_id Int32  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) , status String  CODEC(ZSTD(1)) , reason String  CODEC(ZSTD(1)) ,instance String  CODEC(ZSTD(1)) ,instance_type String  CODEC(ZSTD(1)) , duration Int32  CODEC(ZSTD(1)) ,  timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,status,reason,instance,instance_type,duration, timestamp)"

	InsertStatusDurationHorizontalBatchQuery = "Insert into statusHorizontalTable( monitor_id ,plugin_id , status , reason ,instance ,instance_type , duration  , timestamp )"

	CreateStatusDurationMetricTableQuery = "CREATE TABLE IF NOT EXISTS statusTableMetric( monitor_id Int32  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) , status String  CODEC(ZSTD(1)) , reason String  CODEC(ZSTD(1)) , duration Int32  CODEC(ZSTD(1)) ,  timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,status,reason,duration, timestamp)"

	InsertStatusDurationMetricBatchQuery = "Insert into statusTableMetric( monitor_id ,plugin_id , status , reason , duration  , timestamp )"

	CreatePolicyDurationTableQuery = "CREATE TABLE IF NOT EXISTS policyTable( monitor_id Int32  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) ,policyId Int64  CODEC(ZSTD(1)) ,severity String  CODEC(ZSTD(1)) ,value String  CODEC(ZSTD(1)) ,instance String  CODEC(ZSTD(1)) ,instance_type String  CODEC(ZSTD(1)) , duration Int32  CODEC(ZSTD(1)) , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)), policy_threshold String  CODEC(ZSTD(1)) )engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (monitor_id,severity,instance,instance_type,value,duration, timestamp)"

	InsertPolicyDurationBatchQuery = "Insert into policyTable( monitor_id ,plugin_id , policyId ,severity ,value,instance ,instance_type, duration , timestamp ,policy_threshold)"

	CreateEventPolicyTableQuery = "CREATE TABLE IF NOT EXISTS policyTable1( event_source String   CODEC(ZSTD(1)) ,group String  CODEC(ZSTD(1)) ,message String  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) ,policyId Int64  CODEC(ZSTD(1)) ,severity String  CODEC(ZSTD(1)) ,metric String  CODEC(ZSTD(1)) ,value Int64  CODEC(ZSTD(1)) ,type String  CODEC(ZSTD(1)) , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (event_source,group,message,severity,metric,value,type, timestamp)"

	InsertEventPolicyBatchQuery = "INSERT Into policyTable1( event_source ,group ,message,plugin_id ,policyId ,severity ,metric ,value ,type , timestamp )"

	CreateEventPolicyTableQuery1 = "CREATE TABLE IF NOT EXISTS policyTable3( event_source String  CODEC(ZSTD(1)) ,message String  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) ,policyId Int64  CODEC(ZSTD(1)) ,severity String  CODEC(ZSTD(1)) ,metric String  CODEC(ZSTD(1)) ,value Int64  CODEC(ZSTD(1)) ,type String  CODEC(ZSTD(1)) , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)), plugin_id) order by (event_source,message,severity,metric,value,type, timestamp)"

	InsertEventPolicyBatchQuery1 = "INSERT Into policyTable3( event_source ,message,plugin_id ,policyId ,severity ,metric ,value ,type  , timestamp )"

	CreateMetricPolicyTableQuery = "CREATE TABLE IF NOT EXISTS policyTable2( object_id Int64   CODEC(ZSTD(1)) ,group String  CODEC(ZSTD(1)) ,message String  CODEC(ZSTD(1)) ,plugin_id String  CODEC(ZSTD(1)) ,policyId Int64  CODEC(ZSTD(1)) ,severity String  CODEC(ZSTD(1)) ,metric String  CODEC(ZSTD(1)) ,value Int64  CODEC(ZSTD(1)) ,type String  CODEC(ZSTD(1)) , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by (object_id,group,message,severity,metric,value,type, timestamp)"

	InsertMetricPolicyBatchQuery = "INSERT Into policyTable2( object_id ,group ,message,plugin_id ,policyId ,severity ,metric ,value ,type  , timestamp )"

	CreateAuditTableQuery = "CREATE TABLE IF NOT EXISTS auditTable(event_source String  CODEC(ZSTD(1)) , audit_module String  CODEC(ZSTD(1)) , audit_remote_ip String  CODEC(ZSTD(1)) ,audit_operation String  CODEC(ZSTD(1)) , audit_user String  CODEC(ZSTD(1)) , audit_message String  CODEC(ZSTD(1)) , audit_status String  CODEC(ZSTD(1)) , audit_history String  CODEC(ZSTD(1)) ,  timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp))) order by (event_source, audit_module, audit_remote_ip, audit_operation, audit_user, audit_message, audit_status, audit_history, timestamp)"

	InsertAuditTableBatchQuery = "Insert into auditTable( event_source, audit_module, audit_remote_ip, audit_operation, audit_user, audit_message, audit_status, audit_history, timestamp)"

	CreateUserNotificationTableQuery = "CREATE TABLE IF NOT EXISTS userNotificationTable(event_source String  CODEC(ZSTD(1)) , user_notification_type String  CODEC(ZSTD(1)) , user_notification_severity String  CODEC(ZSTD(1)) , user_notification_message String  CODEC(ZSTD(1)) , user_notification_id Int64  CODEC(ZSTD(1)) , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp))) order by (event_source, user_notification_type, user_notification_severity, user_notification_message, user_notification_id, timestamp)"

	InsertUserNotificationTableBatchQuery = "Insert into userNotificationTable(event_source, user_notification_type, user_notification_severity, user_notification_message, user_notification_id, timestamp)"

	CreateTrapTableQuery = "CREATE TABLE IF NOT EXISTS trapTable(event_source String  CODEC(ZSTD(1)) , trap_name String  CODEC(ZSTD(1)) , trap_oid String  CODEC(ZSTD(1)) ,trap_version Int64  CODEC(ZSTD(1)) , enterprise_id String  CODEC(ZSTD(1)) , trap_vendor String  CODEC(ZSTD(1)) , trap_message String  CODEC(ZSTD(1)) ,trap_severity String  CODEC(ZSTD(1)) ,  timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp))) order by (event_source, trap_name, trap_version, trap_oid, enterprise_id,trap_vendor, trap_message,timestamp)"

	InsertTrapTableBatchQuery = "Insert into trapTable(event_source , trap_name , trap_oid ,trap_version , enterprise_id , trap_vendor , trap_message , trap_severity  ,timestamp)"

	CreateTableQueryType7 = "CREATE TABLE IF NOT EXISTS system_cpu_free_percent (monitor_id Int32  CODEC(ZSTD(1)) , sum  Float64 CODEC(ZSTD(1)) , count Int64  CODEC(ZSTD(1)) , min  Float64 CODEC(ZSTD(1)) , max  Float64 CODEC(ZSTD(1)) ,timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp))) order by (monitor_id,sum,min,max,count, timestamp)"

	CreateFlowTableQuery1 = "CREATE TABLE IF NOT EXISTS flowTable1( event_source String  CODEC(ZSTD(1)) , plugin_id String  CODEC(ZSTD(1)) ,volume_bytes_per_sec Int64  CODEC(ZSTD(1)) ,source_port Int64  CODEC(ZSTD(1)) ,source_as Int64  CODEC(ZSTD(1)) ,source_city String  CODEC(ZSTD(1)) ,source_ip String   CODEC(ZSTD(1)) ,timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine==MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)), plugin_id) ORDER by (event_source,volume_bytes_per_sec,source_port,source_as,source_city, timestamp )"

	InsertFlowTableBatchQuery1 = "Insert into flowTable1 (event_source,plugin_id,volume_bytes_per_sec,source_port,source_as,source_city, source_ip, timestamp)"

	CreateAuditTableQuery1 = "CREATE TABLE IF NOT EXISTS auditTable1(event_source String  CODEC(ZSTD(1)) , audit_module String  CODEC(ZSTD(1)) , audit_remote_ip String  CODEC(ZSTD(1)) ,audit_operation String  CODEC(ZSTD(1)) , audit_user String  CODEC(ZSTD(1)) , audit_message String  CODEC(ZSTD(1)) , audit_status String  CODEC(ZSTD(1)) , audit_history String  CODEC(ZSTD(1)) , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp))) order by (event_source, audit_module, audit_remote_ip, audit_operation, audit_user, audit_message, audit_status, audit_history, timestamp)"

	InsertAuditTableBatchQuery1 = "Insert into auditTable1( event_source, audit_module, audit_remote_ip, audit_operation, audit_user, audit_message, audit_status, audit_history, timestamp)"

	CreateTrapTableQuery1 = "CREATE TABLE IF NOT EXISTS trapTable1(event_source String CODEC(ZSTD(1)) , plugin String  CODEC(ZSTD(1)) ,trap_name String  CODEC(ZSTD(1)) , trap_oid String  CODEC(ZSTD(1)) ,trap_version String  CODEC(ZSTD(1)) , enterprise_id String  CODEC(ZSTD(1)) , trap_message String  CODEC(ZSTD(1)) ,trap_severity String  CODEC(ZSTD(1))  , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp))) order by (event_source, trap_name, trap_version, trap_oid, enterprise_id, trap_message,timestamp)"

	InsertTrapTableBatchQuery1 = "Insert into trapTable1(event_source , plugin , trap_name , trap_oid ,trap_version , enterprise_id , trap_message , trap_severity ,timestamp)"

	//CreateHealthTableQuery = "CREATE TABLE IF NOT EXISTS health (event_source String ,engine_type string, pending_events Int64, total_events Int64, queued_events Int64, finished_events Int64, timestamp Int64)engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp)),plugin_id) order by ( event_source , engine_type , pending_events , total_events , queued_events , finished_events , timestamp)"
	CreateHealthTableQuery = "CREATE TABLE IF NOT EXISTS health (event_source String  CODEC(ZSTD(1))  ,engine_type String  CODEC(ZSTD(1)) , pending_events Int64  CODEC(ZSTD(1)) , total_events Int64  CODEC(ZSTD(1)) , queued_events Int64  CODEC(ZSTD(1)) , finished_events Int64  CODEC(ZSTD(1)) , cache_hit_ratio_percentage Int64  CODEC(ZSTD(1)) , cache_hits Int64  CODEC(ZSTD(1)) , cache_touches Int64  CODEC(ZSTD(1)) ,cache_misses Int64  CODEC(ZSTD(1)) , cache_evictions Int64  CODEC(ZSTD(1)) , cache_expires Int64  CODEC(ZSTD(1)) , cache_overwrites Int64  CODEC(ZSTD(1)) , cache_lookups Int64  CODEC(ZSTD(1)) , cache_average_access_time Int64  CODEC(ZSTD(1)) , cache_entries Int64  CODEC(ZSTD(1)) , vertical_pending_files Int64  CODEC(ZSTD(1)) , horizontal_pending_files Int64  CODEC(ZSTD(1)) , horizontal_aggregation_pending_files Int64  CODEC(ZSTD(1)) , total_aborted_queries Int64  CODEC(ZSTD(1)) , total_aggregation_queries Int64  CODEC(ZSTD(1)) , total_queries Int64  CODEC(ZSTD(1)) , log_pending_queries Int64  CODEC(ZSTD(1)) , flow_pending_queries Int64  CODEC(ZSTD(1)) , metric_pending_queries Int64  CODEC(ZSTD(1)) , drilldown_pending_queries Int64  CODEC(ZSTD(1)) , aiops_pending_queries Int64  CODEC(ZSTD(1)) , timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp))) order by ( event_source , engine_type , pending_events , total_events , queued_events , finished_events , cache_hit_ratio_percentage , cache_hits , cache_touches , cache_misses , cache_evictions , cache_expires , cache_overwrites , cache_lookups , cache_average_access_time, cache_entries , vertical_pending_files, horizontal_pending_files, horizontal_aggregation_pending_files, total_aborted_queries, total_aggregation_queries, total_queries, log_pending_queries, flow_pending_queries, metric_pending_queries, drilldown_pending_queries, aiops_pending_queries,timestamp)"

	InsertHealthQuery = "Insert into health ( event_source , engine_type , pending_events , total_events , queued_events , finished_events , cache_hit_ratio_percentage , cache_hits , cache_touches , cache_misses , cache_evictions , cache_expires , cache_overwrites , cache_lookups , cache_average_access_time, cache_entries , vertical_pending_files, horizontal_pending_files, horizontal_aggregation_pending_files, total_aborted_queries, total_aggregation_queries, total_queries, log_pending_queries, flow_pending_queries, metric_pending_queries, drilldown_pending_queries, aiops_pending_queries,timestamp)"

	CreateRunbookWorkLogTableQuery = "CREATE TABLE IF NOT EXISTS runbookWorklog (event_source String   CODEC(ZSTD(1)) , runbook_worklog_id Int64   CODEC(ZSTD(1)) , runbook_worklog_result String  CODEC(ZSTD(1))  , runbook_worklog_error  String  CODEC(ZSTD(1))  , runbook_worklog_status String  CODEC(ZSTD(1))  , runbook_worklog_type Int64  CODEC(ZSTD(1))  , Object_id Int64  CODEC(ZSTD(1))  , Policy_id Int64  CODEC(ZSTD(1))  , Username String  CODEC(ZSTD(1))  ,   timestamp Int64 CODEC( DoubleDelta,  ZSTD(1)))engine=MergeTree() PARTITION BY (toYYYYMMDD(toDateTime(timestamp))) order by (event_source, runbook_worklog_id , runbook_worklog_result, runbook_worklog_error, runbook_worklog_status , runbook_worklog_type , Object_id , Policy_id , Username ,   timestamp )"

	InsertRunbookWorkLogQuery = "Insert into runbookWorklog(event_source , runbook_worklog_id , runbook_worklog_result , runbook_worklog_error , runbook_worklog_status , runbook_worklog_type , Object_id , Policy_id , Username , timestamp)"

	CreateNetRouteMetricTableQuery = "CREATE TABLE IF NOT EXISTS netRouteMetrics ( object_id Int64 CODEC(ZSTD(1)),   netroute_ping_latency Float64 CODEC(ZSTD(1)),  netroute_max_ping_latency Float64 CODEC(ZSTD(1)), netroute_min_ping_latency Float64 CODEC(ZSTD(1)),   netroute_packet_lost Int64 CODEC(ZSTD(1)), netroute_event String CODEC(ZSTD(1)),  timestamp Int64 CODEC(DoubleDelta, ZSTD(1))) ENGINE = MergeTree()PARTITION BY (toYYYYMMDD(toDateTime(timestamp)))ORDER BY (object_id, timestamp);"

	InsertNetRouteMetricQuery = "INSERT INTO netRouteMetrics ( object_id , netroute_ping_latency, netroute_max_ping_latency, netroute_min_ping_latency,netroute_packet_lost ,netroute_event, timestamp)"

	CreateNetRouteStatusMetricQuery = "CREATE TABLE IF NOT EXISTS netRouteStatusMetrics ( object_id Int64 CODEC(ZSTD(1)),   metric String CODEC(ZSTD(1)),  seconds Int64 CODEC(ZSTD(1)), percent Float64 CODEC(ZSTD(1)),  timestamp Int64 CODEC(DoubleDelta, ZSTD(1))) ENGINE = MergeTree()PARTITION BY (toYYYYMMDD(toDateTime(timestamp)))ORDER BY (object_id, timestamp);"

	InsertNetRouteStatusMetricQuery = "INSERT INTO netRouteStatusMetrics ( object_id , metric, seconds, percent , timestamp)"
)

const ( //scalar metrics

	DummyNumericColumn         = "dummy.numeric.column"  //if constant is changed then do respective changes in clickhouse queries
	DummyNumericColumn2        = "dummy.numeric.column2" //if constant is changed then do respective changes in clickhouse queries
	DummyNumericColumn3        = "dummy.numeric.column3"
	DummyINT8FLOATColumn       = "dummy.int8Float.column"
	DummyINT16FLOATColumn      = "dummy.int16Float.column"
	DummyINT32FLOATColumn      = "dummy.int32Float.column"
	DummyINT64FLOATColumn      = "dummy.int64Float.column"
	DummyINTFLOATColumn        = "dummy.intFloat.column"
	DummyINTFLOATColumnCustom  = "dummy.intFloat.column.custom"
	DummyINTStringColumn       = "dummy.intString.column"
	DummyFLOATINTColumn        = "dummy.floatInt.column"
	SystemCPUPercent           = "system.cpu.percent"
	SystemCPUCores             = "system.cpu.cores"
	SystemDiskUsedPercent      = "system.disk.used.percent"
	SystemMemoryUsedBytes      = "system.memory.used.bytes"
	VLANPorts                  = "vlan.ports"
	SystemNetworkINBytes       = "system.network.in.bytes"
	SystemOSName               = "system.os.name"
	InterfaceAliasName         = "interface~alias.name"
	Interfaces                 = "interfaces"
	SystemMemoryCommittedBytes = "system.memory.committed.bytes"
	PingMaxLatency             = "ping.max.latency"
	SystemOsVersion            = "system.os.version"
	SystemOsRelease            = "system.os.release"

	//for aggregation metrics

	DummyINT8FLOATColumn1  = "interface~dummy.int8Float.column1"
	DummyINT16FLOATColumn1 = "interface~dummy.int16Float.column1"
	DummyINT32FLOATColumn1 = "interface~dummy.int32Float.column1"
	DummyINT64FLOATColumn1 = "interface~dummy.int64Float.column1"
	DummyFLOATColumn1      = "interface~dummy.float.column1"

	//instance metrics

	InterfaceBandwidth                   = "interface~bandwidth"
	InterfaceINTrafficUtilization        = "interface~in.traffic.utilization"
	InterfaceINTrafficUtilizationPercent = "interface~in.traffic.utilization.percent"
	InterfaceOUTTrafficUtilization       = "interface~out.traffic.utilization"
	InterfaceBytesPerSec                 = "interface~bytes.per.sec"
	InterfaceErrorPackets                = "interface~error.packets"
	InterfaceINPackets                   = "interface~in.packets"
	InterfaceOUTPackets                  = "interface~out.packets"
	InterfaceSentOctets                  = "interface~sent.octets"
	InterfaceSentBytes                   = "interface~sent.bytes"
	InterfaceReceivedOctets              = "interface~received.octets"
	InterfaceLastChange                  = "interface~last.change"
	InterfaceOperationalStatus           = "interface~operational.status"
	InterfaceAdminStatus                 = "interface~admin.status"
	InterfaceAlias                       = "interface~alias"
	InterfaceStatus                      = "interface~status"

	INT8CorruptedColumn           = "int8.corrupted.column"
	TimeTickCorruptedColumn       = "time.tick.corrupted.column"
	INT16CorruptedColumn          = "int16.corrupted.column"
	INT32CorruptedColumn          = "int32.corrupted.column"
	INT64CorruptedColumn          = "int64.corrupted.column"
	FLOAT64CorruptedColumnPercent = "float64.corrupted.column.percent"

	INT8CorruptedValuesPlugin    = "991-table"
	INT16CorruptedValuesPlugin   = "992-table"
	INT32CorruptedValuesPlugin   = "993-table"
	INT64CorruptedValuesPlugin   = "994-table"
	FLOAT64CorruptedValuesPlugin = "995-table"
	TimeTicksCorruptedPlugin     = "996-table"

	//config history data

	ConfigOperation         = "config.operation"
	ConfigOperationStatus   = "config.operation.status"
	ConfigOperationOutput   = "config.operation.output"
	ConfigOperationMessage  = "config.operation.message"
	ConfigOperationUserName = "config.operation.user.name"

	//runbook worklog result

	RunbookWorklogId     = "runbook.worklog.id"
	RunbookWorklogResult = "runbook.worklog.result"
	RunbookWorklogError  = "runbook.worklog.error"
	RunbookWorklogStatus = "runbook.worklog.status"
	RunbookWorklogType   = "runbook.worklog.type"
	PolicyId             = "policy.id"
	UserName             = "username"

	// compliance

	CompliancePercent = "compliance.percent"

	// NetRouteMetric

	NetRoutePingLatency    = "netroute.ping.latency"
	NetRouteMaxPingLatency = "netroute.max.ping.latency"
	NetRouteMinPingLatency = "netroute.min.ping.latency"
	NetRoutePacketLost     = "netroute.packet.lost"
	NetRouteEvent          = "netroute.event"
)

var (
	Address = func() string {

		if runtime.GOOS == "windows" {

			return "*************:9000"
		} else {

			return "localhost:9000"
		}
	}()
)

// plugins
var (
	interfacePlugin      = "2000-interface"
	linuxPlugin          = "1000-linux"
	configHistoryPlugin  = "500018-config"
	runbookWorklogPlugin = "500022-runbook.worklog"
	compliancePlugin     = "500023-compliance"
)

var (
	verticalWriters []*VerticalWriter

	aliasNames = []string{"loopback0", "tunnel1", "loopback1", "tunnel2", "loopback2", "tunnel3"}

	metricStringValues = map[string]string{

		SystemOSName:               "linux",
		InterfaceAdminStatus:       "up",
		InterfaceStatus:            "up",
		InterfaceOperationalStatus: "down",
		InterfaceLastChange:        "1 day",
	}

	/*
		map of all the columns in scalar table . Map contains datatype
		, max value  and min value of the particular column
	*/
	SystemMetrics = map[string]map[string]int{

		SystemCPUPercent: {
			MetricDatatype: datastore.FloatingColumn,
			MetricMinValue: 0,
			MetricMaxValue: 100,
		},
		SystemDiskUsedPercent: {
			MetricDatatype: datastore.FloatingColumn,
			MetricMinValue: 0,
			MetricMaxValue: 100,
		},
		SystemMemoryUsedBytes: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: 1000000000,
			MetricMaxValue: 16000000000,
		},
		SystemNetworkINBytes: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: math.MaxInt32 + 1,
			MetricMaxValue: 922337203681,
		},
		SystemOSName: {
			MetricDatatype: datastore.StringColumn,
		},
		VLANPorts: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: 0,
			MetricMaxValue: math.MaxInt8,
		},
		Interfaces: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: 0,
			MetricMaxValue: math.MaxInt16,
		},
		SystemMemoryCommittedBytes: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: 0,
			MetricMaxValue: math.MaxInt32,
		},
		PingMaxLatency: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: 0,
			MetricMaxValue: 1654,
		},
	}

	NetRouteMetrics = map[string]map[string]int{

		NetRoutePingLatency: {
			MetricDatatype: datastore.FloatingColumn,
			MetricMinValue: 0,
			MetricMaxValue: 100,
		},
		NetRouteMaxPingLatency: {
			MetricDatatype: datastore.FloatingColumn,
			MetricMinValue: 0,
			MetricMaxValue: 500,
		},
		NetRouteMinPingLatency: {
			MetricDatatype: datastore.FloatingColumn,
			MetricMinValue: 0,
			MetricMaxValue: 100,
		},
		NetRoutePacketLost: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: 0,
			MetricMaxValue: 3,
		},
	}

	/*
			map of all the columns in instance table . Map contains datatype
			, max value  and min value of the particular column

		int8 column  interface~error.packets
		int16 column interface~in.traffic.utilization
		int32 column - interface~out.traffic.utilization
		int64 column - interface~in.packets , interface~out.packets
		floating column - interface~in.traffic.utilization
	*/
	InterfaceMetrics = map[string]map[string]int{

		InterfaceINPackets: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: math.MaxInt32 + 1,
			MetricMaxValue: 922337203681,
		}, InterfaceOUTPackets: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: math.MaxInt32 + 1,
			MetricMaxValue: 9223372036811,
		}, InterfaceSentOctets: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: 10000,
			MetricMaxValue: 9223372036,
		}, InterfaceReceivedOctets: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: 10000,
			MetricMaxValue: 9223372036,
		},
		InterfaceErrorPackets: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: 0,
			MetricMaxValue: math.MaxInt8,
		},
		InterfaceINTrafficUtilization: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: math.MaxInt8 + 1,
			MetricMaxValue: math.MaxInt16,
		},
		InterfaceOUTTrafficUtilization: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: math.MaxInt16 + 1,
			MetricMaxValue: math.MaxInt32,
		},
		InterfaceBytesPerSec: {
			MetricDatatype: datastore.IntegerColumn,
			MetricMinValue: MaxInt56 + 1,
			MetricMaxValue: MaxInt56 + 10,
		},

		InterfaceINTrafficUtilizationPercent: {
			MetricDatatype: datastore.FloatingColumn,
			MetricMinValue: math.MaxInt8,
			MetricMaxValue: math.MaxInt16,
		},
		InterfaceOperationalStatus: {
			MetricDatatype: datastore.StringColumn,
		}, InterfaceAdminStatus: {
			MetricDatatype: datastore.StringColumn,
		}, InterfaceStatus: {
			MetricDatatype: datastore.StringColumn,
		}, InterfaceLastChange: {
			MetricDatatype: datastore.StringColumn,
		}, InterfaceAlias: {
			MetricDatatype: datastore.StringColumn,
		},
	}

	/*
		below variables of type monitorColumnMetadata contains key value a pair of
		monitor corresponding to its column

		for eg :-

		monitorGroups :- 1 -> server

			2 -> server and so on
	*/
	monitorGroups = make(map[int32]string)

	monitorTags = make(map[int32]string)

	interfaceAdminStatuses = make(map[int32]string)

	interfaceStatuses = make(map[int32]string)

	interfaceOperationalStatuses = make(map[int32]string)

	interfaceLastChanges = make(map[int32]string)

	interfaceAliasNames = make(map[int32]string)

	interfaces = []string{"x", "y", "z", "w", "a"}

	mockMetricTokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	ioEvent storage.DiskIOEvent

	diskIOWaitGroup = &sync.WaitGroup{}

	timeRangeDays = 2 //number of days for which data is to be inserted

	pollingIntervalMinutes = 5 //min

	monitors = int32(10) //total number of monitors for which data is to be inserted

)

/*
Different types of tables contains different populating functions.
Every table is populated in populate databases functions.

insertion process :-

1. Create a map of keys and the values in the memory
2. Loops over a day with the interval of polling time
3. Generate Random data based on the dataType
4. Append the respective for clickhouse and adds the same value in metric batch
5. Then encode the batch with snappy and send it to the vertical writer  and sends the batch in clickHouse for insertion as well
6. Step 2,3,4 and 5  is repeated for number of days
*/

var benchmark = false

func PopulateDatabases(group *sync.WaitGroup, writers []*VerticalWriter, b *broker2.Broker) {

	broker = b

	verticalWriters = writers

	PopulateDefaultColumns()

	createTables()

	populateInterfaceDefaultColumns()

	waitGroup := &sync.WaitGroup{}

	if utils.EnvironmentType == utils.DatastoreBenchIntegrationEnvironment {

		waitGroup.Add(2)

		PopulateBenchMarkConfigs(true)

		go populateScalarTable(int64(pollingIntervalMinutes)*60, waitGroup)

		go populateInstanceTable(int64(pollingIntervalMinutes)*60, waitGroup)

	} else {

		waitGroup.Add(13)

		go populateVerticalInstanceStatusDurationTable(waitGroup)

		go populateVerticalStatusDurationTable(waitGroup)

		go populateAIOpsEngineTable1(waitGroup)

		go populateVerticalLastTable(waitGroup)

		go populateInstanceTable(int64(pollingIntervalMinutes)*60, waitGroup)

		go populateScalarTable(int64(pollingIntervalMinutes)*60, waitGroup)

		go populateCustomTableType1(600, "4000-dummy-numeric-column", CustomBatchQuery, Invalid, waitGroup, 4)

		go populateCustomTableType1(60, "9001-dummy-numeric-column", CustomBatchQuery1, Int8, waitGroup, 5)

		go populateCustomTableType1(60, "9002-dummy-numeric-column", CustomBatchQuery1, Int16, waitGroup, 6)

		go populateCustomTableType1(60, "9003-dummy-numeric-column", CustomBatchQuery1, Int32, waitGroup, 7)

		go populateCustomTableType1(60, "9004-dummy-numeric-column", CustomBatchQuery1, Int64, waitGroup, 8)

		go populateCustomTableType1(60, "9000-dummy-numeric-column", CustomBatchQuery1, Float64, waitGroup, 9)

		go populateCustomTableType2(600, waitGroup, 10)

		waitGroup.Wait()

		waitGroup.Add(14)

		go populateCustomTableType3(600, waitGroup, 11)

		go populateCustomTableType4(600, waitGroup, 12)

		go populateCustomTableType5(600, waitGroup, 13)

		go populateCustomTableType6(600, waitGroup, 14)

		go populateCorruptedStores(waitGroup)

		go populateTrapDrillDownScenarios(waitGroup)

		go populateCustomTableType7(1200, "301-dummyint8column", DummyINT8FLOATColumn1, Int8, []DataType{Int8, Int8, Float64, Float64}, waitGroup, 16)

		go populateCustomTableType7(1200, "302-dummyint16column", DummyINT16FLOATColumn1, Int16, []DataType{Int16, Int16, Float16, Float64}, waitGroup, 17)

		go populateCustomTableType7(1200, "303-dummyint32column", DummyINT32FLOATColumn1, Int32, []DataType{Int32, Int32, Float16, Float64}, waitGroup, 18)

		go populateCustomTableType7(1200, "304-dummyint64column", DummyINT64FLOATColumn1, Int64, []DataType{Int64, Int64, Float16, Float64}, waitGroup, 19)

		go populateCustomTableType7(1200, "305-dummyfloatcolumn", DummyFLOATColumn1, Float8, []DataType{Float8, Float8, Float16, Float64}, waitGroup, 20)

		go populatePolicyDurationTable(waitGroup)

		go populateNetRouteTable(300, waitGroup)

		go populateNetRouteStatusMetricTable(waitGroup)

	}

	waitGroup.Wait()

	group.Done()
}

//trap drilldown / vertical drilldown scenarios

func populateTrapDrillDownScenarios(waitGroup *sync.WaitGroup) {

	defer waitGroup.Done()

	/*
			There are 3/4 columns inserted in the scenarios
			TrapEnterprise
		    TrapSeverity
		    TrapMessage
		    TrapRawMessage
	*/

	/*

				scenario 1 :
					3 columns are inserted
			    scenario 2:
			        4 columns are inserted for first 2 ticks
			        3 columns are inserted for the next 2 ticks
			   scenario 3:
			        4 columns inserted for first 2 ticks of day 1
			        3 columns inserted for the 2 ticks of the day 2
			   scenario 4:
			        3 columns inserted for first 2 ticks of day 1
			        4 columns inserted for the 2 ticks of day 2
			   scenario 5:
		            3 columns inserted for first 2 ticks
		            4 columns inserted for next 2 ticks
	*/

	eventSource := IP6

	currTime := time.Now().UTC()

	tick := time.Date(currTime.Year(), currTime.Month(), currTime.Day(), 0, 0, 0, 0, time.UTC).Unix()

	datastore.AddBlobColumn(utils.TrapMessage)

	datastore.AddBlobColumn(utils.TrapRawMessage)

	populateTrapDrillDownScenario1(eventSource, "500001-scenario1.trap", tick)

	populateTrapDrillDownScenario2(eventSource, "500001-scenario2.trap", tick)

	populateTrapDrillDownScenario3(eventSource, "500001-scenario3.trap", tick)

	populateTrapDrillDownScenario4(eventSource, "500001-scenario4.trap", tick)

	populateTrapDrillDownScenario5(eventSource, "500001-scenario5.trap", tick)

}

func populateTrapDrillDownScenario1(eventSource, pluginId string, tick int64) {

	columns := []string{utils.TrapOID, utils.TrapEnterprise, utils.TrapSeverity, utils.TrapMessage}

	values := []interface{}{TrapOID1, "cisco1", "high", "message1"}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer := &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	batch := PackEventBatchV3(pluginId, tick, IP6, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

	values = []interface{}{TrapOID1, "cisco1", "high", "message2"}

	rowBuffer = &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	tick++

	batch = PackEventBatchV3(pluginId, tick, eventSource, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

}

func populateTrapDrillDownScenario2(eventSource, pluginId string, tick int64) {

	columns := []string{utils.TrapOID, utils.TrapEnterprise, utils.TrapSeverity, utils.TrapMessage, utils.TrapRawMessage}

	values := []interface{}{TrapOID1, "cisco1", "high", "message1", "raw.message1"}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer := &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	batch := PackEventBatchV3(pluginId, tick, IP6, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

	values = []interface{}{TrapOID1, "cisco1", "high", "message2", "raw.message2"}

	rowBuffer = &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	tick++

	batch = PackEventBatchV3(pluginId, tick, eventSource, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

	columns = []string{utils.TrapOID, utils.TrapEnterprise, utils.TrapSeverity, utils.TrapMessage}

	values = []interface{}{TrapOID1, "cisco1", "high", "message3"}

	dataTypes = []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer = &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	tick++

	batch = PackEventBatchV3(pluginId, tick, IP6, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

	values = []interface{}{TrapOID1, "cisco1", "high", "message4"}

	rowBuffer = &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	tick++

	batch = PackEventBatchV3(pluginId, tick, eventSource, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

}

func populateTrapDrillDownScenario3(eventSource, pluginId string, tick int64) {

	columns := []string{utils.TrapOID, utils.TrapEnterprise, utils.TrapSeverity, utils.TrapMessage, utils.TrapRawMessage}

	values := []interface{}{TrapOID1, "cisco1", "high", "message1", "raw.message1"}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer := &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	batch := PackEventBatchV3(pluginId, tick, IP6, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

	values = []interface{}{TrapOID1, "cisco1", "high", "message2", "raw.message2"}

	rowBuffer = &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	tick++

	batch = PackEventBatchV3(pluginId, tick, eventSource, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

	columns = []string{utils.TrapOID, utils.TrapEnterprise, utils.TrapSeverity, utils.TrapMessage}

	values = []interface{}{TrapOID1, "cisco1", "high", "message3"}

	dataTypes = []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer = &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	tick -= 86400 //changing the date

	batch = PackEventBatchV3(pluginId, tick, IP6, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

	values = []interface{}{TrapOID1, "cisco1", "high", "message4"}

	rowBuffer = &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	tick++

	batch = PackEventBatchV3(pluginId, tick, eventSource, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch
}

func populateTrapDrillDownScenario4(eventSource, pluginId string, tick int64) {

	columns := []string{utils.TrapOID, utils.TrapEnterprise, utils.TrapSeverity, utils.TrapMessage}

	values := []interface{}{TrapOID1, "cisco1", "high", "message1"}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer := &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	batch := PackEventBatchV3(pluginId, tick, IP6, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

	values = []interface{}{TrapOID1, "cisco1", "high", "message2"}

	rowBuffer = &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	tick++

	batch = PackEventBatchV3(pluginId, tick, eventSource, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

	columns = []string{utils.TrapOID, utils.TrapEnterprise, utils.TrapSeverity, utils.TrapMessage, utils.TrapRawMessage}

	values = []interface{}{TrapOID1, "cisco1", "high", "message3", "raw.message3"}

	dataTypes = []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer = &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	tick -= 86400 //changing the date

	batch = PackEventBatchV3(pluginId, tick, IP6, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

	values = []interface{}{TrapOID1, "cisco1", "high", "message4", "raw.message4"}

	rowBuffer = &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	tick++

	batch = PackEventBatchV3(pluginId, tick, eventSource, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

}

func populateTrapDrillDownScenario5(eventSource, pluginId string, tick int64) {

	columns := []string{utils.TrapOID, utils.TrapEnterprise, utils.TrapSeverity, utils.TrapMessage}

	values := []interface{}{TrapOID1, "cisco1", "high", "message1"}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer := &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	batch := PackEventBatchV3(pluginId, tick, IP6, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

	values = []interface{}{TrapOID1, "cisco1", "high", "message2"}

	rowBuffer = &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	tick++

	batch = PackEventBatchV3(pluginId, tick, eventSource, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

	columns = []string{utils.TrapOID, utils.TrapEnterprise, utils.TrapSeverity, utils.TrapMessage, utils.TrapRawMessage}

	values = []interface{}{TrapOID1, "cisco1", "high", "message3", "raw.message3"}

	dataTypes = []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer = &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	tick++

	batch = PackEventBatchV3(pluginId, tick, IP6, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch

	values = []interface{}{TrapOID1, "cisco1", "high", "message4", "raw.message4"}

	rowBuffer = &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	tick++

	batch = PackEventBatchV3(pluginId, tick, eventSource, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriters[0].Pending, 1)

	verticalWriters[0].Events <- batch
}

func PopulateDefaultColumns() {

	datastore.AddBlobColumn(utils.TrapMessage)

	datastore.AddBlobColumn(utils.Message)

	datastore.AddBlobColumn(utils.Event)

	datastore.AddBlobColumn(ConfigOperationOutput)

	datastore.AddFloatingColumn(DummyNumericColumn)

	datastore.AddFloatingColumn(DummyNumericColumn2)

	datastore.AddFloatingColumn(DummyNumericColumn3)

	datastore.AddFloatingColumn(SystemCPUPercent)

	datastore.AddFloatingColumn(InterfaceBandwidth)

	datastore.AddFloatingColumn(NetRoutePingLatency)

	datastore.AddFloatingColumn(NetRouteMinPingLatency)

	datastore.AddFloatingColumn(NetRouteMaxPingLatency)

	datastore.AddSearchableColumn(InterfaceOperationalStatus)

	datastore.AddSearchableColumn(Interface)

	datastore.AddSearchableColumn(InterfaceLastChange)

	datastore.AddSearchableColumn(Message)

	datastore.AddSearchableColumn(InterfaceStatus)

	datastore.AddSearchableColumn(InterfaceAdminStatus)

	datastore.AddColumnEncoder(utils.TrapMessage, Zstd)

	datastore.AddColumnEncoder("policy.message", Zstd)

	datastore.AddColumnEncoder(utils.Message, Zstd)

	datastore.AddColumnEncoder(utils.AuditMessage, Zstd)

	datastore.AddColumnEncoder(utils.UserNotificationMessage, Zstd)

	datastore.AddColumnEncoder(utils.TrapVersion, Zstd)

	datastore.AddColumnEncoder(utils.TrapName, Zstd)

	datastore.AddColumnEncoder(utils.TrapSeverity, Zstd)

	datastore.AddColumnEncoder(utils.TrapEnterpriseId, Zstd)

	datastore.AddHorizontalAggregationField("21007-event.policy###message")

	datastore.AddHorizontalAggregationField("21007-event.policy###severity")

	datastore.AddHorizontalAggregationField("21007-event.policy###metric")

	datastore.AddHorizontalAggregationField(TrapFlapPrefix + utils.TrapName)

	datastore.AddHorizontalAggregationField(TrapFlapPrefix + utils.TrapOID)

	datastore.AddHorizontalAggregationField(TrapFlapPrefix + utils.TrapVersion)

	datastore.AddHorizontalAggregationField(TrapFlapPrefix + utils.TrapEnterpriseId)

	datastore.AddHorizontalAggregationField(TrapFlapPrefix + utils.TrapVendor)

	datastore.AddHorizontalAggregationField(TrapFlapPrefix + utils.TrapMessage)

	datastore.AddHorizontalAggregationField(TrapFlapPrefix + utils.TrapSeverity)

}

/*
	Clickhouse Db structure

Database 1 :- Name  - dbTest

Tables :-

1. Scalar Table

name────────────────────────────┬─type────┬
│ monitor_id                    │ Int32   │
│ plugin_id                     │ Int32   │
│ group                         │ String  │
│ tag                           │ String  │
│ system_cpu_percent            │ Float64 │
│ system_disk_used_percent      │ Float64 │
│ system_memory_used_bytes      │ Int64   │
│ system_network_in_bytes       │ Int64   │
│ system_os_name                │ String  │
│ vlan_ports                    │ Int8    │
│ interfaces                    │ Int16   │
│ system_memory_committed_bytes │ Int32   │
│ system_disk_capacity_bytes 	│ Int64   │
│ system_cpu_user_percent 		│ Float64 │
│ timestamp                     │ Int64   │
└───────────────────────────────┴─────────┴
2. Instance Table

name───────────────┬─type───  ┬
│ monitor_id         │ Int32  │
│ plugin_id          │ Int32  │
│ instance           │ String │
│ instance_type      │ String │
│ group              │ String │
│ tag                │ String │
│ in_packets         │ Int64  │
│ out_packets        │ Int64  │
│ sent_octets        │ Int64  │
│ received_octets    │ Int64  │
│ last_change        │ String │
│ operational_status │ String │
│ admin_status       │ String │
│ status             │ String  │
│ timestamp          │ Int64  │
└────────────────────┴────────┴
*/
func populateInstanceTable(pollingInterval int64, waitGroup *sync.WaitGroup) {

	dates, times := QualifyDates(time.Now().UnixMilli(), timeRangeDays)

	for index := range dates {

		go func(tick int64) {

			var connection clickhouse.Conn

			var ctx context.Context

			var err error

			var instanceBatch driver.Batch

			if !benchmark {

				connection, ctx, err = GetClickhouseDBConnection(TestDatabase1)

				handleError(err)

				defer connection.Close()

				instanceBatch, err = connection.PrepareBatch(ctx, InstanceBatchQuery)

				handleError(err)
			}

			endTick := tick + 86400

			for tick < endTick {

				buffer := bytes2.Buffer{}

				batch := make(map[string][]utils.MotadataMap)

				for monitor := int32(1); monitor <= monitors; monitor++ {

					objectId := INT32ToStringValue(monitor)

					for _, instance := range interfaces {

						mockMetrics := map[string]interface{}{}

						for metric, metadata := range InterfaceMetrics {

							row := utils.MotadataMap{}

							if metadata[MetricDatatype] == datastore.StringColumn {

								if metric == InterfaceOperationalStatus {

									row[Value] = interfaceOperationalStatuses[monitor]

								}

								if metric == InterfaceAdminStatus {

									row[Value] = interfaceAdminStatuses[monitor]

								}

								if metric == InterfaceLastChange {

									row[Value] = interfaceLastChanges[monitor]
								}

								if metric == InterfaceStatus {

									row[Value] = interfaceStatuses[monitor]
								}

								if metric == InterfaceAlias {

									row[Value] = interfaceAliasNames[monitor]
								}

								row[Metric] = metric

								row[Datatype] = metadata[MetricDatatype]

								row[ObjectId] = monitor

								batch[objectId+utils.GroupSeparator+instance] = append(batch[objectId+utils.GroupSeparator+instance], row)

								continue
							}
							max := metadata[MetricMaxValue]

							min := metadata[MetricMinValue]

							var value interface{}

							value = int64(rand.Intn(max-min+1) + min)

							if metadata[MetricDatatype] == datastore.FloatingColumn {

								datastore.AddFloatingColumn(metric)

								value = utils.ToFlOAT(value) + 0.123

							}

							mockMetrics[metric] = value

							row[Metric] = metric
							row[Value] = value
							row[Datatype] = metadata[MetricDatatype]
							row[ObjectId] = monitor

							batch[objectId+utils.GroupSeparator+instance] = append(batch[objectId+utils.GroupSeparator+instance], row)

						}

						batch[objectId+utils.GroupSeparator+instance] = append(batch[objectId+utils.GroupSeparator+instance], utils.MotadataMap{

							Metric:         InterfaceInstanceName,
							Value:          instance,
							Datatype:       String,
							_Instance:      instance,
							utils.ObjectId: monitor,
						})

						batch[objectId+utils.GroupSeparator+instance] = append(batch[objectId+utils.GroupSeparator+instance], utils.MotadataMap{

							Metric:         Interface,
							Value:          instance,
							Datatype:       String,
							_Instance:      instance,
							utils.ObjectId: monitor,
						})

						if !benchmark {

							err = instanceBatch.Append(monitor, interfacePlugin, instance, Interface,
								monitorGroups[monitor],
								monitorTags[monitor],
								mockMetrics[InterfaceINPackets].(int64),
								mockMetrics[InterfaceOUTPackets].(int64),
								mockMetrics[InterfaceSentOctets].(int64),
								mockMetrics[InterfaceReceivedOctets].(int64),
								interfaceLastChanges[monitor],
								interfaceOperationalStatuses[monitor],
								interfaceAdminStatuses[monitor],
								interfaceStatuses[monitor],
								interfaceAliasNames[monitor],
								mockMetrics[InterfaceErrorPackets].(int64),
								mockMetrics[InterfaceINTrafficUtilization].(int64),
								mockMetrics[InterfaceOUTTrafficUtilization].(int64),
								mockMetrics[InterfaceBytesPerSec].(int64),
								mockMetrics[InterfaceINTrafficUtilizationPercent].(float64), tick)

							handleError(err)

						}

					}

				}

				PackPerformanceMetricDataBlockV2(batch, &buffer, tick, interfacePlugin, utils.PerformanceMetric)

				broker.Requests <- buffer.Bytes()

				tick += pollingInterval

			}

			if !benchmark {

				err = instanceBatch.Send()

				handleError(err)
			}

		}(times[index])
	}

	waitGroup.Done()

}

func populateScalarTable(pollingInterval int64, waitGroup *sync.WaitGroup) {

	dates, times := QualifyDates(time.Now().UnixMilli(), timeRangeDays)

	for index := range dates {

		go func(tick int64) {

			var connection clickhouse.Conn

			var ctx context.Context

			var err error

			var scalarBatch driver.Batch

			if !benchmark {

				connection, ctx, err = GetClickhouseDBConnection(TestDatabase1)

				handleError(err)

				defer connection.Close()

				scalarBatch, err = connection.PrepareBatch(ctx, ScalarBatchQuery)

				handleError(err)

			}

			endTick := tick + 86400

			for tick < endTick {

				batch := make(map[string][]utils.MotadataMap)

				buffer := bytes2.Buffer{}

				for monitor := int32(1); monitor <= monitors; monitor++ {

					objectId := ToString(monitor)

					mockMetrics := map[string]interface{}{}

					for metric, metadata := range SystemMetrics {

						row := utils.MotadataMap{}

						if metadata[MetricDatatype] == datastore.StringColumn {

							row[Metric] = metric
							row[Value] = metricStringValues[metric]
							row[Datatype] = metadata[MetricDatatype]
							row[_Instance] = utils.Empty
							row[ObjectId] = monitor

							batch[objectId] = append(batch[objectId], row)

							mockMetrics[metric] = metricStringValues[metric]

							continue
						}

						max := metadata[MetricMaxValue]

						min := metadata[MetricMinValue]

						var value interface{}

						value = int64(rand.Intn(max-min+1) + min)

						if metadata[MetricDatatype] == datastore.FloatingColumn {

							value = float64(value.(int64)) + rand.Float64()

							if value.(float64) > float64(max) {

								value = float64(max)
							}
						}

						mockMetrics[metric] = value

						row[Metric] = metric
						row[Value] = value
						row[Datatype] = metadata[MetricDatatype]
						row[_Instance] = utils.Empty
						row[ObjectId] = monitor

						batch[objectId] = append(batch[objectId], row)

					}

					if !benchmark {

						err = scalarBatch.Append(monitor, linuxPlugin,
							monitorGroups[monitor],
							monitorTags[monitor],
							ToFixed(mockMetrics[SystemCPUPercent].(float64)),
							ToFixed(mockMetrics[SystemDiskUsedPercent].(float64)),
							mockMetrics[SystemMemoryUsedBytes].(int64),
							mockMetrics[SystemNetworkINBytes].(int64),
							mockMetrics[SystemOSName].(string),
							mockMetrics[VLANPorts].(int64),
							mockMetrics[Interfaces].(int64),
							mockMetrics[SystemMemoryCommittedBytes].(int64),
							mockMetrics[PingMaxLatency].(int64), tick)

						handleError(err)
					}

				}

				PackPerformanceMetricDataBlockV2(batch, &buffer, tick, linuxPlugin, utils.PerformanceMetric)

				broker.Requests <- buffer.Bytes()

				tick += pollingInterval

			}

			if !benchmark {

				err = scalarBatch.Send()

				handleError(err)
			}

		}(times[index])

	}

	waitGroup.Done()

}

/*-------------------------custom store-----------------------------------------------*/
/*

Type1 :- monitor wise the data type rotation

Type2 :- date wise the data type rotation

Type3 :- date + plugin wise data type rotation
		 explanation := for plugin 7000 the data type alternates between int64 and string
						for plugin 8000 the data type alternates between float64 and string
						for plugin 10000 the data type alternates between float64 and int64

Type4 :- date + plugin wise int float data type combinations
		explanation := for plugin 9990 the data type alternates between int8, float8, float16, float64
                     for plugin 9991 the data type alternates between int16, float8, float16, float64
                     for plugin 9992 the data type alternates between int32, float8, float16, float64
                     for plugin 9993 the data type alternates between int64, float8, float16, float64

Type5 :- Custom 2 Day Query of INT-FLOAT datatype combination
		 explanation := for plugin 5000 data is inserted for two days and for two monitor
        Day 1 - Monitor 1 -> Int64 and Day 2 - Monitor1 -> float64 , Monitor2 -> float64

		 for plugin 5001 data is inserted for two days and for two monitor
        Day 1 - Monitor 1 -> Int64 and Day 2 - Monitor1 -> string , Monitor2 -> string


Type6 :- Custom 2 Day Query of INT-FLOAT datatype combination
		explanation := for plugin 7770 the current day datatype is INT and previous day datatype is FLOAT
  						for plugin 7771 the current day datatype is FLOAT and previous day datatype is INT


*/
func populateCustomTableType1(pollingInterval int64, plugin string, batchQuery string, fixedDataType DataType, waitGroup *sync.WaitGroup, writerId int) {

	connection, ctx, err := GetClickhouseDBConnection(TestDatabase2)

	handleError(err)

	defer connection.Close()

	err = connection.Exec(ctx, "use "+TestDatabase2)

	handleError(err)

	customBatch, err := connection.PrepareBatch(ctx, batchQuery)

	handleError(err)

	dates, times := QualifyDates(time.Now().UnixMilli(), timeRangeDays)

	dataType := fixedDataType

	for index := range dates {

		tick := times[index]

		endTick := tick + 86400

		for tick < endTick {

			batch := make(map[string][]utils.MotadataMap)

			buffer := bytes2.Buffer{}

			for monitor := int32(1); monitor <= 10; monitor++ {

				objectId := ToString(monitor)

				row := utils.MotadataMap{}

				if fixedDataType == Invalid {

					dataType = getDataType(monitor, true)
				}

				value := generateRandomValue(dataType)

				appendClickHouseBatch(monitor, plugin, tick, value, Float64, customBatch)

				row[Metric] = DummyNumericColumn
				row[Value] = value
				row[_Instance] = utils.Empty
				row[ObjectId] = monitor

				row[Datatype] = resolveDataType(dataType)

				batch[objectId] = append(batch[objectId], row)

			}

			PackPerformanceMetricDataBlockV1(batch, &buffer)

			atomic.AddInt32(&verticalWriters[writerId].Pending, 1)

			verticalWriters[writerId].Events <- utils.MotadataMap{
				utils.Plugin:            plugin,
				utils.Tick:              tick,
				datastore.DatastoreType: utils.PerformanceMetric,
				utils.BatchSize:         len(batch),
				utils.Buffer:            buffer.Bytes(),
			}

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(plugin)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: plugin, StoreType: utils.Index, Tick: utils.UnixToSeconds(tick), LookupEvent: false,
			}

			tick += pollingInterval
		}

	}

	err = customBatch.Send()

	handleError(err)

	waitGroup.Done()

}

func populateCustomTableType2(pollingInterval int64, waitGroup *sync.WaitGroup, writerId int) {

	connection, ctx, err := GetClickhouseDBConnection(TestDatabase2)

	plugin := "6000-dummy-numeric-column"

	handleError(err)

	defer connection.Close()

	err = connection.Exec(ctx, "use "+TestDatabase2)

	handleError(err)

	customBatch, err := connection.PrepareBatch(ctx, CustomBatchQuery2)

	handleError(err)

	dates, times := QualifyDates(time.Now().UnixMilli(), timeRangeDays)

	for index := range dates {

		dataType := getDataType(int32(index), false)

		tick := times[index]

		endTick := tick + 86400

		for tick < endTick {

			batch := make(map[string][]utils.MotadataMap)

			buffer := bytes2.Buffer{}

			for monitor := int32(1); monitor <= 10; monitor++ {

				objectId := ToString(monitor)

				row := utils.MotadataMap{}

				value := generateRandomValue(dataType)

				appendClickHouseBatch(monitor, plugin, tick, value, String, customBatch)

				row[Metric] = DummyNumericColumn2
				row[Value] = value
				row[_Instance] = utils.Empty
				row[ObjectId] = monitor

				row[Datatype] = resolveDataType(dataType)

				batch[objectId] = append(batch[objectId], row)

			}

			PackPerformanceMetricDataBlockV1(batch, &buffer)

			atomic.AddInt32(&verticalWriters[writerId].Pending, 1)

			verticalWriters[writerId].Events <- utils.MotadataMap{
				utils.Plugin:            plugin,
				utils.Tick:              tick,
				datastore.DatastoreType: utils.PerformanceMetric,
				utils.BatchSize:         len(batch),
				utils.Buffer:            buffer.Bytes(),
			}

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(plugin)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: plugin, StoreType: utils.Index, Tick: utils.UnixToSeconds(tick), LookupEvent: false,
			}

			tick += pollingInterval
		}

	}

	err = customBatch.Send()

	handleError(err)

	waitGroup.Done()

}

func populateCustomTableType3(polingInterval int64, waitGroup *sync.WaitGroup, writerId int) {

	var count = 0

	connection, ctx, err := GetClickhouseDBConnection(TestDatabase2)

	handleError(err)

	defer connection.Close()

	err = connection.Exec(ctx, "use "+TestDatabase2)

	handleError(err)

	dates, times := QualifyDates(time.Now().UnixMilli(), 10)

	for index := len(dates) - 1; index >= 0; index-- {

		count++

		dataType := Int64

		if count%2 != 0 {

			dataType = String
		}

		writeCustomTableType3(times[index], polingInterval, dataType, "7000-table", connection, ctx, writerId)

		dataType = String

		if count%2 != 0 {

			dataType = Float64

		}

		writeCustomTableType3(times[index], polingInterval, dataType, "8000-table", connection, ctx, writerId)

		dataType = Float64

		if count%2 != 0 {

			dataType = Int64

		}

		writeCustomTableType3(times[index], polingInterval, dataType, "10000-table", connection, ctx, writerId)

	}

	waitGroup.Done()
}

func populateCustomTableType4(pollingInterval int64, waitGroup *sync.WaitGroup, writerId int) {

	connection, ctx, err := GetClickhouseDBConnection(TestDatabase2)

	err = connection.Exec(ctx, "use "+TestDatabase2)

	handleError(err)

	writeINT8FLOATRecords(connection, ctx, pollingInterval, writerId)
	writeINT16FLOATRecords(connection, ctx, pollingInterval, writerId)
	writeINT32FLOATRecords(connection, ctx, pollingInterval, writerId)
	writeINT64FLOATRecords(connection, ctx, pollingInterval, writerId)

	waitGroup.Done()
}

/*----------------------------------- Populate INT Float custom data along with string------------------------------*/

/*
Data is stored in following manner :-

# We have 2 dataTypes Datatype 1 and datatype 2 and data is stored for 2 days only

1. For current day data is stored in higher datatype
2. For previous day data is stored in lower datatype

DummyTableType 3  -

Current Day Data :- Float64
Previous Day Data :- Int64
*/
func populateCustomTableType5(pollingInterval int64, waitGroup *sync.WaitGroup, writerId int) {

	datastore.AddFloatingColumn(DummyINTFLOATColumnCustom)

	connection, ctx, err := GetClickhouseDBConnection(TestDatabase2)

	handleError(err)

	err = connection.Exec(ctx, "use "+TestDatabase2)

	handleError(err)

	writeCustomMetricInt64Float("5000-table", pollingInterval, connection, ctx, writerId)

	writeCustomMetricInt64String("5001-table", pollingInterval, connection, ctx, writerId)

	waitGroup.Done()
}

/*------------------------------Executor Level Data type conversion-----------------------------------*/
func populateCustomTableType6(pollingInterval int64, waitGroup *sync.WaitGroup, writerId int) {

	connection, ctx, err := GetClickhouseDBConnection(TestDatabase2)

	handleError(err)

	defer connection.Close()

	err = connection.Exec(ctx, "use "+TestDatabase2)

	handleError(err)

	writeIntFloatMetric(connection, ctx, pollingInterval, writerId)

	writeFloatIntMetric(connection, ctx, pollingInterval, writerId)

	waitGroup.Done()

}

func populateCustomTableType7(pollingInterval int64, plugin, column string, fixedDataType DataType, dataTypes []DataType, waitGroup *sync.WaitGroup, writerId int) {

	datastore.AddFloatingColumn(column)

	datastore.UpdateVerticalAggregations(column, true)

	dates, times := QualifyDates(time.Now().UnixMilli(), 5)

	instance := "x"

	count := 0

	for index := len(dates) - 1; index >= 0; index-- {

		var dataType DataType

		if count <= 1 {

			dataType = fixedDataType

		} else {

			dataType = dataTypes[rand.Intn(len(dataTypes))]

		}

		tick := times[index]

		endTick := tick + 86400

		for tick < endTick {

			batch := make(map[string][]utils.MotadataMap)

			buffer := bytes2.Buffer{}

			for monitor := int32(1); monitor <= 10; monitor++ {

				key := ToString(monitor) + utils.GroupSeparator + instance

				row := utils.MotadataMap{}

				value := generateRandomValueV1(dataType)

				row[Metric] = column
				row[Value] = value
				row[_Instance] = instance
				row[ObjectId] = monitor

				row[Datatype] = resolveDataType(dataType)

				batch[key] = append(batch[key], row)

				batch[key] = append(batch[key], utils.MotadataMap{

					Metric:         InterfaceInstanceName,
					Value:          instance,
					Datatype:       String,
					_Instance:      instance,
					utils.ObjectId: monitor,
				})

				batch[key] = append(batch[key], utils.MotadataMap{

					Metric:         InterfaceAliasName,
					Value:          interfaceAliasNames[monitor],
					Datatype:       String,
					_Instance:      instance,
					utils.ObjectId: monitor,
				})

			}

			PackPerformanceMetricDataBlockV1(batch, &buffer)

			atomic.AddInt32(&verticalWriters[writerId].Pending, 1)

			verticalWriters[writerId].Events <- utils.MotadataMap{
				utils.Plugin:            plugin,
				utils.Tick:              tick,
				datastore.DatastoreType: utils.PerformanceMetric,
				utils.BatchSize:         len(batch),
				utils.Buffer:            buffer.Bytes(),
			}

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(plugin)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: plugin, StoreType: utils.Index, Tick: utils.UnixToSeconds(tick), LookupEvent: false,
			}

			tick += pollingInterval

		}

		count++

	}

	waitGroup.Done()

}

/*----------------------------------- Populate Corrupted Store functions------------------------------*/
func populateCorruptedStores(waitGroup *sync.WaitGroup) {

	//corrupted store plugin id 999
	startDate, startTick := QualifyDates(time.Now().UnixMilli(), 1)

	/*

		taking 6 different datatype and corrupting the segment files
	*/

	tick := startTick[0]

	date := startDate[0]

	corruptTimeTicks := false

	for index, plugin := range []string{INT8CorruptedValuesPlugin, INT16CorruptedValuesPlugin, INT32CorruptedValuesPlugin, INT64CorruptedValuesPlugin, FLOAT64CorruptedValuesPlugin, TimeTicksCorruptedPlugin} {

		if plugin == TimeTicksCorruptedPlugin {

			corruptTimeTicks = true
		}

		writeCorruptedStores(tick, plugin, int32(index+1), getCorruptedColumn(plugin), date, corruptTimeTicks, true)
	}

	waitGroup.Done()

}

func populateInterfaceDefaultColumns() {

	for monitor := int32(1); monitor <= monitors; monitor++ {

		if monitor <= monitors/2 {

			monitorGroups[monitor] = ServerGroup

			monitorTags[monitor] = Tag1

			interfaceAdminStatuses[monitor] = "up"

			interfaceStatuses[monitor] = "up"

			interfaceOperationalStatuses[monitor] = "maintenance"

			interfaceLastChanges[monitor] = "1 day"

			interfaceAliasNames[monitor] = aliasNames[rand.Intn(len(aliasNames))]

		} else {

			monitorGroups[monitor] = NetworkGroup

			monitorTags[monitor] = Tag2

			interfaceAdminStatuses[monitor] = "down"

			interfaceStatuses[monitor] = "down"

			interfaceOperationalStatuses[monitor] = "unknown"

			interfaceLastChanges[monitor] = "1 year"

			interfaceAliasNames[monitor] = aliasNames[rand.Intn(len(aliasNames))]

		}

	}
}

func populateAIOpsEngineTable1(group *sync.WaitGroup) {

	defer group.Done()

	utils.VerticalAggregationSyncTimerSeconds = 10

	currentTime := time.Now()

	startTick := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-1, 0, 0, 0, 0, time.UTC).Unix()

	endTick := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 23, 59, 59, 0, time.UTC).Unix()

	counters := []string{"system.cpu.kernel.percent", "system.memory.used.bytes1"} //float, int

	for _, counter := range counters {

		datastore.UpdateVerticalAggregations(counter, true)
	}

	index := 0

	for startTick < endTick {

		index++

		batch := make(map[string][]utils.MotadataMap)

		for _, counter := range counters {

			for monitor := 1; monitor <= 2; monitor++ {

				objectId := ToString(monitor)

				row := utils.MotadataMap{}

				if counter == "system.cpu.kernel.percent" {

					row["datatype"] = datastore.FloatingColumn

					if index%20 == 0 {

						row["value"] = float64(100)

					} else {

						row["value"] = float64(5)

					}

				} else if counter == "system.memory.used.bytes1" {

					row["datatype"] = datastore.IntegerColumn

					if index%20 == 0 {

						row["value"] = int64(100)

					} else {

						row["value"] = int64(5)

					}
				}

				row["metric"] = counter

				row[utils.ObjectId] = int32(monitor)

				row["instance"] = utils.Empty

				batch[objectId] = append(batch[objectId], row)

			}

		}

		buffer := bytes2.Buffer{}

		PackPerformanceMetricDataBlockV2(batch, &buffer, startTick, AIOpsPlugin1, utils.PerformanceMetric)

		broker.Requests <- buffer.Bytes()

		startTick += 300
	}

}

//vertical last table

var (
	C1VerticalLastValues = make([][]int8, 7)

	C2VerticalLastValues = make([][]int16, 7)

	C3VerticalLastValues = make([][]int32, 7)

	C4VerticalLastValues = make([][]int64, 7)

	C5VerticalLastValues = make([][]float64, 7)

	C6VerticalLastValues = make([][]string, 7)
)

func populateVerticalLastTable(group *sync.WaitGroup) {

	defer group.Done()

	startTime := time.Now().UTC()

	startTime = time.Date(startTime.Year(), startTime.Month(), startTime.Day()-6, 0, 0, 0, 0, time.UTC)

	columns := []string{"c1", "c2", "c3", "c4", "c5", "c6"}

	eventSources := []string{"1", "2"}

	pool := utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools)

	encoder := NewEncoder(pool)

	tokenizer := &utils.Tokenizer{

		Tokens: make([]string, 30),
	}

	c1Count := int8(0)
	c2Count := int16(math.MaxInt8)
	c3Count := int32(math.MaxInt16)
	c4Count := int64(math.MaxInt32)
	c5Count := float64(math.MaxInt8)

	c6ColumnValues := []string{"coldstart", "warm", "link down", "linkup", "link fail"}

	for i := 0; i < 7; i++ {

		startTick := startTime.Unix()

		endTick := startTick + 86400

		store := datastore.GetStore(datastore.GetStoreName(utils.UnixToSeconds(startTick), "sample", datastore.VerticalStore), utils.PerformanceMetric, true, true, encoder, tokenizer)

		c1Values := make(map[string][]int8)
		c2Values := make(map[string][]int16)
		c3Values := make(map[string][]int32)
		c4Values := make(map[string][]int64)
		c5Values := make(map[string][]float64)
		c6Values := make(map[string][]string)

		ticks := make(map[string][]int32)

		for startTick < endTick {

			value := c6ColumnValues[rand.Intn(len(c6ColumnValues))]

			for _, column := range columns {

				for _, source := range eventSources {

					key := datastore.GetMetricKey(source, column, "")

					if column == "c1" {

						c1Values[key] = append(c1Values[key], c1Count)

					} else if column == "c2" {

						c2Values[key] = append(c2Values[key], c2Count)

					} else if column == "c3" {

						c3Values[key] = append(c3Values[key], c3Count)

					} else if column == "c4" {

						c4Values[key] = append(c4Values[key], c4Count)

					} else if column == "c5" {

						c5Values[key] = append(c5Values[key], c5Count)

					} else {

						c6Values[key] = append(c6Values[key], value)

					}

					ticks[key+utils.KeySeparator+"time"+utils.KeySeparator+"0"] = append(ticks[key+utils.KeySeparator+"time"+utils.KeySeparator+"0"], utils.UnixToSeconds(startTick))
				}
			}

			C1VerticalLastValues[i] = append(C1VerticalLastValues[i], c1Count)

			c1Count++

			C2VerticalLastValues[i] = append(C2VerticalLastValues[i], c2Count)

			c2Count++

			C3VerticalLastValues[i] = append(C3VerticalLastValues[i], c3Count)

			c3Count++

			C4VerticalLastValues[i] = append(C4VerticalLastValues[i], c4Count)

			c4Count++

			C5VerticalLastValues[i] = append(C5VerticalLastValues[i], c5Count)

			c5Count++

			C6VerticalLastValues[i] = append(C6VerticalLastValues[i], value)

			startTick += 300
		}

		for key, entries := range c1Values {

			bufferIndex, bufferBytes, _ := encoder.EncodeINT8Values(None, entries, utils.MaxValueBytes)

			_ = store.Put([]byte(key+utils.KeySeparator+"0"), bufferBytes, encoder, tokenizer)

			encoder.MemoryPool.ReleaseBytePool(bufferIndex)

			writeTimeTicks(encoder, store, []byte(key+utils.KeySeparator+"time"+utils.KeySeparator+"0"), ticks[key+utils.KeySeparator+"time"+utils.KeySeparator+"0"], tokenizer)

		}

		for key, entries := range c2Values {

			bufferIndex, bufferBytes, _ := encoder.EncodeINT16Values(None, entries, utils.MaxValueBytes)

			_ = store.Put([]byte(key+utils.KeySeparator+"0"), bufferBytes, encoder, tokenizer)

			encoder.MemoryPool.ReleaseBytePool(bufferIndex)

			writeTimeTicks(encoder, store, []byte(key+utils.KeySeparator+"time"+utils.KeySeparator+"0"), ticks[key+utils.KeySeparator+"time"+utils.KeySeparator+"0"], tokenizer)

		}

		for key, entries := range c3Values {

			bufferIndex, bufferBytes, _ := encoder.EncodeINT32Values(entries, None, Int32, 32, utils.MaxValueBytes)

			_ = store.Put([]byte(key+utils.KeySeparator+"0"), bufferBytes, encoder, tokenizer)

			encoder.MemoryPool.ReleaseBytePool(bufferIndex)

			writeTimeTicks(encoder, store, []byte(key+utils.KeySeparator+"time"+utils.KeySeparator+"0"), ticks[key+utils.KeySeparator+"time"+utils.KeySeparator+"0"], tokenizer)

		}

		for key, entries := range c4Values {

			bufferIndex, bufferBytes, _ := encoder.EncodeINT64Values(entries, None, Int64, utils.MaxValueBytes)

			_ = store.Put([]byte(key+utils.KeySeparator+"0"), bufferBytes, encoder, tokenizer)

			encoder.MemoryPool.ReleaseBytePool(bufferIndex)

			writeTimeTicks(encoder, store, []byte(key+utils.KeySeparator+"time"+utils.KeySeparator+"0"), ticks[key+utils.KeySeparator+"time"+utils.KeySeparator+"0"], tokenizer)

		}

		for key, entries := range c5Values {

			bufferIndex, bufferBytes, _ := encoder.EncodeFLOAT64Values(entries, None, Float64, utils.MaxValueBytes)

			_ = store.Put([]byte(key+utils.KeySeparator+"0"), bufferBytes, encoder, tokenizer)

			encoder.MemoryPool.ReleaseBytePool(bufferIndex)

			writeTimeTicks(encoder, store, []byte(key+utils.KeySeparator+"time"+utils.KeySeparator+"0"), ticks[key+utils.KeySeparator+"time"+utils.KeySeparator+"0"], tokenizer)

		}

		for key, entries := range c6Values {

			bufferIndex, bufferBytes, _ := encoder.EncodeStringValues(entries, None, utils.MaxValueBytes, "")

			_ = store.Put([]byte(key+utils.KeySeparator+"0"), bufferBytes, encoder, tokenizer)

			encoder.MemoryPool.ReleaseBytePool(bufferIndex)

			writeTimeTicks(encoder, store, []byte(key+utils.KeySeparator+"time"+utils.KeySeparator+"0"), ticks[key+utils.KeySeparator+"time"+utils.KeySeparator+"0"], tokenizer)

		}

		startTime = startTime.AddDate(0, 0, 1)

	}

	verticalLastExpectedValues := make(utils.MotadataMap)

	verticalLastExpectedValues["c1"] = C1VerticalLastValues
	verticalLastExpectedValues["c2"] = C2VerticalLastValues
	verticalLastExpectedValues["c3"] = C3VerticalLastValues
	verticalLastExpectedValues["c4"] = C4VerticalLastValues
	verticalLastExpectedValues["c5"] = C5VerticalLastValues
	verticalLastExpectedValues["c6"] = C6VerticalLastValues

	bufferBytes, _ := json.MarshalIndent(verticalLastExpectedValues, "", " ")

	_ = os.WriteFile(filepath.Dir(utils.CurrentDir)+utils.PathSeparator+"grid-vertical-last.json", bufferBytes, 0777)

}

func writeTimeTicks(encoder Encoder, store *storage.Store, keyBytes []byte, ticks []int32, tokenizer *utils.Tokenizer) {

	bufferIndex, bufferBytes := encoder.WriteUINT32Value(uint32(len(ticks)), 0)

	defer encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	var valueBytes []byte

	poolIndex := utils.NotAvailable

	if len(ticks) <= 10 {

		poolIndex, valueBytes = encoder.EncodeRLEDeltaINT32Values(ticks, ticks[0], len(bufferBytes)+utils.MaxValueBytes)

	} else {

		poolIndex, valueBytes = encoder.EncodeBP32INTValues(ticks, len(bufferBytes)+utils.MaxValueBytes)
	}

	copy(valueBytes[utils.MaxValueBytes:], bufferBytes)

	_ = store.Put(keyBytes, valueBytes, encoder, tokenizer)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

}

//duration table

func populateVerticalInstanceStatusDurationTable(waitGroup *sync.WaitGroup) {

	defer waitGroup.Done()

	connection, ctx, _ := GetClickhouseDBConnection(TestDatabase1)

	dates, times := QualifyDates(time.Now().UnixMilli(), 30)

	objects := 10

	instanceType := "interface"

	// status , reason , duration

	columns := []string{datastore.Instance, datastore.ObjectStatusFlapHistory, datastore.Reason, datastore.Duration, utils.ObjectId}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.IntegerColumn, datastore.IntegerColumn}

	for index := range dates {

		var clickHouseBatches []utils.MotadataMap

		tick := times[index]

		endTick := tick + 86400

		for tick < endTick {

			buffer := bytes2.Buffer{}

			batch := make(map[string][]utils.MotadataMap)

			for objectId := 0; objectId < objects; objectId++ {

				values := make([]interface{}, len(columns))

				tempStatus := EventStringColumnValues[datastore.Status][rand.Intn(len(EventStringColumnValues[datastore.Status]))]

				instance := EventStringColumnValues[datastore.Instance][rand.Intn(len(EventStringColumnValues[datastore.Instance]))]

				values[0] = instance

				values[1] = tempStatus

				values[2] = tempStatus + "time"

				values[3] = int32(10)

				values[4] = int32(objectId)

				for columnIndex, column := range columns {

					if column == datastore.Instance || column == utils.ObjectId {

						continue
					}

					row := utils.MotadataMap{}

					row[Metric] = "interface~" + column

					row[ObjectId] = objectId

					row[Value] = values[columnIndex]

					row[Datatype] = dataTypes[columnIndex]

					batch[ToString(objectId)+utils.GroupSeparator+ToString(values[0])] = append(batch[ToString(objectId)+utils.GroupSeparator+ToString(values[0])], row)

				}

				batch[ToString(objectId)+utils.GroupSeparator+ToString(values[0])] = append(batch[ToString(objectId)+utils.GroupSeparator+ToString(values[0])], utils.MotadataMap{

					Metric:         Interface + "~instance.name",
					Value:          ToString(values[0]),
					Datatype:       String,
					_Instance:      ToString(values[0]),
					utils.ObjectId: objectId,
				})

				batch[ToString(objectId)+utils.GroupSeparator+ToString(values[0])] = append(batch[ToString(objectId)+utils.GroupSeparator+ToString(values[0])], utils.MotadataMap{

					Metric:         Interface,
					Value:          ToString(values[0]),
					Datatype:       String,
					_Instance:      ToString(values[0]),
					utils.ObjectId: objectId,
				})

				clickHouseBatch := make(utils.MotadataMap)

				clickHouseBatch[datastore.Reason] = tempStatus + "time"

				clickHouseBatch[datastore.Status] = tempStatus

				clickHouseBatch[datastore.Monitor] = int32(objectId)

				clickHouseBatch[utils.Plugin] = statusDurationInstanceMetricPlugin

				clickHouseBatch[datastore.Instance] = instance

				clickHouseBatch[datastore.InstanceType] = instanceType

				clickHouseBatch[datastore.Duration] = int32(10)

				clickHouseBatch[EventTimestamp] = tick

				clickHouseBatch[Timestamp] = tick

				clickHouseBatches = append(clickHouseBatches, clickHouseBatch)

			}

			PackPerformanceMetricDataBlockV2(batch, &buffer, tick, statusDurationInstanceMetricPlugin, utils.ObjectStatusFlapMetric)

			broker.Requests <- buffer.Bytes()

			tick = tick + int64(300)

		}

		statusDurationInstanceMetricBatch, err := connection.PrepareBatch(ctx, InsertStatusDurationInstanceMetricBatchQuery)

		handleError(err)

		mergeClickHouseBatches(clickHouseBatches, InstanceStatusDurationMetric, statusDurationInstanceMetricBatch, "")

		flushClickHouseBatches(statusDurationInstanceMetricBatch)
	}

}

func populateVerticalStatusDurationTable(waitGroup *sync.WaitGroup) {

	defer waitGroup.Done()

	connection, ctx, _ := GetClickhouseDBConnection(TestDatabase1)

	dates, times := QualifyDates(time.Now().UnixMilli(), 30)

	objects := 10

	// status , reason , duration

	columns := []string{datastore.ObjectStatusFlapHistory, datastore.Reason, datastore.Duration, utils.ObjectId}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.IntegerColumn, datastore.IntegerColumn}

	for index := range dates {

		var clickHouseBatches []utils.MotadataMap

		tick := times[index]

		endTick := tick + 86400

		for tick < endTick {

			buffer := bytes2.Buffer{}

			batch := make(map[string][]utils.MotadataMap)

			for objectId := 0; objectId < objects; objectId++ {

				values := make([]interface{}, len(columns))

				tempStatus := EventStringColumnValues[datastore.Status][rand.Intn(len(EventStringColumnValues[datastore.Status]))]

				values[0] = tempStatus

				values[1] = tempStatus + "time"

				values[2] = int32(10)

				values[3] = int32(objectId)

				for columnIndex, column := range columns {

					if column == utils.ObjectId {

						continue
					}

					row := utils.MotadataMap{}

					row[Metric] = column

					row[ObjectId] = objectId

					row[Value] = values[columnIndex]

					row[Datatype] = dataTypes[columnIndex]

					batch[ToString(objectId)] = append(batch[ToString(objectId)], row)

				}

				clickHouseBatch := make(utils.MotadataMap)

				clickHouseBatch[datastore.Reason] = tempStatus + "time"

				clickHouseBatch[datastore.Status] = tempStatus

				clickHouseBatch[datastore.Monitor] = int32(objectId)

				clickHouseBatch[utils.Plugin] = statusDurationMetricPlugin

				clickHouseBatch[datastore.Duration] = int32(10)

				clickHouseBatch[EventTimestamp] = tick

				clickHouseBatch[Timestamp] = tick

				clickHouseBatches = append(clickHouseBatches, clickHouseBatch)

			}

			PackPerformanceMetricDataBlockV2(batch, &buffer, tick, statusDurationMetricPlugin, utils.ObjectStatusFlapMetric)

			broker.Requests <- buffer.Bytes()

			tick = tick + int64(300)

		}

		statusDurationMetricBatch, err := connection.PrepareBatch(ctx, InsertStatusDurationMetricBatchQuery)

		handleError(err)

		mergeClickHouseBatches(clickHouseBatches, ObjectStatusDurationMetric, statusDurationMetricBatch, "")

		flushClickHouseBatches(statusDurationMetricBatch)
	}

}

func populatePolicyDurationTable(group *sync.WaitGroup) {

	defer group.Done()

	var err error

	connection, ctx, err := GetClickhouseDBConnection(TestDatabase1)

	dates, times := QualifyDates(time.Now().UnixMilli(), days)

	columns := []string{utils.PolicyId, "duration", utils.PolicySeverity, utils.PolicyThreshold, utils.Value, utils.ObjectId, datastore.Instance}

	dataTypes := []byte{datastore.IntegerColumn, datastore.IntegerColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.IntegerColumn, datastore.StringColumn}

	instanceType := "interface"

	objectId := int32(1)

	policyId := int64(8780424176)

	instance := "x"

	for index := len(dates) - 1; index >= 0; index-- {

		policyDurationBatch, err = connection.PrepareBatch(ctx, InsertPolicyDurationBatchQuery)

		handleError(err)

		var clickHouseBatches []utils.MotadataMap

		tick := times[index]

		endTick := tick + 86400

		clickHouseBatch := make(map[string]interface{})

		severity := utils.Empty

		value := utils.Empty

		previousSeverity := utils.Empty

		previousValue := utils.Empty

		duration := 0

		for tick < endTick {

			severity = EventStringColumnValues[utils.PolicySeverity][rand.Intn(len(EventStringColumnValues[utils.PolicySeverity])-1)]

			value = EventStringColumnValues[utils.Value][rand.Intn(len(EventStringColumnValues[utils.Value])-1)]

			if severity != previousSeverity {

				//call clickhouse append function
				if previousSeverity != utils.Empty {

					clickHouseBatch[utils.ObjectId] = objectId

					clickHouseBatch[utils.Plugin] = policyDurationPlugin

					clickHouseBatch[utils.PolicyId] = policyId

					clickHouseBatch[utils.PolicySeverity] = previousSeverity

					clickHouseBatch[utils.Value] = previousValue

					clickHouseBatch[datastore.Instance] = instance

					clickHouseBatch[datastore.InstanceType] = instanceType

					clickHouseBatch[datastore.Duration] = duration

					clickHouseBatch[EventTimestamp] = tick

					clickHouseBatch[utils.PolicyThreshold] = ""

					clickHouseBatches = append(clickHouseBatches, clickHouseBatch)

					values := []interface{}{policyId, duration, previousSeverity, "", previousValue, objectId, instance}

					packEventBatchV1(policyDurationPlugin, tick, columns, values, dataTypes, utils.PolicyFlapHistory)

					duration = 0
				}

				clickHouseBatch = make(map[string]interface{})

				previousSeverity = severity

				previousValue = value

			}

			tick = tick + int64(300)

			duration += 300

		}

		mergeClickHouseBatches(clickHouseBatches, MetricPolicyDuration, policyDurationBatch, "")

		flushClickHouseBatches(policyDurationBatch)

	}

	err = connection.Close()

	handleError(err)

}

func populateNetRouteTable(pollingInterval int64, waitGroup *sync.WaitGroup) {

	datastore.AddBlobColumn(NetRouteEvent)

	dates, times := QualifyDates(time.Now().UnixMilli(), timeRangeDays)

	for index := range dates {

		go func(tick int64) {

			var connection clickhouse.Conn

			var ctx context.Context

			var err error

			var netRouteBatch driver.Batch

			if !benchmark {

				connection, ctx, err = GetClickhouseDBConnection(TestDatabase1)

				handleError(err)

				defer connection.Close()

				netRouteBatch, err = connection.PrepareBatch(ctx, InsertNetRouteMetricQuery)

				handleError(err)

			}

			endTick := tick + 86400

			for tick < endTick {

				batch := make(map[string][]utils.MotadataMap)

				eventBatch := make(map[string][]utils.MotadataMap)

				buffer := bytes2.Buffer{}

				buffer2 := bytes2.Buffer{}

				for id := int64(1); id <= int64(monitors); id++ {

					netRouteId := ToString(id)

					mockMetrics := map[string]interface{}{}

					for metric, metadata := range NetRouteMetrics {

						row := utils.MotadataMap{}

						max := metadata[MetricMaxValue]

						min := metadata[MetricMinValue]

						var value interface{}

						value = int64(rand.Intn(max-min+1) + min)

						if metadata[MetricDatatype] == datastore.FloatingColumn {

							value = float64(value.(int64)) + rand.Float64()

							if value.(float64) > float64(max) {

								value = float64(max)
							}
						}

						mockMetrics[metric] = value

						row[Metric] = metric
						row[Value] = value
						row[Datatype] = metadata[MetricDatatype]
						row[_Instance] = utils.Empty
						row[ObjectId] = id

						batch[netRouteId] = append(batch[netRouteId], row)

					}

					value := EventStringColumnValues[NetRouteEvent][rand.Intn(5)]

					mockMetrics[NetRouteEvent] = value

					mockMetrics[Timestamp] = tick

					row := utils.MotadataMap{}

					row[Metric] = NetRouteEvent
					row[Value] = value
					row[Datatype] = datastore.StringColumn
					row[_Instance] = utils.Empty
					row[ObjectId] = id

					eventBatch[netRouteId] = append(eventBatch[netRouteId], row)

					err = netRouteBatch.Append(
						id,
						mockMetrics[NetRoutePingLatency].(float64),
						mockMetrics[NetRouteMaxPingLatency].(float64),
						mockMetrics[NetRouteMinPingLatency].(float64),
						mockMetrics[NetRoutePacketLost].(int64),
						mockMetrics[NetRouteEvent].(string),
						tick,
					)

					handleError(err)

				}

				PackNetRouteMetric(batch, &buffer, tick, datastore.NetRouteMetricPlugin, utils.NetRouteMetric)

				PackNetRouteMetric(eventBatch, &buffer2, tick, datastore.NetRouteEventPlugin, utils.NetRouteMetric)

				broker.Requests <- buffer.Bytes()

				broker.Requests <- buffer2.Bytes()

				tick += pollingInterval

			}

			if !benchmark {

				err = netRouteBatch.Send()

				handleError(err)
			}

		}(times[index])

	}

	waitGroup.Done()
}

func populateNetRouteStatusMetricTable(waitGroup *sync.WaitGroup) {

	counters := utils.MotadataMap{
		"netroute.disabletime.percent":     true,
		"netroute.disabletime.seconds":     true,
		"netroute.downtime.percent":        true,
		"netroute.downtime.seconds":        true,
		"netroute.maintenancetime.percent": true,
		"netroute.maintenancetime.seconds": true,
		"netroute.unknowntime.percent":     true,
		"netroute.unknowntime.seconds":     true,
		"netroute.uptime.percent":          true,
		"netroute.uptime.seconds":          true,
		"netroute.suspendtime.percent":     true,
		"netroute.suspendtime.seconds":     true,
		"netroute.unreachabletime.percent": true,
		"netroute.unreachabletime.seconds": true,
	}

	for counter := range counters {

		datastore.UpdateVerticalAggregations(counter, true)
	}

	defer waitGroup.Done()

	connection, ctx, err := GetClickhouseDBConnection(TestDatabase1)

	handleError(err)

	defer connection.Close()

	dates, times := QualifyDates(time.Now().UnixMilli(), days)

	netRoutes := int64(10)

	// status , reason , duration

	statuses := EventStringColumnValues["netroute.status"]

	for index := range dates {

		tick := times[index]

		endTick := tick + 86400

		for tick < endTick {

			netRouteStatusMetricBatch, err := connection.PrepareBatch(ctx, InsertNetRouteStatusMetricQuery)

			handleError(err)

			for netRouteId := int64(1); netRouteId < netRoutes; netRouteId++ {

				tempStatus := statuses[rand.Intn(len(statuses))]

				duration := int64(10)

				for _, status := range statuses {

					if status == tempStatus {

						err = netRouteStatusMetricBatch.Append(
							netRouteId,
							datastore.NetRoute+utils.DotSeparator+status+"time",
							duration,
							float64(100),
							tick,
						)

						handleError(err)
					} else {

						err = netRouteStatusMetricBatch.Append(
							netRouteId,
							datastore.NetRoute+utils.DotSeparator+status+"time",
							int64(0),
							float64(0),
							tick,
						)

						handleError(err)
					}

				}
				packStatusMetric(datastore.NetRouteStatusPlugin, tick, utils.NetRouteStatusMetric, uint64(netRouteId), datastore.NetRoute+utils.DotSeparator+tempStatus+"time", "", "", uint64(duration))

			}

			if !benchmark {

				err = netRouteStatusMetricBatch.Send()

				handleError(err)

			}

			tick = tick + int64(300)

		}

	}

}

// custom functions for insertions

// Clickhouse data is in string format only . Column datatype is string only
func writeCustomTableType3(tick, pollingInterval int64, dataType DataType, plugin string, connection clickhouse.Conn, ctx context.Context, writerId int) {

	customBatch, err := connection.PrepareBatch(ctx, CustomBatchQuery3)

	handleError(err)

	endTick := tick + 86400

	for tick < endTick {

		batch := make(map[string][]utils.MotadataMap)

		buffer := bytes2.Buffer{}

		for monitor := int32(1); monitor <= 10; monitor++ {

			objectId := ToString(monitor)

			row := utils.MotadataMap{}

			value := generateRandomValue(dataType)

			err = customBatch.Append(monitor, plugin, ToString(value), tick)

			handleError(err)

			row[Metric] = DummyNumericColumn3

			row[Value] = value

			row[_Instance] = utils.Empty

			row[ObjectId] = monitor

			row[Datatype] = resolveDataType(dataType)

			batch[objectId] = append(batch[objectId], row)

		}

		PackPerformanceMetricDataBlockV1(batch, &buffer)

		atomic.AddInt32(&verticalWriters[writerId].Pending, 1)

		verticalWriters[writerId].Events <- utils.MotadataMap{
			utils.Plugin:            plugin,
			utils.Tick:              tick,
			datastore.DatastoreType: utils.PerformanceMetric,
			utils.BatchSize:         len(batch),
			utils.Buffer:            buffer.Bytes(),
		}

		utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(plugin)), utils.IndexWriters)] <- utils.IndexEvent{

			Plugin: plugin, StoreType: utils.Index, Tick: utils.UnixToSeconds(tick), LookupEvent: false,
		}

		tick += pollingInterval
	}

	err = customBatch.Send()

	handleError(err)

}

/*
1.In below functions we are making a slice of datatype. Slice contains only single int dataType as specified
in function name and all other possible float datatype greater than the int datatype.

2. For each day we select a random datatype from the list and put data of particular date in respective datatype only.

3.In clickhouse we insert every data in float64(max datatype) as it is not feasible to insert the data of different
datatype in a single column

for eg :- writeINT8FLOATRecords function  will populate the data of 31 days of 10 monitors. Each day contains a
random data of datatype INT8,FLOAt8,FLOAT16 or FLOAT64

The data is being inserted in order to cover the datatype conversion scenarios of various int types
*/
func writeINT8FLOATRecords(connection clickhouse.Conn, ctx context.Context, pollingInterval int64, writerId int) {

	dataTypes := []DataType{Int8, Float8, Float16, Float64}

	datastore.AddFloatingColumn(DummyINT8FLOATColumn)

	plugin := "9990-int8-float"

	dates, times := QualifyDates(time.Now().UnixMilli(), 31)

	customBatch, err := connection.PrepareBatch(ctx, CustomINT8FLOATBatchQuery)

	handleError(err)

	for index := len(dates) - 1; index >= 0; index-- {

		dataType := dataTypes[rand.Intn(len(dataTypes))]

		tick := times[index]

		endTick := tick + 86400

		for tick < endTick {

			batch := make(map[string][]utils.MotadataMap)

			buffer := bytes2.Buffer{}

			for monitor := int32(1); monitor <= 10; monitor++ {

				objectId := ToString(monitor)

				row := utils.MotadataMap{}

				value := generateRandomValue(dataType)

				err = customBatch.Append(monitor, plugin, utils.ToFlOAT(value), tick)

				handleError(err)

				row[Metric] = DummyINT8FLOATColumn

				row[Value] = value

				row[_Instance] = utils.Empty

				row[ObjectId] = monitor

				row[Datatype] = resolveDataType(dataType)

				batch[objectId] = append(batch[objectId], row)

			}

			PackPerformanceMetricDataBlockV1(batch, &buffer)

			atomic.AddInt32(&verticalWriters[writerId].Pending, 1)

			verticalWriters[writerId].Events <- utils.MotadataMap{
				utils.Plugin:            plugin,
				utils.Tick:              tick,
				datastore.DatastoreType: utils.PerformanceMetric,
				utils.BatchSize:         len(batch),
				utils.Buffer:            buffer.Bytes(),
			}

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(plugin)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: plugin, StoreType: utils.Index, Tick: utils.UnixToSeconds(tick), LookupEvent: false,
			}

			tick += pollingInterval

		}

	}

	err = customBatch.Send()

	handleError(err)
}

func writeINT16FLOATRecords(connection clickhouse.Conn, ctx context.Context, pollingInterval int64, writerId int) {

	dataTypes := []DataType{Int16, Float8, Float16, Float64}

	datastore.AddFloatingColumn(DummyINT16FLOATColumn)

	plugin := "9991-dummy-int16-column"

	dates, times := QualifyDates(time.Now().UnixMilli(), 31)

	customBatch, err := connection.PrepareBatch(ctx, CustomINT16FLOATBatchQuery)

	handleError(err)

	for index := len(dates) - 1; index >= 0; index-- {

		dataType := dataTypes[rand.Intn(len(dataTypes))]

		tick := times[index]

		endTick := tick + 86400

		for tick < endTick {

			batch := make(map[string][]utils.MotadataMap)

			buffer := bytes2.Buffer{}

			for monitor := int32(1); monitor <= 10; monitor++ {

				objectId := ToString(monitor)

				row := utils.MotadataMap{}

				value := generateRandomValue(dataType)

				err = customBatch.Append(monitor, plugin, utils.ToFlOAT(value), tick)

				handleError(err)

				row[Metric] = DummyINT16FLOATColumn
				row[Value] = value
				row[_Instance] = utils.Empty
				row[ObjectId] = monitor

				row[Datatype] = resolveDataType(dataType)

				batch[objectId] = append(batch[objectId], row)

			}

			PackPerformanceMetricDataBlockV1(batch, &buffer)

			atomic.AddInt32(&verticalWriters[writerId].Pending, 1)

			verticalWriters[writerId].Events <- utils.MotadataMap{
				utils.Plugin:            plugin,
				utils.Tick:              tick,
				datastore.DatastoreType: utils.PerformanceMetric,
				utils.BatchSize:         len(batch),
				utils.Buffer:            buffer.Bytes(),
			}

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(plugin)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: plugin, StoreType: utils.Index, Tick: utils.UnixToSeconds(tick), LookupEvent: false,
			}

			tick += pollingInterval

		}

	}

	err = customBatch.Send()

	handleError(err)
}

func writeINT32FLOATRecords(connection clickhouse.Conn, ctx context.Context, pollingInterval int64, writerId int) {

	dataTypes := []DataType{Int32, Float8, Float16, Float64}

	datastore.AddFloatingColumn(DummyINT32FLOATColumn)

	plugin := "9992-int32-float-column"

	dates, times := QualifyDates(time.Now().UnixMilli(), 31)

	customBatch, err := connection.PrepareBatch(ctx, CustomINT32FLOATBatchQuery)

	handleError(err)

	for index := len(dates) - 1; index >= 0; index-- {

		dataType := dataTypes[rand.Intn(len(dataTypes))]

		tick := times[index]

		endTick := tick + 86400

		for tick < endTick {

			batch := make(map[string][]utils.MotadataMap)

			buffer := bytes2.Buffer{}

			for monitor := int32(1); monitor <= 10; monitor++ {

				objectId := ToString(monitor)

				row := utils.MotadataMap{}

				value := generateRandomValue(dataType)

				err = customBatch.Append(monitor, plugin, utils.ToFlOAT(value), tick)

				handleError(err)

				row[Metric] = DummyINT32FLOATColumn

				row[Value] = value

				row[_Instance] = utils.Empty

				row[ObjectId] = monitor

				row[Datatype] = resolveDataType(dataType)

				batch[objectId] = append(batch[objectId], row)

			}

			PackPerformanceMetricDataBlockV1(batch, &buffer)

			atomic.AddInt32(&verticalWriters[writerId].Pending, 1)

			verticalWriters[writerId].Events <- utils.MotadataMap{
				utils.Plugin:            plugin,
				utils.Tick:              tick,
				datastore.DatastoreType: utils.PerformanceMetric,
				utils.BatchSize:         len(batch),
				utils.Buffer:            buffer.Bytes(),
			}

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(plugin)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: plugin, StoreType: utils.Index, Tick: utils.UnixToSeconds(tick), LookupEvent: false,
			}

			tick += pollingInterval

		}

	}

	err = customBatch.Send()

	handleError(err)
}

func writeINT64FLOATRecords(connection clickhouse.Conn, ctx context.Context, pollingInterval int64, writerId int) {

	dataTypes := []DataType{Int64, Float8, Float16, Float64}

	datastore.AddFloatingColumn(DummyINT64FLOATColumn)

	plugin := "9993-int64-float"

	dates, times := QualifyDates(time.Now().UnixMilli(), 31)

	customBatch, err := connection.PrepareBatch(ctx, CustomINT64FLOATBatchQuery)

	handleError(err)

	for index := len(dates) - 1; index >= 0; index-- {

		dataType := dataTypes[rand.Intn(len(dataTypes))]

		tick := times[index]

		endTick := tick + 86400

		for tick < endTick {

			batch := make(map[string][]utils.MotadataMap)

			buffer := bytes2.Buffer{}

			for monitor := int32(1); monitor <= 10; monitor++ {

				objectId := ToString(monitor)

				row := utils.MotadataMap{}

				value := generateRandomValue(dataType)

				err = customBatch.Append(monitor, plugin, utils.ToFlOAT(value), tick)

				handleError(err)

				row[Metric] = DummyINT64FLOATColumn

				row[Value] = value

				row[_Instance] = utils.Empty

				row[ObjectId] = monitor

				row[Datatype] = resolveDataType(dataType)

				batch[objectId] = append(batch[objectId], row)

			}

			PackPerformanceMetricDataBlockV1(batch, &buffer)

			atomic.AddInt32(&verticalWriters[writerId].Pending, 1)

			verticalWriters[writerId].Events <- utils.MotadataMap{
				utils.Plugin:            plugin,
				utils.Tick:              tick,
				datastore.DatastoreType: utils.PerformanceMetric,
				utils.BatchSize:         len(batch),
				utils.Buffer:            buffer.Bytes(),
			}

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(plugin)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: plugin, StoreType: utils.Index, Tick: utils.UnixToSeconds(tick), LookupEvent: false,
			}

			tick += pollingInterval

		}

	}

	err = customBatch.Send()

	handleError(err)
}

func writeCustomMetricInt64Float(plugin string, pollingInterval int64, connection clickhouse.Conn, ctx context.Context, writerId int) {

	dates, times := QualifyDates(time.Now().UnixMilli(), days)

	count := 0

	for index := len(dates) - 1; index >= 0; index-- {

		customBatch, err := connection.PrepareBatch(ctx, CustomINTFLOATBatchQueryCustom)

		handleError(err)

		count++

		tick := times[index]

		endTick := tick + 86400

		var dataType DataType

		var startingIndex int

		if count == 1 {

			dataType = Int64

			startingIndex = 1

		} else {

			dataType = Float64

			startingIndex = 2

		}

		for tick < endTick {

			batch := make(map[string][]utils.MotadataMap)

			buffer := bytes2.Buffer{}

			for monitor := int32(startingIndex); monitor <= int32(startingIndex); monitor++ {

				objectId := ToString(monitor)

				row := utils.MotadataMap{}

				value := generateRandomValue(dataType)

				err = customBatch.Append(monitor, plugin, utils.ToFlOAT(value), tick)

				handleError(err)

				row[Metric] = DummyINTFLOATColumnCustom

				row[Value] = value

				row[_Instance] = utils.Empty

				row[ObjectId] = monitor

				row[Datatype] = resolveDataType(dataType)

				batch[objectId] = append(batch[objectId], row)

			}

			PackPerformanceMetricDataBlockV1(batch, &buffer)

			atomic.AddInt32(&verticalWriters[writerId].Pending, 1)

			verticalWriters[writerId].Events <- utils.MotadataMap{
				utils.Plugin:            plugin,
				utils.Tick:              tick,
				datastore.DatastoreType: utils.PerformanceMetric,
				utils.BatchSize:         len(batch),
				utils.Buffer:            buffer.Bytes(),
			}

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(plugin)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: plugin, StoreType: utils.Index, Tick: utils.UnixToSeconds(tick), LookupEvent: false,
			}

			tick += pollingInterval
		}

		err = customBatch.Send()

		handleError(err)

	}

}

func writeCustomMetricInt64String(plugin string, pollingInterval int64, connection clickhouse.Conn, ctx context.Context, writerId int) {

	dates, times := QualifyDates(time.Now().UnixMilli(), days)

	count := 0

	for index := len(dates) - 1; index >= 0; index-- {

		customBatch, err := connection.PrepareBatch(ctx, CustomINTStringBatchQueryCustom)

		handleError(err)

		count++

		tick := times[index]

		endTick := tick + 86400

		var dataType DataType

		var startingIndex int

		if count == 1 {

			dataType = String

			startingIndex = 1

		} else {

			dataType = Int64

			startingIndex = 2

		}

		for tick < endTick {

			batch := make(map[string][]utils.MotadataMap)

			buffer := bytes2.Buffer{}

			for monitor := int32(startingIndex); monitor <= int32(startingIndex); monitor++ {

				objectId := ToString(monitor)

				row := utils.MotadataMap{}

				value := generateRandomValue(dataType)

				err = customBatch.Append(monitor, plugin, ToString(value), tick)

				handleError(err)

				row[Metric] = DummyINTStringColumn

				row[Value] = value

				row[_Instance] = utils.Empty

				row[ObjectId] = monitor

				row[Datatype] = resolveDataType(dataType)

				batch[objectId] = append(batch[objectId], row)

			}

			PackPerformanceMetricDataBlockV1(batch, &buffer)

			atomic.AddInt32(&verticalWriters[writerId].Pending, 1)

			verticalWriters[writerId].Events <- utils.MotadataMap{
				utils.Plugin:            plugin,
				utils.Tick:              tick,
				datastore.DatastoreType: utils.PerformanceMetric,
				utils.BatchSize:         len(batch),
				utils.Buffer:            buffer.Bytes(),
			}

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(plugin)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: plugin, StoreType: utils.Index, Tick: utils.UnixToSeconds(tick), LookupEvent: false,
			}

			tick += pollingInterval
		}

		err = customBatch.Send()

		handleError(err)

	}

}

func writeIntFloatMetric(connection clickhouse.Conn, ctx context.Context, pollingInterval int64, writerId int) {

	datastore.AddFloatingColumn(DummyINTFLOATColumn)

	plugin := "7770-float-int"

	dates, times := QualifyDates(time.Now().UnixMilli(), days)

	customBatch, err := connection.PrepareBatch(ctx, CustomINTFLOATBatchQuery)

	handleError(err)

	for index := range dates {

		var dataType DataType

		if index == 0 {
			dataType = Int8
		} else {
			dataType = Float64
		}

		tick := times[index]

		endTick := tick + 86400

		for tick < endTick {

			batch := make(map[string][]utils.MotadataMap)

			buffer := bytes2.Buffer{}

			for monitor := int32(1); monitor <= 10; monitor++ {

				objectId := ToString(monitor)

				row := utils.MotadataMap{}

				value := generateRandomValue(dataType)

				err = customBatch.Append(monitor, plugin, utils.ToFlOAT(value), tick)

				handleError(err)

				row[Metric] = DummyINTFLOATColumn

				row[Value] = value

				row[_Instance] = utils.Empty

				row[ObjectId] = monitor

				row[Datatype] = resolveDataType(dataType)

				batch[objectId] = append(batch[objectId], row)

			}

			PackPerformanceMetricDataBlockV1(batch, &buffer)

			atomic.AddInt32(&verticalWriters[writerId].Pending, 1)

			verticalWriters[writerId].Events <- utils.MotadataMap{
				utils.Plugin:            plugin,
				utils.Tick:              tick,
				datastore.DatastoreType: utils.PerformanceMetric,
				utils.BatchSize:         len(batch),
				utils.Buffer:            buffer.Bytes(),
			}

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(plugin)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: plugin, StoreType: utils.Index, Tick: utils.UnixToSeconds(tick), LookupEvent: false,
			}

			tick += pollingInterval

		}

	}

	err = customBatch.Send()

	handleError(err)
}

func writeFloatIntMetric(connection clickhouse.Conn, ctx context.Context, pollingInterval int64, writerId int) {

	datastore.AddFloatingColumn(DummyFLOATINTColumn)

	plugin := "7771-float-int-column"

	dates, times := QualifyDates(time.Now().UnixMilli(), days)

	customBatch, err := connection.PrepareBatch(ctx, CustomFLOATINTBatchQuery)

	handleError(err)

	for index := range dates {

		var dataType DataType

		if index == 0 {
			dataType = Float64
		} else {
			dataType = Int8
		}

		tick := times[index]

		endTick := tick + 86400

		for tick < endTick {

			batch := make(map[string][]utils.MotadataMap)

			buffer := bytes2.Buffer{}

			for monitor := int32(1); monitor <= 10; monitor++ {

				objectId := ToString(monitor)

				row := utils.MotadataMap{}

				value := generateRandomValue(dataType)

				err = customBatch.Append(monitor, plugin, utils.ToFlOAT(value), tick)

				handleError(err)

				row[Metric] = DummyFLOATINTColumn

				row[Value] = value

				row[_Instance] = utils.Empty

				row[ObjectId] = monitor

				row[Datatype] = resolveDataType(dataType)

				batch[objectId] = append(batch[objectId], row)

			}

			PackPerformanceMetricDataBlockV1(batch, &buffer)

			atomic.AddInt32(&verticalWriters[writerId].Pending, 1)

			verticalWriters[writerId].Events <- utils.MotadataMap{
				utils.Plugin:            plugin,
				utils.Tick:              tick,
				datastore.DatastoreType: utils.PerformanceMetric,
				utils.BatchSize:         len(batch),
				utils.Buffer:            buffer.Bytes(),
			}

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(plugin)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: plugin, StoreType: utils.Index, Tick: utils.UnixToSeconds(tick), LookupEvent: false,
			}

			tick += pollingInterval

		}

	}

	err = customBatch.Send()

	handleError(err)
}

// flush data in the DB
func flushMotadataDBMetricCache(cacheEntries map[string][][]interface{}, corruptTimeTick bool, corruptValue bool) {

	encoder := NewEncoder(utils.NewMemoryPool(6, utils.MaxPoolLength, false, utils.DefaultBlobPools))

	bufferIndex := -1

	var bufferBytes []byte

	for key, values := range cacheEntries {

		tokens := strings.Split(key, utils.KeySeparator)

		storeName := tokens[len(tokens)-1]

		store := datastore.GetStore(storeName, utils.PerformanceMetric, true, true, encoder, mockMetricTokenizer)

		keyPart := strings.Join(tokens[:len(tokens)-2], utils.KeySeparator)

		key = strings.Join(tokens[:len(tokens)-1], utils.KeySeparator)

		err := flushTimeTicks(keyPart+utils.KeySeparator+"time"+utils.KeySeparator+tokens[len(tokens)-2], values[0], encoder, store, corruptTimeTick)

		handleError(err)

		bufferIndex, bufferBytes, err = encodeMetricValueCache(values[1][1:], encoder, values[1][0].(DataType), corruptValue)

		handleError(err)

		if corruptValue {

			bufferBytes[len(bufferBytes)-5] = 1
			bufferBytes[len(bufferBytes)-4] = 1
			bufferBytes[len(bufferBytes)-3] = 1
			bufferBytes[len(bufferBytes)-2] = 1
			bufferBytes[len(bufferBytes)-1] = 1
		}

		err = store.Put([]byte(key), bufferBytes, encoder, mockMetricTokenizer)

		handleError(err)

		encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	}
}

func encodeMetricValueCache(values []interface{}, encoder Encoder, dataType DataType, corruptValue bool) (int, []byte, error) {

	bufferIndex := -1

	var bufferBytes []byte

	var err error

	switch {

	case dataType == Int8:

		bufferIndex, bufferBytes, err = encoder.EncodeINT8Values(None, INT64InterfaceToINT8Values(values), utils.MaxValueBytes)

	case dataType == Int16:

		bufferIndex, bufferBytes, err = encoder.EncodeINT16Values(None, INT64InterfaceToINT16Values(values), utils.MaxValueBytes)

	case dataType == Int24 || dataType == Int32:

		bufferIndex, bufferBytes, err = encoder.EncodeINT32Values(INT64InterfaceToINT32Values(values), None, dataType, GetDataTypeBits(dataType), utils.MaxValueBytes)

	case dataType == Int40 || dataType == Int48 || dataType == Int56 || dataType == Int64:

		bufferIndex, bufferBytes, err = encoder.EncodeINT64Values(INT64InterfaceToINT64Values(values), None, dataType, utils.MaxValueBytes)

	case dataType == Float8 || dataType == Float16 || dataType == Float64:

		bufferIndex, bufferBytes, err = encoder.EncodeFLOAT64Values(utils.InterfaceToFLOAT64Values(values), None, dataType, utils.MaxValueBytes)

	}

	if corruptValue {

		bufferBytes[utils.MaxValueBytes] = byte(dataType) | byte(RLEDictionary)
	}

	return bufferIndex, bufferBytes, err
}

func flushTimeTicks(key string, ticks []interface{}, encoder Encoder, store *storage.Store, corrupt bool) error {

	bufferIndex := -1

	var bufferBytes []byte

	var err error

	length := len(ticks)

	var bytes []byte

	bufferIndex, bufferBytes = encoder.WriteUINT32Value(uint32(length), 0)

	defer encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	if length <= RLEDeltaEncodingMaxValues {

		bufferIndex, bytes = encoder.EncodeRLEDeltaINT32Values(INT32InterfaceToINT32Values(ticks), ticks[0].(int32), len(bufferBytes)+utils.MaxValueBytes)

	} else {

		bufferIndex, bytes = encoder.EncodeBP32INTValues(INT32InterfaceToINT32Values(ticks), len(bufferBytes)+utils.MaxValueBytes)
	}

	copy(bytes[utils.MaxValueBytes:], bufferBytes)

	if corrupt {

		bytes[len(bytes)-5] = 1
		bytes[len(bytes)-4] = 1
		bytes[len(bytes)-3] = 1
		bytes[len(bytes)-2] = 1
		bytes[len(bytes)-1] = 1
	}

	err = store.Put([]byte(key), bytes, encoder, mockMetricTokenizer)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	return err
}

func WriteStringMetric(key, value string, storeName string) error {

	encoder := NewEncoder(utils.NewMemoryPool(6, utils.MaxPoolLength, false, utils.DefaultBlobPools))

	keyBytes := []byte(key)

	store := datastore.GetStore(storeName, utils.StaticMetric, true, true, encoder, mockMetricTokenizer)

	poolIndex, bufferBytes := encoder.MemoryPool.AcquireBytePool(utils.MaxValueBufferBytes)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	_, bytes, err := store.Get(keyBytes, bufferBytes, encoder, ioEvent, diskIOWaitGroup, mockMetricTokenizer, false)

	handleError(err)

	var valueBytes []byte

	tokens := strings.Split(key, utils.KeySeparator)

	if len(tokens) == 4 && !strings.Contains(tokens[2], utils.InstanceSeparator) {

		mappingStore := datastore.GetStore(datastore.Object+utils.HyphenSeparator+"mappings", utils.Mapping, true, true, encoder, mockMetricTokenizer)

		err = mappingStore.PutStringMapping(tokens[0]+utils.GroupSeparator+tokens[1], encoder)

		handleError(err)

	}

	index, valueBytes := encoder.MemoryPool.AcquireBytePool(len(value) + utils.MaxValueBytes)

	copy(valueBytes[utils.MaxValueBytes:], value)

	if bytes == nil {

		err = store.Put(keyBytes, valueBytes, encoder, mockMetricTokenizer)

	} else if !bytes2.Equal(bytes, valueBytes[utils.MaxValueBytes:]) {

		err = store.Put(keyBytes, valueBytes, encoder, mockMetricTokenizer)
	}

	encoder.MemoryPool.ReleaseBytePool(index)

	return err
}

func writeNumericMetric(key string, tick int64, metric string, value interface{}, dataType DataType, status bool, numericMetricPolls map[string][][]interface{}) map[string][][]interface{} {

	currentDataType := Invalid

	position := -1

	if !status {

		if valueType := reflect.ValueOf(value).Kind(); valueType == reflect.Float64 {

			if datastore.IsFloatingColumn(metric) {

				if value, ok := value.(float64); ok {

					currentDataType = GetDataTypeFloat(value)

				}
			} else {

				value = int64(Round(value.(float64)))

				currentDataType = GetDataTypeINT(int(value.(int64)))
			}

		} else if valueType == reflect.Int64 || valueType == reflect.Uint64 || valueType == reflect.Int8 || valueType == reflect.Int16 || valueType == reflect.Int32 {

			if value, ok := value.(int64); ok {

				currentDataType = GetDataTypeINT(int(value))

			}
		}

	} else {

		currentDataType = dataType
	}

	cacheValues, _ := numericMetricPolls[key]

	var values []interface{}

	if cacheValues == nil {

		cacheValues = make([][]interface{}, 2)

		cacheValues[0] = make([]interface{}, 0)

		cacheValues[1] = make([]interface{}, 0)

	}

	values = cacheValues[1]

	cacheValues[0], position = datastore.AppendTimeTick(utils.UnixToSeconds(tick), cacheValues[0])

	if len(values) == 0 {

		if currentDataType >= Float8 {

			values = append(append(values, currentDataType), ToFixed(value.(float64)))

		} else {

			values = append(append(values, currentDataType), value.(int64))

		}

	} else {

		previousDataType := values[0].(DataType)

		if currentDataType >= Float8 || previousDataType >= Float8 {

			if currentDataType < Float8 { //previous datatype was float but current is

				value = float64(value.(int64))
			}

			values = datastore.AppendValue(position, ToFixed(value.(float64)), previousDataType, currentDataType, values)

		} else {

			values = datastore.AppendValue(position, value, previousDataType, currentDataType, values)
		}

	}

	cacheValues[1] = values

	numericMetricPolls[key] = cacheValues

	return numericMetricPolls

}

func getCorruptedColumn(plugin string) string {

	switch plugin {

	case INT8CorruptedValuesPlugin:

		return INT8CorruptedColumn

	case INT16CorruptedValuesPlugin:
		return INT16CorruptedColumn

	case INT32CorruptedValuesPlugin:
		return INT32CorruptedColumn

	case INT64CorruptedValuesPlugin:
		return INT64CorruptedColumn

	case FLOAT64CorruptedValuesPlugin:
		return FLOAT64CorruptedColumnPercent

	case TimeTicksCorruptedPlugin:
		return TimeTickCorruptedColumn

	}

	return ""
}

func writeCorruptedStores(tick int64, plugin string, monitor int32, corruptedColumn, date string, corruptTimeTick, corruptValue bool) {

	endTick := tick + 86400

	metricCaches := map[string][][]interface{}{}

	for tick < endTick {

		dataType := getDataType(monitor, true)

		value := generateRandomValue(dataType)

		key := INT32ToStringValue(monitor) + utils.KeySeparator + corruptedColumn + utils.KeySeparator + INTToStringValue(0)

		key = key + utils.KeySeparator + (date + utils.HyphenSeparator + datastore.VerticalStore + utils.HyphenSeparator + plugin)

		metricCaches = writeNumericMetric(key, tick, corruptedColumn, value, Invalid, false, metricCaches)

		tick += 1200
	}

	flushMotadataDBMetricCache(metricCaches, corruptTimeTick, corruptValue)
}

// clickhouse db constants

func GetClickhouseDBConnection(databaseName string) (clickhouse.Conn, context.Context, error) {

	var err error

	connection, err := clickhouse.Open(&clickhouse.Options{

		Addr: []string{Address},

		Auth: clickhouse.Auth{

			Database: databaseName,

			Username: Username,

			Password: Password,
		},

		DialTimeout: 20 * time.Minute,
	})

	if err != nil {

		return nil, nil, err
	}

	//don't set block size otherwise delta function value changes and testcases will fail. At every block size clickhouse set values to 0
	ctx := clickhouse.Context(context.Background(), clickhouse.WithSettings(clickhouse.Settings{
		"max_threads":                     5,
		"max_partitions_per_insert_block": 1000,
		"max_execution_time":              1200,
	}))

	err = connection.Exec(ctx, "use "+databaseName)

	handleError(err)

	return connection, ctx, nil

}

func createTables() {

	connection, ctx, err := GetClickhouseDBConnection(TestDatabase1)

	handleError(err)

	utils.CreateClickHouseTables(connection, ctx, []string{CreateScalarTableQuery, CreateInstanceTableQuery, CreateStatusDurationInstanceMetricTableQuery, CreateStatusDurationHorizontalTableQuery, CreateStatusDurationMetricTableQuery, CreatePolicyDurationTableQuery, CreateConfigHistoryQuery, CreateNetRouteMetricTableQuery, CreateNetRouteStatusMetricQuery})

	connection.Close()

	connection, ctx, err = GetClickhouseDBConnection(TestDatabase2)

	handleError(err)

	utils.CreateClickHouseTables(connection, ctx, []string{CreateScalarMetricDummyTableQuery, CreateScalarMetricDummyTableQuery1,
		CreateScalarMetricDummyTable2Query, CreateDummyTable3Query,
		CreateDummyINT8FLOATTableQuery, CreateDummyINT16FLOATTableQuery,
		CreateDummyINT32FLOATTableQuery, CreateDummyINT64FLOATTableQuery,
		CreateDummyINTFLOATTableQuery, CreateDummyFLOATINTTableQuery,
		CreateDummyINTFLOATTableQueryCustom, CreateDummyINTStringTableQueryCustom, CreateTableQueryType7})

	connection.Close()
}

// sends batch to clickhouse
func flushClickHouseBatches(batch driver.Batch) {

	err := batch.Send()

	handleError(err)

}

func appendClickHouseBatch(monitor int32, plugin string, tick int64, value interface{}, dataType DataType, batch driver.Batch) {

	if dataType != String {

		err := batch.Append(monitor, plugin, utils.ToFlOAT(value), tick)

		handleError(err)

	} else {

		err := batch.Append(monitor, plugin, ToString(value), tick)

		handleError(err)

	}
}

func getDataType(index int32, excludeStringDataType bool) DataType {

	index = index % 6

	if excludeStringDataType && index == 0 {

		index = 1
	}

	var dataType DataType

	switch index {

	case 1:
		dataType = Int8

	case 2:
		dataType = Int16

	case 3:
		dataType = Int32

	case 4:
		dataType = Int64

	case 5:
		dataType = Float64

	case 0:

		dataType = String

	}

	return dataType

}

func generateRandomValue(dataType DataType) interface{} {

	var value interface{}

	switch dataType {

	case Int8:

		value = int64(rand.Intn(math.MaxInt8))

	case Int16:

		value = int64(rand.Intn(math.MaxInt16-math.MaxInt8) + math.MaxInt8)

	case Int32:

		value = int64(rand.Intn(math.MaxInt32-math.MaxInt16) + math.MaxInt16)

	case Int64:

		value = int64(rand.Intn(math.MaxInt32+100-math.MaxInt32) + math.MaxInt32)

	case Float8:

		value = utils.ToFlOAT(rand.Intn(math.MaxInt8)) + 0.01

	case Float16:

		value = utils.ToFlOAT(rand.Intn(math.MaxInt16)) + 0.01

	case Float64:

		value = float64(rand.Intn(math.MaxInt32+1000)) + rand.Float64()

	case String:

		value = INTToStringValue(rand.Intn(100))
	}

	return value
}

func QualifyDates(tick int64, days int) (qualifiedDates []string, startTimes []int64) {

	startTime := time.UnixMilli(tick).UTC()

	for i := 0; i < days; i++ {

		year, month, day := startTime.Date()

		qualifiedDates = append(qualifiedDates, utils.SecondsToDate(utils.UnixToSeconds(startTime.Unix())))

		startTimes = append(startTimes, time.Date(year, month, day, 0, 0, 0, 0, time.UTC).Unix())

		startTime = startTime.AddDate(0, 0, -1)
	}

	return
}

// DataType conversion methods

func INT64InterfaceToINT8Values(values []interface{}) (int8Values []int8) {

	for _, value := range values {

		int8Values = append(int8Values, int8(value.(int64)))
	}

	return
}

func INT64InterfaceToINT16Values(values []interface{}) (int16Values []int16) {

	for _, value := range values {

		int16Values = append(int16Values, int16(value.(int64)))
	}

	return
}

func INT64InterfaceToINT32Values(values []interface{}) (int32Values []int32) {

	for _, value := range values {

		int32Values = append(int32Values, int32(value.(int64)))
	}

	return
}

func INT32InterfaceToINT32Values(values []interface{}) (int32Values []int32) {

	for _, value := range values {

		int32Values = append(int32Values, value.(int32))
	}

	return
}

func INT64InterfaceToINT64Values(values []interface{}) (int64Values []int64) {

	for _, value := range values {

		int64Values = append(int64Values, value.(int64))
	}

	return
}

func Float64InterfaceToFloat64Values(values []interface{}) (float64values []float64) {

	for _, value := range values {

		float64values = append(float64values, value.(float64))
	}

	return
}

func UINT64InterfaceToUINT64Values(values []interface{}) (uint64Values []uint64) {

	for _, value := range values {

		uint64Values = append(uint64Values, uint64(value.(int64)))
	}

	return
}

func handleError(err error) {

	if err != nil {

		panic(err)
	}
}

func resolveDataType(dataType DataType) int {

	if dataType == String {

		return datastore.StringColumn

	} else if dataType >= Float8 {

		return datastore.FloatingColumn

	} else {

		return datastore.IntegerColumn
	}

}

func PackPerformanceMetricDataBlockV1(dataBlocks map[string][]utils.MotadataMap, buffer *bytes2.Buffer) {

	for dataBlock := range dataBlocks {

		rowBuffer := bytes2.Buffer{}

		// first 4 bytes for length of the column buffer

		EncodeINT32Value(0, &rowBuffer)

		tokens := strings.Split(dataBlock, utils.GroupSeparator)

		//tokens[0] - object id , tokens[1] - instance

		//write object id
		EncodeINT32Value(int32(ToINT(tokens[0])), &rowBuffer)

		//write instance

		instance := utils.Empty

		if len(tokens) == 2 {

			instance = tokens[1]
		}

		EncodeINT32Value(int32(len(instance)), &rowBuffer)

		if instance != utils.Empty {

			rowBuffer.WriteString(instance)

		}

		rows := dataBlocks[dataBlock]

		for _, row := range rows {

			dataType := byte(row.GetIntValue("datatype"))

			rowBuffer.WriteByte(dataType)

			column := row.GetStringValue("metric")

			EncodeINT32Value(int32(len(column)), &rowBuffer)

			rowBuffer.WriteString(column)

			value := row["value"]

			if dataType == datastore.StringColumn {

				EncodeINT32Value(int32(len(ToString(value))), &rowBuffer)

				rowBuffer.WriteString(ToString(value))

			} else if dataType == datastore.FloatingColumn {

				EncodeFLOAT64Value(utils.ToFlOAT(value), &rowBuffer)
			} else {

				EncodeINT64Value(int64(ToINT(value)), &rowBuffer)
			}

		}

		EncodeINT32ValueAt(int32(rowBuffer.Len()-4), 0, &rowBuffer)

		rowBuffer.Write(utils.EOTBytes)

		buffer.Write(rowBuffer.Bytes())
	}

}

func PackPerformanceMetricDataBlockV2(dataBlocks map[string][]utils.MotadataMap, buffer *bytes2.Buffer, tick int64, pluginId string, dataStoreType utils.DatastoreType) {

	EncodeINT64Value(0, buffer) //ack id

	buffer.WriteByte(byte(0)) //operation type

	for dataBlock := range dataBlocks {

		rowBuffer := bytes2.Buffer{}

		EncodeINT64Value(tick, &rowBuffer) //tick

		EncodeINT32Value(int32(len(pluginId)), &rowBuffer)

		rowBuffer.WriteString(pluginId)

		rowBuffer.WriteByte(byte(StringToINT(utils.VerticalFormat)))

		rowBuffer.WriteByte(byte(dataStoreType))

		tokens := strings.Split(dataBlock, utils.GroupSeparator)

		//tokens[0] - object id , tokens[1] - instance

		//write object id
		EncodeINT32Value(int32(ToINT(tokens[0])), &rowBuffer)

		//write instance

		instance := utils.Empty

		if len(tokens) == 2 {

			instance = tokens[1]
		}

		EncodeINT32Value(int32(len(instance)), &rowBuffer)

		if instance != utils.Empty {

			rowBuffer.WriteString(instance)

		}

		rows := dataBlocks[dataBlock]

		for _, row := range rows {

			dataType := byte(row.GetIntValue("datatype"))

			rowBuffer.WriteByte(dataType)

			column := row.GetStringValue("metric")

			EncodeINT32Value(int32(len(column)), &rowBuffer)

			rowBuffer.WriteString(column)

			value := row["value"]

			if dataType == datastore.StringColumn {

				EncodeINT32Value(int32(len(ToString(value))), &rowBuffer)

				rowBuffer.WriteString(ToString(value))

			} else if dataType == datastore.FloatingColumn {

				EncodeFLOAT64Value(utils.ToFlOAT(value), &rowBuffer)
			} else {

				EncodeINT64Value(int64(ToINT(value)), &rowBuffer)
			}

		}

		dataBytes := snappy.Encode(nil, rowBuffer.Bytes())

		EncodeINT32Value(int32(len(dataBytes)), buffer)

		buffer.Write(dataBytes)

	}

}

func PackNetRouteMetric(dataBlocks map[string][]utils.MotadataMap, buffer *bytes2.Buffer, tick int64, pluginId string, dataStoreType utils.DatastoreType) {

	EncodeINT64Value(0, buffer) //ack id

	buffer.WriteByte(byte(0)) //operation type

	for dataBlock := range dataBlocks {

		rowBuffer := bytes2.Buffer{}

		EncodeINT64Value(tick, &rowBuffer) //tick

		EncodeINT32Value(int32(len(pluginId)), &rowBuffer)

		rowBuffer.WriteString(pluginId)

		rowBuffer.WriteByte(byte(StringToINT(utils.VerticalFormat)))

		rowBuffer.WriteByte(byte(dataStoreType))

		tokens := strings.Split(dataBlock, utils.GroupSeparator)

		//tokens[0] - object id , tokens[1] - instance

		//write object id
		EncodeINT64Value(int64(ToINT(tokens[0])), &rowBuffer)

		//write instance

		instance := utils.Empty

		if len(tokens) == 2 {

			instance = tokens[1]
		}

		EncodeINT32Value(int32(len(instance)), &rowBuffer)

		if instance != utils.Empty {

			rowBuffer.WriteString(instance)

		}

		rows := dataBlocks[dataBlock]

		for _, row := range rows {

			dataType := byte(row.GetIntValue("datatype"))

			rowBuffer.WriteByte(dataType)

			column := row.GetStringValue("metric")

			EncodeINT32Value(int32(len(column)), &rowBuffer)

			rowBuffer.WriteString(column)

			value := row["value"]

			if dataType == datastore.StringColumn {

				EncodeINT32Value(int32(len(ToString(value))), &rowBuffer)

				rowBuffer.WriteString(ToString(value))

			} else if dataType == datastore.FloatingColumn {

				EncodeFLOAT64Value(utils.ToFlOAT(value), &rowBuffer)
			} else {

				EncodeINT64Value(int64(ToINT(value)), &rowBuffer)
			}

		}

		dataBytes := snappy.Encode(nil, rowBuffer.Bytes())

		EncodeINT32Value(int32(len(dataBytes)), buffer)

		buffer.Write(dataBytes)

	}

}

func generateRandomValueV1(dataType DataType) interface{} {

	var value interface{}

	switch dataType {

	case Int8:

		value = int64(rand.Intn(math.MaxInt8 / 4))

	case Int16:

		value = int64(rand.Intn(math.MaxInt16/4-math.MaxInt8/4) + math.MaxInt8/4)

	case Int32:

		value = int64(rand.Intn(math.MaxInt32/4-math.MaxInt16/4) + math.MaxInt16/4)

	case Int64:

		value = int64(rand.Intn(math.MaxInt32/4+100-math.MaxInt32/4) + math.MaxInt32/4)

	case Float8:

		value = utils.ToFlOAT(rand.Intn(math.MaxInt8/4)) + 0.01

	case Float16:

		value = utils.ToFlOAT(rand.Intn(math.MaxInt16/4)) + 0.01

	case Float64:

		value = float64(rand.Intn(math.MaxInt32/4+1000)) + rand.Float64()

	case String:

		value = INTToStringValue(rand.Intn(100))
	}

	return value
}

func PopulateBenchMarkConfigs(aggregate bool) {

	benchmark = true

	timeRangeDays = 2

	monitors = 10

	datastore.UpdateVerticalAggregations(SystemCPUPercent, aggregate)

	datastore.UpdateVerticalAggregations(SystemDiskUsedPercent, aggregate)

	datastore.UpdateVerticalAggregations(SystemMemoryUsedBytes, aggregate)

	datastore.UpdateVerticalAggregations(SystemNetworkINBytes, aggregate)

	datastore.UpdateVerticalAggregations(SystemOSName, aggregate)

	datastore.UpdateVerticalAggregations(VLANPorts, aggregate)

	datastore.UpdateVerticalAggregations(Interfaces, aggregate)

	datastore.UpdateVerticalAggregations(SystemMemoryCommittedBytes, aggregate)

	datastore.UpdateVerticalAggregations(PingMaxLatency, aggregate)

	datastore.UpdateVerticalAggregations(InterfaceINPackets, aggregate)

	datastore.UpdateVerticalAggregations(InterfaceOUTPackets, aggregate)

	datastore.UpdateVerticalAggregations(InterfaceSentOctets, aggregate)

	datastore.UpdateVerticalAggregations(InterfaceReceivedOctets, aggregate)

	datastore.UpdateVerticalAggregations(InterfaceErrorPackets, aggregate)

	datastore.UpdateVerticalAggregations(InterfaceINTrafficUtilization, aggregate)

	datastore.UpdateVerticalAggregations(InterfaceOUTTrafficUtilization, aggregate)

	datastore.UpdateVerticalAggregations(InterfaceINTrafficUtilizationPercent, aggregate)

	datastore.UpdateVerticalAggregations(InterfaceOperationalStatus, aggregate)

	datastore.UpdateVerticalAggregations(InterfaceAdminStatus, aggregate)

	datastore.UpdateVerticalAggregations(InterfaceStatus, aggregate)

	datastore.UpdateVerticalAggregations(InterfaceLastChange, aggregate)

	datastore.UpdateVerticalAggregations(InterfaceAlias, aggregate)

}

func packStatusMetric(plugin string, tick int64, dataStoreType utils.DatastoreType, objectId uint64, metric, status, reason string, duration uint64) {

	dataBuffer := bytes2.Buffer{}

	EncodeINT64Value(tick, &dataBuffer)

	EncodeINT32Value(int32(len(plugin)), &dataBuffer)

	dataBuffer.WriteString(plugin)

	dataBuffer.WriteByte(byte(StringToINT(utils.VerticalFormat)))

	dataBuffer.WriteByte(byte(dataStoreType))

	dataBuffer.Write(binary.LittleEndian.AppendUint64(nil, objectId))

	writeStringValue(&dataBuffer, "")

	dataBuffer.WriteByte(0)

	writeStringValue(&dataBuffer, "")

	writeStringValue(&dataBuffer, metric)

	dataBuffer.WriteByte(0)

	writeStringValue(&dataBuffer, "")

	writeStringValue(&dataBuffer, status)

	dataBuffer.WriteByte(0)

	writeStringValue(&dataBuffer, "")

	writeStringValue(&dataBuffer, reason)

	dataBuffer.WriteByte(0)

	writeStringValue(&dataBuffer, "")

	dataBuffer.Write(binary.LittleEndian.AppendUint64(nil, duration))

	tempBuffer := bytes2.Buffer{}

	tempBuffer.Write(make([]byte, 9))

	data := snappy.Encode(nil, dataBuffer.Bytes())

	tempBuffer.Write(binary.LittleEndian.AppendUint32(nil, uint32(len(data))))

	tempBuffer.Write(data)

	broker.Requests <- tempBuffer.Bytes()

}

func writeStringValue(buffer *bytes2.Buffer, value string) {

	buffer.Write(binary.LittleEndian.AppendUint32(nil, uint32(len(value))))

	buffer.WriteString(value)

}
