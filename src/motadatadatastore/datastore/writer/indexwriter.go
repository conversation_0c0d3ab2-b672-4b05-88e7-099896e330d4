/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
	We write sparse index, termed as posting list.
	There are two types of posting lists: the normal posting list and the dummy posting list.
	The dummy posting list is used to identify plugin data in the corresponding time tick.
	In the normal posting list, we identify the time tick for the plugin's content.

	Note :
		- If the columns are searchable, we tokenize the data with space separators and then write a posting list for each token.
		  For non-searchable columns, we write data without tokenization in the posting list.
		- And for time tick representation we used bitmap.
*/

/* Change Logs:
* 2025-05-05			 Swapnil <PERSON><PERSON>		M<PERSON>ATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-08  			 Hardik Vala			MOTADATA-6073 Updated trim behaviour for Encoding String Values, discard default indexer column
* 2025-05-10             Dhaval Bera            MOTADATA-6174  Added Unmap function to release memory-mapped resources
* 2025-06-04             Aashil Shah            MOTADATA-5780 Called custom MmapAnonymous and Munmap functions
 */

package writer

import (
	"encoding/binary"
	"errors"
	"fmt"
	"github.com/dolthub/swiss"
	"github.com/kelindar/bitmap"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	. "motadatadatastore/storage"
	"motadatadatastore/utils"
	"runtime"
	"strings"
	"sync"
	"time"
)

var indexWriterLogger = utils.NewLogger("Index Writer", "writer")

type IndexWriter struct {
	tokenizer *utils.Tokenizer // used in store funcs

	waitGroup *sync.WaitGroup

	tokens *swiss.Map[string, struct{}]

	encoder Encoder

	decoder Decoder

	event DiskIOEvent

	indexType utils.DatastoreType

	Events chan utils.IndexEvent

	ShutdownNotifications chan bool

	valueBytes []byte

	writerId, tickPosition int

	shutdown bool
}

func NewIndexWriter(writerId int) *IndexWriter {

	pool := utils.NewMemoryPool(7, utils.MaxPoolLength, true, utils.DefaultBlobPools)

	bytes, err := utils.MmapAnonymous(utils.MaxValueBufferBytes)

	if err != nil {

		indexWriterLogger.Error(fmt.Sprintf("error %v occurred while mapping annonymous buffer for index writer %v", err, writerId))

		bytes = make([]byte, utils.MaxValueBufferBytes)
	}

	return &IndexWriter{

		writerId: writerId,

		encoder: NewEncoder(pool),

		decoder: NewDecoder(pool),

		ShutdownNotifications: make(chan bool, 5),

		valueBytes: bytes,

		Events: make(chan utils.IndexEvent, 1_00_00),

		tokens: swiss.NewMap[string, struct{}](1000),

		event: DiskIOEvent{},

		waitGroup: &sync.WaitGroup{},
	}
}

func (writer *IndexWriter) Start() {

	writer.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	go func() {

		utils.WriterEngineShutdownMutex.Add(1)

		for {

			if writer.shutdown || utils.GlobalShutdown {

				break
			}

			writer.run()
		}

		utils.WriterEngineShutdownMutex.Done()
	}()

}

func (writer *IndexWriter) run() {

	timer := time.NewTicker(time.Second * time.Duration(utils.GetMemoryPoolShrinkTimerSeconds()))

	defer func() {

		if err := recover(); err != nil {

			timer.Stop()

			stackTraceBytes := make([]byte, 1<<20)

			indexWriterLogger.Error(fmt.Sprintf("error %v occurred in index writer %v", err, writer.writerId))

			indexWriterLogger.Error(fmt.Sprintf("!!!STACK TRACE for index writer %v!!! \n %v", writer.writerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			indexWriterLogger.Error(fmt.Sprintf("index writer %v restarted", writer.writerId))

			return
		}

	}()

	for {

		select {

		case <-timer.C:

			writer.encoder.MemoryPool.ShrinkPool()

		case event := <-writer.Events:

			writer.writeIndex(event)

		case <-writer.ShutdownNotifications:

			indexWriterLogger.Info(fmt.Sprintf("shutting down Index writer %v", writer.writerId))

			if err := utils.Munmap(writer.valueBytes); err != nil {

				indexWriterLogger.Warn(fmt.Sprintf("failed to unmap anonymous mapped buffer,reason: %v", err.Error()))
			}

			writer.encoder.MemoryPool.Unmap()

			writer.shutdown = true

			return
		}
	}
}

func (writer *IndexWriter) writeIndex(event utils.IndexEvent) {

	defer writer.decoder.MemoryPool.TestPoolLeak()

	startTime := time.Now().UnixMilli()

	writer.tickPosition = utils.CalculateTickPosition(event.Tick)

	if utils.DebugEnabled() {

		indexWriterLogger.Debug(fmt.Sprintf("id:%v, plugin: %v, tick: %v", writer.writerId, event.Plugin, event.Tick))
	}

	// means posting list otherwise dummy posting list
	if event.LookupEvent {

		writer.resolveIndex(event.StoreType)

		if event.NumericContext != nil {

			store := datastore.GetStore(datastore.GetStoreName(event.Tick, event.Column+utils.HyphenSeparator+event.Plugin, datastore.HorizontalStore)+utils.HyphenSeparator+INTToStringValue(int(Int64)), writer.indexType, true, true, writer.encoder, writer.tokenizer)

			if store == nil {

				indexWriterLogger.Error(fmt.Sprintf(utils.ErrorAcquireStore, datastore.GetStoreName(event.Tick, event.Column+utils.HyphenSeparator+event.Plugin, datastore.HorizontalStore)+utils.HyphenSeparator+INTToStringValue(int(Int64))) + fmt.Sprintf(MessageWriterId, writer.writerId))

				return
			}

			for token := range event.NumericContext {

				bufferIndex, keyBytes := writer.encoder.MemoryPool.AcquireBytePool(8)

				binary.BigEndian.PutUint64(keyBytes, uint64(token))

				if err := writer.writeSparseIndexPostingList(store, Int64, keyBytes); err != nil {

					indexWriterLogger.Error(fmt.Sprintf(utils.ErrorUpdatePostingListIndex, token, event.Column+utils.HyphenSeparator+event.Plugin, err) + fmt.Sprintf(MessageWriterId, writer.writerId))
				}

				writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)
			}
		} else if event.StringContext != nil {

			poolIndex, values := writer.encoder.MemoryPool.AcquireStringPool(utils.NotAvailable)

			defer writer.encoder.MemoryPool.ReleaseStringPool(poolIndex)

			startPoolIndex, startIndices := writer.encoder.MemoryPool.AcquireINTPool(utils.NotAvailable)

			defer writer.encoder.MemoryPool.ReleaseINTPool(startPoolIndex)

			endPoolIndex, endIndices := writer.encoder.MemoryPool.AcquireINTPool(utils.NotAvailable)

			defer writer.encoder.MemoryPool.ReleaseINTPool(endPoolIndex)

			store := datastore.GetStore(datastore.GetStoreName(event.Tick, event.Column+utils.HyphenSeparator+event.Plugin, datastore.HorizontalStore)+utils.HyphenSeparator+INTToStringValue(int(String)), writer.indexType, true, true, writer.encoder, writer.tokenizer)

			if store == nil {

				indexWriterLogger.Error(fmt.Sprintf(utils.ErrorAcquireStore, datastore.GetStoreName(event.Tick, event.Column+utils.HyphenSeparator+event.Plugin, datastore.HorizontalStore)+utils.HyphenSeparator+INTToStringValue(int(String))) + fmt.Sprintf(MessageWriterId, writer.writerId))

				return
			}

			if datastore.IsSearchableColumn(event.Column) || datastore.IsBlobColumn(event.Column) {

				for token := range event.StringContext {

					for _, token := range utils.Tokenize(token, values, startIndices, endIndices) {

						if datastore.IsStopWord(token) {

							continue
						}

						writer.tokens.Put(token, struct{}{})
					}
				}

				writer.tokens.Iter(func(token string, _ struct{}) (stop bool) {

					writer.tokens.Delete(token)

					if err := writer.writeSparseIndexPostingList(store, String, []byte(strings.ToLower(token))); err != nil {

						indexWriterLogger.Error(fmt.Sprintf(utils.ErrorUpdatePostingListIndex, token, event.Column+utils.HyphenSeparator+event.Plugin, err) + fmt.Sprintf(MessageWriterId, writer.writerId))
					}

					return stop
				})
			} else {

				for token := range event.StringContext {

					if err := writer.writeSparseIndexPostingList(store, String, []byte(strings.ToLower(token))); err != nil {

						indexWriterLogger.Error(fmt.Sprintf(utils.ErrorUpdatePostingListIndex, token, event.Column+utils.HyphenSeparator+event.Plugin, err) + fmt.Sprintf(MessageWriterId, writer.writerId))
					}
				}
			}
		}

		if utils.DebugEnabled() {

			indexWriterLogger.Debug(fmt.Sprintf("id %v took %v ms to write index of %v for %v", writer.writerId, time.Now().UnixMilli()-startTime, event.Column, event.Plugin))
		}

	} else { //writing non filter posting list

		store := datastore.GetStore(datastore.GetStoreName(event.Tick, utils.DummyPostingListStoreName, datastore.HorizontalStore)+utils.HyphenSeparator+INTToStringValue(int(String)), utils.Index, true, true, writer.encoder, writer.tokenizer)

		if store == nil {

			indexWriterLogger.Error(fmt.Sprintf(utils.ErrorAcquireStore, datastore.GetStoreName(event.Tick, utils.DummyPostingListStoreName, datastore.HorizontalStore)+utils.HyphenSeparator+INTToStringValue(int(String))) + fmt.Sprintf(MessageWriterId, writer.writerId))

			return
		}

		if err := writer.writeSparseIndexPostingList(store, Int32, []byte(event.Plugin)); err != nil {

			indexWriterLogger.Error(fmt.Sprintf("err %v occurred while writing sparse index positing list index, store: %v for writerId %v", err.Error(), store.GetName(), writer.writerId))
		}

		if utils.DebugEnabled() {

			indexWriterLogger.Debug(fmt.Sprintf("id %v took %v ms to write index for plugin %v", writer.writerId, time.Now().UnixMilli()-startTime, event.Plugin))
		}

	}
}

func (writer *IndexWriter) writeSparseIndexPostingList(store *Store, dataType DataType, keyBytes []byte) error {

	if len(keyBytes) > 0 {

		indexBitmap := bitmap.Bitmap{}

		found, valueBytes, err := store.Get(keyBytes, writer.valueBytes, writer.encoder, writer.event, writer.waitGroup, writer.tokenizer, false)

		if err != nil {

			store.Delete(keyBytes, writer.encoder, writer.tokenizer)

			indexWriterLogger.Error(fmt.Sprintf(utils.ErrorUpdatePostingListIndex, string(keyBytes), store.GetName(), err))

			found = false
		}

		if found && valueBytes != nil && len(valueBytes) > 0 {

			bitmapPoolIndex, bitmapBytes, err := writer.decoder.DecodeSnappy(valueBytes)

			if err != nil {

				return errors.New(fmt.Sprintf(utils.ErrorDecodeValues, string(keyBytes), store.GetName(), err.Error()))
			}

			defer writer.decoder.MemoryPool.ReleaseBytePool(bitmapPoolIndex)

			indexBitmap = bitmap.FromBytes(bitmapBytes)

			if !indexBitmap.Contains(uint32(writer.tickPosition)) {

				indexBitmap.Set(uint32(writer.tickPosition))

				poolIndex, valueBytes := writer.encoder.EncodeSnappy(indexBitmap.ToBytes(), utils.MaxValueBytes)

				defer writer.encoder.MemoryPool.ReleaseBytePool(poolIndex)

				err = store.Put(keyBytes, valueBytes, writer.encoder, writer.tokenizer)

				if err != nil {

					return errors.New(fmt.Sprintf("id: %v, error occurred while writing index for storeName %v , dataType %v, searchToken %v  , error %v", writer.writerId, store.GetName(), INTToStringValue(int(dataType)), string(keyBytes), err.Error()))

				}
			}

		} else {

			indexBitmap.Set(uint32(writer.tickPosition))

			poolIndex, valueBytes := writer.encoder.EncodeSnappy(indexBitmap.ToBytes(), utils.MaxValueBytes)

			defer writer.encoder.MemoryPool.ReleaseBytePool(poolIndex)

			err = store.Put(keyBytes, valueBytes, writer.encoder, writer.tokenizer)

			if err != nil {

				return errors.New(fmt.Sprintf("id: %v, error occurred while writing index for storeName %v , dataType %v , searchToken %v ,error %v", writer.writerId, store.GetName(), INTToStringValue(int(dataType)), string(keyBytes), err.Error()))
			}
		}
	}

	return nil
}

func (writer *IndexWriter) resolveIndex(storeType utils.DatastoreType) {

	if storeType == utils.PolicyFlapHistory || storeType == utils.EventPolicy || storeType == utils.PolicyResult || storeType == utils.RunbookWorklog || storeType == utils.PolicyAggregation || storeType == utils.MetricPolicy {

		writer.indexType = utils.PolicyIndex

	} else if storeType == utils.Log {

		writer.indexType = utils.LogIndex

	} else if storeType == utils.Flow {

		writer.indexType = utils.FlowIndex

	} else if storeType == utils.Trap || storeType == utils.TrapFlapHistory {

		writer.indexType = utils.TrapIndex

	} else if storeType == utils.CorrelationMetric {

		writer.indexType = utils.CorrelatedMetricIndex

	} else if storeType == utils.Notification {

		writer.indexType = utils.NotificationIndex

	} else if storeType == utils.Audit {

		writer.indexType = utils.AuditIndex

	} else if storeType == utils.PerformanceMetric || storeType == utils.ObjectStatusMetric || storeType == utils.StatusFlapHistory {

		writer.indexType = utils.MetricIndex

	} else if storeType == utils.HealthMetric {

		writer.indexType = utils.HealthMetricIndex

	} else if storeType == utils.ConfigHistory {

		writer.indexType = utils.ConfigHistoryIndex

	} else if storeType == utils.Compliance {

		writer.indexType = utils.ComplianceIndex

	} else {

		writer.indexType = utils.None
	}
}
