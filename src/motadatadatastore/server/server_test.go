/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05             Vedant Dokania         Motadata-5451  Status Flap horizontal new testcases
* 2025-04-21			 <PERSON><PERSON><PERSON>-5873  Changes in Testwatch to test reload functionality for DataSyncTimerSeconds
* 2025-05-14             Vedant Dokania         Motadata -6249  Records length changes
* 2025-05-26  			 <PERSON><PERSON>val <PERSON>ra			MOTADATA-6333  Discarded Query which exceeds query start time threshold
* 2025-06-03			 <PERSON><PERSON><PERSON>tadata-6393  Updated With Master Branch
* 2025-06-25			 <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>-6555  Added Test Case For Object Flush Notification
 */

package server

import (
	bytes2 "bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"github.com/fsnotify/fsnotify"
	"github.com/golang/snappy"
	zmq "github.com/pebbe/zmq4"
	"github.com/stretchr/testify/assert"
	broker2 "motadatadatastore/broker"
	"motadatadatastore/cache"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/datastore/query"
	"motadatadatastore/datastore/writer"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"reflect"
	"runtime"
	"strings"
	"testing"
	"time"
)

var (
	serverAcks chan bool

	dummySubscriber *zmq.Socket

	insertionPublisher *zmq.Socket

	selectionPublisher *zmq.Socket

	diskIOEventBatches []storage.DiskIOEventBatch
)

var testDir = filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.TestQueryDir + utils.PathSeparator

const (
	FromDateTime = "from.datetime"

	ToDateTime = "to.datetime"

	VisualizationTimeline = "visualization.timeline"

	Last6Hours = "last.6.hours"

	Last1Hour = "last.hour"

	Last12Hours = "last.12.hours"

	_1HourGranularity = "1 h"

	events = "events"
)

func TestMain(m *testing.M) {

	for _, arg := range os.Args {

		if strings.Contains(arg, "bench") {

			utils.EnvironmentType = utils.DatastoreBenchIntegrationEnvironment

			break
		}
	}

	runtime.GC()

	//don't alter number of writers
	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	if err != nil {

		panic(err)
	}

	utils.SystemBootSequence = utils.Datastore

	if utils.EnvironmentType != utils.DatastoreBenchIntegrationEnvironment {

		utils.EnvironmentType = utils.DatastoreTestEnvironment
	}

	if utils.InitConfigs(utils.UpdateConfigs(bytes, utils.MotadataMap{

		"datastore.vertical.writers":          2,
		"datastore.horizontal.writers":        2,
		"system.log.level":                    0,
		"datastore.metric.query.executors":    4,
		"datastore.aiops.query.executors":     1,
		"datastore.query.abort.timer.seconds": 300,
		"searchable.columns": utils.MotadataMap{
			"message": true,
		},
		utils.DiagnosticProfileDurationSeconds: 2,
	})) {

		if utils.EnvironmentType != utils.DatastoreBenchIntegrationEnvironment {

			utils.AIOpsEngineDir = filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + "aiops"

			dummySubscriber, err = zmq.NewSocket(zmq.PULL)

			err := dummySubscriber.Bind("tcp://*:" + utils.GetDatastorePublisherPort())

			if err != nil {
				panic(err)
			}

			insertionPublisher, err = zmq.NewSocket(zmq.PUB)

			err = insertionPublisher.Bind("tcp://*:" + utils.GetDatastoreNotificationPort())

			if err != nil {
				panic(err)
			}

			selectionPublisher, err = zmq.NewSocket(zmq.PUB)

			err = selectionPublisher.Bind("tcp://*:" + utils.GetDatastoreReaderPort())

			if err != nil {

				panic(err)
			}

		}

		serverAcks = make(chan bool, 2)

		file, _ := os.Create(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDataConfigFile)

		file.WriteString("{\"manager.id\":\"dummy\"}")

		cache.InitCacheEngine()

		Start()

		time.Sleep(time.Second * 2)

		initDatabase()

		writer.PopulateIndexColumns()

		datastore.AddSearchableColumn(utils.Message)

		utils.SetLogLevel(0)

		utils.SetTaskLogging(utils.Yes)

		utils.IOCPWorkers = 16

		utils.DiskIOWorkers = 300

		diskIOWorkers = make([]*storage.DiskIOWorker, utils.DiskIOWorkers)

		for id := range diskIOWorkers {

			diskIOWorkers[id] = storage.NewIOWorker(id)

			diskIOWorkers[id].Start()
		}

		diskIOEventBatches = make([]storage.DiskIOEventBatch, utils.MaxStoreParts)

		for i := range diskIOEventBatches {

			diskIOEventBatches[i] = storage.DiskIOEventBatch{}
		}

		if utils.EnvironmentType != utils.DatastoreBenchIntegrationEnvironment {

			utils.EnvironmentType = utils.DatastoreTestEnvironment

		} else {

			publisher.responseSocketShutdownNotifications <- true

			time.Sleep(time.Millisecond * 10)
		}

		m.Run()

		if utils.EnvironmentType != utils.DatastoreBenchIntegrationEnvironment {

			if insertionPublisher != nil {

				_ = insertionPublisher.Close()
			}

			if selectionPublisher != nil {

				_ = selectionPublisher.Close()
			}

			if dummySubscriber != nil {

				_ = dummySubscriber.Close()
			}
		}
	}

}

func TestPublisher(t *testing.T) {

	utils.GlobalShutdown = false

	assertions := assert.New(t)

	indexUpdateReceived := false

	for {

		bytes, err := dummySubscriber.RecvBytes(0)

		topicLength := int32(bytes[0]) |
			int32(bytes[1])<<8

		topic := bytes[2 : 2+topicLength]

		bytes = bytes[2+topicLength:]

		assertions.Nil(err)

		if strings.Contains(string(topic), utils.DatastoreOperation) {

			decodedBytes, err := snappy.Decode(nil, bytes)

			assertions.Nil(err)

			context := make(utils.MotadataMap)

			context = utils.UnmarshalJson(decodedBytes, context)

			switch context.GetIntValue(utils.OperationType) {

			case utils.IndexUpdate:

				indexUpdateReceived = true
			}

		}

		if indexUpdateReceived {

			assertions.True(true)

			break
		}

	}

}

func TestHealthCheckJobRequest(t *testing.T) {

	assertions := assert.New(t)

	found := false

	healthCheckupJob.ShutdownNotifications <- true

	time.Sleep(time.Second)

	utils.HealthCheckupJobRequests <- struct{}{}

outer:
	for {

		select {

		case event := <-utils.HealthCheckupJobNotifications:

			if _, ok := event[utils.AbortedQueries]; ok {

				found = true

				break outer
			}

		}

	}

	assertions.True(found)

}

func TestPublisherAIOpsNotification(t *testing.T) {

	buffer := &bytes2.Buffer{}

	buffer.Write(query.DataBytes) //Non HearBeat byte

	buffer.WriteByte(byte(0))

	buffer.WriteByte(byte(1)) //anomaly

	id := 0

	column := "system.cpu.percent"

	records := 5

	engineType := query.Metric

	for id, engineType = range executorAllocations {

		if engineType == query.AIOps {

			break
		}
	}

	executors[id].ShutdownNotifications <- true

	time.Sleep(time.Millisecond)

	codec.EncodeINT16Value(int16(id), buffer)

	codec.EncodeINT16Value(int16(len("sub.query.id")), buffer)

	buffer.WriteString("sub.query.id")

	buffer.WriteByte(byte(codec.Int64))

	codec.EncodeINT16Value(int16(len(column)), buffer)

	buffer.Write([]byte(column))

	buffer.WriteByte(0) // without group by

	codec.EncodeINT32Value(int32(records), buffer)

	for i := 0; i < records; i++ {

		codec.EncodeFLOAT64Value(float64(i), buffer)
	}

	utils.AIOpsEngineRequests <- buffer.Bytes()[:buffer.Len()]

	responseBytes := <-executors[id].AIOpsEngineResponses

	responseBuffer := bytes2.NewBuffer(responseBytes)

	subQueryId := string(responseBuffer.Next(codec.ReadINTValue(responseBuffer.Next(2))))

	assertions := assert.New(t)

	assertions.Equal("sub.query.id", subQueryId)

	assertions.Equal(1, int(responseBuffer.Next(1)[0]))

	assertions.Equal("Unable to detect anomaly, reason: insufficient data...minimum 15 points required", string(responseBuffer.Next(codec.ReadINTValue(responseBuffer.Next(2)))))

	executors[id].Start(workers, availableWorkers)

	time.Sleep(time.Second * 5)
}

func TestSubscriberQueryTopic(t *testing.T) {

	queueSize = 2

	utils.GlobalShutdown = false

	assertions := assert.New(t)

	router := NewRequestRouter(0, nil)

	for i := 0; i < 100; i++ {

		publishQuery(i, selectionPublisher, assertions)
	}

	limit := false

	for {

		bytes, err := dummySubscriber.RecvBytes(0)

		buffer := bytes2.NewBuffer(bytes)

		topicLength := codec.ReadINTValue(buffer.Next(2))

		topic := string(buffer.Next(topicLength))

		if !strings.Contains(topic, utils.DatastoreQueryResponse) {

			continue
		}

		bytes = buffer.Bytes()

		assertions.Nil(err)

		assertions.Contains(topic, utils.DatastoreQueryResponse)

		bytes, _ = snappy.Decode(nil, bytes)

		buffer = bytes2.NewBuffer(bytes)

		//queryId

		_ = int(binary.LittleEndian.Uint64(buffer.Next(8)))

		//sub query id

		_ = int(binary.LittleEndian.Uint64(buffer.Next(8)))

		progress, _ := buffer.ReadByte()

		valid := false

		if int(progress) == 100 {

			valid = true
		}

		assertions.True(valid)

		_, errors, _, _ := utils.UnpackResponse(buffer, false)

		if strings.Contains(errors, utils.ErrorQueryQueueMaxLimitReached) {

			limit = true

			break
		}
	}

	assertions.True(limit)

	router.Responses <- "0^0^0^0"

	publishQuery(100, insertionPublisher, assertions)
}

func TestRouterWidgetCreateNotification(t *testing.T) {

	assertions := assert.New(t)

	indexableColumns := []string{"source.port", "destination.port"}

	plugin := "50000-flow-1"

	notification := make(utils.MotadataMap)

	notification[plugin] = utils.MotadataMap{

		utils.Type: utils.Flow,

		writer.VolumeBytesPerSec: 1,

		utils.IndexableColumns: indexableColumns,
	}

	notification[utils.DatastoreFormat] = utils.HorizontalFormat

	bufferBytes, err := json.Marshal(notification)

	assertions.Nil(err)

	assertions.NotNil(bufferBytes)

	buffer := bytes2.Buffer{}

	//operation type
	buffer.WriteByte(utils.WidgetCreate)

	buffer.Write(bufferBytes)

	requestRouters[RouterTypeNotification].Requests <- buffer.Bytes()

	time.Sleep(time.Second * 3)

	bytes, err := utils.ReadLogFile("Job Manager", "job")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "update phase 1 request received"))

}

func TestWidgetDeleteNotification(t *testing.T) {

	assertions := assert.New(t)

	indexableColumns := []string{"source.port", "destination.port"}

	plugin := "50000-flow-1"

	notification := make(utils.MotadataMap)

	notification[plugin] = utils.MotadataMap{

		utils.Type: utils.Flow,

		writer.VolumeBytesPerSec: 1,

		utils.IndexableColumns: indexableColumns,
	}

	//goland:noinspection Annotator
	notification[utils.DatastoreFormat] = utils.HorizontalFormat

	bufferBytes, err := json.Marshal(notification)

	assertions.Nil(err)

	assertions.NotNil(bufferBytes)

	buffer := bytes2.Buffer{}

	//operation type
	buffer.WriteByte(utils.WidgetDelete)

	buffer.Write(bufferBytes)

	requestRouters[RouterTypeNotification].Requests <- buffer.Bytes()

	time.Sleep(time.Second * 3)

	bytes, err := utils.ReadLogFile("Request Router", "server")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "delete aggregation view"))

}

func TestRouterLogModificationNotification(t *testing.T) {

	assertions := assert.New(t)

	notification := make(utils.MotadataMap)

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.MotadataDatastoreConfigFile, bytes, 0777)

	notification[utils.SystemLogLevel] = utils.LogLevelTrace

	notification[LogLevelResetTimerSeconds] = 1

	bufferBytes, err := json.Marshal(notification)

	assertions.Nil(err)

	assertions.NotNil(bufferBytes)

	buffer := bytes2.Buffer{}

	//operation type
	buffer.WriteByte(utils.ModifyLogLevel)

	buffer.Write(bufferBytes)

	requestRouters[RouterTypeNotification].Requests <- buffer.Bytes()

	time.Sleep(time.Second * 4)

	assertions.Equal(utils.GetLogLevel(), utils.LogLevelInfo)

}

func TestRouterBackupProfileNotification(t *testing.T) {

	utils.SetLogLevel(0)

	defer utils.SetLogLevel(2)

	assertions := assert.New(t)

	notification := make(utils.MotadataMap)

	notification["datastore.types"] = []interface{}{"metric", "log", "system.event"}

	bufferBytes, err := json.Marshal(notification)

	assertions.Nil(err)

	assertions.NotNil(bufferBytes)

	buffer := bytes2.Buffer{}

	//operation type
	buffer.WriteByte(utils.BackupProfile)

	buffer.Write(bufferBytes)

	requestRouters[RouterTypeNotification].Requests <- buffer.Bytes()

	time.Sleep(time.Second * 1)

	utils.AssertLogMessage(assertions, "Request Router", "server", "received for backup profile")

}

func TestRouterIndexableAcknowledgementNotification(t *testing.T) {

	assertions := assert.New(t)

	notification := make(utils.MotadataMap)

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.MotadataDatastoreConfigFile, bytes, 0777)

	bufferBytes, err := json.Marshal(notification)

	assertions.Nil(err)

	assertions.NotNil(bufferBytes)

	buffer := bytes2.Buffer{}

	assertions.False(Acknowledged.Load())

	//operation type
	buffer.WriteByte(utils.Acknowledge)

	buffer.Write(bufferBytes)

	requestRouters[RouterTypeNotification].Requests <- buffer.Bytes()

	time.Sleep(time.Second * 1)

	assertions.True(Acknowledged.Load())

}

func TestRouterRetentionJobNotification(t *testing.T) {

	utils.SetLogLevel(0)

	defer utils.SetLogLevel(2)

	assertions := assert.New(t)

	utils.StoreRetentionJobs = make(chan map[int]map[string]int, 500)

	context := map[int]map[string]int{}

	bufferBytes, err := json.Marshal(context)

	assertions.Nil(err)

	assertions.NotNil(bufferBytes)

	buffer := bytes2.Buffer{}

	//operation type
	buffer.WriteByte(utils.RetentionJob)

	buffer.Write(bufferBytes)

	requestRouters[RouterTypeNotification].Requests <- buffer.Bytes()

	time.Sleep(time.Second * 2)

}

func TestQueryEngineType(t *testing.T) {

	assertion := assert.New(t)

	context := make(utils.MotadataMap)

	dataSources := make(utils.MotadataMap)

	dataSources[utils.Type] = int(utils.PerformanceMetric)

	context[utils.DrillDown] = utils.Yes

	context[query.VisualizationDataSources] = dataSources

	assertion.Equal(query.Metric, getQueryEngineType(context))

	context.Delete(utils.DrillDown)

	assertion.Equal(query.Metric, getQueryEngineType(context))

	context.GetMapValue(query.VisualizationDataSources)[utils.Type] = int(utils.Flow)

	assertion.Equal(query.Flow, getQueryEngineType(context))

	context.GetMapValue(query.VisualizationDataSources)[utils.Type] = int(utils.Log)

	assertion.Equal(query.Log, getQueryEngineType(context))

	context[query.VisualizationCategory] = query.Forecast

	assertion.Equal(query.AIOps, getQueryEngineType(context))

}

//Don't use logTable3 in the testcase as it has more records in clickhouse as compared to motadatadatastore

// Log specific plugin
func TestLogDrillDownResultType1(t *testing.T) {

	defer verifyFailure(t)

	utils.SetTaskLogging("yes")

	publisher.responseSocketShutdownNotifications <- true

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch.json")

	timeline := utils.GetTimeline(Last1Hour)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

// Log Specific plugin with filter
func TestLogDrillDownResultType2(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-leveldescription.json")

	timeline := utils.GetTimeline(Last1Hour)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and  level_description in [ 'fatal','info','debug' , 'error', 'warn'] order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

// Log Specific plugin with filter incremental
func TestLogDrillDownResultType3(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-leveldescription.json")

	timeline := utils.GetTimeline(Last6Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and level_description in ['fatal','info','debug', 'error', 'warn'] order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

// Log Search Plugin with filter incremental
func TestLogDrillDownResultType4(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-message-type4.json")

	timeline := utils.GetTimeline(Last6Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	utils.MaxLineChartTicks = 100

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable4 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

// Log Search Plugin with filter incremental
func TestLogDrillDownResultType5(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-message-type4.json")

	timeline := utils.GetTimeline(Last6Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	utils.MaxLineChartTicks = 100

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable4 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)
}

func TestLogDrillDownResultType6(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch.json")

	timeline := utils.GetTimeline(utils.Last48Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

// Log Specific plugin with filter
func TestLogDrillDownResultType7(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-leveldescription.json")

	timeline := utils.GetTimeline(utils.ThisWeek)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and  level_description in [ 'fatal','info','debug' , 'error', 'warn'] order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

// Log Search Plugin with pagination
func TestLogDrillDownResultType5Pagination(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-message-type4-pagination.json")

	timeline := utils.GetTimeline(Last6Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, true)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable4 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

	//send pagination request

	queryContext["pagination.query"] = "yes"

	queryContext["page"] = 1

	queryContext["page.size"] = 1000

	sendResponse(router, queryContext, true)

	results, errs = getResponse(router, queryContext, true, false)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	assertions.Equal(4, len(motadataDBTableResult))

	pageSize := 1000

	assertions.Equal(pageSize, len(motadataDBTableResult["message^value"]))

	assertions.Equal(pageSize, len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(pageSize, len(motadataDBTableResult["event^value"]))

	assertions.Equal(pageSize, len(motadataDBTableResult["Timestamp"]))

	for _, msg := range motadataDBTableResult["message^value"] {

		assertions.NotEmpty(msg)
	}

	for _, eventId := range motadataDBTableResult["event.id^value"] {

		assertions.NotEmpty(eventId)
	}

	for _, event := range motadataDBTableResult["event^value"] {

		assertions.NotEmpty(event)
	}

}

// Log Search Plugin with pagination
func TestLogDrillDownResultType5PaginationType2(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-message-type4-pagination.json")

	timeline := utils.GetTimeline(utils.ThisWeek)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, true)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable4 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

	//send pagination request

	queryContext["pagination.query"] = "yes"

	queryContext["page"] = 1

	queryContext["page.size"] = 1000

	sendResponse(router, queryContext, true)

	results, errs = getResponse(router, queryContext, true, false)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	assertions.Equal(4, len(motadataDBTableResult))

	pageSize := 1000

	assertions.Equal(pageSize, len(motadataDBTableResult["message^value"]))

	assertions.Equal(pageSize, len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(pageSize, len(motadataDBTableResult["event^value"]))

	assertions.Equal(pageSize, len(motadataDBTableResult["Timestamp"]))

	for _, msg := range motadataDBTableResult["message^value"] {

		assertions.NotEmpty(msg)
	}

	for _, eventId := range motadataDBTableResult["event.id^value"] {

		assertions.NotEmpty(eventId)
	}

	for _, event := range motadataDBTableResult["event^value"] {

		assertions.NotEmpty(event)
	}

}

// Log specific plugin with pagination
func TestLogDrillDownResultPaginationType3(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-logid-eventsource-pagination.json")

	timeline := utils.GetTimeline(Last1Hour)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, true)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

	pageSize := 100

	queryContext["pagination.query"] = "yes"

	queryContext["page"] = 1

	queryContext["page.size"] = pageSize

	sendResponse(router, queryContext, true)

	results, errs = getResponse(router, queryContext, true, false)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	assertions.Equal(4, len(motadataDBTableResult))

	assertions.Equal(pageSize, len(motadataDBTableResult["log.id^value"]))

	assertions.Equal(pageSize, len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(pageSize, len(motadataDBTableResult["event.source^value"]))

	assertions.Equal(pageSize, len(motadataDBTableResult["Timestamp"]))

	for _, eventId := range motadataDBTableResult["event.id^value"] {

		assertions.NotEmpty(eventId)
	}

	for _, event := range motadataDBTableResult["event.source^value"] {

		assertions.NotEmpty(event)
	}

}

// Log specific plugin with Search pagination
func TestLogDrillDownResultPaginationType4(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-logid-eventsource-pagination.json")

	timeline := utils.GetTimeline(Last1Hour)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, true)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

	pageSize := 100

	queryContext["pagination.query"] = "yes"

	queryContext["page"] = 1

	queryContext["page.size"] = pageSize

	queryContext[query.PaginationSearchEvent] = results[events]["event.id^value"][3]

	sendResponse(router, queryContext, true)

	results, errs = getResponse(router, queryContext, true, false)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	assertions.Equal(4, len(motadataDBTableResult))

	assertions.Equal(1, len(motadataDBTableResult["log.id^value"]))

	assertions.Equal(1, len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(1, len(motadataDBTableResult["event.source^value"]))

	assertions.Equal(1, len(motadataDBTableResult["Timestamp"]))

	for _, eventId := range motadataDBTableResult["event.id^value"] {

		assertions.NotEmpty(eventId)
	}

	for _, event := range motadataDBTableResult["event.source^value"] {

		assertions.NotEmpty(event)
	}

}

// Log Search Plugin with Search pagination
func TestLogDrillDownResultPaginationType5(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-eventsource-logsearchplugin-pagination.json")

	timeline := utils.GetTimeline(Last1Hour)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, true)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

	queryContext["pagination.query"] = "yes"

	queryContext[query.PaginationSearchEvent] = results[events]["event.id^value"][3]

	sendResponse(router, queryContext, true)

	results, errs = getResponse(router, queryContext, true, false)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	assertions.Equal(3, len(motadataDBTableResult))

	assertions.Equal(1, len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(1, len(motadataDBTableResult["event.source^value"]))

	assertions.Equal(1, len(motadataDBTableResult["Timestamp"]))

	for _, eventId := range motadataDBTableResult["event.id^value"] {

		assertions.NotEmpty(eventId)
	}

	for _, event := range motadataDBTableResult["event.source^value"] {

		assertions.NotEmpty(event)
	}

}

// Log specific plugin with pagination datatype conversion
func TestLogDrillDownResultPaginationType6(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-dummyintstringcolumn-pagination.json")

	timeline := utils.GetTimeline(Last1Hour)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, true)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

	pageSize := 100

	queryContext["pagination.query"] = "yes"

	queryContext["page"] = 1

	queryContext["page.size"] = pageSize

	sendResponse(router, queryContext, true)

	results, errs = getResponse(router, queryContext, true, false)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	assertions.Equal(3, len(motadataDBTableResult))

	assertions.Equal(pageSize, len(motadataDBTableResult["dummy.intString.column^value"]))

	assertions.Equal(pageSize, len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(pageSize, len(motadataDBTableResult["Timestamp"]))

	for _, eventId := range motadataDBTableResult["event.id^value"] {

		assertions.NotEmpty(eventId)
	}

}

// Log specific plugin with message contains and event category
func TestLogDrillDownResultType6FilterByEventCategoryMessage(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-message-type6.json")

	timeline := utils.GetTimeline(utils.Today)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable4 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and message = 'message1' order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

func TestLogDrillDownResultType6FilterByMessageEventCategory(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-message-eventcategory-type6.json")

	timeline := utils.GetTimeline(utils.Today)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable4 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and message = 'message1' order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

// Log Search Plugin with filter incremental
func TestLogDrillDownResultType8(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-message-type8.json")

	timeline := utils.GetTimeline(utils.Today)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	utils.MaxLineChartTicks = 100

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable4 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source in ['************','************','10.20.40.142'] order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

func TestLogDrillDownResultType9(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-message-type9.json")

	timeline := utils.GetTimeline(Last6Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	utils.MaxLineChartTicks = 100

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable4 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source not in ['10.20.40.142','************'] order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

func TestLogDrillDownResultType10(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-message-type10.json")

	timeline := utils.GetTimeline(Last6Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	utils.MaxLineChartTicks = 100

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable4 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and message not like '%message1%' order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

func TestLogDrillDownResultType11(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-message-type11.json")

	timeline := utils.GetTimeline(Last6Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	utils.MaxLineChartTicks = 100

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable4 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and message like 'message%' order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

func TestLogDrillDownResultType12(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-message-type12.json")

	timeline := utils.GetTimeline(Last6Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	utils.MaxLineChartTicks = 100

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable4 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and message like '%message1' order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

func TestLogDrillDownResultType13(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-message-type13.json")

	timeline := utils.GetTimeline(Last6Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	utils.MaxLineChartTicks = 100

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable4 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and message not like '%failed%' order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

func TestLogDrillDownResultType14(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-message-type14.json")

	timeline := utils.GetTimeline(Last6Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	utils.MaxLineChartTicks = 100

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable4 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

//Drill down new testcases

func TestDrillDownAudit(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "drilldown-audit-auditmodule-auditoperation-audituser.json")

	queryContext := make(utils.MotadataMap)

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	timeline := utils.GetTimeline(utils.Last6Hours)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseQuery := "select audit_module, audit_operation, audit_user, audit_message, audit_status, audit_remote_ip, (timestamp*1000) as audit_timestamp from auditTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc"

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedAuditModules []string

	var expectedAuditOperations []string

	var expectedAuditUsers []string

	var expectedAuditMessages []string

	var expectedAuditStatuses []string

	var expectedAuditRemoteIPs []string

	var expectedAuditTimestamps []int64

	for rows.Next() {

		var auditModule string

		var auditOperation string

		var auditUser string

		var auditMessage string

		var auditStatus string

		var auditRemoteIP string

		var timestamp int64

		err = rows.Scan(&auditModule, &auditOperation, &auditUser, &auditMessage, &auditStatus, &auditRemoteIP, &timestamp)

		expectedAuditModules = append(expectedAuditModules, auditModule)

		expectedAuditOperations = append(expectedAuditOperations, auditOperation)

		expectedAuditUsers = append(expectedAuditUsers, auditUser)

		expectedAuditMessages = append(expectedAuditMessages, auditMessage)

		expectedAuditStatuses = append(expectedAuditStatuses, auditStatus)

		expectedAuditRemoteIPs = append(expectedAuditRemoteIPs, auditRemoteIP)

		expectedAuditTimestamps = append(expectedAuditTimestamps, timestamp)

	}

	assertions.EqualValues(expectedAuditMessages, utils.ToStringList(motadataDBTable["audit.message^value"]))

	assertions.EqualValues(expectedAuditModules, utils.ToStringList(motadataDBTable["audit.module^value"]))

	assertions.EqualValues(expectedAuditOperations, utils.ToStringList(motadataDBTable["audit.operation^value"]))

	assertions.EqualValues(expectedAuditUsers, utils.ToStringList(motadataDBTable["audit.user^value"]))

	assertions.EqualValues(expectedAuditStatuses, utils.ToStringList(motadataDBTable["audit.status^value"]))

	assertions.EqualValues(expectedAuditRemoteIPs, utils.ToStringList(motadataDBTable["audit.remote.ip^value"]))

	assertions.EqualValues(expectedAuditTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

func TestDrillDownAuditType2(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "drilldown-audit-auditmodule-auditoperation-audituser.json")

	queryContext := make(utils.MotadataMap)

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	timeline := utils.GetTimeline(utils.Last48Hours)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseQuery := "select audit_module, audit_operation, audit_user, audit_message, audit_status, audit_remote_ip, (timestamp*1000) as audit_timestamp from auditTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc"

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedAuditModules []string

	var expectedAuditOperations []string

	var expectedAuditUsers []string

	var expectedAuditMessages []string

	var expectedAuditStatuses []string

	var expectedAuditRemoteIPs []string

	var expectedAuditTimestamps []int64

	for rows.Next() {

		var auditModule string

		var auditOperation string

		var auditUser string

		var auditMessage string

		var auditStatus string

		var auditRemoteIP string

		var timestamp int64

		err = rows.Scan(&auditModule, &auditOperation, &auditUser, &auditMessage, &auditStatus, &auditRemoteIP, &timestamp)

		expectedAuditModules = append(expectedAuditModules, auditModule)

		expectedAuditOperations = append(expectedAuditOperations, auditOperation)

		expectedAuditUsers = append(expectedAuditUsers, auditUser)

		expectedAuditMessages = append(expectedAuditMessages, auditMessage)

		expectedAuditStatuses = append(expectedAuditStatuses, auditStatus)

		expectedAuditRemoteIPs = append(expectedAuditRemoteIPs, auditRemoteIP)

		expectedAuditTimestamps = append(expectedAuditTimestamps, timestamp)

	}

	assertions.EqualValues(expectedAuditMessages, utils.ToStringList(motadataDBTable["audit.message^value"]))

	assertions.EqualValues(expectedAuditModules, utils.ToStringList(motadataDBTable["audit.module^value"]))

	assertions.EqualValues(expectedAuditOperations, utils.ToStringList(motadataDBTable["audit.operation^value"]))

	assertions.EqualValues(expectedAuditUsers, utils.ToStringList(motadataDBTable["audit.user^value"]))

	assertions.EqualValues(expectedAuditStatuses, utils.ToStringList(motadataDBTable["audit.status^value"]))

	assertions.EqualValues(expectedAuditRemoteIPs, utils.ToStringList(motadataDBTable["audit.remote.ip^value"]))

	assertions.EqualValues(expectedAuditTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

//Flow table

func TestDrillDownFlow(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "drilldown-flow-protocol-sourceport.json")

	queryContext := make(utils.MotadataMap)

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	timeline := utils.GetTimeline(utils.Last6Hours)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseQuery := "select protocol, source_port, (timestamp*1000) as flow_timestamp from flowTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc"

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedProtocolValues []string

	var expectedSourcePortValues []int64

	var expectedFlowTimestamps []int64

	for rows.Next() {

		var protocol string

		var sourcePort int64

		var timestamp int64

		err = rows.Scan(&protocol, &sourcePort, &timestamp)

		expectedProtocolValues = append(expectedProtocolValues, protocol)

		expectedSourcePortValues = append(expectedSourcePortValues, sourcePort)

		expectedFlowTimestamps = append(expectedFlowTimestamps, timestamp)

	}

	assertions.EqualValues(expectedProtocolValues, utils.ToStringList(motadataDBTable["protocol^value"]))

	assertions.EqualValues(expectedSourcePortValues, utils.ToINT64Values(motadataDBTable["source.port^value"]))

	assertions.EqualValues(expectedFlowTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

func TestDrillDownFlowType2(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "drilldown-flow-protocol-sourceport.json")

	queryContext := make(utils.MotadataMap)

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	timeline := utils.GetTimeline(utils.ThisWeek)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseQuery := "select protocol, source_port, (timestamp*1000) as flow_timestamp from flowTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc"

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedProtocolValues []string

	var expectedSourcePortValues []int64

	var expectedFlowTimestamps []int64

	for rows.Next() {

		var protocol string

		var sourcePort int64

		var timestamp int64

		err = rows.Scan(&protocol, &sourcePort, &timestamp)

		expectedProtocolValues = append(expectedProtocolValues, protocol)

		expectedSourcePortValues = append(expectedSourcePortValues, sourcePort)

		expectedFlowTimestamps = append(expectedFlowTimestamps, timestamp)

	}

	assertions.EqualValues(expectedProtocolValues, utils.ToStringList(motadataDBTable["protocol^value"]))

	assertions.EqualValues(expectedSourcePortValues, utils.ToINT64Values(motadataDBTable["source.port^value"]))

	assertions.EqualValues(expectedFlowTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

func TestDrillDownFlowFilterBySourcePort(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "drilldown-flow-protocol-sourceport-filterby-sourceport.json")

	queryContext := make(utils.MotadataMap)

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	timeline := utils.GetTimeline(utils.Last6Hours)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseQuery := "select protocol, source_port, (timestamp*1000) as flow_timestamp from flowTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and source_port not in [1,2,3,100,105,98,113,114,78] order by timestamp desc"

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedProtocolValues []string

	var expectedSourcePortValues []int64

	var expectedFlowTimestamps []int64

	for rows.Next() {

		var protocol string

		var sourcePort int64

		var timestamp int64

		err = rows.Scan(&protocol, &sourcePort, &timestamp)

		expectedProtocolValues = append(expectedProtocolValues, protocol)

		expectedSourcePortValues = append(expectedSourcePortValues, sourcePort)

		expectedFlowTimestamps = append(expectedFlowTimestamps, timestamp)

	}

	assertions.EqualValues(expectedProtocolValues, utils.ToStringList(motadataDBTable["protocol^value"]))

	assertions.EqualValues(expectedSourcePortValues, utils.ToINT64Values(motadataDBTable["source.port^value"]))

	assertions.EqualValues(expectedFlowTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

// drilldown filter

// Log Specific plugin with drilldown filter
func TestLogDrillDownResultType15(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-leveldescription-type15.json")

	timeline := utils.GetTimeline(Last1Hour)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and  level_description in [ 'fatal','info','debug' , 'error', 'warn'] order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

// Log Specific plugin with drilldown filter +data filter
func TestLogDrillDownResultType16(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-leveldescription-type16.json")

	timeline := utils.GetTimeline(Last1Hour)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and  level_description in [ 'fatal','info','debug' , 'error', 'warn'] and event_source in ('************' ,'************') order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

// data security filter
func TestLogDrillDownResultType17(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-leveldescription-type17.json")

	timeline := utils.GetTimeline(Last1Hour)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source in ('************' ,'************') order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

// Log Search Plugin with filter incremental
func TestLogDrillDownResultType18(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-message-type18.json")

	timeline := utils.GetTimeline(Last6Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable4 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

//Rolling Window testcases

func TestLogDrillDownResultType19(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch.json")

	timeline, partialTimelineQuery := utils.GetRollingWindowTimeline(1, 9, 15, -1)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where " + partialTimelineQuery + " order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

// Rolling Window Log Specific plugin with filter incremental
func TestLogDrillDownResultType20(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-leveldescription.json")

	timeline, partialTimelineQuery := utils.GetRollingWindowTimeline(2, 9, 15, -1)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where " + partialTimelineQuery + " and level_description in ['fatal','info','debug', 'error', 'warn'] order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

// Improvement 2722 limit the drilldown records
func TestLogDrillDownResultType21(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch.json")

	timeline := utils.GetTimeline(utils.Last48Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[utils.MaxRecords] = 100

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc limit 100"

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

// Log missing scenarios testcases

// Scenario Level description ordinal string columns misses random time in the testcases ( missing column not in first position)
func TestLogDrillDownResultType22(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-leveldecription-log-type22.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	clickHouseEventQuery := "select event_source, level_description ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var levelDescriptions []string

	for rows.Next() {

		var eventSource string

		var levelDescription string

		var timestamp int64

		err = rows.Scan(&eventSource, &levelDescription, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		levelDescriptions = append(levelDescriptions, levelDescription)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDBTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDBTableResult["event.source^value"]))

	assertions.Equal(levelDescriptions, utils.ToStringList(motadataDBTableResult["level.description^value"]))

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDBTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDBTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["level.description^value"]))

}

// Scenario Level description ordinal string columns misses random time in the testcases ( missing column in first position)
func TestLogDrillDownResultType23(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-leveldecription-log-type23.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	clickHouseEventQuery := "select event_source, level_description ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var levelDescriptions []string

	for rows.Next() {

		var eventSource string

		var levelDescription string

		var timestamp int64

		err = rows.Scan(&eventSource, &levelDescription, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		levelDescriptions = append(levelDescriptions, levelDescription)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDBTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDBTableResult["event.source^value"]))

	assertions.Equal(levelDescriptions, utils.ToStringList(motadataDBTableResult["level.description^value"]))

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDBTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDBTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["level.description^value"]))

}

// Scenario Log Level (Int 8) integer columns misses random time in the testcases ( missing column not in first position)
func TestLogDrillDownResultType24(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-loglevel-log-type24.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDbTableResult := results[events]

	clickHouseEventQuery := "select event_source, log_level ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var logLevels []int64

	for rows.Next() {

		var eventSource string

		var logLevel int64

		var timestamp int64

		err = rows.Scan(&eventSource, &logLevel, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		logLevels = append(logLevels, logLevel)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDbTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDbTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDbTableResult["log.level^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(logLevels, utils.ToINT64Values(motadataDbTableResult["log.level^value"]))

	} else {

		for index, value := range motadataDbTableResult["log.level^value"] {

			if value == utils.Empty {

				assertions.EqualValues(logLevels[index], int64(0))

			} else {

				assertions.EqualValues(codec.ToString(logLevels[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDbTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDbTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["log.level^value"]))

}

// Scenario Log Level (Int 8) integer columns misses random time in the testcases ( missing column  in first position)
func TestLogDrillDownResultType25(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-loglevel-log-type25.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDbTableResult := results[events]

	clickHouseEventQuery := "select event_source, log_level ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var logLevels []int64

	for rows.Next() {

		var eventSource string

		var logLevel int64

		var timestamp int64

		err = rows.Scan(&eventSource, &logLevel, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		logLevels = append(logLevels, logLevel)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDbTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDbTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDbTableResult["log.level^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(logLevels, utils.ToINT64Values(motadataDbTableResult["log.level^value"]))

	} else {

		for index, value := range motadataDbTableResult["log.level^value"] {

			if value == utils.Empty {

				assertions.EqualValues(logLevels[index], int64(0))

			} else {

				assertions.EqualValues(codec.ToString(logLevels[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDbTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDbTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["log.level^value"]))

}

// Scenario bytes sent (Int 64) integer columns misses random time in the testcases ( missing column not in first position)
func TestLogDrillDownResultType26(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-bytessent-log-type26.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDbTableResult := results[events]

	clickHouseEventQuery := "select event_source, bytes_sent ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var values []int64

	for rows.Next() {

		var eventSource string

		var value int64

		var timestamp int64

		err = rows.Scan(&eventSource, &value, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		values = append(values, value)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDbTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDbTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDbTableResult["bytes.sent^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(values, utils.ToINT64Values(motadataDbTableResult["bytes.sent^value"]))

	} else {

		for index, value := range motadataDbTableResult["bytes.sent^value"] {

			if value == utils.Empty {

				assertions.EqualValues(values[index], int64(0))

			} else {

				assertions.EqualValues(codec.ToString(values[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDbTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDbTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["bytes.sent^value"]))

}

// Scenario bytes sent (Int 64) integer columns misses random time in the testcases ( missing column in first position)
func TestLogDrillDownResultType27(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-bytessent-log-type27.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDbTableResult := results[events]

	clickHouseEventQuery := "select event_source, bytes_sent ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var values []int64

	for rows.Next() {

		var eventSource string

		var value int64

		var timestamp int64

		err = rows.Scan(&eventSource, &value, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		values = append(values, value)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDbTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDbTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDbTableResult["bytes.sent^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(values, utils.ToINT64Values(motadataDbTableResult["bytes.sent^value"]))

	} else {

		for index, value := range motadataDbTableResult["bytes.sent^value"] {

			if value == utils.Empty {

				assertions.EqualValues(values[index], int64(0))

			} else {

				assertions.EqualValues(codec.ToString(values[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDbTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDbTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["bytes.sent^value"]))

}

// Scenario processes (Int 32) integer columns misses random time in the testcases ( missing column not in first position)
func TestLogDrillDownResultType28(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-processes-log-type28.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDbTableResult := results[events]

	clickHouseEventQuery := "select event_source, processes ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var values []int64

	for rows.Next() {

		var eventSource string

		var value int64

		var timestamp int64

		err = rows.Scan(&eventSource, &value, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		values = append(values, value)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDbTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDbTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDbTableResult["processes^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(values, utils.ToINT64Values(motadataDbTableResult["processes^value"]))

	} else {

		for index, value := range motadataDbTableResult["processes^value"] {

			if value == utils.Empty {

				assertions.Equal(values[index], int64(0))

			} else {

				assertions.Equal(codec.ToString(values[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDbTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDbTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["processes^value"]))

}

// Scenario processes (Int 32) integer columns misses random time in the testcases ( missing column in first position)
func TestLogDrillDownResultType29(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-processes-log-type29.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDbTableResult := results[events]

	clickHouseEventQuery := "select event_source, processes ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var values []int64

	for rows.Next() {

		var eventSource string

		var value int64

		var timestamp int64

		err = rows.Scan(&eventSource, &value, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		values = append(values, value)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDbTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDbTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDbTableResult["processes^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(values, utils.ToINT64Values(motadataDbTableResult["processes^value"]))

	} else {

		for index, value := range motadataDbTableResult["processes^value"] {

			if value == utils.Empty {

				assertions.Equal(values[index], int64(0))

			} else {

				assertions.Equal(codec.ToString(values[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDbTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDbTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["processes^value"]))

}

// Scenario bytes received (Int 16) integer columns misses random time in the testcases ( missing column not in first position)
func TestLogDrillDownResultType30(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-bytesreceived-log-type30.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDbTableResult := results[events]

	clickHouseEventQuery := "select event_source, bytes_received ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var values []int64

	for rows.Next() {

		var eventSource string

		var value int64

		var timestamp int64

		err = rows.Scan(&eventSource, &value, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		values = append(values, value)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDbTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDbTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDbTableResult["bytes.received^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(values, utils.ToINT64Values(motadataDbTableResult["bytes.received^value"]))

	} else {

		for index, value := range motadataDbTableResult["bytes.received^value"] {

			if value == utils.Empty {

				assertions.Equal(values[index], int64(0))

			} else {

				assertions.Equal(codec.ToString(values[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDbTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDbTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["bytes.received^value"]))

}

// Scenario bytes received (Int 16) integer columns misses random time in the testcases ( missing column  in first position)
func TestLogDrillDownResultType31(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-bytesreceived-log-type31.json")

	timeline := utils.GetTimeline(utils.Today)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDbTableResult := results[events]

	clickHouseEventQuery := "select event_source, bytes_received ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var values []int64

	for rows.Next() {

		var eventSource string

		var value int64

		var timestamp int64

		err = rows.Scan(&eventSource, &value, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		values = append(values, value)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDbTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDbTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDbTableResult["bytes.received^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(values, utils.ToINT64Values(motadataDbTableResult["bytes.received^value"]))

	} else {

		for index, value := range motadataDbTableResult["bytes.received^value"] {

			if value == utils.Empty {

				assertions.Equal(values[index], int64(0))

			} else {

				assertions.Equal(codec.ToString(values[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDbTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDbTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["bytes.received^value"]))

}

// Scenario source port ordinal column(Int 16) integer columns misses random time in the testcases ( missing column not in first position)
func TestLogDrillDownResultType32(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-sourceport-log-type32.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDbTableResult := results[events]

	clickHouseEventQuery := "select event_source, source_port ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var values []int64

	for rows.Next() {

		var eventSource string

		var value int64

		var timestamp int64

		err = rows.Scan(&eventSource, &value, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		values = append(values, value)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDbTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDbTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDbTableResult["source.port^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(values, utils.ToINT64Values(motadataDbTableResult["source.port^value"]))

	} else {

		for index, value := range motadataDbTableResult["source.port^value"] {

			if value == utils.Empty {

				assertions.Equal(values[index], int64(0))

			} else {

				assertions.Equal(codec.ToString(values[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDbTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDbTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDbTableResult["source.port^value"]))

}

// Scenario source port ordinal column(Int 16) integer columns misses random time in the testcases ( missing columnin first position)
func TestLogDrillDownResultType33(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-sourceport-log-type33.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	clickHouseEventQuery := "select event_source, source_port ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var values []int64

	for rows.Next() {

		var eventSource string

		var value int64

		var timestamp int64

		err = rows.Scan(&eventSource, &value, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		values = append(values, value)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDBTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDBTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDBTableResult["source.port^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(values, utils.ToINT64Values(motadataDBTableResult["source.port^value"]))

	} else {

		for index, value := range motadataDBTableResult["source.port^value"] {

			if value == utils.Empty {

				assertions.Equal(values[index], int64(0))

			} else {

				assertions.Equal(codec.ToString(values[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDBTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDBTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["source.port^value"]))

}

// Scenario Level description ordinal string columns misses random time in the testcases ( missing column in first position)
func TestLogDrillDownResultType34(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-leveldecription-filterby-eventsource-log-type34.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	clickHouseEventQuery := "select event_source, level_description ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and  event_source in ('************' , '************') order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var levelDescriptions []string

	for rows.Next() {

		var eventSource string

		var levelDescription string

		var timestamp int64

		err = rows.Scan(&eventSource, &levelDescription, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		levelDescriptions = append(levelDescriptions, levelDescription)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDBTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDBTableResult["event.source^value"]))

	assertions.Equal(levelDescriptions, utils.ToStringList(motadataDBTableResult["level.description^value"]))

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDBTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDBTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["level.description^value"]))

}

// Scenario Log Level (Int 8) integer columns misses random time in the testcases ( missing column  in first position)
func TestLogDrillDownResultType35(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-loglevel-filterby-eventsource-log-type35.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	clickHouseEventQuery := "select event_source, log_level ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source in ('10.20.40.142' , '10.20.40.143') order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var logLevels []int64

	for rows.Next() {

		var eventSource string

		var logLevel int64

		var timestamp int64

		err = rows.Scan(&eventSource, &logLevel, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		logLevels = append(logLevels, logLevel)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDBTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDBTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDBTableResult["log.level^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(logLevels, utils.ToINT64Values(motadataDBTableResult["log.level^value"]))

	} else {

		for index, value := range motadataDBTableResult["log.level^value"] {

			if value == utils.Empty {

				assertions.EqualValues(logLevels[index], int64(0))

			} else {

				assertions.EqualValues(codec.ToString(logLevels[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDBTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDBTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["log.level^value"]))

}

// Scenario bytes sent (Int 64) integer columns misses random time in the testcases ( missing column in first position)
func TestLogDrillDownResultType36(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-bytessent-filterby-eventsource-log-type36.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	clickHouseEventQuery := "select event_source, bytes_sent ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source in ('************' , '************') order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var values []int64

	for rows.Next() {

		var eventSource string

		var value int64

		var timestamp int64

		err = rows.Scan(&eventSource, &value, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		values = append(values, value)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDBTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDBTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDBTableResult["bytes.sent^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(values, utils.ToINT64Values(motadataDBTableResult["bytes.sent^value"]))

	} else {

		for index, value := range motadataDBTableResult["bytes.sent^value"] {

			if value == utils.Empty {

				assertions.EqualValues(values[index], int64(0))

			} else {

				assertions.EqualValues(codec.ToString(values[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDBTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDBTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["bytes.sent^value"]))

}

// Scenario processes (Int 32) integer columns misses random time in the testcases ( missing column in first position)
func TestLogDrillDownResultType37(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-processes-filterby-eventsource-log-type37.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	clickHouseEventQuery := "select event_source, processes ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + "  and event_source in ('10.20.40.142' , '10.20.40.143') order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var values []int64

	for rows.Next() {

		var eventSource string

		var value int64

		var timestamp int64

		err = rows.Scan(&eventSource, &value, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		values = append(values, value)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDBTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDBTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDBTableResult["processes^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(values, utils.ToINT64Values(motadataDBTableResult["processes^value"]))

	} else {

		for index, value := range motadataDBTableResult["processes^value"] {

			if value == utils.Empty {

				assertions.Equal(values[index], int64(0))

			} else {

				assertions.Equal(codec.ToString(values[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDBTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDBTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["processes^value"]))

}

// Scenario bytes received (Int 16) integer columns misses random time in the testcases ( missing column  in first position)
func TestLogDrillDownResultType38(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-bytesreceived-filterby-eventsource-log-type38.json")

	timeline := utils.GetTimeline(utils.Today)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	clickHouseEventQuery := "select event_source, bytes_received ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source in ('10.20.40.142' , '10.20.40.143') order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var values []int64

	for rows.Next() {

		var eventSource string

		var value int64

		var timestamp int64

		err = rows.Scan(&eventSource, &value, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		values = append(values, value)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDBTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDBTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDBTableResult["bytes.received^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(values, utils.ToINT64Values(motadataDBTableResult["bytes.received^value"]))

	} else {

		for index, value := range motadataDBTableResult["bytes.received^value"] {

			if value == utils.Empty {

				assertions.Equal(values[index], int64(0))

			} else {

				assertions.Equal(codec.ToString(values[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDBTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDBTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["bytes.received^value"]))

}

// Scenario source port ordinal column(Int 16) integer columns misses random time in the testcases ( missing columnin first position)
func TestLogDrillDownResultType39(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-sourceport-filterby-eventsource-log-type39.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDBTableResult := results[events]

	clickHouseEventQuery := "select event_source, source_port ,timestamp*1000 from logTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source in ('10.20.40.142' , '10.20.40.143') order by timestamp desc "

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	var eventSources []string

	var values []int64

	for rows.Next() {

		var eventSource string

		var value int64

		var timestamp int64

		err = rows.Scan(&eventSource, &value, &timestamp)

		timeStamps = append(timeStamps, timestamp)

		eventSources = append(eventSources, eventSource)

		values = append(values, value)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDBTableResult["Timestamp"]))

	assertions.Equal(eventSources, utils.ToStringList(motadataDBTableResult["event.source^value"]))

	var stringValue bool

	for _, value := range motadataDBTableResult["source.port^value"] {

		if value == utils.Empty || reflect.TypeOf(value).Name() == "string" {

			stringValue = true

			break
		}
	}

	if !stringValue {

		assertions.Equal(values, utils.ToINT64Values(motadataDBTableResult["source.port^value"]))

	} else {

		for index, value := range motadataDBTableResult["source.port^value"] {

			if value == utils.Empty {

				assertions.Equal(values[index], int64(0))

			} else {

				assertions.Equal(codec.ToString(values[index]), codec.ToString(value))
			}

		}
	}

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDBTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.id^value"]))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.source^value"]))

	// event Source cannot be empty as it is present in all the string

	for _, value := range utils.ToStringList(motadataDBTableResult["event.source^value"]) {

		assertions.NotEmpty(value)
	}

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["source.port^value"]))

}

// Log Specific plugin with filter with drilldownfilter
func TestLogDrillDownResultType40(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-leveldescription-eventsource.json")

	timeline := utils.GetTimeline(utils.Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseEventQuery := "select timestamp*1000 from logTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and  level_description in [ 'fatal','info','debug' , 'error', 'warn'] and event_source in [ '************' , '************' ] order by timestamp desc "

	assertEventColumnResult(results, assertions, clickHouseEventQuery)

}

//--------------------drilldown new testcases moved from drilldown_test.go------------------------------

//status flap horizontal

func TestDrillDownStatusFlapLast1HourFilterByInterface(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Hour)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-statusflaphistory-horizontal-filterby-interface.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[query.VisualizationGranularity] = _1HourGranularity

	clickHouseQuery := "select monitor_id, duration,status, timestamp*1000 as Timestamp from statusHorizontalTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id in [0,1] order by timestamp desc"

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := make(map[string]struct{})

	for rows.Next() {

		var tick int64

		var duration int32

		var status string

		var monitor int32

		err = rows.Scan(&monitor, &duration, &status, &tick)

		clickHouseValue[codec.INT32ToStringValue(duration)+utils.GroupSeparator+status+utils.GroupSeparator+codec.INT64ToStringValue(tick)+utils.GroupSeparator+
			codec.INT32ToStringValue(monitor)] = struct{}{}

	}

	motadataDBValues := make(map[string]struct{})

	motadataDBTicks := utils.ToINT64Values(motadataDBTable["Timestamp"])

	motadataDBDurations := utils.ToINT64Values(motadataDBTable["duration^value"])

	motadataDBStatuses := utils.ToStringList(motadataDBTable["status.flap.history^value"])

	motadataDBMonitors := utils.ToINT64Values(motadataDBTable["object.id^value"])

	for index := range motadataDBTicks {

		motadataDBValues[codec.INT64ToStringValue(motadataDBDurations[index])+utils.GroupSeparator+motadataDBStatuses[index]+utils.GroupSeparator+codec.INT64ToStringValue(motadataDBTicks[index])+
			utils.GroupSeparator+codec.INT64ToStringValue(motadataDBMonitors[index])] = struct{}{}
	}

	assertions.Equal(len(clickHouseValue), len(motadataDBValues))

	for key := range clickHouseValue {

		if _, ok := motadataDBValues[key]; ok {

			assertions.True(true)
		} else {

			assertions.Fail("key mismatched ")
		}
	}
}

// status flap single request
func TestDrillDownStatusFlapLast1HourFilterByInterfaceType1(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Hour)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-statusflaphistory-horizontal-type1.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[query.VisualizationGranularity] = _1HourGranularity

	clickHouseQuery := "select monitor_id, duration,status, timestamp*1000 as Timestamp from statusHorizontalTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id in [1] order by timestamp desc"

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := make(map[string]struct{})

	for rows.Next() {

		var tick int64

		var duration int32

		var status string

		var monitor int32

		err = rows.Scan(&monitor, &duration, &status, &tick)

		clickHouseValue[codec.INT32ToStringValue(duration)+utils.GroupSeparator+status+utils.GroupSeparator+codec.INT64ToStringValue(tick)+utils.GroupSeparator+
			codec.INT32ToStringValue(monitor)] = struct{}{}

	}

	motadataDBValues := make(map[string]struct{})

	motadataDBTicks := utils.ToINT64Values(motadataDBTable["Timestamp"])

	motadataDBDurations := utils.ToINT64Values(motadataDBTable["duration^value"])

	motadataDBStatuses := utils.ToStringList(motadataDBTable["status.flap.history^value"])

	motadataDBMonitors := utils.ToINT64Values(motadataDBTable["object.id^value"])

	for index := range motadataDBTicks {

		motadataDBValues[codec.INT64ToStringValue(motadataDBDurations[index])+utils.GroupSeparator+motadataDBStatuses[index]+utils.GroupSeparator+codec.INT64ToStringValue(motadataDBTicks[index])+
			utils.GroupSeparator+codec.INT64ToStringValue(motadataDBMonitors[index])] = struct{}{}
	}

	assertions.Equal(len(clickHouseValue), len(motadataDBValues))

	for key := range clickHouseValue {

		if _, ok := motadataDBValues[key]; ok {

			assertions.True(true)
		} else {

			assertions.Fail("key mismatched ")
		}
	}
}

// Bug 6249
func TestDrillDownStatusFlap2DaysFilterByInterfaceType1(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(utils.Today)

	timeline[ToDateTime] = timeline.GetInt64Value(ToDateTime) + 86400000

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-statusflaphistory-horizontal-type1.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[query.VisualizationGranularity] = _1HourGranularity

	clickHouseQuery := "select monitor_id, duration,status, timestamp*1000 as Timestamp from statusHorizontalTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id in [1] order by timestamp desc"

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := make(map[string]struct{})

	for rows.Next() {

		var tick int64

		var duration int32

		var status string

		var monitor int32

		err = rows.Scan(&monitor, &duration, &status, &tick)

		clickHouseValue[codec.INT32ToStringValue(duration)+utils.GroupSeparator+status+utils.GroupSeparator+codec.INT64ToStringValue(tick)+utils.GroupSeparator+
			codec.INT32ToStringValue(monitor)] = struct{}{}

	}

	motadataDBValues := make(map[string]struct{})

	motadataDBTicks := utils.ToINT64Values(motadataDBTable["Timestamp"])

	motadataDBDurations := utils.ToINT64Values(motadataDBTable["duration^value"])

	motadataDBStatuses := utils.ToStringList(motadataDBTable["status.flap.history^value"])

	motadataDBMonitors := utils.ToINT64Values(motadataDBTable["object.id^value"])

	for index := range motadataDBTicks {

		motadataDBValues[codec.INT64ToStringValue(motadataDBDurations[index])+utils.GroupSeparator+motadataDBStatuses[index]+utils.GroupSeparator+codec.INT64ToStringValue(motadataDBTicks[index])+
			utils.GroupSeparator+codec.INT64ToStringValue(motadataDBMonitors[index])] = struct{}{}
	}

	assertions.Equal(len(clickHouseValue), len(motadataDBValues))

	for key := range clickHouseValue {

		if _, ok := motadataDBValues[key]; ok {

			assertions.True(true)
		} else {

			assertions.Fail("key mismatched ")
		}
	}
}

// status flap single request
func TestDrillDownStatusFlapThisWeekFilterByInterface(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(utils.ThisWeek)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-statusflaphistory-horizontal-type1.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[query.VisualizationGranularity] = _1HourGranularity

	clickHouseQuery := "select monitor_id, duration,status, timestamp*1000 as Timestamp from statusHorizontalTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id in [1] order by timestamp desc"

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := make(map[string]struct{})

	for rows.Next() {

		var tick int64

		var duration int32

		var status string

		var monitor int32

		err = rows.Scan(&monitor, &duration, &status, &tick)

		clickHouseValue[codec.INT32ToStringValue(duration)+utils.GroupSeparator+status+utils.GroupSeparator+codec.INT64ToStringValue(tick)+utils.GroupSeparator+
			codec.INT32ToStringValue(monitor)] = struct{}{}

	}

	motadataDBValues := make(map[string]struct{})

	motadataDBTicks := utils.ToINT64Values(motadataDBTable["Timestamp"])

	motadataDBDurations := utils.ToINT64Values(motadataDBTable["duration^value"])

	motadataDBStatuses := utils.ToStringList(motadataDBTable["status.flap.history^value"])

	motadataDBMonitors := utils.ToINT64Values(motadataDBTable["object.id^value"])

	for index := range motadataDBTicks {

		motadataDBValues[codec.INT64ToStringValue(motadataDBDurations[index])+utils.GroupSeparator+motadataDBStatuses[index]+utils.GroupSeparator+codec.INT64ToStringValue(motadataDBTicks[index])+
			utils.GroupSeparator+codec.INT64ToStringValue(motadataDBMonitors[index])] = struct{}{}
	}

	assertions.Equal(len(clickHouseValue), len(motadataDBValues))

	for key := range clickHouseValue {

		if _, ok := motadataDBValues[key]; ok {

			assertions.True(true)
		} else {

			assertions.Fail("key mismatched ")
		}
	}
}

// config history

func TestDrillDownConfigHistory(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, event, config_operation , config_operation_status, config_operation_output , config_operation_message , config_operation_user_name from configHistory where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source = '************' order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-drilldown-confighistory-allfields.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var events []string

	var configOperations []string

	var configOperationStatuses []string

	var configOperationOutputs []string

	var configOperationMessages []string

	var configOperationUserNames []string

	for rows.Next() {

		var tick int64

		var event string

		var configOperation string

		var configOperationStatus string

		var configOperationOutput string

		var configOperationMessage string

		var configOperationUserName string

		err = rows.Scan(&tick, &event, &configOperation, &configOperationStatus, &configOperationOutput, &configOperationMessage, &configOperationUserName)

		events = append(events, event)

		configOperations = append(configOperations, configOperation)

		configOperationStatuses = append(configOperationStatuses, configOperationStatus)

		configOperationOutputs = append(configOperationOutputs, configOperationOutput)

		configOperationMessages = append(configOperationMessages, configOperationMessage)

		configOperationUserNames = append(configOperationUserNames, configOperationUserName)

		clickHouseTicks = append(clickHouseTicks, tick)

	}

	assertions.EqualValues(clickHouseTicks, writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"]))

	assertions.EqualValues(configOperations, utils.ToStringList(motadataDBTable["config.operation^value"]))

	assertions.EqualValues(configOperationOutputs, utils.ToStringList(motadataDBTable["config.operation.output^value"]))

	assertions.EqualValues(configOperationMessages, utils.ToStringList(motadataDBTable["config.operation.message^value"]))

	assertions.EqualValues(configOperationUserNames, utils.ToStringList(motadataDBTable["config.operation.user.name^value"]))

	assertions.EqualValues(events, utils.ToStringList(motadataDBTable["event^value"]))

}

func TestDrillDownRunbookWorklogV1(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(utils.Today)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, event_source, runbook_worklog_id , runbook_worklog_result , runbook_worklog_error , runbook_worklog_status , runbook_worklog_type ,   Policy_id , Username  from runbookWorklog where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and Object_id in ( 1 , 2, 3, 4,5 , 6 , 7 , 8 ) order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-drilldown-allfields-filterby-objectid.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var eventSources []string

	var runbookWorklogIds []int64

	var runbookWorklogResults []string

	var runbookWorklogErrors []string

	var runbookWorklogStatuses []string

	var runbookWorklogTypes []int64

	var policyIds []int64

	var usernames []string

	for rows.Next() {

		var tick int64

		var eventSource string

		var runbookWorklogId int64

		var runbookWorklogResult string

		var runbookWorklogError string

		var runbookWorklogStatus string

		var runbookWorklogType int64

		var policyId int64

		var username string

		err = rows.Scan(&tick, &eventSource, &runbookWorklogId, &runbookWorklogResult, &runbookWorklogError, &runbookWorklogStatus, &runbookWorklogType, &policyId, &username)

		eventSources = append(eventSources, eventSource)

		runbookWorklogIds = append(runbookWorklogIds, runbookWorklogId)

		runbookWorklogResults = append(runbookWorklogResults, runbookWorklogResult)

		runbookWorklogErrors = append(runbookWorklogErrors, runbookWorklogError)

		runbookWorklogStatuses = append(runbookWorklogStatuses, runbookWorklogStatus)

		runbookWorklogTypes = append(runbookWorklogTypes, runbookWorklogType)

		policyIds = append(policyIds, policyId)

		usernames = append(usernames, username)

		clickHouseTicks = append(clickHouseTicks, tick)

	}

	assertions.EqualValues(clickHouseTicks, writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"]))

	assertions.EqualValues(eventSources, utils.ToStringList(motadataDBTable["event.source^value"]))

	assertions.EqualValues(runbookWorklogResults, utils.ToStringList(motadataDBTable["runbook.worklog.result^value"]))

	assertions.EqualValues(runbookWorklogErrors, utils.ToStringList(motadataDBTable["runbook.worklog.error^value"]))

	assertions.EqualValues(runbookWorklogStatuses, utils.ToStringList(motadataDBTable["runbook.worklog.status^value"]))

	assertions.EqualValues(runbookWorklogTypes, utils.ToINT64Values(motadataDBTable["runbook.worklog.type^value"]))

	assertions.EqualValues(runbookWorklogIds, utils.ToINT64Values(motadataDBTable["runbook.worklog.id^value"]))

	assertions.EqualValues(policyIds, utils.ToINT64Values(motadataDBTable["policy.id^value"]))

	assertions.EqualValues(usernames, utils.ToStringList(motadataDBTable["username^value"]))

}

func TestDrillDownRunbookWorklogV2(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(utils.Today)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, event_source, Object_id , runbook_worklog_result , runbook_worklog_error , runbook_worklog_status , runbook_worklog_type ,   Policy_id , Username  from runbookWorklog where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and runbook_worklog_id in ( 1 , 2 , 3 ,4 ,5 , 6 , 7 , 8 ) order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-drilldown-allfields-filterby-runbookworklogid.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var eventSources []string

	var objectIds []int64

	var runbookWorklogResults []string

	var runbookWorklogErrors []string

	var runbookWorklogStatuses []string

	var runbookWorklogTypes []int64

	var policyIds []int64

	var usernames []string

	for rows.Next() {

		var tick int64

		var eventSource string

		var objectId int64

		var runbookWorklogResult string

		var runbookWorklogError string

		var runbookWorklogStatus string

		var runbookWorklogType int64

		var policyId int64

		var username string

		err = rows.Scan(&tick, &eventSource, &objectId, &runbookWorklogResult, &runbookWorklogError, &runbookWorklogStatus, &runbookWorklogType, &policyId, &username)

		eventSources = append(eventSources, eventSource)

		objectIds = append(objectIds, objectId)

		runbookWorklogResults = append(runbookWorklogResults, runbookWorklogResult)

		runbookWorklogErrors = append(runbookWorklogErrors, runbookWorklogError)

		runbookWorklogStatuses = append(runbookWorklogStatuses, runbookWorklogStatus)

		runbookWorklogTypes = append(runbookWorklogTypes, runbookWorklogType)

		policyIds = append(policyIds, policyId)

		usernames = append(usernames, username)

		clickHouseTicks = append(clickHouseTicks, tick)

	}

	assertions.EqualValues(clickHouseTicks, writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"]))

	assertions.EqualValues(eventSources, utils.ToStringList(motadataDBTable["event.source^value"]))

	assertions.EqualValues(runbookWorklogResults, utils.ToStringList(motadataDBTable["runbook.worklog.result^value"]))

	assertions.EqualValues(runbookWorklogErrors, utils.ToStringList(motadataDBTable["runbook.worklog.error^value"]))

	assertions.EqualValues(runbookWorklogStatuses, utils.ToStringList(motadataDBTable["runbook.worklog.status^value"]))

	assertions.EqualValues(runbookWorklogTypes, utils.ToINT64Values(motadataDBTable["runbook.worklog.type^value"]))

	assertions.EqualValues(objectIds, utils.ToINT64Values(motadataDBTable["object.id^value"]))

	assertions.EqualValues(policyIds, utils.ToINT64Values(motadataDBTable["policy.id^value"]))

	assertions.EqualValues(usernames, utils.ToStringList(motadataDBTable["username^value"]))

}

// Flow drill Down int8 column , int16 column , int32 column , int64 column without condition
func TestDrillDownFlowDestinationPortDestinationASVolumeBytesPerSecVolumeBytesPerPacket(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "drilldown-flow-destinationport-destinationas-volumebytespersec-volumebytesperpacket.json")

	timeline := utils.GetTimeline(Last12Hours)

	clickHouseQuery := "select destination_port, destination_as, volume_bytes_per_sec, volume_bytes_per_packet, (timestamp*1000) as flow_timestamp from flowTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedDestinationPorts []int64

	var expectedDestinationAS []int64

	var expectedVolumeBytesPerSec []int64

	var expectedVolumeBytesPerPacket []int64

	var expectedFlowTimestamps []int64

	for rows.Next() {

		var destinationPort int64

		var destinationASN int64

		var volumeBytesPerSec int64

		var volumeBytesPerPacket int64

		var timestamp int64

		err = rows.Scan(&destinationPort, &destinationASN, &volumeBytesPerSec, &volumeBytesPerPacket, &timestamp)

		expectedDestinationPorts = append(expectedDestinationPorts, destinationPort)

		expectedDestinationAS = append(expectedDestinationAS, destinationASN)

		expectedVolumeBytesPerSec = append(expectedVolumeBytesPerSec, volumeBytesPerSec)

		expectedVolumeBytesPerPacket = append(expectedVolumeBytesPerPacket, volumeBytesPerPacket)

		expectedFlowTimestamps = append(expectedFlowTimestamps, timestamp)

	}

	assertions.EqualValues(expectedDestinationPorts, utils.ToINT64Values(motadataDBTable["destination.port^value"]))

	assertions.EqualValues(expectedDestinationAS, utils.ToINT64Values(motadataDBTable["destination.as^value"]))

	assertions.EqualValues(expectedVolumeBytesPerSec, utils.ToINT64Values(motadataDBTable["volume.bytes.per.sec^value"]))

	assertions.EqualValues(expectedVolumeBytesPerPacket, utils.ToINT64Values(motadataDBTable["volume.bytes.per.packet^value"]))

	assertions.EqualValues(expectedFlowTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

// Flow drill Down int8 column , int16 column , int32 column , int64 column with condition
func TestDrillDownFlowDestinationPortDestinationASVolumeBytesPerSecVolumeBytesPerPacketFilterByEventSource(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "drilldown-flow-destinationport-destinationas-volumebytespersec-volumebytesperpacket-filterby-eventsource.json")

	timeline := utils.GetTimeline(Last12Hours)

	clickHouseQuery := "select destination_port, destination_as, volume_bytes_per_sec, volume_bytes_per_packet, (timestamp*1000) as flow_timestamp from flowTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source in ['************','************','10.20.40.142'] order by timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedDestinationPorts []int64

	var expectedDestinationAS []int64

	var expectedVolumeBytesPerSec []int64

	var expectedVolumeBytesPerPacket []int64

	var expectedFlowTimestamps []int64

	for rows.Next() {

		var destinationPort int64

		var destinationASN int64

		var volumeBytesPerSec int64

		var volumeBytesPerPacket int64

		var timestamp int64

		err = rows.Scan(&destinationPort, &destinationASN, &volumeBytesPerSec, &volumeBytesPerPacket, &timestamp)

		expectedDestinationPorts = append(expectedDestinationPorts, destinationPort)

		expectedDestinationAS = append(expectedDestinationAS, destinationASN)

		expectedVolumeBytesPerSec = append(expectedVolumeBytesPerSec, volumeBytesPerSec)

		expectedVolumeBytesPerPacket = append(expectedVolumeBytesPerPacket, volumeBytesPerPacket)

		expectedFlowTimestamps = append(expectedFlowTimestamps, timestamp)

	}

	assertions.EqualValues(expectedDestinationPorts, utils.ToINT64Values(motadataDBTable["destination.port^value"]))

	assertions.EqualValues(expectedDestinationAS, utils.ToINT64Values(motadataDBTable["destination.as^value"]))

	assertions.EqualValues(expectedVolumeBytesPerSec, utils.ToINT64Values(motadataDBTable["volume.bytes.per.sec^value"]))

	assertions.EqualValues(expectedVolumeBytesPerPacket, utils.ToINT64Values(motadataDBTable["volume.bytes.per.packet^value"]))

	assertions.EqualValues(expectedFlowTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

//audit testcases

func TestDrillDownAuditFilterByAuditModuleGroupByAuditModule(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-audit-filterby-auditmodule-groupby-auditmodule.json")

	timeline := utils.GetTimeline(utils.Last1Month)

	clickHouseQuery := "select audit_module, audit_operation, audit_user, audit_message, audit_status, audit_remote_ip, (timestamp*1000) as audit_timestamp from auditTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and audit_module = 'user' order by timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedAuditModules []string

	var expectedAuditOperations []string

	var expectedAuditUsers []string

	var expectedAuditMessages []string

	var expectedAuditStatuses []string

	var expectedAuditRemoteIPs []string

	var expectedAuditTimestamps []int64

	for rows.Next() {

		var auditModule string

		var auditOperation string

		var auditUser string

		var auditMessage string

		var auditStatus string

		var auditRemoteIP string

		var timestamp int64

		err = rows.Scan(&auditModule, &auditOperation, &auditUser, &auditMessage, &auditStatus, &auditRemoteIP, &timestamp)

		expectedAuditModules = append(expectedAuditModules, auditModule)

		expectedAuditOperations = append(expectedAuditOperations, auditOperation)

		expectedAuditUsers = append(expectedAuditUsers, auditUser)

		expectedAuditMessages = append(expectedAuditMessages, auditMessage)

		expectedAuditStatuses = append(expectedAuditStatuses, auditStatus)

		expectedAuditRemoteIPs = append(expectedAuditRemoteIPs, auditRemoteIP)

		expectedAuditTimestamps = append(expectedAuditTimestamps, timestamp)

	}

	assertions.EqualValues(expectedAuditMessages, utils.ToStringList(motadataDBTable["audit.message^value"]))

	assertions.EqualValues(expectedAuditModules, utils.ToStringList(motadataDBTable["audit.module^value"]))

	assertions.EqualValues(expectedAuditOperations, utils.ToStringList(motadataDBTable["audit.operation^value"]))

	assertions.EqualValues(expectedAuditUsers, utils.ToStringList(motadataDBTable["audit.user^value"]))

	assertions.EqualValues(expectedAuditStatuses, utils.ToStringList(motadataDBTable["audit.status^value"]))

	assertions.EqualValues(expectedAuditRemoteIPs, utils.ToStringList(motadataDBTable["audit.remote.ip^value"]))

	assertions.EqualValues(expectedAuditTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

func TestDrillDownAuditFilterByAuditStatusGroupByAuditModule(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-audit-filterby-auditstatus-groupby-auditmodule.json")

	timeline := utils.GetTimeline(utils.Last1Month)

	clickHouseQuery := "select audit_module, audit_operation, audit_user, audit_message, audit_status, audit_remote_ip, (timestamp*1000) as audit_timestamp from auditTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and audit_status in ['22 (succeed)', '34 {failed}', '[pending]'] order by timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedAuditModules []string

	var expectedAuditOperations []string

	var expectedAuditUsers []string

	var expectedAuditMessages []string

	var expectedAuditStatuses []string

	var expectedAuditRemoteIPs []string

	var expectedAuditTimestamps []int64

	for rows.Next() {

		var auditModule string

		var auditOperation string

		var auditUser string

		var auditMessage string

		var auditStatus string

		var auditRemoteIP string

		var timestamp int64

		err = rows.Scan(&auditModule, &auditOperation, &auditUser, &auditMessage, &auditStatus, &auditRemoteIP, &timestamp)

		expectedAuditModules = append(expectedAuditModules, auditModule)

		expectedAuditOperations = append(expectedAuditOperations, auditOperation)

		expectedAuditUsers = append(expectedAuditUsers, auditUser)

		expectedAuditMessages = append(expectedAuditMessages, auditMessage)

		expectedAuditStatuses = append(expectedAuditStatuses, auditStatus)

		expectedAuditRemoteIPs = append(expectedAuditRemoteIPs, auditRemoteIP)

		expectedAuditTimestamps = append(expectedAuditTimestamps, timestamp)

	}

	assertions.EqualValues(expectedAuditMessages, utils.ToStringList(motadataDBTable["audit.message^value"]))

	assertions.EqualValues(expectedAuditModules, utils.ToStringList(motadataDBTable["audit.module^value"]))

	assertions.EqualValues(expectedAuditOperations, utils.ToStringList(motadataDBTable["audit.operation^value"]))

	assertions.EqualValues(expectedAuditUsers, utils.ToStringList(motadataDBTable["audit.user^value"]))

	assertions.EqualValues(expectedAuditStatuses, utils.ToStringList(motadataDBTable["audit.status^value"]))

	assertions.EqualValues(expectedAuditRemoteIPs, utils.ToStringList(motadataDBTable["audit.remote.ip^value"]))

	assertions.EqualValues(expectedAuditTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

func TestDrillDownAuditFilterByAuditModuleOperationTypeGroupByAuditModule(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-audit-filterby-auditmodule-auditoperation-groupby-auditmodule.json")

	timeline := utils.GetTimeline(utils.Last1Month)

	clickHouseQuery := "select audit_module, audit_operation, audit_user, audit_message, audit_status, audit_remote_ip, (timestamp*1000) as audit_timestamp from auditTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and audit_module = 'user' and audit_operation in ('Discovery', 'Topology', 'Login') order by timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedAuditModules []string

	var expectedAuditOperations []string

	var expectedAuditUsers []string

	var expectedAuditMessages []string

	var expectedAuditStatuses []string

	var expectedAuditRemoteIPs []string

	var expectedAuditTimestamps []int64

	for rows.Next() {

		var auditModule string

		var auditOperation string

		var auditUser string

		var auditMessage string

		var auditStatus string

		var auditRemoteIP string

		var timestamp int64

		err = rows.Scan(&auditModule, &auditOperation, &auditUser, &auditMessage, &auditStatus, &auditRemoteIP, &timestamp)

		expectedAuditModules = append(expectedAuditModules, auditModule)

		expectedAuditOperations = append(expectedAuditOperations, auditOperation)

		expectedAuditUsers = append(expectedAuditUsers, auditUser)

		expectedAuditMessages = append(expectedAuditMessages, auditMessage)

		expectedAuditStatuses = append(expectedAuditStatuses, auditStatus)

		expectedAuditRemoteIPs = append(expectedAuditRemoteIPs, auditRemoteIP)

		expectedAuditTimestamps = append(expectedAuditTimestamps, timestamp)

	}

	assertions.EqualValues(expectedAuditMessages, utils.ToStringList(motadataDBTable["audit.message^value"]))

	assertions.EqualValues(expectedAuditModules, utils.ToStringList(motadataDBTable["audit.module^value"]))

	assertions.EqualValues(expectedAuditOperations, utils.ToStringList(motadataDBTable["audit.operation^value"]))

	assertions.EqualValues(expectedAuditUsers, utils.ToStringList(motadataDBTable["audit.user^value"]))

	assertions.EqualValues(expectedAuditStatuses, utils.ToStringList(motadataDBTable["audit.status^value"]))

	assertions.EqualValues(expectedAuditRemoteIPs, utils.ToStringList(motadataDBTable["audit.remote.ip^value"]))

	assertions.EqualValues(expectedAuditTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))
}

//user notification testcases

func TestDrillDownUserNotification(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-usernotification.json")

	timeline := utils.GetTimeline(utils.Last1Month)

	clickHouseQuery := "select user_notification_severity, user_notification_type, user_notification_message, (timestamp*1000) notification_timestamp from userNotificationTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[query.QueryAbortRequired] = utils.Yes

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var notificationMessages []string

	var notificationTypes []string

	var notificationSeverities []string

	var timestamps []int64

	for rows.Next() {

		var notificationSeverity string

		var notificationType string

		var notificationMessage string

		var timestamp int64

		err = rows.Scan(&notificationSeverity, &notificationType, &notificationMessage, &timestamp)

		notificationMessages = append(notificationMessages, notificationMessage)

		notificationTypes = append(notificationTypes, notificationType)

		notificationSeverities = append(notificationSeverities, notificationSeverity)

		timestamps = append(timestamps, timestamp)

	}

	assertions.EqualValues(notificationMessages, utils.ToStringList(motadataDBTable["user.notification.message^value"]))

	assertions.EqualValues(notificationTypes, utils.ToStringList(motadataDBTable["user.notification.type^value"]))

	assertions.EqualValues(notificationSeverities, utils.ToStringList(motadataDBTable["user.notification.severity^value"]))

	assertions.EqualValues(timestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))
}

func TestDrillDownUserNotificationFilterByUserNotificationSeverityUserNotificationType(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-usernotification-filterby-usernotificationseverity-usernotificationtype.json")

	timeline := utils.GetTimeline(utils.Last1Month)

	clickHouseQuery := "select user_notification_severity, user_notification_type, user_notification_message, (timestamp*1000) notification_timestamp from userNotificationTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and user_notification_type = 'System' and user_notification_severity in ('Info', 'Fatal') order by timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var notificationMessages []string

	var notificationTypes []string

	var notificationSeverities []string

	var timestamps []int64

	for rows.Next() {

		var notificationSeverity string

		var notificationType string

		var notificationMessage string

		var timestamp int64

		err = rows.Scan(&notificationSeverity, &notificationType, &notificationMessage, &timestamp)

		notificationMessages = append(notificationMessages, notificationMessage)

		notificationTypes = append(notificationTypes, notificationType)

		notificationSeverities = append(notificationSeverities, notificationSeverity)

		timestamps = append(timestamps, timestamp)

	}

	assertions.EqualValues(notificationMessages, utils.ToStringList(motadataDBTable["user.notification.message^value"]))

	assertions.EqualValues(notificationTypes, utils.ToStringList(motadataDBTable["user.notification.type^value"]))

	assertions.EqualValues(notificationSeverities, utils.ToStringList(motadataDBTable["user.notification.severity^value"]))

	assertions.EqualValues(timestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))
}

// horizontal qualification

func TestDrillDownEventPolicyMessageSeverityMetric(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-policymessage-severity-metric.json")

	timeline := utils.GetTimeline(Last1Hour)

	clickHouseQuery := "select message, severity,metric,(timestamp *1000) as Timestamp from policyTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedMessages []string

	var expectedSeverity []string

	var expectedMetrics []string

	var expectedTimestamps []int64

	for rows.Next() {

		var message string

		var severity string

		var metric string

		var timestamp int64

		err = rows.Scan(&message, &severity, &metric, &timestamp)

		expectedMessages = append(expectedMessages, message)

		expectedSeverity = append(expectedSeverity, severity)

		expectedMetrics = append(expectedMetrics, metric)

		expectedTimestamps = append(expectedTimestamps, timestamp)

	}

	assertions.EqualValues(expectedMessages, utils.ToStringList(motadataDBTable["message^value"]))

	assertions.EqualValues(expectedSeverity, utils.ToStringList(motadataDBTable["severity^value"]))

	assertions.EqualValues(expectedMetrics, utils.ToStringList(motadataDBTable["metric^value"]))

	assertions.EqualValues(expectedTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

func TestDrillDownMetricPolicyMessageSeverityMetric(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-metricpolicymessage-severity-metric.json")

	timeline := utils.GetTimeline(Last1Hour)

	clickHouseQuery := "select message, severity,metric,(timestamp *1000) as Timestamp from policyTable2 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedMessages []string

	var expectedSeverity []string

	var expectedMetrics []string

	var expectedTimestamps []int64

	for rows.Next() {

		var message string

		var severity string

		var metric string

		var timestamp int64

		err = rows.Scan(&message, &severity, &metric, &timestamp)

		expectedMessages = append(expectedMessages, message)

		expectedSeverity = append(expectedSeverity, severity)

		expectedMetrics = append(expectedMetrics, metric)

		expectedTimestamps = append(expectedTimestamps, timestamp)

	}

	assertions.EqualValues(expectedMessages, utils.ToStringList(motadataDBTable["message^value"]))

	assertions.EqualValues(expectedSeverity, utils.ToStringList(motadataDBTable["severity^value"]))

	assertions.EqualValues(expectedMetrics, utils.ToStringList(motadataDBTable["metric^value"]))

	assertions.EqualValues(expectedTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

func TestDrillDownAuditMessageFilterByAuditModuleGroupByAuditModule(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-drilldown-auditmessage-filterby-auditmodule-groupby-auditmodule.json")

	timeline := utils.GetTimeline(Last1Hour)

	clickHouseQuery := "select audit_module, audit_message, (timestamp*1000) as audit_timestamp from auditTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and audit_module in ('user','discovery', 'topology') order by timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedAuditModules []string

	var expectedAuditMessages []string

	var expectedAuditTimestamps []int64

	for rows.Next() {

		var auditModule string

		var auditMessage string

		var timestamp int64

		err = rows.Scan(&auditModule, &auditMessage, &timestamp)

		expectedAuditModules = append(expectedAuditModules, auditModule)

		expectedAuditMessages = append(expectedAuditMessages, auditMessage)

		expectedAuditTimestamps = append(expectedAuditTimestamps, timestamp)

	}

	assertions.EqualValues(len(expectedAuditMessages), len(utils.ToStringList(motadataDBTable["audit.message^value"])))

	assertions.EqualValues(len(expectedAuditModules), len(utils.ToStringList(motadataDBTable["audit.module^value"])))

	assertions.EqualValues(expectedAuditTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

//policy duration

func TestDrillDownPolicyFlapLast24Hours(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Hour)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-policyflaphistory.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[query.VisualizationGranularity] = _1HourGranularity

	//granularity := codec.INTToStringValue(getGranularity(queryContext))

	clickHouseQuery := "select monitor_id,duration,instance,severity,policyId,timestamp*1000 as Timestamp from policyTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc"

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseDBTicks []int64

	var clickHouseSeverities []string

	var clickHouseDurations []int64

	var clickHouseInstances []string

	for rows.Next() {

		var monitorId int32

		var duration int32

		var instance string

		var severity string

		var policyId int64

		var tick int64

		err = rows.Scan(&monitorId, &duration, &instance, &severity, &policyId, &tick)

		clickHouseDurations = append(clickHouseDurations, int64(duration))

		clickHouseSeverities = append(clickHouseSeverities, severity)

		clickHouseDBTicks = append(clickHouseDBTicks, tick)

		clickHouseInstances = append(clickHouseInstances, instance)

	}

	assertions.EqualValues(clickHouseDBTicks, utils.ToINT64Values(motadataDBTable["Timestamp"]))

	assertions.EqualValues(clickHouseDurations, utils.ToINT64Values(motadataDBTable["duration^value"]))

	assertions.EqualValues(clickHouseInstances, utils.ToStringList(motadataDBTable["instance^value"]))

	assertions.EqualValues(clickHouseSeverities, utils.ToStringList(motadataDBTable["severity^value"]))

}

// Bug ID 1576
func TestDrillDownPolicyFlapLast1Month(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(utils.Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-policyflaphistory.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[query.VisualizationGranularity] = _1HourGranularity

	//granularity := codec.INTToStringValue(getGranularity(queryContext))

	clickHouseQuery := "select monitor_id,duration,instance,severity,policyId,timestamp*1000 as Timestamp from policyTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc"

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseDBTicks []int64

	var clickHouseSeverities []string

	var clickHouseDurations []int64

	var clickHouseInstances []string

	for rows.Next() {

		var monitorId int32

		var duration int32

		var instance string

		var severity string

		var policyId int64

		var tick int64

		err = rows.Scan(&monitorId, &duration, &instance, &severity, &policyId, &tick)

		clickHouseDurations = append(clickHouseDurations, int64(duration))

		clickHouseSeverities = append(clickHouseSeverities, severity)

		clickHouseDBTicks = append(clickHouseDBTicks, tick)

		clickHouseInstances = append(clickHouseInstances, instance)

	}

	assertions.EqualValues(clickHouseDBTicks, utils.ToINT64Values(motadataDBTable["Timestamp"]))

	assertions.EqualValues(clickHouseDurations, utils.ToINT64Values(motadataDBTable["duration^value"]))

	assertions.EqualValues(clickHouseInstances, utils.ToStringList(motadataDBTable["instance^value"]))

	assertions.EqualValues(clickHouseSeverities, utils.ToStringList(motadataDBTable["severity^value"]))

}

// Don't change the position of below 2 testcases . It should run at last
// User Notification
func TestDrillDownNotificationType1(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	// Getting 100 data only

	utils.MaxHistoricalRecords = 100

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "drilldown-notification-notificationtype-notificationseverity.json")

	queryContext := make(utils.MotadataMap)

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	timeline := utils.GetTimeline(utils.Last6Hours)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseQuery := "select user_notification_type, user_notification_severity, (timestamp*1000) as user_notification_timestamp from userNotificationTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc limit 100"

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedUserNotificationTypeValues []string

	var expectedUserNotificationSeverityValues []string

	var expectedUserNotificationTimestamps []int64

	for rows.Next() {

		var userNotificationType string

		var userNotificationSeverity string

		var timestamp int64

		err = rows.Scan(&userNotificationType, &userNotificationSeverity, &timestamp)

		expectedUserNotificationTypeValues = append(expectedUserNotificationTypeValues, userNotificationType)

		expectedUserNotificationSeverityValues = append(expectedUserNotificationSeverityValues, userNotificationSeverity)

		expectedUserNotificationTimestamps = append(expectedUserNotificationTimestamps, timestamp)

	}

	assertions.EqualValues(expectedUserNotificationTypeValues, utils.ToStringList(motadataDBTable["user.notification.type^value"]))

	assertions.EqualValues(expectedUserNotificationSeverityValues, utils.ToStringList(motadataDBTable["user.notification.severity^value"]))

	assertions.EqualValues(expectedUserNotificationTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

// Incremental with max limit reached
func TestDrillDownNotificationType2(t *testing.T) {

	defer verifyFailure(t)

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	// Getting 400 data only

	utils.MaxHistoricalRecords = 250

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "drilldown-notification-notificationtype-notificationseverity.json")

	queryContext := make(utils.MotadataMap)

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	timeline := utils.GetTimeline(utils.Last1Hour)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	clickHouseQuery := "select user_notification_type, user_notification_severity, (timestamp*1000) as user_notification_timestamp from userNotificationTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc limit 250"

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedUserNotificationTypeValues []string

	var expectedUserNotificationSeverityValues []string

	var expectedUserNotificationTimestamps []int64

	for rows.Next() {

		var userNotificationType string

		var userNotificationSeverity string

		var timestamp int64

		err = rows.Scan(&userNotificationType, &userNotificationSeverity, &timestamp)

		expectedUserNotificationTypeValues = append(expectedUserNotificationTypeValues, userNotificationType)

		expectedUserNotificationSeverityValues = append(expectedUserNotificationSeverityValues, userNotificationSeverity)

		expectedUserNotificationTimestamps = append(expectedUserNotificationTimestamps, timestamp)

	}

	assertions.EqualValues(expectedUserNotificationTypeValues, utils.ToStringList(motadataDBTable["user.notification.type^value"]))

	assertions.EqualValues(expectedUserNotificationSeverityValues, utils.ToStringList(motadataDBTable["user.notification.severity^value"]))

	assertions.EqualValues(expectedUserNotificationTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

func TestHistogramSystemCPUPercentGroupByMonitorCustomTimeline(t *testing.T) {

	defer verifyFailure(t)

	datastore.Close()

	loadMOTADATA4095Data()

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-avg-scalar-metric-systemcpupercent-groupby-monitor-type2.json")

	router := requestRouters[RouterTypeReader]

	queryContext := utils.MotadataMap{}

	err := json.Unmarshal(bytes, &queryContext)

	assertions.Nil(err)

	timeline := utils.GetTimeline(utils.Custom1Month)

	timeline[FromDateTime] = 1730505600000

	timeline[ToDateTime] = 1733183999000

	queryContext[VisualizationTimeline] = timeline

	queryContext["skip-incremental"] = utils.Yes

	datastore.Init()

	datastore.UpdateVerticalAggregations("system.cpu.percent", true)

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Empty(errs)

	floatValues, _ := removeDummyFLOAT64ValueSlice(results[events]["105^system.cpu.percent^avg"], nil)

	assertions.Len(floatValues, 3)

	floatValues, _ = removeDummyFLOAT64ValueSlice(results[events]["66^system.cpu.percent^avg"], nil)

	assertions.Len(floatValues, 36)

}

// bug Motadata-3607
func TestDrillDownEventHistoryEmptyMessage(t *testing.T) {

	defer verifyFailure(t)

	datastore.Close()

	loadMOTADATA3607Data()

	utils.MaxWorkerEventKeyGroupLength = 48

	datastore.Init()

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch-filterby-eventsource.json")

	timeline := utils.GetTimeline(utils.Today)

	timeline[ToDateTime] = 1727258843000

	timeline[FromDateTime] = 1727258602000

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	motadataDBTable := results[events]

	assertions.NotNil(motadataDBTable["message^value"])

	assertions.NotNil(motadataDBTable["event.source^value"])

	assertions.NotNil(motadataDBTable["Timestamp"])

	assertions.NotNil(motadataDBTable["event.id^value"])

	for _, message := range motadataDBTable["message^value"] {

		assertions.NotEmpty(message.(string))
	}

}

func TestDrillDownAuditWithMissingData(t *testing.T) {

	defer verifyFailure(t)

	datastore.Close()

	loadMOTADATA2858Data()

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "drilldown-motadata-2858-type2.json")

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	sendResponse(router, queryContext, true)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	assertions.Len(results[events]["Timestamp"], 3)

}

func TestDrillDownNotificationWithInvalidFilter(t *testing.T) {

	datastore.Close()

	loadMOTADATA2858Data()

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "drilldown-motadata-2858-type1.json")

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	sendResponse(router, queryContext, true)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.True(len(results) == 0)

}

func TestDrillDownEmptyMessageBugType1(t *testing.T) {

	datastore.Close()

	defer verifyFailure(t)

	loadMOTADATA2937Data()

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "drilldown-motadata-2937-type1.json")

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	assertions.True(len(results[events]["message^value"]) == 263)

	assertions.True(len(results[events]["event.source^value"]) == 263)

	for index, message := range results[events]["message^value"] {

		assertions.NotEmpty(message)

		assertions.Equal(int64(67), results[events]["event.source^value"][index])
	}
}

func TestDrillDownEmptyMessageBugType2(t *testing.T) {

	datastore.Close()

	defer verifyFailure(t)

	loadMOTADATA2937Data()

	router := requestRouters[RouterTypeReader]

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "drilldown-motadata-2937-type2.json")

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(results)

	assertions.True(len(results[events]["message^value"]) == 263)

	assertions.True(len(results[events]["event.source^value"]) == 263)

	for index, message := range results[events]["message^value"] {

		assertions.NotEmpty(message)

		assertions.Equal(int64(67), results[events]["event.source^value"][index])
	}
}

func TestSubscriberStartBrokerV1(t *testing.T) {

	assertions := assert.New(t)

	defer func() {

		utils.SystemBootSequence = utils.Datastore
	}()

	utils.SystemBootSequence = utils.Broker

	var err error

	testSubscriber := Subscriber{}

	testSubscriber.notificationSocket, err = testSubscriber.getSocket(DatastoreBrokerOperationTopic, SocketTypeNotification)

	testSubscriber.notificationSocket.Close()

	testSubscriber.Start()

	assertions.Nil(err)

}

func TestSubscriberStartBrokerV2(t *testing.T) {

	assertions := assert.New(t)

	defer func() {

		utils.SystemBootSequence = utils.Datastore
	}()

	utils.SystemBootSequence = utils.Broker

	var err error

	testSubscriber := Subscriber{}

	testSubscriber.notificationSocket, err = testSubscriber.getSocket(DatastoreBrokerOperationTopic, SocketTypeNotification)

	assertions.Nil(err)

	testSubscriber.availabilityWriterSocket, err = testSubscriber.getSocket(DatastoreBrokerOperationTopic, SocketTypeNotification)

	assertions.Nil(err)

	testSubscriber.availabilityWriterSocket.Close()

	testSubscriber.Start()

	assertions.Nil(err)

}

func TestSubscriberStartBrokerV3(t *testing.T) {

	assertions := assert.New(t)

	defer func() {

		utils.SystemBootSequence = utils.Datastore
	}()

	utils.SystemBootSequence = utils.Broker

	var err error

	testSubscriber := Subscriber{}

	testSubscriber.notificationSocket, err = testSubscriber.getSocket(DatastoreBrokerOperationTopic, SocketTypeNotification)

	assertions.Nil(err)

	testSubscriber.availabilityWriterSocket, err = testSubscriber.getSocket(DatastoreBrokerOperationTopic, SocketTypeNotification)

	assertions.Nil(err)

	testSubscriber.metricWriterSocket, err = testSubscriber.getSocket(DatastoreBrokerOperationTopic, SocketTypeNotification)

	assertions.Nil(err)

	testSubscriber.metricWriterSocket.Close()

	testSubscriber.Start()

	assertions.Nil(err)

}

func TestSubscriberStartBrokerV4(t *testing.T) {

	assertions := assert.New(t)

	defer func() {

		utils.SystemBootSequence = utils.Datastore
	}()

	utils.SystemBootSequence = utils.Broker

	var err error

	testSubscriber := Subscriber{}

	testSubscriber.notificationSocket, err = testSubscriber.getSocket(DatastoreBrokerOperationTopic, SocketTypeNotification)

	assertions.Nil(err)

	testSubscriber.availabilityWriterSocket, err = testSubscriber.getSocket(DatastoreBrokerOperationTopic, SocketTypeNotification)

	assertions.Nil(err)

	testSubscriber.metricWriterSocket, err = testSubscriber.getSocket(DatastoreBrokerOperationTopic, SocketTypeNotification)

	assertions.Nil(err)

	testSubscriber.eventWriterSocket, err = testSubscriber.getSocket(DatastoreBrokerOperationTopic, SocketTypeNotification)

	assertions.Nil(err)

	testSubscriber.eventWriterSocket.Close()

	testSubscriber.Start()

	assertions.Nil(err)

}

// Motadata-3423
func TestDrillDownConfigHistoryEmptyMessage(t *testing.T) {

	defer verifyFailure(t)

	datastore.Close()

	loadMOTADATA3423Data()

	router := requestRouters[RouterTypeReader]

	defer cache.Clear()

	timeline := utils.GetTimeline(utils.Last7Days)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-drilldown-confighistory-configevent-filterby-eventsource.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	timeline[ToDateTime] = 1727091900000

	timeline[FromDateTime] = 1727004600000

	queryContext[VisualizationTimeline] = timeline

	datastore.AlterIndexableColumns("500019-config", map[string]interface{}{
		"event.source": "",
	}, utils.Add)

	datastore.AddBlobColumn("config.event")

	datastore.Init()

	sendResponse(router, queryContext, false)

	results, errs := getResponse(router, queryContext, false, false)

	motadataDBTable := results[events]

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	assertions.Equal(9, len(writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"])))

	assertions.Equal(9, len(utils.ToStringList(motadataDBTable["config.event^value"])))

	for _, value := range utils.ToStringList(motadataDBTable["config.event^value"]) {

		assertions.NotEmpty(value)

	}

}

func TestRegisterDatastore(t *testing.T) {

	defer verifyFailure(t)

	assertions := assert.New(t)

	file, err := os.Create(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDataConfigFile)

	assert.Nil(t, err)

	assert.NotNil(t, file)

	file.WriteString("{\"manager.id\":\"dummy\"}")

	file.Close()

	go HandleShutdownEvent(serverAcks)

	utils.ShutdownNotifications <- "testing1" + utils.GroupSeparator + "testing2" + utils.GroupSeparator + "testing3"

	<-serverAcks

	Start()

	utils.AssertLogMessage(assertions, "Server", "server", "datastore registered... ip")
}

func loadMOTADATA2858Data() bool {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir)

	_ = utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"motadata-2858.zip", utils.CurrentDir)

	bytes, err := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	if err != nil {

		return false
	}

	if utils.InitConfigs(utils.UpdateConfigs(bytes, utils.MotadataMap{})) {

		datastore.Init()

		return true
	}

	return false

}

func loadMOTADATA2937Data() bool {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir)

	_ = utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"motadata-2937.zip", utils.CurrentDir)

	bytes, err := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	if err != nil {

		return false
	}

	if utils.InitConfigs(utils.UpdateConfigs(bytes, utils.MotadataMap{})) {

		datastore.Init()

		return true
	}

	return false

}

func loadMOTADATA3423Data() bool {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"motadata-3423.zip", utils.CurrentDir)

	return false

}

func loadMOTADATA3607Data() {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"motadata-3607.zip", utils.CurrentDir)

}

func loadMOTADATA4095Data() {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"motadata-4095.zip", utils.CurrentDir)

}

//QueryAbort

func TestQueryAbort(t *testing.T) {

	publisher.ShutDown()

	time.Sleep(time.Second * 2)

	defer func() {

		publisher = nil

		publisher = GetPublisher()

		publisher.Start()

	}()

	router := requestRouters[RouterTypeReader]

	router.abortTimerThresholdMillis = 3

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch.json")

	timeline := utils.GetTimeline(utils.Last1Month)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	sendResponse(router, queryContext, false)

	valid := false

	for {

		select {

		case response := <-utils.PublisherResponses:

			topicLength := int32(response[0]) |
				int32(response[1])<<8

			if !strings.Contains(string(response[2:2+topicLength]), utils.DatastoreQueryResponse) {

				continue
			}

			if strings.Contains(string(response[2:2+topicLength]), "datastore.notification") {

				continue
			}

			response = response[2+topicLength:]

			bytes, err := snappy.Decode(nil, response)

			if err != nil {

				panic(err)
			}

			buffer := bytes2.NewBuffer(bytes)

			//queryId

			_ = int(binary.LittleEndian.Uint64(buffer.Next(8)))

			//sub query id

			_ = int(binary.LittleEndian.Uint64(buffer.Next(8)))

			progress, _ := buffer.ReadByte()

			if progress != 100 {

				continue
			}

			result, errs, _, _ := utils.UnpackResponse(buffer, false)

			assertions.Nil(result)

			if strings.Contains(errs, utils.ErrorQueryAborted) {

				valid = true

				break
			}

		}

		if valid {

			break
		}
	}

	router.abortTimerThresholdMillis = 300

	assertions.True(valid)
}

func TestSubscriberClosing(t *testing.T) {

	assertions := assert.New(t)

	subscriber.Close = true

	time.Sleep(time.Second*5 + utils.SubscriberRCVTimeout)

	timeout := utils.SubscriberRCVTimeout

	defer func() {

		utils.SubscriberRCVTimeout = timeout
	}()

	utils.SubscriberRCVTimeout = time.Second * 1

	subscriber = nil

	assert.NotNil(t, zContext)

	subscriber = GetSubscriber()

	subscriber.Start()

	subscriber.Close = true

	time.Sleep(time.Second*2 + utils.SubscriberRCVTimeout)

	utils.AssertLogMessage(assertions, "Subscriber", "server", "reader socket closed")

	utils.AssertLogMessage(assertions, "Subscriber", "server", "AIOps query response socket closed")

	utils.AssertLogMessage(assertions, "Subscriber", "server", "notification socket closed")

}

func TestPublisherClosing(t *testing.T) {

	assertions := assert.New(t)

	publisher.ShutDown()

	time.Sleep(time.Second * 2)

	publisher = nil

	publisher = GetPublisher()

	publisher.Start()

	publisher.ShutDown()

	time.Sleep(time.Second * 2)

	utils.AssertLogMessage(assertions, "Publisher", "server", fmt.Sprintf(shutdownMessage, "AI-Engine"))

	utils.AssertLogMessage(assertions, "Publisher", "server", fmt.Sprintf("shutting down the batch request channel for %s", utils.SystemBootSequence))

	utils.AssertLogMessage(assertions, "Publisher", "server", fmt.Sprintf(shutdownMessage, utils.SystemBootSequence+" response"))

}

func TestSocketReconnect(t *testing.T) {

	assertions := assert.New(t)

	utils.SocketConnectionWatcherTime = time.Second * 1

	go startConnectionWatcher()

	threshold := utils.MaxSocketIdleConnectionTime

	defer func() {
		utils.MaxSocketIdleConnectionTime = threshold
	}()

	utils.MaxSocketIdleConnectionTime = time.Second * 8

	utils.SetLogLevel(utils.LogLevelTrace)

	time.Sleep(time.Second*12 + utils.SubscriberRCVTimeout)

	utils.AssertLogMessage(assertions, "Server", "server", "socket connection idle time exceeds threshold")

	utils.AssertLogMessage(assertions, "Server", "server", "shutting down publisher")

	utils.AssertLogMessage(assertions, "Server", "server", "starting socket reconnection")

	utils.AssertLogMessage(assertions, "Server", "server", "starting new publisher and subscriber")

	utils.AssertLogMessage(assertions, "Server", "server", fmt.Sprintf("adress : %s:%s is up, boot sequence : %s,", utils.GetHost(), utils.GetDatastoreNotificationPort(), utils.SystemBootSequence))

	utils.AssertLogMessage(assertions, "Server", "server", fmt.Sprintf("adress : %s:%s is up, boot sequence : %s,", utils.GetHost(), utils.GetDatastoreReaderPort(), utils.SystemBootSequence))

	connectionWatcherShutdownNotifications <- true
}

func TestHandleShutDown(t *testing.T) {

	go HandleShutdownEvent(serverAcks)

	utils.ShutdownNotifications <- "testing1" + utils.GroupSeparator + "testing2" + utils.GroupSeparator + "testing3"

	<-serverAcks

	time.Sleep(time.Second * 1)

	bytes, err := utils.ReadLogFile("Server", "server")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "Shutdown message received from testing1 for DATASTORE, message: testing2")

}

func TestPanicWriterRouter(t *testing.T) {

	router := requestRouters[RouterTypeNotification]

	router.Requests <- []byte{0}

	time.Sleep(time.Second * 1)

	router = requestRouters[RouterTypeReader]

	router.executors = nil

	router.startReader()

	time.Sleep(time.Second * 1)
}

func TestServerV1(t *testing.T) {

	t.Skip()

	assertions := assert.New(t)

	assertions.Equal(getPendingQueryMetricByQueryEngineType(query.QueryEngineType(100)), "log")

	_, err := os.Create(utils.CurrentDir + utils.PathSeparator + utils.Patch + "datastore-events")

	assertions.Nil(err)

	utils.InstallationMode = utils.Empty

	utils.AIOpsEngineQueryExecutors = 0

	utils.DatastoreMotaOpsServiceEnabled = true

	subscriber = GetSubscriber()

	Start()

	utils.SystemBootSequence = utils.Broker

	utils.DataWriters = 5

	utils.DataAggregators = 5

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	assertions.Nil(err)

	utils.InitConfigs(utils.UpdateConfigs(bytes, utils.MotadataMap{
		"datastore.env.type": "prod",
	}))

	subscriber = nil

	subscriber = GetSubscriber()

	assertions.Nil(Start())

	utils.RegistrationId = "dummy"

	subscriber = nil

	subscriber = GetSubscriber()

	utils.SystemBootSequence = utils.Datastore

	utils.AIOpsEngineQueryExecutors = 1

	utils.InitConfigs(utils.UpdateConfigs(bytes, utils.MotadataMap{
		"datastore.env.type":            "prod",
		utils.AIOpsEngineSubscriberPort: "dummy",
	}))

	subscriber = nil

	subscriber = GetSubscriber()

	subscriber.Start()

	utils.AIOpsEngineQueryExecutors = 0

	utils.SystemBootSequence = utils.Broker

	assertions.NotNil(subscriber)

	utils.InitConfigs(utils.UpdateConfigs(bytes, utils.MotadataMap{
		"datastore.env.type":      "prod",
		utils.DatastoreReaderPort: "dummy",
		utils.DatastoreHost:       "dummy",
	}))

	subscriber = nil

	subscriber = GetSubscriber()

	subscriber.Start()

	utils.SystemBootSequence = utils.Datastore

	utils.InitConfigs(utils.UpdateConfigs(bytes, utils.MotadataMap{
		"datastore.env.type":      "prod",
		utils.DatastoreReaderPort: "dummy",
		utils.DatastoreHost:       "dummy",
	}))

	subscriber = nil

	subscriber = GetSubscriber()

	subscriber.Start()

	utils.RegistrationId = utils.Empty

	assertions.Nil(GetSubscriber())

}

func TestRouterPanicRecoverV1(t *testing.T) {

	assertions := assert.New(t)

	router := NewRequestRouter(RouterTypeReader, nil)

	router.Start()

	context := utils.MotadataMap{
		query.VisualizationDataSources: utils.MotadataMap{
			utils.Type: nil,
		},
	}

	bytes := []byte{utils.DatastoreRead}

	data, err := json.Marshal(context)

	assertions.NoError(err)

	bytes = append(bytes, data...)

	router.Requests <- bytes

	time.Sleep(time.Second * 1)

	utils.AssertLogMessage(assertions, "Request Router", "server", "occurred in query router")

}

func TestRouterPanicRecoverV2(t *testing.T) {

	assertions := assert.New(t)

	router := NewRequestRouter(RouterTypeReader, nil)

	router.Start()

	context := utils.MotadataMap{
		query.QueryId: nil,
	}

	bytes := []byte{utils.DatastoreRead}

	data, err := json.Marshal(context)

	assertions.NoError(err)

	bytes = append(bytes, data...)

	router.Requests <- bytes

	time.Sleep(time.Second * 1)

	utils.AssertLogMessage(assertions, "Request Router", "server", "occurred in query router")

}

func TestRouterPanicRecoverV3(t *testing.T) {

	storeBackupJob = nil

	assertions := assert.New(t)

	router := NewRequestRouter(RouterTypeNotification, nil)

	router.Start()

	bytes := []byte{utils.BackupJob}

	router.Requests <- bytes

	time.Sleep(time.Second * 1)

	utils.AssertLogMessage(assertions, "Request Router", "server", "occurred in notification router")
}

func TestRouterProfileNotification(t *testing.T) {

	storeBackupJob = nil

	assertions := assert.New(t)

	router := NewRequestRouter(RouterTypeNotification, nil)

	router.Start()

	bytes := []byte{utils.Diagnostic}

	router.Requests <- bytes

	time.Sleep(time.Second * 3)

	utils.AssertLogMessage(assertions, "Diagnostic", "system", "Diagnostic started for")
}

// don't change the position of the test case

//Pending router testcases

func TestProcessQueuedQueries(t *testing.T) {

	utils.PublisherResponses = make(chan []byte, 10000)

	assertions := assert.New(t)

	router := NewRequestRouter(RouterTypeReader, nil)

	for priority := 0; priority < MaxPriority; priority++ {

		for index := 1; index <= 3; index++ {

			router.queuedQueries[query.Metric][priority].Put(index, utils.MotadataMap{

				"queryId": index,
			})
		}
	}

	completed := false

	go func() {

		for {

			if !completed {

				select {

				case _ = <-executors[0].Requests:

				}
			} else {

				return
			}

		}

	}()

	router.processQueuedQueries(query.Metric, 15, 1)

	//If one executor is pending than request is distributed in sequentially
	assertions.Equal(router.queuedQueries[query.Metric][0].Size(), 2)

	router.processQueuedQueries(query.Metric, 14, 1)

	//If one executor is pending than request is distributed in sequentially
	assertions.Equal(router.queuedQueries[query.Metric][1].Size(), 2)

	router.processQueuedQueries(query.Metric, 13, 1)

	//If one executor is pending than request is distributed in sequentially
	assertions.Equal(router.queuedQueries[query.Metric][2].Size(), 2)

	router.processQueuedQueries(query.Metric, 12, 1)

	//If one executor is pending than request is distributed in sequentially
	assertions.Equal(router.queuedQueries[query.Metric][3].Size(), 2)

	router.processQueuedQueries(query.Metric, 11, 1)

	//If one executor is pending than request is distributed in sequentially
	assertions.Equal(router.queuedQueries[query.Metric][4].Size(), 2)

	router.processQueuedQueries(query.Metric, 10, 1)

	//If one executor is pending than request is distributed in sequentially
	assertions.Equal(router.queuedQueries[query.Metric][0].Size(), 1)

	//Case 2 Increase pending queries to 2

	router.processQueuedQueries(query.Metric, 9, 2)

	//Since last query executed is 1 so need to remove 1 queries from priority 1
	assertions.Equal(router.queuedQueries[query.Metric][1].Size(), 1)

	for priority := 0; priority < MaxPriority; priority++ {

		router.queuedQueries[query.Metric][priority].Clear()

		for index := 1; index <= 2; index++ {

			router.queuedQueries[query.Metric][priority].Put(index, utils.MotadataMap{

				"queryId": index,
			})
		}
	}

	router.priorities[query.Metric] = 0

	// 2 pending queries in each priority
	router.processQueuedQueries(query.Metric, 10, 4)

	// 2 queries from 0
	// 2 queries from 1

	assertions.Equal(router.queuedQueries[query.Metric][0].Size(), 0)

	assertions.Equal(router.queuedQueries[query.Metric][1].Size(), 1)

	// Again getting 6 more queries to empty the queue

	router.processQueuedQueries(query.Metric, 6, 6)

	// 2 queries from 2
	// 2 queries from 3
	// 2 queries from 4

	assertions.Equal(router.queuedQueries[query.Metric][0].Size(), 0)

	assertions.Equal(router.queuedQueries[query.Metric][1].Size(), 0)

	assertions.Equal(router.queuedQueries[query.Metric][2].Size(), 0)

	assertions.Equal(router.queuedQueries[query.Metric][3].Size(), 0)

	assertions.Equal(router.queuedQueries[query.Metric][4].Size(), 0)

	// Pending < queue

	router.queuedQueries[query.Metric][0].Put(1, utils.MotadataMap{

		"queryId": 1,
	})

	router.processQueuedQueries(query.Metric, 1, 3)

	assertions.Equal(router.queuedQueries[query.Metric][0].Size(), 0)

	completed = true

	time.Sleep(time.Second * 1)

}

func TestSubscriberV1(t *testing.T) {

	subscriber.Close = true

	subscriber = nil

	time.Sleep(time.Second * 1)

	assertions := assert.New(t)

	utils.EnvironmentType = utils.DatastoreTestEnvironment

	zContext, _ = zmq.NewContext()

	publisher, _ := zContext.NewSocket(zmq.PUB)

	err := publisher.Bind("tcp://127.0.0.1:9999")

	assertions.Nil(err)

	utils.SystemBootSequence = utils.Datastore

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	assertions.NoError(err)

	utils.InitConfigs(utils.UpdateConfigs(bytes, utils.MotadataMap{

		utils.DatastoreNotificationPort: 9999,
		utils.DatastoreReaderPort:       2345,
		utils.AIOpsEngineSubscriberPort: 7675,
	}))

	utils.AIOpsEngineQueryExecutors = 1

	subscriber = nil

	localSubscriber := GetSubscriber()

	localSubscriber.AIOpsEngineQuerySocket.Close()

	localSubscriber.Start()

	subscriber = nil

	localSubscriber = GetSubscriber()

	localSubscriber.readerSocket.Close()

	localSubscriber.Start()

	utils.SystemBootSequence = utils.Broker

	subscriber = nil

	utils.SubscriberRCVTimeout = time.Millisecond * 20

	localSubscriber = GetSubscriber()

	broker = broker2.NewBroker(nil, nil)

	broker.Requests = make(chan []byte, 100)

	utils.SystemBootSequence = utils.Broker

	utils.SetLogLevel(0)

	utils.SystemBootSequence = utils.Broker

	localSubscriber.Start()

	time.Sleep(time.Millisecond * 5)

	_, err = publisher.SendBytes([]byte(utils.RegistrationId+utils.DotSeparator+DatastoreBrokerOperationTopic), zmq.DONTWAIT)

	time.Sleep(time.Second * 2)

	_, err = publisher.SendBytes([]byte(utils.RegistrationId+utils.DotSeparator+DatastoreBrokerOperationTopic), zmq.DONTWAIT)

	_, err = publisher.SendBytes([]byte(utils.RegistrationId+utils.DotSeparator+DatastoreBrokerOperationTopic), zmq.DONTWAIT)

	time.Sleep(time.Second * 2)

	utils.GlobalShutdown = true

	_, err = publisher.SendBytes([]byte(utils.RegistrationId+utils.DotSeparator+DatastoreBrokerOperationTopic), zmq.DONTWAIT)

	_, err = publisher.SendBytes([]byte(utils.RegistrationId+utils.DotSeparator+DatastoreBrokerOperationTopic), zmq.DONTWAIT)

	time.Sleep(time.Second * 1)

	subscriber = nil

	localSubscriber = GetSubscriber()

	broker = broker2.NewBroker(nil, nil)

	broker.Requests = make(chan []byte, 100)

	utils.SystemBootSequence = utils.Broker

	utils.GlobalShutdown = false

	localSubscriber.Start()

	publisher.Close()

	go zContext.Term()

	time.Sleep(time.Second)

	utils.AssertLogMessage(assertions, "Subscriber", "server", "notification socket closed")

}

func TestSubscriberV2(t *testing.T) {

	assertions := assert.New(t)

	utils.EnvironmentType = utils.DatastoreTestEnvironment

	zContext, _ = zmq.NewContext()

	publisher, _ := zContext.NewSocket(zmq.PUB)

	err := publisher.Bind("tcp://127.0.0.1:9998")

	assertions.Nil(err)

	defer publisher.Close()

	utils.SystemBootSequence = utils.Datastore

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	assertions.NoError(err)

	utils.InitConfigs(utils.UpdateConfigs(bytes, utils.MotadataMap{

		utils.DatastoreReaderPort:       9998,
		utils.AIOpsEngineSubscriberPort: 7675,
		utils.DatastoreNotificationPort: 3243,
	}))

	utils.AIOpsEngineQueryExecutors = 0

	utils.SystemBootSequence = utils.Datastore

	subscriber = nil

	utils.SubscriberRCVTimeout = time.Millisecond * 20

	localSubscriber := GetSubscriber()

	utils.SystemBootSequence = utils.Datastore

	requestRouters = make([]*RequestRouter, 2)

	requestRouters[RouterTypeNotification] = NewRequestRouter(RouterTypeNotification, executorAllocations)

	requestRouters[RouterTypeNotification].Requests = make(chan []byte, 100)

	requestRouters[RouterTypeReader] = NewRequestRouter(RouterTypeReader, executorAllocations)

	requestRouters[RouterTypeReader].Requests = make(chan []byte, 100)

	utils.GlobalShutdown = false

	utils.SetLogLevel(0)

	utils.SystemBootSequence = utils.Datastore

	localSubscriber.Start()

	time.Sleep(time.Millisecond * 10)

	_, err = publisher.SendBytes([]byte(utils.RegistrationId+utils.DotSeparator+DatastoreQueryTopic), zmq.DONTWAIT)

	time.Sleep(time.Second * 1)

	_, err = publisher.SendBytes([]byte(utils.RegistrationId+utils.DotSeparator+DatastoreQueryTopic), zmq.DONTWAIT)

	_, err = publisher.SendBytes([]byte(utils.RegistrationId+utils.DotSeparator+DatastoreQueryTopic), zmq.DONTWAIT)

	time.Sleep(time.Second * 1)

	utils.GlobalShutdown = true

	_, err = publisher.SendBytes([]byte(utils.RegistrationId+utils.DotSeparator+DatastoreQueryTopic), zmq.DONTWAIT)

	_, err = publisher.SendBytes([]byte(utils.RegistrationId+utils.DotSeparator+DatastoreQueryTopic), zmq.DONTWAIT)

	time.Sleep(time.Second * 1)

	subscriber = nil

	localSubscriber = GetSubscriber()

	utils.SystemBootSequence = utils.Datastore

	utils.GlobalShutdown = false

	localSubscriber.Start()

	time.Sleep(time.Millisecond * 100)

	requestRouters = nil

	_, err = publisher.SendBytes([]byte(utils.RegistrationId+utils.DotSeparator+DatastoreQueryTopic), zmq.DONTWAIT)

	_, err = publisher.SendBytes([]byte(utils.RegistrationId+utils.DotSeparator+DatastoreQueryTopic), zmq.DONTWAIT)

	time.Sleep(time.Second * 1)

	requestRouters = make([]*RequestRouter, 2)

	requestRouters[RouterTypeNotification] = NewRequestRouter(RouterTypeNotification, executorAllocations)

	requestRouters[RouterTypeNotification].Requests = make(chan []byte, 100)

	requestRouters[RouterTypeReader] = NewRequestRouter(RouterTypeReader, executorAllocations)

	requestRouters[RouterTypeReader].Requests = make(chan []byte, 100)

	time.Sleep(time.Millisecond * 100)

	subscriber = nil

	utils.GlobalShutdown = false

	utils.SystemBootSequence = utils.Datastore

	localSubscriber = GetSubscriber()

	localSubscriber.Start()

	go zContext.Term()

	time.Sleep(time.Millisecond * 100)

	utils.AssertLogMessage(assertions, "Subscriber", "server", "reader socket closed")

}

func TestSubscriberV3(t *testing.T) {

	assertions := assert.New(t)

	utils.EnvironmentType = utils.DatastoreTestEnvironment

	zContext, _ = zmq.NewContext()

	publisher, _ := zContext.NewSocket(zmq.PUSH)

	err := publisher.Connect("tcp://127.0.0.1:9996")

	assertions.Nil(err)

	defer publisher.Close()

	utils.SystemBootSequence = utils.Datastore

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	assertions.NoError(err)

	utils.InitConfigs(utils.UpdateConfigs(bytes, utils.MotadataMap{

		utils.AIOpsEngineSubscriberPort: 9996,
		utils.DatastoreReaderPort:       9998,
		utils.DatastoreNotificationPort: 3243,
	}))

	requestRouters = make([]*RequestRouter, 2)

	requestRouters[RouterTypeNotification] = NewRequestRouter(RouterTypeNotification, executorAllocations)

	requestRouters[RouterTypeNotification].Requests = make(chan []byte, 100)

	requestRouters[RouterTypeReader] = NewRequestRouter(RouterTypeReader, executorAllocations)

	requestRouters[RouterTypeReader].Requests = make(chan []byte, 100)

	utils.AIOpsEngineQueryExecutors = 1

	utils.SystemBootSequence = utils.Datastore

	subscriber = nil

	utils.SubscriberRCVTimeout = time.Second * 1

	localSubscriber := GetSubscriber()

	utils.SystemBootSequence = utils.Datastore

	utils.GlobalShutdown = false

	utils.SetLogLevel(0)

	utils.SystemBootSequence = utils.Datastore

	localSubscriber.Start()

	time.Sleep(time.Millisecond * 10)

	utils.GlobalShutdown = true

	_, err = publisher.SendBytes([]byte(utils.RegistrationId+utils.DotSeparator+DatastoreQueryTopic), zmq.DONTWAIT)

	time.Sleep(time.Second * 1)

	subscriber = nil

	utils.SubscriberRCVTimeout = time.Millisecond * 10

	localSubscriber = GetSubscriber()

	utils.SystemBootSequence = utils.Datastore

	utils.GlobalShutdown = false

	localSubscriber.Start()

	go zContext.Term()

	time.Sleep(time.Millisecond * 100)

	utils.AssertLogMessage(assertions, "Subscriber", "server", "AIOps query response socket closed")
}

func TestWatch(t *testing.T) {

	assertions := assert.New(t)

	bytes, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.MotadataDatastoreConfigFile, utils.UpdateConfigs(bytes, utils.MotadataMap{
		utils.DatastoreMotaOpsServiceStatus: "yes",
		utils.MappingStoreCacheConfigs: utils.MotadataMap{

			"dummy": 21,
		},
	}), 0755)

	event := fsnotify.Event{}

	event.Name = utils.MotadataDatastoreConfigFile

	watch(event)

	os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.MotadataDatastoreConfigFile, utils.UpdateConfigs(bytes, utils.MotadataMap{
		utils.DatastoreMotaOpsServiceStatus: "no",
	}), 0755)

	watch(event)

	event.Name = utils.HorizontalAggregations

	utils.SystemBootSequence = utils.Broker

	dataWriters = make([]*broker2.DataWriter, 1)

	dataWriters[0] = broker2.NewDataWriter(0, nil)

	dataWriters[0].HorizontalAggregationChangeNotifications = make(chan []byte, 100)

	watch(event)

	event.Name = utils.MotadataDatastoreConfigFile

	os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.MotadataDatastoreConfigFile, utils.UpdateConfigs(bytes, utils.MotadataMap{
		utils.DatastoreBrokerDataWriterSyncTimers: utils.MotadataMap{

			"1": float64(100),
		},
	}), 0755)

	watch(event)

	assertions.Equal(int64(100), utils.DataWriterSyncTimers[utils.PerformanceMetric])

	assertions.Equal(utils.DataWriterSyncMaxTimerSeconds, utils.DataWriterSyncTimers[utils.Flow])

	assertions.False(utils.DatastoreMotaOpsServiceEnabled)

}

func TestQueryCreationAgeLimit(t *testing.T) {

	assertions := assert.New(t)

	router := NewRequestRouter(RouterTypeReader, nil)

	router.Start()

	bytes := []byte{utils.DatastoreRead}

	queryBytes, _ := os.ReadFile(testDir + "histogram-allrecords-logsearch.json")

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[query.QueryCreationTime] = time.Now().UTC().Add(-(time.Duration(utils.QueryCreationAgeThresholdSeconds)*time.Second + time.Second)).Unix()

	data, err := json.Marshal(queryContext)

	assertions.NoError(err)

	bytes = append(bytes, data...)

	router.Requests <- bytes

	time.Sleep(time.Second * 1)

	assertions.Equal(router.discardedQueries, 1)

	bytes = []byte{utils.DatastoreRead}

	queryContext = utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[utils.QueryPriority] = utils.P1

	queryContext[query.QueryCreationTime] = time.Now().UTC().Add(-(time.Duration(utils.QueryCreationAgeThresholdSeconds)*time.Second + time.Second)).Unix()

	data, err = json.Marshal(queryContext)

	assertions.NoError(err)

	bytes = append(bytes, data...)

	router.Requests <- bytes

	time.Sleep(time.Second * 1)

	assertions.Equal(router.discardedQueries, 1)

	assertions.Equal(router.discardedQueries, 1)

	bytes = []byte{utils.DatastoreRead}

	queryContext = utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[utils.QueryPriority] = utils.P4

	queryContext[query.QueryCreationTime] = time.Now().UTC().Add(-(time.Duration(utils.QueryCreationAgeThresholdSeconds)*time.Second + time.Second)).Unix()

	data, err = json.Marshal(queryContext)

	assertions.NoError(err)

	bytes = append(bytes, data...)

	router.Requests <- bytes

	time.Sleep(time.Second * 1)

	assertions.Equal(router.discardedQueries, 1)

	bytes = []byte{utils.DatastoreRead}

	queryContext = utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[utils.QueryPriority] = utils.P0

	queryContext[query.QueryCreationTime] = time.Now().UTC().Add(-(time.Duration(utils.QueryCreationAgeThresholdSeconds)*time.Second + time.Second)).Unix()

	data, err = json.Marshal(queryContext)

	assertions.NoError(err)

	bytes = append(bytes, data...)

	router.Requests <- bytes

	time.Sleep(time.Second * 1)

	assertions.Equal(router.discardedQueries, 2)

}

func TestProcessFlushNotifications_FirstTimePlugin(t *testing.T) {
	publisher := &Publisher{}

	pluginName := "testPlugin"
	objects := []string{"obj1", "obj2"}

	notification := utils.MotadataMap{
		utils.OperationType: utils.Flush,
		utils.EventContext: utils.MotadataMap{
			datastore.Object: objects,
			utils.Plugin:     pluginName,
		},
	}

	objectContexts := make(map[string]utils.MotadataMap)

	publisher.processFlushNotifications(notification, objectContexts)

	expected := utils.MotadataMap{
		utils.OperationType: utils.Flush,
		utils.EventContext: utils.MotadataMap{
			datastore.Object: objects,
			utils.Plugin:     pluginName,
		},
	}

	if !reflect.DeepEqual(objectContexts[pluginName][utils.EventContext], expected[utils.EventContext]) {
		t.Errorf("Expected eventContext: %+v, got: %+v", expected[utils.EventContext], objectContexts[pluginName][utils.EventContext])
	}
}

func TestProcessFlushNotifications_SubsequentPluginMerge(t *testing.T) {
	publisher := &Publisher{}

	pluginName := "testPlugin"
	initialObjects := []string{"obj1"}
	newObjects := []string{"obj2", "obj3"}

	objectContexts := map[string]utils.MotadataMap{
		pluginName: {
			utils.OperationType: utils.Flush,
			utils.EventContext: utils.MotadataMap{
				datastore.Object: initialObjects,
				utils.Plugin:     pluginName,
			},
		},
	}

	notification := utils.MotadataMap{
		utils.OperationType: utils.Flush,
		utils.EventContext: utils.MotadataMap{
			datastore.Object: newObjects,
			utils.Plugin:     pluginName,
		},
	}

	publisher.processFlushNotifications(notification, objectContexts)

	expectedObjects := append(initialObjects, newObjects...)

	actualContext := objectContexts[pluginName][utils.EventContext].(utils.MotadataMap)
	if !reflect.DeepEqual(actualContext[datastore.Object], expectedObjects) {
		t.Errorf("Expected merged objects: %v, got: %v", expectedObjects, actualContext[datastore.Object])
	}
}

func TestProcessFlushNotifications_MissingObjectKey(t *testing.T) {
	publisher := &Publisher{}
	pluginName := "testPlugin"

	notification := utils.MotadataMap{
		utils.OperationType: utils.Flush,
		utils.EventContext: utils.MotadataMap{
			utils.Plugin: pluginName,
		},
	}

	objectContexts := make(map[string]utils.MotadataMap)

	publisher.processFlushNotifications(notification, objectContexts)

	actualContext := objectContexts[pluginName][utils.EventContext].(utils.MotadataMap)

	if actualContext[datastore.Object] != nil {
		t.Errorf("Expected object key to be nil, got: %v", actualContext[datastore.Object])
	}
}

func verifyFailure(t *testing.T) {

	if t.Failed() {

		utils.BackupFailingTestEnvironment(utils.CurrentDir, filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))), "Server")
	}
}

//helper functions

func assertEventColumnResult(results map[string]map[string][]interface{}, assertions *assert.Assertions, clickHouseEventQuery string) {

	motadataDBTableResult := results[events]

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {
		panic(err)
	}

	rows, err := connection.Query(ctx, clickHouseEventQuery)

	assertions.Nil(err)

	var timeStamps []int64

	for rows.Next() {

		var timestamp int64

		err = rows.Scan(&timestamp)

		timeStamps = append(timeStamps, timestamp)

	}

	assertions.Equal(timeStamps, utils.ToINT64Values(motadataDBTableResult["Timestamp"]))

	//the length of all three columns should be same
	assertions.Equal(len(timeStamps), len(utils.ToINT64Values(motadataDBTableResult["Timestamp"])))

	assertions.Equal(len(timeStamps), len(motadataDBTableResult["event.id^value"]))

	if _, ok := motadataDBTableResult["event^value"]; ok {

		assertions.Equal(len(timeStamps), len(motadataDBTableResult["event^value"]))

	}

	for _, eventId := range motadataDBTableResult["event.id^value"] {

		assertions.NotEmpty(eventId.(string))
	}

}

func getResponse(router *RequestRouter, queryContext utils.MotadataMap, abort bool, paginationQuery bool) (map[string]map[string][]interface{}, string) {

	results := make(map[string]map[string][]interface{}, 3)

	timer := time.NewTicker(time.Minute * 5)

	for {

		select {

		case response := <-utils.PublisherResponses:

			topicLength := int32(response[0]) |
				int32(response[1])<<8

			if !strings.Contains(string(response[2:2+topicLength]), utils.DatastoreQueryResponse) {

				continue
			}

			response = response[2+topicLength:]

			bytes, err := snappy.Decode(nil, response)

			if err != nil {

				panic(err)
			}

			buffer := bytes2.NewBuffer(bytes)

			//queryId

			_ = int(binary.LittleEndian.Uint64(buffer.Next(8)))

			//sub query id

			_ = int(binary.LittleEndian.Uint64(buffer.Next(8)))

			progress, _ := buffer.ReadByte()

			result, errs, _, _ := utils.UnpackResponse(buffer, false)

			if queryContext.GetStringValue(query.VisualizationCategory) == query.Gauge || queryContext.GetStringValue(query.VisualizationCategory) == query.Grid || queryContext.GetStringValue("skip-incremental") == utils.Yes {

				if progress == 100 {

					results[events] = result

					return results, errs
				}

			} else {

				if errs == utils.Empty {

					var field string

					if _, ok := result["event.id^value"]; ok {

						field = events

						for key, value := range result {

							if results[field] == nil {

								results[field] = make(map[string][]interface{}, 1)
							}

							results[field][key] = append(results[field][key], value...)
						}

					}

				} else {

					abortResponse(router, queryContext)

					return results, errs
				}

				if paginationQuery && abort {

					abortResponse(router, queryContext)

					time.Sleep(time.Millisecond * 500)

					results[events] = result

					return results, errs
				}

				if progress == 100 {

					if !paginationQuery && !abort {

						abortResponse(router, queryContext)

					}

					time.Sleep(time.Millisecond * 500)

					return results, errs

				} else {

					if abort {

						time.Sleep(time.Second * 1)

						abortResponse(router, queryContext)

						time.Sleep(time.Second * 1)

						return nil, "query aborted"
					}

					sendResponse(router, queryContext, true)

					time.Sleep(time.Millisecond * 500)
				}
			}

		case <-timer.C:

			routerLogger.Error("query time limit exceeded")

			return nil, "query time limit exceeded"

		}
	}

	return nil, utils.Empty
}

func sendResponse(router *RequestRouter, queryContext utils.MotadataMap, resolved bool) {

	queryContext[query.QueryCreationTime] = time.Now().UTC().Unix()

	if !resolved {

		queryContext.GetMapValue(query.VisualizationDataSources)[utils.Type] = utils.ResolveQueryType(queryContext.GetMapValue(query.VisualizationDataSources).GetStringValue(utils.Type))

		queryContext[query.QueryId] = time.Now().UnixNano()

		queryContext[query.SubQueryId] = time.Now().UnixMilli()

	}
	buffer := bytes2.NewBuffer([]byte{})

	buffer.WriteByte(utils.DatastoreRead)

	bytes, _ := json.Marshal(&queryContext)

	buffer.Write(bytes)

	router.Requests <- buffer.Bytes()

}

func abortResponse(router *RequestRouter, queryContext utils.MotadataMap) {

	buffer := bytes2.NewBuffer([]byte{})

	buffer.WriteByte(utils.QueryAbort)

	context := make(utils.MotadataMap)

	context["query.id"] = queryContext.GetInt64Value("query.id")

	bytes, _ := json.Marshal(&context)

	buffer.Write(bytes)

	router.Requests <- buffer.Bytes()

}

func initDatabase() {

	utils.CleanUpStores()

	err := os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator)

	if !os.IsNotExist(err) && err != nil {

		panic(err)
	}

	sourcePath := filepath.Dir(filepath.Dir(utils.CurrentDir)) + utils.PathSeparator + "motadatadatastore" + utils.PathSeparator + "datastore" + utils.PathSeparator + "query" + utils.PathSeparator + "datastore"

	destinationPath := utils.CurrentDir + utils.PathSeparator + "datastore"

	err = utils.CloneDirectory(sourcePath, destinationPath)

	if err != nil {

		panic(err)
	}

	sourcePath = filepath.Dir(filepath.Dir(utils.CurrentDir)) + utils.PathSeparator + "motadatadatastore" + utils.PathSeparator + "datastore" + utils.PathSeparator + "query" + utils.PathSeparator + utils.ConfigDir

	destinationPath = utils.CurrentDir + utils.PathSeparator + utils.ConfigDir

	err = utils.CloneDirectory(sourcePath, destinationPath)

	if err != nil {

		panic(err)
	}

	datastore.Init()
}

func publishQuery(i int, dummyPublisher *zmq.Socket, assertions *assert.Assertions) {

	queryContext := utils.MotadataMap{
		query.SubQueryId: float64(123450 + i),
		query.VisualizationDataSources: map[string]interface{}{

			"type": "metric",
		},
	}

	queryContext[query.QueryCreationTime] = time.Now().UTC().Unix()

	bufferBytes, _ := json.Marshal(queryContext)

	_, err := dummyPublisher.SendMessage([]byte(utils.RegistrationId+utils.DotSeparator+DatastoreQueryTopic), append([]byte{2}, bufferBytes...))

	assertions.Nil(err)

}

func removeDummyFLOAT64ValueSlice(values []interface{}, ticks []interface{}) ([]float64, []int64) {

	var filteredValues []float64

	var filteredTicks []int64

	for index := range values {

		if values[index].(float64) != utils.DummyFLOAT64Value {

			filteredValues = append(filteredValues, values[index].(float64))

			if ticks != nil {

				filteredTicks = append(filteredTicks, ticks[index].(int64))
			}
		}
	}

	return filteredValues, filteredTicks

}
