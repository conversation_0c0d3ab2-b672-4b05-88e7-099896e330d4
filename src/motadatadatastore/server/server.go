/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-07             Vedant D. Dokania     	MOTADATA-4698 Changed port check logic and added multiple ports for checking
* 2025-04-09			 <PERSON><PERSON><PERSON> Shah			Motadata-4859  Refactoring "fmt.Sprintf" to update go version to 1.24
* 2025-06-04             A<PERSON>l Shah            MOTADATA-5780 Updated process Kill logic and handled shutdown for connectionWatcher
 */

/*
 * Package server implements the core server functionality for the Motadata database system.
 *
 * This package is responsible for:
 * 1. Starting and managing the database server lifecycle
 * 2. Initializing and coordinating all system components:
 *    - Query engine for processing client queries
 *    - Storage engine for data persistence
 *    - Job engine for background tasks and maintenance
 *    - Index engine for efficient data retrieval
 *    - Request engine for handling client communications
 * 3. Managing communication channels between components
 * 4. Handling configuration changes and system events
 * 5. Implementing graceful shutdown procedures
 * 6. Monitoring system health and performance
 *
 * The server architecture uses a modular design with separate components for different
 * responsibilities, allowing for scalability and maintainability. Communication between
 * components is primarily done through channels to ensure thread safety.
 */

package server

import (
	bytes2 "bytes"
	"fmt"
	"github.com/fsnotify/fsnotify"
	zmq "github.com/pebbe/zmq4"
	"github.com/shirou/gopsutil/process"
	broker2 "motadatadatastore/broker"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/datastore/query"
	"motadatadatastore/datastore/writer"
	"motadatadatastore/job"
	"motadatadatastore/motaops"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"os/exec"
	"strings"
	"sync/atomic"
	"time"
)

// Server component global variables
var (

	// Storage and I/O components
	diskIOWorkers []*storage.DiskIOWorker // Workers for handling disk I/O operations

	// Data writers for different storage formats
	verticalWriters     []*writer.VerticalWriter     // Writers for vertical format data (entity-centric)
	staticMetricWriters []*writer.StaticMetricWriter // Writers for static metrics
	horizontalWriters   []*writer.HorizontalWriter   // Writers for horizontal format data (time-series)
	indexWriters        []*writer.IndexWriter        // Writers for index data
	healthMetricWriters []*writer.HorizontalWriter   // Writers for health metrics

	// Query processing components
	workers          []*query.Worker   // Workers for processing queries
	availableWorkers []atomic.Bool     // Tracks availability status of workers
	executors        []*query.Executor // Executors for running queries

	// Logging
	serverLogger = utils.NewLogger("Server", "server") // Logger for server operations

	// Communication components
	subscriber     *Subscriber      // ZMQ subscriber for receiving messages
	publisher      *Publisher       // ZMQ publisher for sending messages
	requestRouters []*RequestRouter // Routers for handling client requests

	// Data processing components
	broker          *broker2.Broker           // Broker for message distribution
	motaOps         *motaops.MotaOps          // Operations management component
	dataReaders     []*writer.DataReader      // Readers for processing incoming data
	dataWriters     []*broker2.DataWriter     // Writers for sending data to storage
	dataAggregators []*broker2.DataAggregator // Aggregators for data processing

	// Background jobs
	aggregationJobs   []*job.AggregationJob      // Jobs for data aggregation
	eventAggregators  []*writer.EventAggregator  // Aggregators for event data
	metricAggregators []*writer.MetricAggregator // Aggregators for metric data

	// Resource allocation maps
	executorAllocations, workerAllocations map[int]query.QueryEngineType // Maps for tracking resource allocations

	// Maintenance jobs
	storeBackupJob     *job.StoreBackupJob      // Job for backing up data stores
	syncJobManager     *job.StoreSyncJobManager // Manager for synchronization jobs
	storeCleanUpJobs   []*job.StoreCleanUpJob   // Jobs for cleaning up stores
	mappingCleanupJobs []*job.MappingCleanupJob // Jobs for cleaning up mappings
	indexJob           *job.IndexJob            // Job for maintaining indexes
	retentionJob       *job.RetentionJob        // Job for enforcing data retention policies
	healthCheckupJob   *job.HealthCheckupJob    // Job for system health monitoring
	manager            *job.Manager             // Manager for coordinating jobs

	// ZeroMQ context
	zContext *zmq.Context // ZeroMQ context for messaging

	// Shutdown coordination
	watcherShutdownNotifications chan bool // Channel for shutdown notifications

	connectionWatcherShutdownNotifications chan bool

	// Connection monitoring
	SocketIdleTime time.Duration // Tracks idle time for socket connections
)

const (
	DatastoreOperationTopic = "datastore.operation"

	DatastoreBrokerOperationTopic = "datastore.broker.operation"

	DatastoreQueryTopic = "datastore.query"

	LogLevelResetTimerSeconds = "log.level.reset.timer.seconds"
)

func Start() error {

	serverLogger.Info(fmt.Sprintf("starting motadata %v", utils.SystemBootSequence))

	if _, err := os.Stat(utils.CurrentDir + utils.PathSeparator + utils.Patch + "datastore-events"); err == nil {

		_ = os.RemoveAll(utils.EventDir)

		_ = os.Rename(utils.CurrentDir+utils.PathSeparator+utils.Patch+"datastore-events", utils.EventDir)
	}

	create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

	create(utils.TempDir)

	create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations)

	create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat + utils.HyphenSeparator + utils.Aggregations)

	create(utils.TempDir + utils.HyphenSeparator + utils.Aggregations)

	create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.HealthStatistics)

	if utils.SystemBootSequence == utils.Datastore {

		datastore.Init()

		InitQueryEngine()

		InitJobEngine()

		initIndexEngine()

		dataReaders = make([]*writer.DataReader, 4)

		dataReaders[0] = writer.NewDataReader(verticalWriters, nil, nil, nil)

		dataReaders[1] = writer.NewDataReader(nil, horizontalWriters, nil, nil)

		dataReaders[2] = writer.NewDataReader(nil, nil, nil, eventAggregators)

		dataReaders[3] = writer.NewDataReader(nil, nil, healthMetricWriters, nil)

		for id := range dataReaders {

			dataReaders[id].Start()
		}

		go initBrokerEngine()

		if utils.GetEnvironmentType() != utils.DatastoreTestEnvironment {

			go startConfigWatcher()

		}

		initRequestEngine()

		if err := register(); err != nil {

			serverLogger.Error(err.Error())

			return err
		}

		if utils.DatastoreMotaOpsServiceEnabled {

			motaOps = motaops.NewMotaOps()

			motaOps.Start()
		}

	} else {

		utils.PublisherResponses = make(chan []byte, utils.DataWriters)

		utils.PublisherNotifications = make(chan utils.MotadataMap, utils.GetPublisherEventChannelSize())

		dataAggregators = make([]*broker2.DataAggregator, utils.DataAggregators)

		dataWriters = make([]*broker2.DataWriter, utils.DataWriters)

		broker = broker2.NewBroker(dataWriters, dataAggregators)

		broker.Start()

		for id := 0; id < len(dataAggregators); id++ {

			dataAggregators[id] = broker2.NewDataAggregator(id)

			dataAggregators[id].Start()
		}

		for id := range dataWriters {

			dataWriters[id] = broker2.NewDataWriter(id, dataAggregators)

			dataWriters[id].Start()
		}

		if utils.GetEnvironmentType() != utils.DatastoreTestEnvironment {

			go startConfigWatcher()

		}

		initRequestEngine()
	}

	if utils.AIOpsEngineQueryExecutors > 0 {

		go initAIOpsEngine()
	}

	/*

		Config watcher keeps an eye on the motadata config file and if anything changes notify the same , Currently we are waiting for two events , write ( nano , gedit) , rename (vim).

		It calls reload config parameters function and certain parameters are fixed which can be hot reload.

		In case of log if log level is debug or trace than it gets rest after 30 minutes

	*/

	if utils.GetEnvironmentType() != utils.DatastoreTestEnvironment {

		go startConnectionWatcher()

	}

	return nil
}

func initAIOpsEngine() {

	processes, _ := process.Processes()

	for _, process := range processes {

		if processName, err := process.Name(); err == nil && (strings.Contains(processName, utils.AIOpsEngine)) {

			process, err := os.FindProcess(int(process.Pid))

			if err == nil {

				_ = process.Kill() // This works on Windows
			}
		}

	}

	for {

		if !utils.GlobalShutdown {

			_, err := exec.Command(utils.AIOpsEngineDir+utils.PathSeparator+utils.AIOpsEngine,
				codec.INTToStringValue(utils.GetLogLevel()),
				codec.INTToStringValue(utils.AIOpsEngineQueryExecutors),
				utils.GetAIOpsEnginePublisherPort(),
				utils.GetAIOpsEngineSubscriberPort()).Output()

			serverLogger.Error(fmt.Sprintf("AI-Engine process exited unexpectedly...%v", err))
		}
	}

}

func initBrokerEngine() {

	processes, _ := process.Processes()

	for _, process := range processes {

		if processName, err := process.Name(); err == nil && (strings.Contains(processName, utils.BrokerEngine)) {

			process, err := os.FindProcess(int(process.Pid))

			if err == nil {

				_ = process.Kill()
			}

		}

	}

	for {

		if utils.EnvironmentType == utils.DatastoreTestEnvironment {

			break
		}

		if !utils.GlobalShutdown {

			_, err := exec.Command(utils.CurrentDir+utils.PathSeparator+utils.BrokerEngine, utils.Broker).Output()

			if err != nil {

				serverLogger.Error(fmt.Sprintf("Datastore Broker process exited unexpectedly...%v", err))

				continue
			}

			serverLogger.Error("Datastore Broker process exited unexpectedly...")
		}
	}

}

func startConfigWatcher() {

	watcherShutdownNotifications = make(chan bool, 5)

	aggregationWatcher, err := fsnotify.NewWatcher()

	if err != nil {

		serverLogger.Error(fmt.Sprintf("failed to initialize %v aggregation watcher, reason: %v", utils.SystemBootSequence, err.Error()))

		return
	}

	configWatcher, err := fsnotify.NewWatcher()

	if err != nil {

		serverLogger.Error(fmt.Sprintf("failed to initialize %v config watcher, reason: %v", utils.SystemBootSequence, err.Error()))

		return
	}

	defer func() {

		err = aggregationWatcher.Close()

		if err != nil {

			serverLogger.Error(fmt.Sprintf("unable to close %v aggregation watcher , error %v", utils.SystemBootSequence, err.Error()))
		} else {

			serverLogger.Info(fmt.Sprintf("%v aggregation watcher closed..", utils.SystemBootSequence))
		}

		err = configWatcher.Close()

		if err != nil {

			serverLogger.Error(fmt.Sprintf("unable to close %v config watcher , error %v", utils.SystemBootSequence, err.Error()))
		} else {

			serverLogger.Info(fmt.Sprintf("%v config watcher closed..", utils.SystemBootSequence))
		}
	}()

	err = aggregationWatcher.Add(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.HorizontalAggregations)

	if err != nil {

		serverLogger.Error(fmt.Sprintf("failed to initialize %v aggregation watcher, reason: %v", utils.SystemBootSequence, err.Error()))
	}

	err = configWatcher.Add(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	if err != nil {

		serverLogger.Error(fmt.Sprintf("failed to initialize %v config watcher, reason: %v", utils.SystemBootSequence, err.Error()))
	}

	for {

		select {

		case event := <-aggregationWatcher.Events:

			if (event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Rename == fsnotify.Rename || event.Op&fsnotify.Chmod == fsnotify.Chmod) && !strings.Contains(event.Name, utils.InstanceSeparator) {

				watch(event)

				if event.Op&fsnotify.Rename == fsnotify.Rename || event.Op&fsnotify.Chmod == fsnotify.Chmod {

					err = aggregationWatcher.Add(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.HorizontalAggregations)

					if err != nil {

						serverLogger.Error(fmt.Sprintf("failed to initialize %v aggregation watcher after receiving remove watch event, reason: %v", utils.SystemBootSequence, err.Error()))

						return
					}
				}

			}

		case configEvent := <-configWatcher.Events:

			if (configEvent.Op&fsnotify.Write == fsnotify.Write || configEvent.Op&fsnotify.Rename == fsnotify.Rename || configEvent.Op&fsnotify.Chmod == fsnotify.Chmod) && !strings.Contains(configEvent.Name, utils.InstanceSeparator) {

				watch(configEvent)

				if configEvent.Op&fsnotify.Rename == fsnotify.Rename || configEvent.Op&fsnotify.Chmod == fsnotify.Chmod {

					err = configWatcher.Add(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

					if err != nil {

						serverLogger.Error(fmt.Sprintf("failed to initialize %v config watcher after receiving remove watch event, reason: %v", utils.SystemBootSequence, err.Error()))

						return
					}
				}

			}

		case err = <-aggregationWatcher.Errors:

			serverLogger.Error(fmt.Sprintf("err %v occurred in %v aggregation watcher", err.Error(), utils.SystemBootSequence))

		case err = <-configWatcher.Errors:

			serverLogger.Error(fmt.Sprintf("err %v occurred in %v config watcher", err.Error(), utils.SystemBootSequence))

		case <-watcherShutdownNotifications:

			serverLogger.Info(fmt.Sprintf("shutting down %v watcher...", utils.SystemBootSequence))

			return

		}
	}
}

func watch(event fsnotify.Event) {

	tokens := strings.Split(event.Name, utils.PathSeparator)

	fileName := tokens[len(tokens)-1]

	bytes, err := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + fileName)

	if err == nil && len(bytes) > 0 {

		if strings.EqualFold(fileName, utils.MotadataDatastoreConfigFile) {

			serverLogger.Info(fmt.Sprintf("%v config update request received for file %v", utils.SystemBootSequence, fileName))

			serviceEnabled := utils.DatastoreMotaOpsServiceEnabled

			_, updatedConfigs := utils.ReloadConfigs(bytes)

			if len(updatedConfigs) > 0 && updatedConfigs.Contains(utils.DatastoreMotaOpsServiceStatus) && utils.SystemBootSequence == utils.Datastore {

				if serviceEnabled {

					if !utils.DatastoreMotaOpsServiceEnabled {

						motaOps.Shutdown()
					}
				} else {

					if utils.DatastoreMotaOpsServiceEnabled {

						motaOps = motaops.NewMotaOps()

						motaOps.Start()
					}
				}

				if updatedConfigs.Contains(utils.MappingStoreCacheConfigs) {

					mappingStores := updatedConfigs.GetMapValue(utils.MappingStoreCacheConfigs)

					for store := range mappingStores {

						if utils.GetMappingCacheRecords(store) != mappingStores.GetIntValue(store) {

							datastore.UpdateStoreCacheMapping(store, mappingStores.GetIntValue(store))
						}

					}
				}
			}
		} else if strings.EqualFold(fileName, utils.HorizontalAggregations) && utils.SystemBootSequence == utils.Broker {

			serverLogger.Info(fmt.Sprintf("%v config update request received for file %v", utils.SystemBootSequence, fileName))

			for _, dataWriter := range dataWriters {

				dataWriter.HorizontalAggregationChangeNotifications <- bytes
			}
		}

	}

}

func initRequestEngine() {

	var err error

	zContext, err = zmq.NewContext()

	if err != nil {

		panic(err)
	}

	time.Sleep(time.Second)

	err = zContext.SetIoThreads(utils.GetIOWorkerThreads())

	if err != nil {

		panic(err)
	}

	time.Sleep(time.Second)

	publisher = GetPublisher()

	subscriber = GetSubscriber()

	if utils.SystemBootSequence == utils.Datastore {

		InitRequestRouters()

	}

	publisher.Start()

	subscriber.Start() //Starting the subscriber after every listener has started so that no one is blocked

}

func InitRequestRouters() {

	requestRouters = make([]*RequestRouter, 2)

	requestRouters[RouterTypeReader] = NewRequestRouter(RouterTypeReader, executorAllocations)

	utils.Responses = requestRouters[RouterTypeReader].Responses

	utils.Requests = requestRouters[RouterTypeReader].Requests

	utils.DrillDownQueryRequests = requestRouters[RouterTypeReader].drillDownRequests

	requestRouters[RouterTypeReader].Start()

	utils.PublisherResponses = make(chan []byte, utils.QueryExecutors)

	utils.PublisherNotifications = make(chan utils.MotadataMap, utils.GetPublisherEventChannelSize())

	requestRouters[RouterTypeNotification] = NewRequestRouter(RouterTypeNotification, nil)

	requestRouters[RouterTypeNotification].Start()
}

func initIndexEngine() {

	utils.VerticalFormatSyncNotifications = make(chan utils.WriterSyncEvent, 1_000_00)

	utils.HorizontalFormatSyncNotifications = make(chan utils.WriterSyncEvent, 1_000_00)

	utils.IndexWriterRequests = make([]chan utils.IndexEvent, utils.IndexWriters)

	utils.HorizontalFormatHealthSyncNotifications = make(chan utils.WriterSyncEvent, 1_000_00)

	verticalWriters = make([]*writer.VerticalWriter, utils.VerticalWriters)

	staticMetricWriters = make([]*writer.StaticMetricWriter, utils.StaticMetricWriters)

	horizontalWriters = make([]*writer.HorizontalWriter, utils.HorizontalWriters)

	indexWriters = make([]*writer.IndexWriter, utils.IndexWriters)

	healthMetricWriters = make([]*writer.HorizontalWriter, utils.HealthMetricWriters)

	for id := 0; id < len(indexWriters); id++ {

		indexWriters[id] = writer.NewIndexWriter(id)

		utils.IndexWriterRequests[id] = indexWriters[id].Events

		indexWriters[id].Start()
	}

	for id := 0; id < len(staticMetricWriters); id++ {

		staticMetricWriters[id] = writer.NewStaticMetricWriter(id)

		staticMetricWriters[id].Start()
	}

	for id := 0; id < len(verticalWriters); id++ {

		verticalWriters[id] = writer.NewVerticalWriter(id, staticMetricWriters)

		verticalWriters[id].Start()

	}

	for id := 0; id < len(horizontalWriters); id++ {

		horizontalWriters[id] = writer.NewHorizontalWriter(id)

		horizontalWriters[id].Start()

	}

	for id := 0; id < len(healthMetricWriters); id++ {

		healthMetricWriters[id] = writer.NewHorizontalWriter(id)

		healthMetricWriters[id].Start()
	}

}

func create(dir string) {

	_, err := os.Stat(dir)

	if os.IsNotExist(err) {

		err = os.MkdirAll(dir, 0755)

		if err != nil {

			serverLogger.Error(fmt.Sprintf("failed to create %v dir for %v, reason: %v", dir, utils.SystemBootSequence, err.Error()))
		}
	}

}

func InitJobEngine() {

	utils.StoreSyncJobAddNotifications = make(chan string, 1_00_000)

	utils.StoreSyncDirtyNotifications = make(chan string, 1_00_000)

	utils.StoreSyncJobRemoveNotifications = make(chan string, 1_00_000)

	utils.MetricAggregationRequests = make([]chan utils.MetricAggregationRequest, utils.MetricAggregators)

	storeCleanUpJobs = make([]*job.StoreCleanUpJob, utils.StoreCleanUpJobs)

	mappingCleanupJobs = make([]*job.MappingCleanupJob, utils.MappingCleanupJobs)

	aggregationJobs = make([]*job.AggregationJob, utils.AggregationJobs)

	metricAggregators = make([]*writer.MetricAggregator, utils.MetricAggregators)

	eventAggregators = make([]*writer.EventAggregator, utils.EventAggregators)

	storeBackupJob = job.NewStoreBackupJob()

	storeBackupJob.Start()

	syncJobManager = job.NewStoreSyncJobManager()

	syncJobManager.Start()

	for id := 0; id < utils.StoreCleanUpJobs; id++ {

		storeCleanUpJobs[id] = job.NewStoreCleanUpJob(id)

		storeCleanUpJobs[id].Start()

	}

	for id := 0; id < utils.MappingCleanupJobs; id++ {

		mappingCleanupJobs[id] = job.NewMappingCleanUpJob(id)

		mappingCleanupJobs[id].Start()
	}

	indexJob = job.NewIndexJob()

	indexJob.Start()

	if utils.Aggregation {

		utils.AggregationJobQueryAcks = make([]chan int, utils.AggregationJobs) //for probing

		utils.AggregationJobWriteNotifications = make([]chan utils.MotadataMap, utils.AggregationJobs) //for probing

		utils.EventAggregationSyncNotifications = make(chan utils.EventAggregationSyncEvent, 1_000_00)

		for id := 0; id < len(aggregationJobs); id++ {

			aggregationJobs[id] = job.NewAggregationJob(id, executors)

			utils.AggregationJobQueryAcks[id] = aggregationJobs[id].JobQueryAcks

			utils.AggregationJobWriteNotifications[id] = aggregationJobs[id].Notifications

			aggregationJobs[id].Start()

		}

		for id := 0; id < len(metricAggregators); id++ {

			metricAggregators[id] = writer.NewMetricAggregator(id)

			utils.MetricAggregationRequests[id] = metricAggregators[id].Requests

			metricAggregators[id].Start()
		}

		for id := 0; id < len(eventAggregators); id++ {

			eventAggregators[id] = writer.NewEventAggregator(id)

			eventAggregators[id].Start()
		}

		manager = job.NewManager()

		manager.Start()
	}

	retentionJob = job.NewRetentionJob()

	retentionJob.Start()

	healthCheckupJob = job.NewHealthCheckupJob()

	healthCheckupJob.Start()

}

func InitQueryEngine() {

	diskIOWorkers = make([]*storage.DiskIOWorker, utils.DiskIOWorkers)

	for id := range diskIOWorkers {

		diskIOWorkers[id] = storage.NewIOWorker(id)

		diskIOWorkers[id].Start()
	}

	workers = make([]*query.Worker, utils.QueryWorkers)

	executors = make([]*query.Executor, utils.QueryExecutors)

	executorAllocations = make(map[int]query.QueryEngineType)

	workerAllocations = make(map[int]query.QueryEngineType)

	position := 0

	for id := 0; id < utils.MetricQueryWorkers; id++ {

		workers[id] = query.NewWorker(id, query.Metric)

		workers[id].Start(executors)

		workerAllocations[id] = query.Metric

	}

	position += utils.MetricQueryWorkers

	for id := position; id < position+utils.FlowQueryWorkers; id++ {

		workers[id] = query.NewWorker(id, query.Flow)

		workers[id].Start(executors)

		workerAllocations[id] = query.Flow

	}

	position += utils.FlowQueryWorkers

	for id := position; id < position+utils.LogQueryWorkers; id++ {

		workers[id] = query.NewWorker(id, query.Log)

		workers[id].Start(executors)

		workerAllocations[id] = query.Log

	}

	position += utils.LogQueryWorkers

	for id := position; id < position+utils.DrillDownQueryWorkers; id++ {

		workers[id] = query.NewWorker(id, query.DrillDown)

		workers[id].Start(executors)

		workerAllocations[id] = query.DrillDown

	}

	position += utils.DrillDownQueryWorkers

	if utils.AIOpsEngineQueryExecutors > 0 {

		for id := position; id < position+utils.AIOpsEngineQueryWorkers; id++ {

			workers[id] = query.NewWorker(id, query.AIOps)

			workers[id].Start(executors)

			workerAllocations[id] = query.AIOps

		}

		position += utils.AIOpsEngineQueryWorkers
	}

	availableWorkers = make([]atomic.Bool, utils.QueryWorkers)

	position = 0

	for id := 0; id < position+utils.MetricQueryExecutors; id++ {

		executors[id] = query.NewExecutor(id, query.Metric)

		executors[id].Start(workers, availableWorkers)

		executorAllocations[id] = query.Metric
	}

	position += utils.MetricQueryExecutors

	for id := position; id < position+utils.FlowQueryExecutors; id++ {

		executors[id] = query.NewExecutor(id, query.Flow)

		executors[id].Start(workers, availableWorkers)

		executorAllocations[id] = query.Flow
	}

	position += utils.FlowQueryExecutors

	for id := position; id < position+utils.LogQueryExecutors; id++ {

		executors[id] = query.NewExecutor(id, query.Log)

		executors[id].Start(workers, availableWorkers)

		executorAllocations[id] = query.Log
	}

	position += utils.LogQueryExecutors

	for id := position; id < position+utils.DrillDownQueryExecutors; id++ {

		executors[id] = query.NewExecutor(id, query.DrillDown)

		executors[id].Start(workers, availableWorkers)

		executorAllocations[id] = query.DrillDown
	}

	position += utils.DrillDownQueryExecutors

	if utils.AIOpsEngineQueryExecutors > 0 {

		for id := position; id < position+utils.AIOpsEngineQueryExecutors; id++ {

			executors[id] = query.NewExecutor(id, query.AIOps)

			executors[id].Start(workers, availableWorkers)

			executorAllocations[id] = query.AIOps
		}

		position += utils.AIOpsEngineQueryExecutors
	}
}

func register() error {

	if utils.InstallationMode == utils.Empty {

		utils.InstallationMode = "STANDALONE"
	}

	utils.ResetRegistrationId()

	var version string

	bytes, _ := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.DatastoreVersion)

	if bytes != nil {

		version = string(bytes2.Split(bytes, []byte(utils.NewLineSeparator))[0])
	}

	utils.Publish(utils.MotadataMap{

		utils.EventType:                            utils.Registration,
		utils.RemoteEventProcessorType:             utils.Datastore,
		utils.RemoteEventProcessorVersion:          version,
		utils.RemoteEventProcessorIP:               utils.GetLocalIPAddress(),
		utils.RemoteEventProcessorHost:             utils.GetLocalHostName(),
		utils.RemoteEventProcessorUUID:             utils.RegistrationId,
		utils.RemoteEventProcessorInstallationMode: utils.InstallationMode,
	})

	serverLogger.Info(fmt.Sprintf("datastore registered... ip : %s , host : %s id : %s version : %s ", utils.GetLocalIPAddress(), utils.GetLocalHostName(), utils.RegistrationId, version))

	go func() {

		heartBeatTimer := time.NewTicker(time.Second * 10)

		heartBeatContext := make(utils.MotadataMap)

		heartBeatContext[utils.EventType] = utils.RemoteEventProcessorHeartbeat

		heartBeatContext[utils.RemoteEventProcessorType] = utils.Datastore

		heartBeatContext[utils.RemoteEventProcessorUUID] = utils.RegistrationId

		heartBeatContext[utils.RemoteEventProcessorInstallationMode] = utils.InstallationMode

		heartBeatContext[utils.Duration] = time.Now().Unix()

		for {

			if utils.GlobalShutdown {

				heartBeatTimer.Stop()

				return
			}

			select {

			case <-heartBeatTimer.C:

				if utils.TraceEnabled() {

					serverLogger.Trace(fmt.Sprintf("heartbeat send to the motadata context -%v", heartBeatContext))
				}

				utils.Publish(heartBeatContext)

			}

		}

	}()

	return nil
}

func HandleShutdownEvent(serverAcks chan bool) {

	for {

		select {

		case notification := <-utils.ShutdownNotifications:

			utils.GlobalShutdown = true

			go func() {

				<-time.After(time.Second * 300)

				shutdown(serverAcks)
			}()

			tokens := strings.SplitN(notification, utils.GroupSeparator, 3)

			serverLogger.Fatal(fmt.Sprintf("Shutdown message received from %v for %v, message: %v", tokens[0], utils.SystemBootSequence, tokens[1]))

			serverLogger.Fatal(fmt.Sprintf("\n!!!STACK TRACE!!!\n%v", tokens[2]))

			if utils.SystemBootSequence == utils.Datastore {

				processes, _ := process.Processes()

				for _, process := range processes {

					if processName, err := process.Name(); err == nil && (strings.Contains(processName, utils.AIOpsEngine)) || (strings.Contains(processName, utils.AIOpsEngine)) {

						process, err := os.FindProcess(int(process.Pid))

						if err == nil {

							_ = process.Kill() // This works on Windows
						}

					}

				}

				shutdownInsertionEngine()

				ShutdownJobEngine()

				ShutdownRequestRouters()

				ShutdownQueryEngine()

				if utils.DatastoreMotaOpsServiceEnabled && motaOps != nil {

					motaOps.ShutdownNotifications <- true

				}

			} else {

				for id := range dataWriters {

					dataWriters[id].ShutdownNotifications <- true
				}

				for id := range dataAggregators {

					dataAggregators[id].ShutdownNotifications <- true
				}

				utils.BrokerEngineShutdownMutex.Wait()

				if broker != nil {

					broker.ShutdownNotifications <- true
				}

			}

			if watcherShutdownNotifications != nil {

				watcherShutdownNotifications <- true
			}

			if connectionWatcherShutdownNotifications != nil {

				connectionWatcherShutdownNotifications <- true
			}

			shutdown(serverAcks)
		}
	}
}

func ShutdownRequestRouters() {

	for id := range requestRouters {

		requestRouters[id].ShutdownNotifications <- true
	}
}

func shutdownInsertionEngine() {

	for id := range dataReaders {

		dataReaders[id].ShutdownNotifications <- true
	}

	for id := range verticalWriters {

		verticalWriters[id].ShutdownNotifications <- true
	}

	for id := range staticMetricWriters {

		staticMetricWriters[id].ShutdownNotifications <- true

	}

	for id := range horizontalWriters {

		horizontalWriters[id].ShutdownNotifications <- true
	}

	for id := range indexWriters {

		indexWriters[id].ShutdownNotifications <- true
	}

	for id := range healthMetricWriters {

		healthMetricWriters[id].ShutdownNotifications <- true
	}

	utils.WriterEngineShutdownMutex.Wait()

	if utils.Aggregation {

		for id := range metricAggregators {

			metricAggregators[id].ShutdownNotifications <- true
		}

		for id := range eventAggregators {

			eventAggregators[id].ShutdownNotifications <- true
		}

		utils.AggregatorEngineShutdownMutex.Wait()
	}
}

func ShutdownQueryEngine() {

	for id := range executors {

		executors[id].ShutdownNotifications <- true
	}

	for id := range workers {

		workers[id].ShutdownNotifications <- true
	}

	utils.QueryEngineShutdownMutex.Wait()

	for id := range diskIOWorkers {

		diskIOWorkers[id].ShutdownNotifications <- true
	}

}

func ShutdownJobEngine() {

	if syncJobManager != nil {

		syncJobManager.ShutdownNotifications <- true

	}

	if utils.Aggregation {

		for id := range aggregationJobs {

			aggregationJobs[id].ShutdownNotifications <- true
		}

		if manager != nil {

			manager.ShutdownNotifications <- true
		}
	}

	if retentionJob != nil {

		retentionJob.ShutdownNotifications <- true
	}

	if healthCheckupJob != nil {

		healthCheckupJob.ShutdownNotifications <- true
	}

	for id := range storeCleanUpJobs {

		storeCleanUpJobs[id].ShutdownNotifications <- true
	}

	for id := range mappingCleanupJobs {

		mappingCleanupJobs[id].ShutdownNotifications <- true
	}

	if storeBackupJob != nil {

		storeBackupJob.ShutdownNotifications <- true
	}

	if indexJob != nil {

		indexJob.ShutdownNotifications <- true
	}

	utils.JobEngineShutdownMutex.Wait()
}

func shutdown(serverAcks chan bool) {

	if utils.SystemBootSequence == utils.Datastore {

		datastore.Close()
	}

	if publisher != nil {

		publisher.ShutDown()

	}

	if subscriber != nil {

		subscriber.ShutDown()
	}

	if zContext != nil {

		_ = zContext.Term()

		zContext = nil
	}

	utils.CloseGCTracer()

	serverAcks <- true
}

func startConnectionWatcher() {

	connectionWatcherShutdownNotifications = make(chan bool, 5)

	ticker := time.NewTicker(utils.SocketConnectionWatcherTime)

outer:
	for {

		select {

		case <-utils.KeepAliveNotifications:

			SocketIdleTime = 0

			if utils.TraceEnabled() {

				serverLogger.Trace(fmt.Sprintf("heartbeat event arrived for %s", utils.SystemBootSequence))
			}

		case <-ticker.C:

			SocketIdleTime += utils.SocketConnectionWatcherTime

			if SocketIdleTime > utils.MaxSocketIdleConnectionTime {

				serverLogger.Info(fmt.Sprintf("socket connection idle time exceeds threshold for %s", utils.SystemBootSequence))

				if publisher != nil {

					if utils.TraceEnabled() {

						serverLogger.Trace(fmt.Sprintf("shutting down publisher for %s", utils.SystemBootSequence))
					}

					publisher.Disconnect()
				}

				if subscriber != nil {

					if utils.TraceEnabled() {

						serverLogger.Trace(fmt.Sprintf("shutting down subscriber for %s", utils.SystemBootSequence))
					}

					subscriber.ShutDown()

					subscriber = nil

				}

				time.Sleep(time.Second * 2)

				var ports []string

				if utils.SystemBootSequence == utils.Datastore {

					ports = []string{utils.GetDatastoreNotificationPort(), utils.GetDatastoreReaderPort()}

				} else if utils.SystemBootSequence == utils.Broker {

					ports = []string{utils.GetDatastoreNotificationPort(), utils.GetDatastoreAvailabilityWriterPort(), utils.GetDatastoreMetricWriterPort(), utils.GetDatastoreEventWriterPort()}

				}

				for port, open := range utils.CheckPorts(utils.GetHost(), ports, 3) {

					if !open {

						serverLogger.Error(fmt.Sprintf("failed to connect with address : %s:%s for %s, terminating reconnection of socket", utils.GetHost(), port, utils.SystemBootSequence))

						SocketIdleTime = 0

						continue outer
					}

					serverLogger.Info(fmt.Sprintf("adress : %s:%s is up, boot sequence : %s,", utils.GetHost(), port, utils.SystemBootSequence))
				}

				serverLogger.Info(fmt.Sprintf("starting socket reconnection %s", utils.SystemBootSequence))

				serverLogger.Info(fmt.Sprintf("starting new publisher and subscriber for %s", utils.SystemBootSequence))

				subscriber = GetSubscriber()

				publisher.Reconnect()

				subscriber.Start()

				SocketIdleTime = 0
			}

		case <-connectionWatcherShutdownNotifications:

			serverLogger.Info(fmt.Sprintf("shutting down connection watcher for %s", utils.SystemBootSequence))

			return
		}

	}

}
