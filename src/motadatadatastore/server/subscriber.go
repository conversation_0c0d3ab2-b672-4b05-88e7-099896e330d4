/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-07             Vedant D. Dokania     	MOTADATA-4698 Introduced the new ports for availability , metric and event
* 2025-06-04             A<PERSON>l Shah            MOTADATA-5780 Introduced ErrorSocketTimeout error
 */

package server

import (
	bytes2 "bytes"
	"fmt"
	zmq "github.com/pebbe/zmq4"
	"motadatadatastore/codec"
	"motadatadatastore/datastore/query"
	"motadatadatastore/utils"
	"strings"
	"time"
)

var subscriberLogger = utils.NewLogger("Subscriber", "server")

type Subscriber struct {
	readerSocket, notificationSocket, availabilityWriterSocket, metricWriterSocket, eventWriterSocket, AIOpsEngineQuerySocket *zmq.Socket

	Close bool
}

const (
	subscriberTopic = "subscribed topic of socket type %v : %s for %s"

	contextTerminated = "Context was terminated"

	SocketTypeAvailability = "availability"

	SocketTypeMetric = "metric"

	SocketTypeEvent = "event"

	// SocketTypeNotification used to get various notification in the router like (widgetCreate , backUpProfile ...)
	SocketTypeNotification = "notification"

	SocketTypeReader = "reader"
)

func GetSubscriber() *Subscriber {

	if utils.RegistrationId == utils.Empty && utils.EnvironmentType != utils.DatastoreTestEnvironment {

		subscriberLogger.Error(fmt.Sprintf("failed to start subscriber for %v, reason : registration id is empty", utils.SystemBootSequence))

		return nil

	}

	var err error

	if subscriber == nil {

		subscriber = &Subscriber{
			Close: false,
		}

		if utils.SystemBootSequence == utils.Datastore {

			if subscriber.notificationSocket, err = subscriber.getSocket(DatastoreOperationTopic, SocketTypeNotification); err != nil {

				return nil
			}

			if subscriber.readerSocket, err = subscriber.getSocket(DatastoreQueryTopic, SocketTypeReader); err != nil {

				return nil
			}

			if utils.AIOpsEngineQueryExecutors > 0 {

				subscriber.AIOpsEngineQuerySocket, _ = zContext.NewSocket(zmq.PULL)

				_ = subscriber.AIOpsEngineQuerySocket.SetRcvtimeo(utils.SubscriberRCVTimeout)
			}

		}

		if utils.SystemBootSequence == utils.Broker {

			// for receiving heartbeat
			if subscriber.notificationSocket, err = subscriber.getSocket(DatastoreBrokerOperationTopic, SocketTypeNotification); err != nil {

				return nil
			}

			if subscriber.availabilityWriterSocket, err = subscriber.getSocket(DatastoreBrokerOperationTopic, SocketTypeAvailability); err != nil {

				return nil
			}

			if subscriber.metricWriterSocket, err = subscriber.getSocket(DatastoreBrokerOperationTopic, SocketTypeMetric); err != nil {

				return nil
			}

			if subscriber.eventWriterSocket, err = subscriber.getSocket(DatastoreBrokerOperationTopic, SocketTypeEvent); err != nil {

				return nil
			}

		}

	}

	return subscriber
}

func (subscriber *Subscriber) Start() {

	if utils.SystemBootSequence == utils.Broker {

		if err := connect(subscriber.notificationSocket, utils.GetDatastoreNotificationPort(), SocketTypeNotification); err != nil {

			return
		}

		if err := connect(subscriber.availabilityWriterSocket, utils.GetDatastoreAvailabilityWriterPort(), SocketTypeAvailability); err != nil {

			return
		}

		if err := connect(subscriber.metricWriterSocket, utils.GetDatastoreMetricWriterPort(), SocketTypeMetric); err != nil {

			return
		}

		if err := connect(subscriber.eventWriterSocket, utils.GetDatastoreEventWriterPort(), SocketTypeEvent); err != nil {

			return
		}

		go subscriber.receive(subscriber.notificationSocket, broker.Requests, SocketTypeNotification)

		go subscriber.receive(subscriber.availabilityWriterSocket, broker.Requests, SocketTypeAvailability)

		go subscriber.receive(subscriber.metricWriterSocket, broker.Requests, SocketTypeMetric)

		go subscriber.receive(subscriber.eventWriterSocket, broker.Requests, SocketTypeEvent)

	}

	if utils.SystemBootSequence == utils.Datastore {

		if err := connect(subscriber.notificationSocket, utils.GetDatastoreNotificationPort(), SocketTypeNotification); err != nil {

			return
		}

		if err := connect(subscriber.readerSocket, utils.GetDatastoreReaderPort(), SocketTypeReader); err != nil {

			return
		}

		go subscriber.receive(subscriber.notificationSocket, requestRouters[RouterTypeNotification].Requests, SocketTypeNotification)

		go subscriber.receive(subscriber.readerSocket, requestRouters[RouterTypeReader].Requests, SocketTypeReader)

		if utils.AIOpsEngineQueryExecutors > 0 {

			err := subscriber.AIOpsEngineQuerySocket.Bind("tcp://127.0.0.1:" + utils.GetAIOpsEngineSubscriberPort())

			if err != nil {

				subscriberLogger.Info(fmt.Sprintf("failed to start AI-Engine subscriber, reason :%v", err.Error()))

				return
			}

			subscriberLogger.Info(fmt.Sprintf("started AI-Engine event subscriber on %v...", utils.GetHost()+":"+utils.GetAIOpsEngineSubscriberPort()))

			go func() { // aiops query socket zmq routine

				defer func() {

					if err := recover(); err != nil {

						subscriberLogger.Fatal(fmt.Sprintf("error %v occurred while receiving event on AIOps query response subscriber", err))

						_ = subscriber.AIOpsEngineQuerySocket.Close()

					}

					subscriberLogger.Info("AIOps query response subscriber exiting...")

				}()

				for {

					if !utils.GlobalShutdown && !subscriber.Close {

						bytes, err := subscriber.AIOpsEngineQuerySocket.RecvBytes(0)

						if IsSocketTimeoutError(err) {

							continue
						}

						if err == nil {

							if !utils.GlobalShutdown {

								if bytes2.Equal(bytes[:4], query.HeartBeatBytes) {

									//process heart beat
								} else {

									if len(bytes) > 6 {

										executor := codec.ReadINTValue(bytes[4:6])

										if len(executors) > executor {

											executors[executor].AIOpsEngineResponses <- bytes[6:]
										}

									} else {

										subscriberLogger.Fatal("failed to send AIOps query response, reason: invalid response")

									}
								}

							} else {

								_ = subscriber.AIOpsEngineQuerySocket.Close()

								subscriberLogger.Info("AIOps query response socket closed, reason: shutdown event received")

								return
							}
						} else {

							if strings.Contains(err.Error(), contextTerminated) {

								_ = subscriber.AIOpsEngineQuerySocket.Close()

								subscriberLogger.Info("AIOps query response socket closed, reason: zmq context was terminated")

								return
							}

							subscriberLogger.Fatal(fmt.Sprintf("failed to subscribe AIOps query response, reason: err %v occurred", err.Error()))

						}

					} else {

						err := subscriber.AIOpsEngineQuerySocket.Close()

						if err != nil {

							subscriberLogger.Error(fmt.Sprintf("error occurred while closing AIOps query response socket reason : %s", err.Error()))
						}

						if subscriber.Close {

							subscriberLogger.Info("AIOps query response socket closed, reason: subscriber close event received")
						} else {

							subscriberLogger.Info("AIOps query response socket closed, reason: shutdown event received")

						}

						return
					}

				}
			}()
		}

	}
}

func (subscriber *Subscriber) getSocket(topic, socketType string) (socket *zmq.Socket, err error) {

	socket, _ = zContext.NewSocket(zmq.SUB)

	_ = socket.SetLinger(0)

	_ = socket.SetRcvtimeo(utils.SubscriberRCVTimeout)

	err = socket.SetSubscribe(utils.RegistrationId + utils.DotSeparator + topic)

	if err != nil {

		subscriberLogger.Info(fmt.Sprintf(utils.ErrorSubscriberStartFailed, utils.SystemBootSequence, socketType, err.Error()))

		return socket, err
	}

	subscriberLogger.Info(fmt.Sprintf(subscriberTopic, socketType, utils.SystemBootSequence, utils.RegistrationId+utils.DotSeparator+topic))

	return socket, err

}

func (subscriber *Subscriber) receive(socket *zmq.Socket, requests chan []byte, socketType string) {

	defer func() {

		if err := recover(); err != nil {

			subscriberLogger.Error(fmt.Sprintf("error %v occurred in socketType %v subscriber...", err, socketType))

			_ = socket.Close()
		}

		subscriberLogger.Info(fmt.Sprintf("%v socket exiting", socketType))
	}()

	for {

		if !utils.GlobalShutdown && !subscriber.Close {

			_, err := socket.RecvBytes(0)

			if IsSocketTimeoutError(err) {

				continue
			}

			bytes, err := socket.RecvBytes(0)

			if IsSocketTimeoutError(err) {

				continue
			}

			if err == nil {

				if !utils.GlobalShutdown {

					if len(bytes) > 0 {

						if utils.TraceEnabled() {

							subscriberLogger.Trace(fmt.Sprintf("sending %v socketType request to router", socketType))
						}

						requests <- bytes

					} else {

						subscriberLogger.Fatal(fmt.Sprintf("%v socket Type request sending failed, reason : request is empty", socketType))
					}

				} else {

					_ = socket.Close()

					subscriberLogger.Info(fmt.Sprintf("%v socket closed, reason : shutdown event received", socketType))

					return
				}
			} else {

				if strings.Contains(err.Error(), contextTerminated) {

					_ = socket.Close()

					subscriberLogger.Info(fmt.Sprintf("%v socket closed, reason: zmq context was terminated", socketType))

					return
				}

				subscriberLogger.Fatal(fmt.Sprintf("failed to subscribe %v socket type request request,reason : error %v occurred", socketType, err))

			}

		} else {

			err := socket.Close()

			if err != nil {

				subscriberLogger.Error(fmt.Sprintf("error occurred while closing %v socket reason : %s", socketType, err.Error()))
			}

			if subscriber.Close {

				subscriberLogger.Info(fmt.Sprintf("%v socket closed, reason : subscriber close event received", socketType))

			} else {

				subscriberLogger.Info(fmt.Sprintf("%v socket closed, reason : shutdown event received", socketType))

			}

			return
		}

	}

}

func (subscriber *Subscriber) ShutDown() {

	if !subscriber.Close {

		subscriber.Close = true

		time.Sleep(utils.SubscriberRCVTimeout + time.Second*5)
	}

}

func connect(socket *zmq.Socket, port, socketType string) (err error) {

	err = socket.Connect("tcp://" + utils.GetHost() + ":" + port)

	if err != nil {

		subscriberLogger.Info(fmt.Sprintf(utils.ErrorSubscriberStartFailed, utils.SystemBootSequence, socketType, err.Error()))

		return
	}

	return err
}

func IsSocketTimeoutError(err error) bool {

	if err != nil {

		return strings.Contains(err.Error(), utils.ErrorSocketTimeOut)

	}

	return false
}
