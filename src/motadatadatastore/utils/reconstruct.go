/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package utils

import (
	"encoding/json"
	"errors"
	"fmt"
	cp "github.com/otiai10/copy"
	"os"
	"strings"
)

var (
	reconstructLogger = NewLogger("Reconstruct", "utils")

	tempBkpDirectory = CurrentDir + PathSeparator + Temp + "backup-utility"

	// here user needs to move all the stores for the backup
	reconstructDirectory = "REPORT-DB-BACKUP-LOCAL-RECONSTRUCT"

	metadata = make(MotadataMap)
)

/* Process of RECONSTRUCT :-

   1. Move all the needed stores to the "REPORT-DB-BACKUP-LOCAL-RECONSTRUCT" name folder
   2. Start running the utility and provide the option for the profile which needs to be restored

*/

func Reconstruct(bkpProfile string) {

	if bkpProfile == Empty {

		reconstructLogger.Error("unknown backup profile provided in the input")

		return
	}

	/* get following things for RECONSTRUCT  from backend :-
	1. get profile name

	*/

	// remove temp backup directory if present
	_ = os.RemoveAll(tempBkpDirectory)

	err := os.MkdirAll(tempBkpDirectory, 0777)

	if err != nil {

		reconstructLogger.Error(fmt.Sprintf("unable to make the backup directory reason %v", err.Error()))
	}

	backupDirectory, err := os.ReadDir(CurrentDir + PathSeparator + reconstructDirectory)

	if err != nil {

		reconstructLogger.Error(fmt.Sprintf("failed to read the reportDB backup directory %v", err.Error()))

		return
	}

	reconstructLogger.Info(fmt.Sprintf("total stores found in the backup directory %v", len(backupDirectory)))

	/*Scenario :- Before version 1.43 we don't have individual backup profile for different stores. ALl the stores are dumped in the same folder.
	So for backward compatibility we need to iterate over that stores also and add that stores also in the temp bkp directory */
	for _, entry := range backupDirectory {

		//  Skip config Zip directory
		if entry.Name() == "config"+ZipExtension {

			continue
		}

		store, _ := strings.CutSuffix(entry.Name(), ZipExtension)

		err = Unzip(CurrentDir+PathSeparator+reconstructDirectory+PathSeparator+entry.Name(), tempBkpDirectory+PathSeparator+store)

		if err != nil {

			reconstructLogger.Error(fmt.Sprintf("failed to unzip the store %v reason : %v", store, err.Error()))
		}

	}

	//read temp backup directory

	bkpDirectory, err := os.ReadDir(tempBkpDirectory)

	if err != nil {

		reconstructLogger.Error(fmt.Sprintf("failed to read the temp backup directory reason %v", err.Error()))

		return

	}

	for _, entry := range bkpDirectory {

		if entry.IsDir() {

			if _, err := os.Stat(CurrentDir + PathSeparator + DatastoreDir + PathSeparator + entry.Name()); errors.Is(err, os.ErrNotExist) && qualifyStore(entry.Name(), bkpProfile) {

				err = cp.Copy(tempBkpDirectory+PathSeparator+entry.Name(), CurrentDir+PathSeparator+DatastoreDir+PathSeparator+entry.Name())

				if err != nil {

					reconstructLogger.Error(fmt.Sprintf("failed to copy the directory from the temp directory for store %v reason : %v", entry.Name(), err.Error()))
				}

				reconstructLogger.Info(fmt.Sprintf("restoring finished for the store %v", entry.Name()))

			}

		}
	}

	reconstructLogger.Info("restoring finished")

}

// For backward compatibility need to check the store metadata
func qualifyStore(store, profile string) bool {

	if strings.HasSuffix(store, "mappings") || strings.HasSuffix(store, DummyPostingListStoreName+HyphenSeparator+"176") {

		return true
	}

	bytes, err := os.ReadFile(tempBkpDirectory + PathSeparator + store + PathSeparator + MetadataFile)

	if err != nil {

		reconstructLogger.Error(fmt.Sprintf("failed to read the metadata file for store %v , reason %v", store, err.Error()))

		return false
	}

	clear(metadata)

	_ = json.Unmarshal(bytes, &metadata)

	if strings.EqualFold(GetStoreBackupProfile(DatastoreType(metadata.GetIntValue("datastore.type"))), profile) {

		return true
	}

	return false

}
