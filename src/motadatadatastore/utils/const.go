/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-07             Vedant D. Dokania     	MOTADATA-4698 Introduced the new constants for port related config
* 2025-03-05			 A<PERSON><PERSON>			Motadata-5190 Added constants to match SonarQube Standard
* 2025-03-05             Vedant Dokania          Motadata-5451  Status Flap Max records constant
* 2025-03-21			 <PERSON><PERSON><PERSON>-5452 Added NetRoute Constant
* 2025-04-02			 <PERSON><PERSON><PERSON>-4859  Added NetRoute Status Metric Datastore Type
* 2025-04-21			 <PERSON><PERSON><PERSON>-5873  Intoduced new DatastoreBrokerDataWriterSyncTimers to configure timer according to datastore type
* 2025-05-08  			 Aashil Shah			MOTADATA-6073 Introduced ErrorLogFile to redirect err logs to file
* 2025-05-26  			 Dhaval Bera			MOTADATA-6333 Added Discard Query Threshold config
* 2025-06-24			 Dhaval Bera			Motadata-6639  Added Constant's Retention Check In Query Parsing
* 2025-06-25			 Swapnil A. Dave		MOTADATA-6555  Added Constant For Object Flush Timer Seconds Config

 */

package utils

import (
	"math"
	"os"
)

var (
	CurrentDir, _ = os.Getwd()

	MetricTimeKeySuffixBytes = []byte("^time")

	KeySeparatorByte byte = '^'

	CheckSumV1Bytes = []byte("mDb1")

	EOTBytes = []byte("EOT")

	AggregationIntervals = []int{5, 30}

	EventAggregationIntervals = []int{5, 30, 360}

	MotadataDatastoreConfigFile = "motadata-datastore.json"

	FullTextViewAggregationIntervals = []int{5, 30}

	MotadataDataConfigFile = "motadata.json"

	Datastore = "DATASTORE"

	Broker = "BROKER"

	Restore = "RESTORE"

	EventDir = CurrentDir + PathSeparator + "datastore-events"

	TempDir = EventDir + PathSeparator + "tmp"

	AggregationDir = EventDir + PathSeparator + "tmp" + HyphenSeparator + Aggregations

	DatastoreTempDir = CurrentDir + PathSeparator + "tmp-datastore"
)

// Operation type
const (
	DatastoreRead = 2

	QueryAbort = 3

	DatastoreBrokerInit = 4

	IndexUpdate = 5

	DatastoreInit = 6

	Probe = 7

	Sync = 8

	Flush = 9

	WidgetCreate = 10

	RetentionJob = 14

	BackupJob = 15

	Recover = 16

	WidgetDelete = 17

	HeartBeat = 18

	HealthStats = 19

	ModifyLogLevel = 20

	Diagnostic = 21

	Acknowledge = 22

	BackupProfile = 23
)

const (
	MaxValueBytes = 8

	Yes = "yes"

	No = "no"

	TestQueryDir = "test-queries"

	PathSeparator = string(os.PathSeparator)

	SpaceSeparator = " "

	NewLineSeparator = "\n"

	LogLevelTrace = 0

	LogLevelDebug = 1

	LogLevelInfo = 2

	BackupContext = "snapshots.ctx"

	// BackupProfileContext file name used to store the profile context for backup job
	BackupProfileContext = "backup.profile.ctx"

	LogDirectory = "datastore-logs"

	SystemLogLevel = "system.log.level"

	LogFileDateFormat = "02-January-2006"

	StoreDateFormat = "02012006"

	LogFileFormat = "02-January-2006 15"

	LogFileTimeFormat = "03:04:05.000000 PM"

	LogFile = "@@@-Motadata-Datastore ###.log"

	ErrorLogFile = "datastore-err.log"

	VerticalAggregations = "vertical-aggregations.json"

	HorizontalAggregations = "horizontal-aggregations.json"

	AggregationContexts = "aggregations.ctx"

	ColumnConfigFile = "columns.json"

	DatastoreDir = "datastore"

	ConfigDir = "config"

	MetadataFile = "metadata"

	StopWordFile = "stopwords.dat"

	DatastoreVariant = "datastore-variant"

	DatastoreVersion = "VERSION"

	Version = "version"

	KeySeparator = "^"

	HyphenSeparator = "-"

	GroupSeparator = "###"

	SpecialSeparator = "§"

	JoinSeparator = "@#$"

	CommaSeparator = ","

	DotSeparator = "."

	InstanceSeparator = "~"

	OrdinalSuffix = ".ordinal"

	AggregationSeparator = "@@@"

	PartSeparator = "#@#"

	Empty = ""

	All = "*"

	Space = " "

	EventContext = "event.context"

	OperationType = "operation.type"

	DatastoreFormat = "datastore.format"

	Aggregations = "aggregations"

	HealthStatistics = "health.stats"

	PluginId = "plugin.id"

	EventTimeStamp = "event.timestamp"

	DummyINT64Value = math.MinInt64

	DummyFLOAT64Value = -math.MaxFloat32

	NotAvailable = -1

	VerticalFormat = "0"

	HorizontalFormat = "1"

	//report datastore event related const

	DatastoreQueryResponse = "datastore.query.response " // (space included)

	DatastoreQueryAck = "datastore.query.ack " // (space included)

	DatastoreOperation = "datastore.operation " //(space included) for publishing datastore notifications

	DatastoreDevEnvironment = "dev"

	DatastoreTestEnvironment = "test"

	DatastoreBenchIntegrationEnvironment = "bench-integration" //with sync on

	DatastoreBenchUnitEnvironment = "bench-unit" //with sync off

	/*
		zmq message constants
	*/

	Interval = "interval"

	AggregationView = "aggregation.view"

	Tick = "tick"

	Plugins = "plugins"

	Plugin = "plugin"

	CompositePlugin = "composite.plugin"

	BatchSize = "batch.size"

	Buffer = "buffer"

	File = "file"

	Columns = "columns"

	Column = "column"

	True = "true"

	False = "false"

	Garbage = "garbage"

	QueryPriority = "query.priority"

	AIOpsEngine = "aiopsengine"

	BrokerEngine = "datastorebroker"

	TimestampKey = "timestamp^tick"

	ValueSuffix = "^value"

	MaxSuffix = "^max"

	DrillDown = "drill.down"

	Type = "type"

	DummyPostingListStoreName = "events"

	Add = "add"

	Remove = "remove"

	Replace = "replace"

	Merge = "merge"

	Filters = "filters"

	DataFilter = "data.filter"

	Id = "id"

	Position = "position"

	ZipExtension = ".zip"
)

type (
	Tokenizer struct {
		Tokens []string

		startIndex, Counts, index int
	}

	MetricAggregationRequest struct {
		StoreType, Column, Key, Plugin string

		Entries [][]interface{}
	}

	IndexEvent struct {
		NumericContext map[int]struct{}

		StringContext map[string]struct{}

		StoreType DatastoreType

		Plugin, Column string

		Tick int32

		LookupEvent bool
	}

	WriterSyncEvent struct {
		File string

		WriterId int
	}

	EventAggregationSyncEvent struct {
		File string

		AggregatorId int
	}
)

const ( //aggregation const

	Sum = "sum"

	Count = "count"

	Min = "min"

	Max = "max"

	Last = "last"

	AggregationJobId = "aggregation.job.id"

	Value = "value"

	MultipartIdxFile = "multipart.idx"
)

var ( //job & directory path var const

	JobDir = CurrentDir + PathSeparator + "jobs"

	AIOpsEngineDir = CurrentDir + PathSeparator + "aiops"

	BackUpDir = CurrentDir + PathSeparator + "datastore-backups"

	VerticalAggregationFuncs = []string{Count, Sum, Min, Max, Last}

	VerticalStringAggregationFuncs = []string{Last}

	HorizontalAggregationFuncs = []string{Count, Sum}
)

type Priority byte

const (
	P0 Priority = iota // ui

	P1 // aggregation

	P2 // caching

	P3 // policy

	P4 // report
)

const (
	DatastorePublisherPort = "datastore.publisher.port"

	DatastoreAvailabilityWriterPort = "datastore.availability.writer.port"

	DatastoreMetricWriterPort = "datastore.metric.writer.port"

	DatastoreEventWriterPort = "datastore.event.writer.port"

	DatastoreNotificationPort = "datastore.notification.port"

	DatastoreReaderPort = "datastore.reader.port"

	AIOpsEnginePublisherPort = "aiops.engine.publisher.port"

	AIOpsEngineSubscriberPort = "aiops.engine.subscriber.port"

	DatastoreHost = "datastore.host"

	RemoteEventProcessorUUID = "remote.event.processor.uuid"

	RemoteEventProcessorHost = "remote.event.processor.host"

	RemoteEventProcessorIP = "remote.event.processor.ip"

	RemoteEventProcessorVersion = "remote.event.processor.version"

	RemoteEventProcessorType = "remote.event.processor.type"

	RemoteEventProcessorHeartbeat = "remote.event.processor.heartbeat"

	RemoteEventProcessorInstallationMode = "remote.event.processor.installation.mode"

	EventType = "event.type"

	Registration = "registration"
)

// config hot-reloading parameters
const (
	TaskLogging = "task.logging"

	DatastoreMaxHistoricalRecords = "datastore.max.historical.records"

	DatastoreMaxStatusFlapHistoricalRecords = "datastore.max.status.flap.historical.records"

	DatastoreMaxSearchQueryRecords = "datastore.max.search.query.records"

	DatastoreMaxGridRecords = "datastore.max.grid.records"

	DatastoreMaxVerticalWriterCacheEntries = "datastore.max.vertical.writer.cache.entries"

	DatastoreMaxAggregationGroups = "datastore.max.aggregation.groups"

	DatastoreMaxWorkerEvents = "datastore.query.max.worker.events"

	DatastoreLogRetentionDays = "datastore.log.retention.days"

	DatastoreMaxCurrentDayEventFileQueueSize = "datastore.max.current.day.event.file.queue.size"

	DatastoreMaxPreviousDayEventFileQueueSize = "datastore.max.previous.day.event.file.queue.size"

	DatastoreBrokerDataWriterSyncTimers = "datastore.broker.data.writer.sync.timers"

	DatastoreBrokerDataAggregatorSyncTimerSeconds = "datastore.broker.data.aggregator.sync.timer.seconds"

	DatastoreMotaOpsServicePort = "datastore.mota.ops.service.port"

	DatastoreMotaOpsServiceStatus = "datastore.mota.ops.service.status"

	DatastoreBrokerMaxAggregationGroupings = "datastore.broker.max.aggregation.groupings"

	DatastoreMaxFlowTopNSelectionGroups = "datastore.max.flow.topn.selection.aggregation.groups"

	DatastoreMaxFlowTopNInsertionRecords = "datastore.max.flow.topn.insertion.aggregation.groups"

	SocketConnectionMaxIdleTimeSeconds = "socket.connection.max.idle.time.seconds"

	MappingStoreCacheConfigs = "mapping.store.cache.configs"

	MaxRecords = "max.records"

	DiagnosticProfileDurationSeconds = "diagnostic.profile.duration.seconds"

	DatastoreQueryCreationAgeThresholdSeconds = "datastore.query.creation.age.threshold.seconds"

	DatastoreQueryStatsLogging = "datastore.query.stats.logging"

	DatastoreObjectFlushTimerSeconds = "datastore.object.flush.timer.seconds"
)

// generic constant
const (
	DatastoreLogExecutorPoolLength = "datastore.log.executor.pool.length"
)

type DatastoreType int

// datastore types
// 0-100 reserved for Application and rest will be used for datastore purpose
const (
	Index DatastoreType = 0

	PerformanceMetric DatastoreType = 1

	ObjectStatusMetric DatastoreType = 2

	Audit DatastoreType = 6

	Notification DatastoreType = 7

	PolicyFlapHistory DatastoreType = 8

	StatusFlapHistory DatastoreType = 9

	TrapFlapHistory DatastoreType = 10

	Log DatastoreType = 11

	Flow DatastoreType = 12

	Trap DatastoreType = 13

	MetricPolicy DatastoreType = 14

	EventPolicy DatastoreType = 15

	CorrelationMetric DatastoreType = 16

	RunbookWorklog DatastoreType = 17

	PolicyResult DatastoreType = 18

	HealthMetric DatastoreType = 20

	ConfigHistory DatastoreType = 21

	Compliance DatastoreType = 22

	NetRouteMetric DatastoreType = 25

	ObjectStatusFlapMetric DatastoreType = 26

	NetRouteStatusMetric DatastoreType = 27

	None DatastoreType = 100

	StaticMetric DatastoreType = 101

	MetricAggregation DatastoreType = 104

	LogAggregation DatastoreType = 105

	FlowAggregation DatastoreType = 106

	PolicyAggregation DatastoreType = 107

	TrapAggregation DatastoreType = 108

	Mapping DatastoreType = 109

	MetricIndex DatastoreType = 110

	CorrelatedMetricIndex DatastoreType = 111

	LogIndex DatastoreType = 112

	FlowIndex DatastoreType = 113

	TrapIndex DatastoreType = 114

	NotificationIndex DatastoreType = 115

	AuditIndex DatastoreType = 116

	PolicyIndex DatastoreType = 117

	HealthMetricIndex DatastoreType = 118

	ConfigHistoryIndex DatastoreType = 119

	ComplianceIndex DatastoreType = 120

	NetRouteStatusMetricAggregation DatastoreType = 121
)

type QueryStatus byte

const (
	Failed QueryStatus = iota

	Succeeded

	Aborted
)

const (
	TempPatch = "temp_patch@"

	Patch = "patch@"

	Temp = "temp-"
)

// default columns
const (
	SearchableColumns = "searchable.columns"

	BlobColumns = "blob.columns"

	GarbageColumns = "garbage.columns"

	IndexableColumns = "indexable.columns"

	FloatingColumns = "floating.columns"

	ColumnEncoders = "column.encoders"
)

// health metrics

const (
	AbortedQueries = "aborted.queries"

	AggregationQueries = "aggregation.queries"

	Queries = "queries"

	DiscardedQueries = "discarded.queries"

	LogPendingQueries = "log.pending.queries"

	FlowPendingQueries = "flow.pending.queries"

	MetricPendingQueries = "metric.pending.queries"

	DrillDownPendingQueries = "drilldown.pending.queries"

	AIOpsPendingQueries = "aiops.pending.queries"

	LogQueryLatency = "log.query.latency.ms"

	FlowQueryLatency = "flow.query.latency.ms"

	MetricQueryLatency = "metric.query.latency.ms"

	DrillDownQueryLatency = "drilldown.query.latency.ms"

	AIOpsQueryLatency = "aiops.query.latency.ms"

	QueryLatency = "query.latency.ms"

	VerticalPendingFiles = "vertical.pending.files"

	HorizontalPendingFiles = "horizontal.pending.files"

	HorizontalAggregationPendingFiles = "horizontal.aggregation.pending.files"
)

// cache metrics
const (
	CacheHitRatioPercentage = "cache.hit.ratio.percentage"

	CacheHits = "cache.hits"

	CacheTouches = "cache.touches"

	CacheMisses = "cache.misses"

	CacheEvictions = "cache.evictions"

	CacheExpires = "cache.expires"

	CacheOverwrites = "cache.overwrites"

	CacheLookups = "cache.lookups"

	CacheAverageAccessTime = "cache.average.access.time"

	CacheEntries = "cache.entries"
)

// common column names
const (
	EventSource = "event.source"

	Event = "event"

	EventCategory = "event.category"

	EventSourceType = "event.source.type"

	EventSeverity = "event.severity"

	Message = "message"

	TrapMessage = "trap.message"

	TrapRawMessage = "trap.raw.message"

	AuditMessage = "audit.message"

	PolicyId = "policy.id"

	PolicyType = "policy.type"

	PolicySeverity = "severity"

	EventField = "event.field"

	RunbookWorklogType = "runbook.worklog.type"

	ObjectId = "object.id"

	NetRouteId = "netroute.id"

	TrapEnterprise = "trap.enterprise"

	TrapEnterpriseId = "trap.enterprise.id"

	TrapOID = "trap.oid"

	TrapName = "trap.name"

	TrapVersion = "trap.version"

	TrapSeverity = "trap.severity"

	TrapVendor = "trap.vendor"

	UserNotificationMessage = "user.notification.message"

	PolicyMetric = "metric"

	PolicyThreshold = "policy.threshold"

	CompliancePolicyID = "compliance.policy.id"

	EventPatternId = "event.pattern.id"

	VolumeBytes = "volume.bytes"

	SourceIFIndex = "source.if.index"

	DestinationIFIndex = "destination.if.index"

	EventId = "event.id"
)

const (
	Raw = "raw"

	Aggregated = "aggregated"
)
