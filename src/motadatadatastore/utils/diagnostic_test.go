/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-06-04             <PERSON><PERSON>l Shah            MOTADATA-5780 Test Case Refactoring
 */

package utils

import (
	"bytes"
	"fmt"
	"github.com/stretchr/testify/assert"
	"os"
	"runtime/pprof"
	"runtime/trace"
	"testing"
	"time"
)

func TestDiagnoseDataStore(t *testing.T) {

	configs[DiagnosticProfileDurationSeconds] = 5

	assertions := assert.New(t)

	SystemBootSequence = "DATASTORE"

	_ = os.RemoveAll(diagnosticDir + PathSeparator)

	StartDiagnostic()

	_, err := os.Stat(diagnosticDir + PathSeparator + fmt.Sprintf("%s.zip", SystemBootSequence))

	assertions.Nil(err)
}

func TestDiagnoseBroker(t *testing.T) {

	configs[DiagnosticProfileDurationSeconds] = 5

	assertions := assert.New(t)

	SystemBootSequence = "BROKER"

	_ = os.RemoveAll(diagnosticDir + PathSeparator)

	StartDiagnostic()

	_, err := os.Stat(diagnosticDir + PathSeparator + fmt.Sprintf("%s.zip", SystemBootSequence))

	assertions.Nil(err)
}

func TestDiagnoseProfile(t *testing.T) {

	SystemBootSequence = "DATASTORE"

	assertions := assert.New(t)

	for _, diagnosticProfile := range diagnosticProfiles {

		err := os.RemoveAll(diagnosticDir)

		assertions.Nil(err)

		err = os.MkdirAll(diagnosticDir+PathSeparator+"dummy", os.ModePerm)

		assertions.Nil(err)

		profile("dummy", diagnosticProfile, time.Second*1)

		_, err = os.Stat(diagnosticDir + PathSeparator + "dummy" + PathSeparator + diagnosticProfile)

		assertions.Nil(err)

	}
}

func TestDiagnosticV1(t *testing.T) {

	assertions := assert.New(t)

	defer func() {
		AssertLogMessage(assertions, "Diagnostic", "system", "occurred while diagnosing")
	}()

	defer cleanUp()

	panic("")
}

func TestDiagnosticV2(t *testing.T) {
	assertions := assert.New(t)

	SystemBootSequence = "DATASTORE"

	dir := diagnosticDir + PathSeparator + SystemBootSequence

	_ = os.RemoveAll(dir)

	_ = os.MkdirAll(diagnosticDir, os.ModePerm)

	_, err := os.Create(dir)

	assertions.NoError(err)

	StartDiagnostic()

	configs[DiagnosticProfileDurationSeconds] = 1

	_ = os.RemoveAll(dir)

	zipFile := diagnosticDir + PathSeparator + SystemBootSequence + ZipExtension

	_ = os.MkdirAll(zipFile, os.ModePerm)

	StartDiagnostic()
}

func TestDiagnosticV3(t *testing.T) {

	assertions := assert.New(t)

	defer func() {

		AssertLogMessage(assertions, "Diagnostic", "system", "failed to collect logs, reason")
	}()

	file := diagnosticDir + PathSeparator + SystemBootSequence + PathSeparator + CPU

	_ = os.RemoveAll(diagnosticDir)

	_ = os.MkdirAll(file, os.ModePerm)

	profileCPU(SystemBootSequence, CPU, time.Second)

	_ = os.RemoveAll(diagnosticDir)

	_ = os.MkdirAll(diagnosticDir+PathSeparator+SystemBootSequence, os.ModePerm)

	buffer := bytes.NewBuffer(nil)

	pprof.StartCPUProfile(buffer)

	defer pprof.StopCPUProfile()

	profileCPU(SystemBootSequence, CPU, time.Second)

	file = diagnosticDir + PathSeparator + SystemBootSequence + PathSeparator + Trace

	_ = os.RemoveAll(diagnosticDir)

	_ = os.MkdirAll(file, os.ModePerm)

	collectTraces(SystemBootSequence, Trace, time.Second)

	_ = os.RemoveAll(diagnosticDir)

	_ = os.MkdirAll(diagnosticDir+PathSeparator+SystemBootSequence, os.ModePerm)

	trace.Start(buffer)

	defer trace.Stop()

	collectTraces(SystemBootSequence, Trace, time.Second)

	file = diagnosticDir + PathSeparator + SystemBootSequence + PathSeparator + Logs

	_ = os.RemoveAll(diagnosticDir)

	_ = os.MkdirAll(diagnosticDir+PathSeparator+SystemBootSequence, os.ModePerm)

	_, _ = os.Create(diagnosticDir + PathSeparator + SystemBootSequence + PathSeparator + Logs)

	dumpLogs(SystemBootSequence)

	_ = os.RemoveAll(diagnosticDir)

	_ = os.RemoveAll(CurrentDir + PathSeparator + LogDirectory)

	dumpLogs(SystemBootSequence)

}
