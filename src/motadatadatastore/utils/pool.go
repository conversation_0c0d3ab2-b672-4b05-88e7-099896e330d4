/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-04-09			 Dhaval <PERSON>ra			Motadata-4859  Refactoring "fmt.Sprintf" to update go version to 1.24
* 2025-05-05			 Swapnil A. Dave		<PERSON>A-6078 introduced blobpool as config parameter as verticalwriter requires 3 blob pools.
* 2025-05-10             Dhaval Bera            MOTADATA-6174  Added Unmap function to release memory-mapped resources
* 2025-06-04             <PERSON><PERSON><PERSON>ATA-5780 Called custom MmapAnonymous and Munmap functions
 */

package utils

import (
	"fmt"
	"math"
	"runtime"
)

type MemoryPool struct {
	stringBuffers [][]string

	float64Buffers [][]float64

	intBuffers [][]int

	int64Buffers [][]int64

	int32Buffers [][]int32

	int16Buffers [][]int16

	int8Buffers [][]int8

	uint64Buffers [][]uint64

	uint32Buffers [][]uint32

	uint16Buffers [][]uint16

	byteBuffers [][]byte

	usedByteBuffers, usedFLOAT64Buffers, usedStringBuffers int64

	usedINT8Buffers, usedINT16Buffers, usedINT32Buffers, usedINT64Buffers, usedINTBuffers int64

	usedUINT16Buffers, usedUINT32Buffers, usedUINT64Buffers int64

	poolLength, poolSize, padding int

	expandable bool
}

const Component = "Pool"

const DefaultBlobPools = 2

var poolLogger = NewLogger(Component, "util")

func NewMemoryPool(poolSize, poolLength int, expandable bool, blobPools int) *MemoryPool {

	padding := 128

	if size := poolLength / 10000; size > 1 {

		padding *= size
	}

	return &MemoryPool{

		byteBuffers: make([][]byte, poolSize+blobPools), //addition 2 pool for blob which will be 1024*1024*2 MB

		int32Buffers: make([][]int32, poolSize),

		int64Buffers: make([][]int64, poolSize),

		int16Buffers: make([][]int16, poolSize),

		int8Buffers: make([][]int8, poolSize),

		intBuffers: make([][]int, poolSize),

		uint16Buffers: make([][]uint16, poolSize),

		uint32Buffers: make([][]uint32, poolSize),

		uint64Buffers: make([][]uint64, poolSize),

		stringBuffers: make([][]string, poolSize),

		float64Buffers: make([][]float64, poolSize),

		poolLength: poolLength,

		expandable: expandable,

		poolSize: poolSize,

		padding: padding,
	}

}

func available(index int, usedPools int64) bool {

	if index == NotAvailable {

		return true
	}

	return usedPools&(1<<index) == 0
}

func (pool *MemoryPool) AcquireBlobPool(size int) (int, []byte) {

	if size == NotAvailable {

		size = MaxBlobBytes + pool.padding
	}

	for index := pool.poolSize; index < pool.poolSize+(len(pool.byteBuffers)-pool.poolSize); index++ {

		if available(index, pool.usedByteBuffers) {

			pool.usedByteBuffers |= 1 << index

			if pool.byteBuffers[index] == nil {

				if size < MaxBlobBytes+pool.padding {

					pool.byteBuffers[index] = make([]byte, size, MaxBlobBytes+pool.padding)

				} else {

					pool.byteBuffers[index] = make([]byte, size)

				}

			} else {

				if size > cap(pool.byteBuffers[index]) {

					pool.byteBuffers[index] = make([]byte, size)
				}

				pool.byteBuffers[index] = pool.byteBuffers[index][:size]
			}

			return index, pool.byteBuffers[index]
		}
	}

	return -1, nil
}

func (pool *MemoryPool) AcquireStringBytePool(size int) (int, []byte) {

	if size == NotAvailable {

		size = (pool.poolLength * MaxStringBytes) + pool.padding
		// as of now we are considering 8 times formula...
	} else if size > (pool.poolLength*MaxStringBytes)+pool.padding {

		return NotAvailable, nil
	}

	for index := range pool.byteBuffers[:pool.poolSize] {

		if available(index, pool.usedByteBuffers) {

			pool.usedByteBuffers |= 1 << index

			if pool.byteBuffers[index] == nil {

				bytes, err := MmapAnonymous((pool.poolLength * MaxStringBytes) + pool.padding)

				if err != nil {

					poolLogger.Error(fmt.Sprintf(ErrorAnonymousBufferAcquire, err.Error()))

					return NotAvailable, nil
				}

				pool.byteBuffers[index] = bytes[:size]

			} else {

				pool.byteBuffers[index] = pool.byteBuffers[index][:size]
			}

			return index, pool.byteBuffers[index]
		}
	}

	return -1, nil
}

func (pool *MemoryPool) AcquireBytePool(size int) (int, []byte) {

	if size == NotAvailable {

		size = (pool.poolLength * MaxStringBytes) + pool.padding
		// as of now we are considering 8 times formula...
	} else if size > (pool.poolLength*MaxStringBytes)+pool.padding {

		return NotAvailable, nil
	}

	for index := range pool.byteBuffers[:pool.poolSize] {

		if available(index, pool.usedByteBuffers) {

			pool.usedByteBuffers |= 1 << index

			if pool.byteBuffers[index] == nil {

				bytes, err := MmapAnonymous((MaxStringBytes * pool.poolLength) + pool.padding)

				if err != nil {

					poolLogger.Error(fmt.Sprintf(ErrorAnonymousBufferAcquire, err.Error()))

					return NotAvailable, nil
				}

				pool.byteBuffers[index] = bytes[:size]

			} else {

				pool.byteBuffers[index] = pool.byteBuffers[index][:size]
			}

			return index, pool.byteBuffers[index]
		}
	}

	return -1, nil
}

func (pool *MemoryPool) ReleaseBytePool(index int) {

	if available(index, pool.usedByteBuffers) {

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf("BYTE pool %v not released, reason: %v", index, fmt.Sprintf(ErrorMaxPoolLeaked, "BYTE")))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

		return
	}

	if index >= pool.poolSize && len(pool.byteBuffers[index]) > MaxBlobBytes+pool.padding {

		pool.byteBuffers[index] = make([]byte, MaxBlobBytes+pool.padding)
	}

	pool.usedByteBuffers &^= 1 << index // free the pool

}

func (pool *MemoryPool) GetBytePool(index int) []byte {

	return pool.byteBuffers[index]
}

func (pool *MemoryPool) GetFLOAT64Pool(index int) []float64 {

	return pool.float64Buffers[index]
}

func (pool *MemoryPool) GetINT8Pool(index int) []int8 {

	return pool.int8Buffers[index]
}

func (pool *MemoryPool) GetINT16Pool(index int) []int16 {

	return pool.int16Buffers[index]
}

func (pool *MemoryPool) GetINT32Pool(index int) []int32 {

	return pool.int32Buffers[index]
}

func (pool *MemoryPool) GetINT64Pool(index int) []int64 {

	return pool.int64Buffers[index]
}

func (pool *MemoryPool) GetINTPool(index int) []int {

	return pool.intBuffers[index]
}

func (pool *MemoryPool) GetStringPool(index int) []string {

	return pool.stringBuffers[index]
}

func (pool *MemoryPool) GetUINT64Pool(index int) []uint64 {

	return pool.uint64Buffers[index]
}

func (pool *MemoryPool) AcquireFLOAT64Pool(size int) (int, []float64) {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding
	} else if size > pool.poolLength+pool.padding && !pool.expandable {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "FLOAT64", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	for index := range pool.float64Buffers[:pool.poolSize] {

		if available(index, pool.usedFLOAT64Buffers) {

			pool.usedFLOAT64Buffers |= 1 << index

			pool.allocateFLOAT64Pool(index, size)

			return index, pool.float64Buffers[index]
		}
	}

	return -1, nil
}

func (pool *MemoryPool) ExpandFLOAT64Pool(index, size int) []float64 {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding
	} else if size > pool.poolLength+pool.padding && !pool.expandable {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "FLOAT64", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	poolLogger.Info(fmt.Sprintf("FLOAT64 Pool expanded from %v to %v", len(pool.float64Buffers[index]), size))

	values := make([]float64, size-len(pool.float64Buffers[index]))

	for i := range values {

		values[i] = DummyFLOAT64Value
	}

	pool.float64Buffers[index] = append(pool.float64Buffers[index], values...)

	return pool.float64Buffers[index]

}

func (pool *MemoryPool) ExpandINT64Pool(index, size int) []int64 {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding
	} else if size > pool.poolLength+pool.padding && !pool.expandable {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "INT64", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	poolLogger.Info(fmt.Sprintf("INT64 Pool expanded from %v to %v", len(pool.int64Buffers[index]), size))

	values := make([]int64, size-len(pool.int64Buffers[index]))

	for i := range values {

		values[i] = DummyINT64Value
	}

	pool.int64Buffers[index] = append(pool.int64Buffers[index], values...)

	return pool.int64Buffers[index]

}

func (pool *MemoryPool) ExpandUINT64Pool(index, size int) []uint64 {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding
	} else if size > pool.poolLength+pool.padding && !pool.expandable {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "UINT64", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	poolLogger.Info(fmt.Sprintf("UINT64 Pool expanded from %v to %v", len(pool.uint64Buffers[index]), size))

	values := make([]uint64, size-len(pool.uint64Buffers[index]))

	for i := range values {

		values[i] = math.MaxUint64
	}

	pool.uint64Buffers[index] = append(pool.uint64Buffers[index], values...)

	return pool.uint64Buffers[index]

}

func (pool *MemoryPool) ExpandINT32Pool(index, size int) []int32 {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding
	} else if size > pool.poolLength+pool.padding && !pool.expandable {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "INT32", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	poolLogger.Info(fmt.Sprintf("INT32 Pool expanded from %v to %v", len(pool.int32Buffers[index]), size))

	values := make([]int32, size-len(pool.int32Buffers[index]))

	pool.int32Buffers[index] = append(pool.int32Buffers[index], values...)

	return pool.int32Buffers[index]

}

func (pool *MemoryPool) ExpandStringPool(index, size int) []string {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding
	} else if size > pool.poolLength+pool.padding && !pool.expandable {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "STRING", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	poolLogger.Info(fmt.Sprintf("STRING Pool expanded from %v to %v", len(pool.stringBuffers[index]), size))

	values := make([]string, size-len(pool.stringBuffers[index]))

	pool.stringBuffers[index] = append(pool.stringBuffers[index], values...)

	return pool.stringBuffers[index]
}

func (pool *MemoryPool) ReleaseFLOAT64Pool(index int) {

	if available(index, pool.usedFLOAT64Buffers) {

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf("FLOAT64 pool %v not released, reason: %v", index, fmt.Sprintf(ErrorMaxPoolLeaked, "FLOAT64")))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

		return
	}

	pool.usedFLOAT64Buffers &^= 1 << index // available the pool
}

func (pool *MemoryPool) AcquireINT8Pool(size int) (int, []int8) {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding
	} else if size > pool.poolLength+pool.padding {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "INT8", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	for index := range pool.int8Buffers[:pool.poolSize] {

		if available(index, pool.usedINT8Buffers) {

			pool.usedINT8Buffers |= 1 << index

			if pool.int8Buffers[index] == nil {

				pool.int8Buffers[index] = make([]int8, size, pool.poolLength+pool.padding)
			} else {

				pool.int8Buffers[index] = pool.int8Buffers[index][:size]
			}
			return index, pool.int8Buffers[index]
		}
	}

	return -1, nil
}

func (pool *MemoryPool) ReleaseINT8Pool(index int) {

	if available(index, pool.usedINT8Buffers) {

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf("INT8 pool %v not released, reason: %v", index, fmt.Sprintf(ErrorMaxPoolLeaked, "INT8")))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

		return
	}

	pool.usedINT8Buffers &^= 1 << index // available the pool
}

func (pool *MemoryPool) AcquireINT16Pool(size int) (int, []int16) {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding
	} else if size > pool.poolLength+pool.padding {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "INT16", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	for index := range pool.int16Buffers[:pool.poolSize] {

		if available(index, pool.usedINT16Buffers) {

			pool.usedINT16Buffers |= 1 << index

			if pool.int16Buffers[index] == nil {

				pool.int16Buffers[index] = make([]int16, size, pool.poolLength+pool.padding)
			} else {

				pool.int16Buffers[index] = pool.int16Buffers[index][:size]
			}
			return index, pool.int16Buffers[index]

		}
	}

	return -1, nil
}

func (pool *MemoryPool) ReleaseINT16Pool(index int) {

	if available(index, pool.usedINT16Buffers) {

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf("INT16 pool %v not released, reason: %v", index, fmt.Sprintf(ErrorMaxPoolLeaked, "INT16")))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

		return
	}

	pool.usedINT16Buffers &^= 1 << index // available the pool
}

func (pool *MemoryPool) AcquireINT32Pool(size int) (int, []int32) {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding // BP32 delta need padding of 128 extra element
	} else if size > pool.poolLength+pool.padding && !pool.expandable {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "INT32", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	for index := range pool.int32Buffers[:pool.poolSize] {

		if available(index, pool.usedINT32Buffers) {

			pool.usedINT32Buffers |= 1 << index

			pool.allocateINT32Pool(index, size)

			return index, pool.int32Buffers[index]
		}
	}

	return -1, nil
}

func (pool *MemoryPool) ReleaseINT32Pool(index int) {

	if available(index, pool.usedINT32Buffers) {

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf("INT32 pool %v not released, reason: %v", index, fmt.Sprintf(ErrorMaxPoolLeaked, "INT32")))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

		return
	}

	pool.usedINT32Buffers &^= 1 << index // available the pool
}

func (pool *MemoryPool) AcquireINT64Pool(size int) (int, []int64) {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding
	} else if size > pool.poolLength+pool.padding && !pool.expandable {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "INT64", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	for index := range pool.int64Buffers[:pool.poolSize] {

		if available(index, pool.usedINT64Buffers) {

			pool.usedINT64Buffers |= 1 << index

			pool.allocateINT64Pool(index, size)

			return index, pool.int64Buffers[index]
		}
	}

	return -1, nil
}

func (pool *MemoryPool) ReleaseINT64Pool(index int) {

	if available(index, pool.usedINT64Buffers) {

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf("INT64 pool %v not released, reason: %v", index, fmt.Sprintf(ErrorMaxPoolLeaked, "INT64")))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

		return
	}

	pool.usedINT64Buffers &^= 1 << index // available the pool
}

func (pool *MemoryPool) AcquireStringPool(size int) (int, []string) {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding
	} else if size > pool.poolLength+pool.padding && !pool.expandable {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "STRING", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	for index := range pool.stringBuffers[:pool.poolSize] {

		if available(index, pool.usedStringBuffers) {

			pool.usedStringBuffers |= 1 << index

			pool.allocateSTRINGPool(index, size)

			return index, pool.stringBuffers[index]
		}
	}

	return -1, nil
}

func (pool *MemoryPool) ReleaseStringPool(index int) {

	if available(index, pool.usedStringBuffers) {

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf("STRING pool %v not released, reason: %v", index, fmt.Sprintf(ErrorMaxPoolLeaked, "STRING")))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

		return
	}

	pool.usedStringBuffers &^= 1 << index // available the pool

}

func (pool *MemoryPool) ResetFLOAT64Pool(index, position int, value float64) {

	if position == -1 {

		position = len(pool.float64Buffers[index])
	}

	for i := range pool.float64Buffers[index][:position] {
		pool.float64Buffers[index][i] = value
	}

}

func (pool *MemoryPool) ResetINT64Pool(index, position int, value int64) {

	if position == -1 {

		position = len(pool.int64Buffers[index])
	}

	for i := range pool.int64Buffers[index][:position] {
		pool.int64Buffers[index][i] = value
	}
}

func (pool *MemoryPool) ResetINT32Pool(index, position int, value int32) {

	if position == -1 {

		position = len(pool.int32Buffers[index])
	}

	for i := range pool.int32Buffers[index][:position] {
		pool.int32Buffers[index][i] = value
	}
}

func (pool *MemoryPool) ResetINT16Pool(index, position int, value int16) {

	if position == -1 {

		position = len(pool.int16Buffers[index])
	}

	for i := range pool.int16Buffers[index][:position] {
		pool.int16Buffers[index][i] = value
	}
}

func (pool *MemoryPool) ResetINT8Pool(index, position int, value int8) {

	if position == -1 {

		position = len(pool.int8Buffers[index])
	}

	for i := range pool.int8Buffers[index][:position] {
		pool.int8Buffers[index][i] = value
	}
}

func (pool *MemoryPool) ResetINTPool(index, position int, value int) {

	if position == -1 {

		position = len(pool.intBuffers[index])
	}

	for i := range pool.intBuffers[index][:position] {
		pool.intBuffers[index][i] = value
	}
}

func (pool *MemoryPool) AcquireUINT16Pool(size int) (int, []uint16) {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding
	} else if size > pool.poolLength+pool.padding {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "UINT16", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	for index := range pool.uint16Buffers[:pool.poolSize] {

		if available(index, pool.usedUINT16Buffers) {

			pool.usedUINT16Buffers |= 1 << index

			if pool.uint16Buffers[index] == nil {

				pool.uint16Buffers[index] = make([]uint16, size, pool.poolLength+pool.padding)
			} else {

				pool.uint16Buffers[index] = pool.uint16Buffers[index][:size]

			}

			return index, pool.uint16Buffers[index]
		}
	}

	return -1, nil
}

func (pool *MemoryPool) ReleaseUINT16Pool(index int) {

	if available(index, pool.usedUINT16Buffers) {

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf("UINT16 pool %v not released, reason: %v", index, fmt.Sprintf(ErrorMaxPoolLeaked, "UINT16")))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

		return
	}

	pool.usedUINT16Buffers &^= 1 << index // available the pool
}

func (pool *MemoryPool) AcquireUINT32Pool(size int) (int, []uint32) {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding
	} else if size > pool.poolLength+pool.padding {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "UINT32", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	for index := range pool.uint32Buffers[:pool.poolSize] {

		if available(index, pool.usedUINT32Buffers) {

			pool.usedUINT32Buffers |= 1 << index

			if pool.uint32Buffers[index] == nil {

				pool.uint32Buffers[index] = make([]uint32, size, pool.poolLength+pool.padding)
			} else {

				pool.uint32Buffers[index] = pool.uint32Buffers[index][:size]
			}

			return index, pool.uint32Buffers[index]
		}
	}

	return -1, nil
}

func (pool *MemoryPool) ReleaseUINT32Pool(index int) {

	if available(index, pool.usedUINT32Buffers) {

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf("UINT32 pool %v not released, reason: %v", index, fmt.Sprintf(ErrorMaxPoolLeaked, "UINT32")))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

		return
	}

	pool.usedUINT32Buffers &^= 1 << index // available the pool
}

func (pool *MemoryPool) AcquireUINT64Pool(size int) (int, []uint64) {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding

	} else if size > pool.poolLength+pool.padding && !pool.expandable {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "UINT64", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	for index := range pool.uint64Buffers[:pool.poolSize] {

		if available(index, pool.usedUINT64Buffers) {

			pool.usedUINT64Buffers |= 1 << index

			pool.allocateUINT64Pool(index, size)

			return index, pool.uint64Buffers[index]
		}
	}

	return -1, nil
}

func (pool *MemoryPool) ReleaseUINT64Pool(index int) {

	if available(index, pool.usedUINT64Buffers) {

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf("UINT64 pool %v not released, reason: %v", index, fmt.Sprintf(ErrorMaxPoolLeaked, "UINT64")))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

		return
	}

	pool.usedUINT64Buffers &^= 1 << index // available the pool
}

func (pool *MemoryPool) AcquireINTPool(size int) (int, []int) {

	if size == NotAvailable {

		size = pool.poolLength + pool.padding
	} else if size > pool.poolLength+pool.padding {

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLengthLimitReached, "INT", size, pool.poolLength+pool.padding))

		size = pool.poolLength + pool.padding
	}

	for index := range pool.intBuffers[:pool.poolSize] {

		if available(index, pool.usedINTBuffers) {

			pool.usedINTBuffers |= 1 << index

			if pool.intBuffers[index] == nil {

				pool.intBuffers[index] = make([]int, size, pool.poolLength+pool.padding)
			} else {

				pool.intBuffers[index] = pool.intBuffers[index][:size]
			}

			return index, pool.intBuffers[index]
		}
	}

	return -1, nil
}

func (pool *MemoryPool) ReleaseINTPool(index int) {

	if available(index, pool.usedINTBuffers) {

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf("INT pool %v not released, reason: %v", index, fmt.Sprintf(ErrorMaxPoolLeaked, "INT")))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

		return
	}

	pool.usedINTBuffers &^= 1 << index // available the pool
}

func (pool *MemoryPool) GetUsedBytePools() int {

	return int(pool.usedByteBuffers)
}

func (pool *MemoryPool) GetUsedUINT16Pools() int {

	return int(pool.usedUINT16Buffers)
}

func (pool *MemoryPool) GetUsedUINT32Pools() int {

	return int(pool.usedUINT32Buffers)
}

func (pool *MemoryPool) GetUsedUINT64Pools() int {

	return int(pool.usedUINT64Buffers)
}

func (pool *MemoryPool) GetUsedStringPools() int {

	return int(pool.usedStringBuffers)
}

func (pool *MemoryPool) GetUsedINT16Pools() int {

	return int(pool.usedINT16Buffers)
}

func (pool *MemoryPool) GetUsedINT8Pools() int {

	return int(pool.usedINT8Buffers)
}

func (pool *MemoryPool) GetUsedINT32Pools() int {

	return int(pool.usedINT32Buffers)
}

func (pool *MemoryPool) GetUsedFLOAT64Pools() int {

	return int(pool.usedFLOAT64Buffers)
}

func (pool *MemoryPool) GetUsedINT64Pools() int {

	return int(pool.usedINT64Buffers)
}

func (pool *MemoryPool) GetUsedINTPools() int {

	return int(pool.usedINTBuffers)
}

func (pool *MemoryPool) GetPoolLength() int {

	return pool.poolLength
}

func (pool *MemoryPool) TestPoolLeak() {

	if pool.usedByteBuffers != 0 {

		pool.usedByteBuffers = 0

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLeaked, "BYTE"))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))
	}

	if pool.usedINT8Buffers != 0 {

		pool.usedINT8Buffers = 0

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLeaked, "INT8"))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))
	}

	if pool.usedINT16Buffers != 0 {

		pool.usedINT16Buffers = 0

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLeaked, "INT16"))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))
	}

	if pool.usedINT32Buffers != 0 {

		pool.usedINT32Buffers = 0

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLeaked, "INT32"))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))
	}

	if pool.usedINT64Buffers != 0 {

		pool.usedINT64Buffers = 0

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLeaked, "INT64"))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))
	}

	if pool.usedINTBuffers != 0 {

		pool.usedINTBuffers = 0

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLeaked, "INT"))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))
	}

	if pool.usedFLOAT64Buffers != 0 {

		pool.usedFLOAT64Buffers = 0

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLeaked, "FLOAT64"))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))
	}

	if pool.usedStringBuffers != 0 {

		pool.usedStringBuffers = 0

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLeaked, "STRING"))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))
	}

	if pool.usedUINT16Buffers != 0 {

		pool.usedUINT16Buffers = 0

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLeaked, "UINT16"))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))
	}

	if pool.usedUINT32Buffers != 0 {

		pool.usedUINT32Buffers = 0

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLeaked, "UINT32"))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))
	}

	if pool.usedUINT64Buffers != 0 {

		pool.usedUINT64Buffers = 0

		stackTraceBytes := make([]byte, 1<<20)

		poolLogger.Fatal(fmt.Sprintf(ErrorMaxPoolLeaked, "UINT64"))

		poolLogger.Error(string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))
	}

}

func (pool *MemoryPool) ShrinkPool() {

	if pool.expandable {

		pool.shrinkINT64Pool()

		pool.shrinkINT32Pool()

		pool.shrinkFLOAT64Pool()

		pool.shrinkSTRINGPool()

		pool.shrinkUINT64Pool()

	}
}

func (pool *MemoryPool) Unmap() {

	// munmap all the pool acquired using mmap

	for index := range pool.byteBuffers[:pool.poolSize] {

		if pool.byteBuffers[index] != nil {

			if err := Munmap(pool.byteBuffers[index][:cap(pool.byteBuffers[index])]); err != nil {

				poolLogger.Error(fmt.Sprintf("failed munmap pool reason %v", err))
			}
		}

	}

}

func (pool *MemoryPool) shrinkINT64Pool() {

	for index := range pool.int64Buffers[:pool.poolSize] {

		if available(index, pool.usedINT64Buffers) {

			if len(pool.int64Buffers[index]) > pool.poolLength+pool.padding {

				if DebugEnabled() {

					poolLogger.Debug(fmt.Sprintf("INT64 pool shrink from %v to %v", len(pool.int64Buffers[index]), pool.poolLength+pool.padding))
				}

				pool.int64Buffers[index] = make([]int64, pool.poolLength+pool.padding)
			}
		}
	}
}

func (pool *MemoryPool) shrinkUINT64Pool() {

	for index := range pool.uint64Buffers[:pool.poolSize] {

		if available(index, pool.usedUINT64Buffers) {

			if len(pool.uint64Buffers[index]) > pool.poolLength+pool.padding {

				if DebugEnabled() {

					poolLogger.Debug(fmt.Sprintf("UINT64 pool shrink from %v to %v", len(pool.uint64Buffers[index]), pool.poolLength+pool.padding))
				}

				pool.uint64Buffers[index] = make([]uint64, pool.poolLength+pool.padding)
			}
		}
	}
}

func (pool *MemoryPool) shrinkINT32Pool() {

	for index := range pool.int32Buffers[:pool.poolSize] {

		if available(index, pool.usedINT32Buffers) {

			if len(pool.int32Buffers[index]) > pool.poolLength+pool.padding {

				if DebugEnabled() {

					poolLogger.Debug(fmt.Sprintf("INT32 pool shrink from %v to %v", len(pool.int32Buffers[index]), pool.poolLength+pool.padding))

				}

				pool.int32Buffers[index] = make([]int32, pool.poolLength+pool.padding)
			}
		}
	}
}

func (pool *MemoryPool) shrinkFLOAT64Pool() {

	for index := range pool.float64Buffers[:pool.poolSize] {

		if available(index, pool.usedFLOAT64Buffers) {

			if len(pool.float64Buffers[index]) > pool.poolLength+pool.padding {

				if DebugEnabled() {

					poolLogger.Debug(fmt.Sprintf("FLOAT64 pool shrink from %v to %v", len(pool.float64Buffers[index]), pool.poolLength+pool.padding))

				}

				pool.float64Buffers[index] = make([]float64, pool.poolLength+pool.padding)
			}
		}
	}
}

func (pool *MemoryPool) shrinkSTRINGPool() {

	for index := range pool.stringBuffers[:pool.poolSize] {

		if available(index, pool.usedStringBuffers) {

			if len(pool.stringBuffers[index]) > pool.poolLength+pool.padding {

				if DebugEnabled() {

					poolLogger.Debug(fmt.Sprintf("STRING pool shrink from %v to %v", len(pool.stringBuffers[index]), pool.poolLength+pool.padding))

				}

				pool.stringBuffers[index] = make([]string, pool.poolLength+pool.padding)
			}
		}
	}
}

func (pool *MemoryPool) allocateFLOAT64Pool(index, size int) {

	if pool.float64Buffers[index] == nil {

		if size > pool.poolLength+pool.padding {

			poolLogger.Info(fmt.Sprintf("FLOAT64 Pool initialized with expanded size %v", size))

			pool.float64Buffers[index] = make([]float64, size)

		} else {

			pool.float64Buffers[index] = make([]float64, size, pool.poolLength+pool.padding)
		}

	} else {

		if size > len(pool.float64Buffers[index]) {

			if len(pool.float64Buffers[index]) >= pool.poolLength+pool.padding {

				poolLogger.Info(fmt.Sprintf("FLOAT64 Pool expanded from %v to %v", len(pool.float64Buffers[index]), size))
			}

			values := make([]float64, size-len(pool.float64Buffers[index]))

			for i := range values {

				values[i] = DummyFLOAT64Value
			}

			pool.float64Buffers[index] = append(pool.float64Buffers[index], values...)

		} else {

			pool.float64Buffers[index] = pool.float64Buffers[index][:size]
		}
	}
}

func (pool *MemoryPool) allocateSTRINGPool(index, size int) {

	if pool.stringBuffers[index] == nil {

		if size > pool.poolLength+pool.padding {

			poolLogger.Info(fmt.Sprintf("STRING Pool initialized with expanded size %v", size))

			pool.stringBuffers[index] = make([]string, size)

		} else {

			pool.stringBuffers[index] = make([]string, size, pool.poolLength+pool.padding)
		}

	} else {

		if size > len(pool.stringBuffers[index]) {

			if len(pool.stringBuffers[index]) >= pool.poolLength+pool.padding {

				poolLogger.Info(fmt.Sprintf("STRING Pool expanded from %v to %v", len(pool.stringBuffers[index]), size))
			}

			for i := range pool.stringBuffers[index] {

				pool.stringBuffers[index][i] = Empty
			}

			values := make([]string, size-len(pool.stringBuffers[index]))

			pool.stringBuffers[index] = append(pool.stringBuffers[index], values...)

		} else {

			pool.stringBuffers[index] = pool.stringBuffers[index][:size]

			for i := range pool.stringBuffers[index][:size] {

				pool.stringBuffers[index][i] = Empty
			}
		}
	}
}

func (pool *MemoryPool) allocateINT64Pool(index, size int) {

	if pool.int64Buffers[index] == nil {

		if size > pool.poolLength+pool.padding {

			poolLogger.Info(fmt.Sprintf("INT64 Pool initialized with expanded size %v", size))

			pool.int64Buffers[index] = make([]int64, size)

		} else {

			pool.int64Buffers[index] = make([]int64, size, pool.poolLength+pool.padding)
		}
	} else {

		if size > len(pool.int64Buffers[index]) {

			if len(pool.int64Buffers[index]) >= pool.poolLength+pool.padding {

				poolLogger.Info(fmt.Sprintf("INT64 Pool expanded from %v to %v", len(pool.int64Buffers[index]), size))
			}

			values := make([]int64, size-len(pool.int64Buffers[index]))

			for i := range values {

				values[i] = DummyINT64Value
			}

			pool.int64Buffers[index] = append(pool.int64Buffers[index], values...)

		} else {

			pool.int64Buffers[index] = pool.int64Buffers[index][:size]
		}
	}
}

func (pool *MemoryPool) allocateUINT64Pool(index, size int) {

	if pool.uint64Buffers[index] == nil {

		if size > pool.poolLength+pool.padding {

			poolLogger.Info(fmt.Sprintf("UINT64 Pool initialized with expanded size %v", size))

			pool.uint64Buffers[index] = make([]uint64, size)

		} else {

			pool.uint64Buffers[index] = make([]uint64, size, pool.poolLength+pool.padding)
		}
	} else {

		if size > len(pool.uint64Buffers[index]) {

			if len(pool.uint64Buffers[index]) >= pool.poolLength+pool.padding {

				poolLogger.Info(fmt.Sprintf("UINT64 Pool expanded from %v to %v", len(pool.uint64Buffers[index]), size))
			}

			values := make([]uint64, size-len(pool.uint64Buffers[index]))

			for i := range values {

				values[i] = math.MaxUint64
			}

			pool.uint64Buffers[index] = append(pool.uint64Buffers[index], values...)

		} else {

			pool.uint64Buffers[index] = pool.uint64Buffers[index][:size]
		}
	}
}

func (pool *MemoryPool) allocateINT32Pool(index, size int) {

	if pool.int32Buffers[index] == nil {

		if size > pool.poolLength+pool.padding {

			poolLogger.Info(fmt.Sprintf("INT32 Pool initialized with expanded size %v", size+pool.padding))

			pool.int32Buffers[index] = make([]int32, size, size+pool.padding)

		} else {

			pool.int32Buffers[index] = make([]int32, size, pool.poolLength+pool.padding)
		}
	} else {

		if size > len(pool.int32Buffers[index]) {

			if len(pool.int32Buffers[index]) >= pool.poolLength+pool.padding {

				poolLogger.Info(fmt.Sprintf("INT32 Pool expanded from %v to %v", len(pool.int32Buffers[index]), size))
			}

			values := make([]int32, size-len(pool.int32Buffers[index]))

			pool.int32Buffers[index] = append(pool.int32Buffers[index], values...)

		} else {

			pool.int32Buffers[index] = pool.int32Buffers[index][:size]
		}
	}
}
