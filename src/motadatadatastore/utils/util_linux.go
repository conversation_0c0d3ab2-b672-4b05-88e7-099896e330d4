/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-06-04             A<PERSON>l Shah            MOTADATA-5780 Handled linux specific Flock, GetStats, MmapAnonymous & Munmap implementations
 */

package utils

import (
	"golang.org/x/sys/unix"
	"os"
	"syscall"
)

const (
	PROT_READ = unix.PROT_READ

	PROT_WRITE = unix.PROT_WRITE

	MAP_PRIVATE = unix.MAP_PRIVATE

	MAP_ANON = unix.MAP_ANON

	MADV_RANDOM = unix.MADV_RANDOM
)

const (
	OpenExisting = os.O_RDWR | os.O_APPEND

	OpenAlways = os.O_CREATE | os.O_RDWR | os.O_APPEND

	FileAttributeNormal = 0

	FileFlagOverlapped = 0
)

func Flock(fileDescriptor int) error {

	return syscall.Flock(fileDescriptor, syscall.LOCK_EX|syscall.LOCK_NB)
}

func GetStats() (uint64, uint64, error) {

	var stat unix.Statfs_t

	_ = unix.Statfs(CurrentDir, &stat)

	availableDiskSizeBytes := stat.Bavail * uint64(stat.Bsize)

	totalDiskSizeBytes := stat.Blocks * uint64(stat.Bsize)

	return availableDiskSizeBytes, totalDiskSizeBytes, nil

}

func MmapAnonymous(length int) ([]byte, error) {

	return unix.Mmap(-1, 0, length, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_ANON)
}

func Munmap(bytes []byte) error {

	return unix.Munmap(bytes)
}
