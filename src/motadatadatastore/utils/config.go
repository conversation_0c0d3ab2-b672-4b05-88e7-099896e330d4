/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-07             Vedant D. Dokania     	MOTADATA-4698 Added new config parameters to get new ports
* 2025-02-10			 Dhaval Bera			Motadata-4913  Updated config version to 1.48
* 2025-02-28			 <PERSON><PERSON>val <PERSON>-5194  Updated config version to 1.49 & Added indexable column
* 2025-03-05			 <PERSON><PERSON><PERSON>-5190  Updated constants to match SonarQube Standard
* 2025-03-18             Vedant D. Dokania      Motadata-5200 Introduced new status flap column max records limit
* 2025-03-21			 Dhaval <PERSON>			Motad<PERSON>-5452  Added Default Columns For NetRoute
* 2025-04-09			 Aashil Shah			Motadata-4859  Refactoring "fmt.Sprintf" to update go version to 1.24
* 2025-04-21			 Aashil Shah			Motadata-5873  Intoduced new DataWriterSyncTimers to configure timer according to datastore type
* 2025-05-10             Dhaval Bera            MOTADATA-6174  Updated Datastore Variant to 1.52
* 2025-05-26			 Aashil Shah			Motadata-6275  Introduced new Event Aggregator Pool Size and Metric Aggregator Pool Size configs
* 2025-05-26             Aashil Shah            MOTADATA-6275  Updated Datastore Variant to 1.53
* 2025-05-26  			 Dhaval Bera			MOTADATA-6333 Added Discard Query Threshold config , added default aggregation column for system description
* 2025-06-10             Vedant Dokania			Motadata-6524 Updated store variant
* 2025-06-04             Aashil Shah            MOTADATA-5780  Introduced new IOCPWorkers config
* 2025-06-10			 Swapnil A. Dave		MOTADATA-6392  aggregation view aggregation column limit added.
* 2025-06-10			 Hardik Vala			MOTADATA-6392  aggregation view aggregation column limit added - configurable and store variant update
* 2025-06-23             Dhaval Bera            MOTADATA-6642  Coonfigurable Cache stores feature added for memory optimisation
* 2025-06-25			 Swapnil A. Dave		MOTADATA-6555  Added Object Flush Timer Seconds Config
* 2025-06-23             Vedant Dokania         Motadata-6370 Updated store variant
 */

package utils

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/CAFxX/gcnotifier"
	"github.com/pbnjay/memory"
	"math"
	"net"
	"os"
	"runtime"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

const StoreVariant = "1.55"

var configs MotadataMap

var configLogger = NewLogger("Configs", "system")

var lock sync.Mutex

// deployment type const
/*
	1. 0 -> <= 8 gb with 8 core
	2. 1 -> >8 && <=16 gb with 8-16 core
	3. 2 -> >16 && <=32 gb with 16-32 core
	4. 3 -> >32 && <=64 gb with 32-64 core
	5. 4 -> >64 with >= 64 core
*/
const (
	ExtraSmall = iota // 8 gb

	Small // >8 - <16 gb

	Medium // >16-<=32 gb

	Large // >32 - <=64 gb

	ExtraLarge // >64 - <=128/256 gb
)

var ( //Config Parameters

	DeploymentType = ExtraSmall

	//never exceed pool length greater than 100000

	MaxPoolLength = 10000

	LogMaxPoolLength = 5000

	OverflowLength = MaxPoolLength - ((MaxPoolLength * 10) / 100)

	LogOverflowLength = LogMaxPoolLength - ((LogMaxPoolLength * 10) / 100)

	MaxValueBufferBytes = MaxPoolLength * 8

	MaxStringBytes = 50

	MaxBlobBytes = 254 * LogOverflowLength //max string chars 254*5000

	MaxStoreParts = 12

	MaxWorkerEvents int

	MaxAggregationGroups = 1_000_000

	MaxHistoricalRecords = 100_000

	MaxStatusFlapHistoricalRecords = 100_000

	MaxSearchQueryRecords = 100_000

	MaxGridRecords = 100_000

	MaxFlowTopNSelectionGroups = 100

	MaxFlowTopNInsertionGroups = 1000

	VerticalStoreProbeDays = 30

	HorizontalStoreProbeDays = 30

	FulltextSearchingProbeDays = 1

	Aggregation = true

	EnvironmentType = "prod"

	StoreSyncTimerSeconds = 30

	IndexerThresholdPercent = 40

	MaxIndexProbes = 1_00_000

	VerticalAggregationSyncTimerSeconds = 30

	DataWriterSyncTimers = make([]int64, 200) // assuming max datastore type as 200

	DataAggregatorSyncTimerSeconds int64

	LogLevelResetTimerSeconds = 15 * 60 // by default 15 min

	MaxWorkerEventKeyGroupLength = 64

	MaxWorkerEventKeys = 8192

	MaxSparklineTicks = 48

	MaxAIOpsTicks = 250

	MaxLineChartTicks = 2000

	MaxBarChartTicks = 24

	MaxDrillDownQueryKeyLength = MaxWorkerEventKeyGroupLength

	MaxDrillDownEvents = 5

	LogRetentionJobDays = 7

	TokenizerLength = 30

	LocalDatastore bool

	SystemBootSequence string

	EventAggregatorPoolLength int

	EventAggregatorPoolSize int

	MetricAggregatorPoolSize int

	MetricExecutorPoolLength int

	FlowExecutorPoolLength int

	LogExecutorPoolLength int

	DrillDownExecutorPoolLength int

	AIOpsExecutorPoolLength int

	MetricWorkerPoolLength int

	FlowWorkerPoolLength int

	LogWorkerPoolLength int

	SyncJobPoolLength int

	CleanupJobPoolLength int

	MaxVerticalWriteCacheEntries = 500

	MaxDataAggregationGroups int

	mappingCacheStores MotadataMap

	walStoreSizes map[DatastoreType]int

	CacheStores map[DatastoreType]struct{}

	DatastoreMotaOpsServiceEnabled = false

	RegistrationId = Empty

	InstallationMode = Empty

	SocketConnectionWatcherTime = time.Second * 30

	MaxSocketIdleConnectionTime = time.Second * 150

	SubscriberRCVTimeout = time.Second * 5

	AggregationViewIndexColumnLimit = 8

	AggregationViewAggregationColumnLimit = 10

	BlobEncodingBytes = 400

	MaxWriterCacheValues = 32 // in memory cache hold value size

	QueryCreationAgeThresholdSeconds = 120

	QueryStatsLogging bool
)

var ( //config goroutines
	DiskIOWorkers int

	IOCPWorkers int

	QueryExecutors int

	QueryWorkers int

	Workers int

	MetricQueryWorkers int

	FlowQueryWorkers int

	LogQueryWorkers int

	AIOpsEngineQueryWorkers int

	DrillDownQueryWorkers int

	MetricQueryExecutors int

	FlowQueryExecutors int

	LogQueryExecutors int

	AIOpsEngineQueryExecutors int

	DrillDownQueryExecutors int

	DataWriters int

	IndexWriters int

	VerticalWriters int

	HorizontalWriters int

	HealthMetricWriters int

	StaticMetricWriters int

	AggregationJobs int

	DataAggregators int

	MetricAggregators int

	EventAggregators int

	StoreCleanUpJobs int

	StoreSyncJobs int

	MappingCleanupJobs int

	CleanupThresholdSeconds int64 = 2 * 60 * 60

	IdleStoreDetectionThresholdSeconds int64 = 2 * 60 * 60

	DatastoreFlushTimerSeconds int

	DatastoreBrokerWriterFlushTimerSeconds int

	MaxCurrentDayEventFileQueueSize int

	MaxPreviousDayEventFileQueueSize int

	HealthMetricFlushTimerSeconds int

	DataWriterTxnBufferBytes int

	DataWriterValueBufferBytes int

	DataWriterSyncMaxTimerSeconds = int64(150)

	DataWriterSyncMinTimerSeconds = int64(45)

	ObjectFlushTimerSeconds int
)

var (
	MappingCleanupColumns = map[string][]DatastoreType{

		"source.ip":         {Flow, FlowAggregation},
		"source.port":       {Flow, FlowAggregation},
		"destination.ip":    {Flow, FlowAggregation},
		"destination.port":  {Flow, FlowAggregation},
		"policy.trigger.id": {PolicyResult},
	}

	DefaultColumns = MotadataMap{
		FloatingColumns: MotadataMap{},
		GarbageColumns:  MotadataMap{},
		IndexableColumns: MotadataMap{

			"500001-trap": MotadataMap{

				EventSource:     "",
				"trap.oid":      "",
				"trap.vendor":   "",
				"trap.severity": "",
			},
			"500000-flow": MotadataMap{
				EventSource:            "",
				"protocol":             "",
				"destination.as":       "",
				"source.as":            "",
				"source.threat":        "",
				"destination.threat":   "",
				"destination.ip":       "",
				"source.ip":            "",
				"source.if.index":      "",
				"destination.if.index": "",
				"application":          "",
				"tcp.flags":            "",
				"source.port":          "",
				"destination.port":     "",
				"tos":                  "",
				"source.country":       "",
				"source.city":          "",
				"destination.country":  "",
				"destination.city":     "",
				"source.aso":           "",
				"destination.aso":      "",
				"destination.ip.as":    "",
				"source.domain":        "",
				"destination.domain":   "",
				"source.isp":           "",
				"destination.isp":      "",
				"user":                 "",
			},
			"500002-audit": MotadataMap{
				EventSource:       "",
				"audit.module":    "",
				"audit.operation": "",
				"audit.user":      "",
				"audit.status":    "",
				"audit.remote.ip": "",
			},
			"500003-policy": MotadataMap{
				ObjectId:          "",
				PolicyType:        "",
				PolicySeverity:    "",
				PolicyId:          "",
				"object.category": "",
				"instance":        "",
				"metric":          "",
				EventSource:       "",
			},
			"500004-policy": MotadataMap{
				PolicyType:     "",
				PolicySeverity: "",
				PolicyId:       "",
				EventSource:    "",
				EventField:     "",
			},
			"500005-policy": MotadataMap{
				PolicyType:     "",
				PolicySeverity: "",
				PolicyId:       "",
				EventSource:    "",
				EventField:     "",
			},
			"500010-policy": MotadataMap{
				EventField:     "",
				EventSource:    "",
				PolicyId:       "",
				PolicyType:     "",
				PolicySeverity: "",
			},
			"500014-health.metric": MotadataMap{
				EventSource:       "",
				"engine.category": "",
				"engine.type":     "",
			},
			"500015-health.metric": MotadataMap{
				EventSource: "",
			},
			"500016-health.metric": MotadataMap{
				EventSource: "",
			},
			"500020-health.metric": MotadataMap{
				EventSource: "",
			},
			"500021-health.metric": MotadataMap{
				EventSource:   "",
				"jvm.gc.name": "",
			},
			"500007-policy.result": MotadataMap{
				"policy.trigger.id": "",
				PolicySeverity:      "",
			},
			"500008-notification": MotadataMap{
				EventSource:                  "",
				"user.notification.type":     "",
				"user.notification.severity": "",
			},
			"490000-correlated.metric": MotadataMap{
				EventSource: "",
			},
			"499998-policy.flap": MotadataMap{
				ObjectId:       "",
				PolicySeverity: "",
				PolicyId:       "",
				"instance":     "",
				EventSource:    "",
			},
			"500009-other": MotadataMap{
				EventSource:        "",
				EventSourceType:    "",
				"event.pattern.id": "",
				EventCategory:      "",
				EventSeverity:      "",
			},
			"500011-log.stat": MotadataMap{
				EventSource:     "",
				EventSourceType: "",
				EventCategory:   "",
			},
			"500018-flow": MotadataMap{
				EventSource: "",
			},
			"500019-config": MotadataMap{
				EventSource: "",
			},
			"500022-runbook.worklog": MotadataMap{
				EventSource:          "",
				"runbook.worklog.id": "",
				RunbookWorklogType:   "",
				ObjectId:             "",
				PolicyId:             "",
				"instance":           "",
				NetRouteId:           "",
			},
			"500023-compliance": MotadataMap{
				"compliance.policy.id": "",
			},
			"499999-event.history": MotadataMap{
				EventSource:        "",
				EventSourceType:    "",
				"event.pattern.id": "",
				EventCategory:      "",
				EventSeverity:      "",
			},
			"500027-policy": MotadataMap{
				NetRouteId:     "",
				PolicyId:       "",
				PolicySeverity: "",
				PolicyType:     "",
				EventField:     "",
			},
			"500028-policy.flap": MotadataMap{
				NetRouteId:     "",
				PolicyId:       "",
				PolicySeverity: "",
			},
		},
		SearchableColumns: MotadataMap{
			"message":   struct{}{},
			"interface": struct{}{},
		},
		ColumnEncoders: MotadataMap{
			"audit.message":             4,
			"message":                   4,
			"user.notification.message": 4,
			TrapMessage:                 4,
			"trap.raw.message":          4,
			"policy.message":            4,
			"event":                     4,
			"config.operation.output":   4,
			"runbook.worklog.result":    4,
			"runbook.worklog.error":     4,
			"config.event":              4,
		},
		BlobColumns: MotadataMap{
			Message:                   struct{}{},
			TrapMessage:               struct{}{},
			TrapRawMessage:            struct{}{},
			AuditMessage:              struct{}{},
			"policy.message":          struct{}{},
			Event:                     struct{}{},
			UserNotificationMessage:   struct{}{},
			"config.operation.output": struct{}{},
			"config.event":            struct{}{},
			"runbook.worklog.result":  struct{}{},
			"runbook.worklog.error":   struct{}{},
		},
	}

	defaultHorizontalAggregationColumns = MotadataMap{
		"499998-policy.flap": MotadataMap{
			"499998-policy.flap@@@0": MotadataMap{
				IndexableColumns: MotadataMap{
					PolicySeverity: struct{}{},
					PolicyId:       struct{}{},
					ObjectId:       struct{}{},
					"instance":     struct{}{},
				},
				PolicySeverity: 0,
				"type":         8,
			},
		},
		"499999-event.history": MotadataMap{
			"499999-event.history@@@0": MotadataMap{
				IndexableColumns: MotadataMap{
					EventSource:     struct{}{},
					EventCategory:   struct{}{},
					EventSourceType: struct{}{},
					EventSeverity:   struct{}{},
				},
				Message: 0,
				"type":  11,
			},
		},
		"500001-trap": MotadataMap{
			"500001-trap@@@0": MotadataMap{
				IndexableColumns: MotadataMap{
					"trap.severity": struct{}{},
					"trap.oid":      struct{}{},
					EventSource:     struct{}{},
				},
				TrapMessage: 0,
				"type":      13,
			},
		},
		"500003-policy": MotadataMap{
			"500003-policy@@@0": MotadataMap{
				IndexableColumns: MotadataMap{
					ObjectId:       struct{}{},
					"instance":     struct{}{},
					PolicySeverity: struct{}{},
					PolicyId:       struct{}{},
				},
				PolicySeverity: 0,
				"type":         14,
			},
		},
		"500004-policy": MotadataMap{
			"500004-policy@@@0": MotadataMap{
				IndexableColumns: MotadataMap{
					PolicySeverity: struct{}{},
					PolicyId:       struct{}{},
					PolicyType:     struct{}{},
					EventSource:    struct{}{},
				},
				PolicySeverity: 0,
				"type":         15,
			},
		},
		"500005-policy": MotadataMap{
			"500005-policy@@@0": MotadataMap{
				IndexableColumns: MotadataMap{
					PolicySeverity: struct{}{},
					PolicyId:       struct{}{},
					PolicyType:     struct{}{},
					EventSource:    struct{}{},
				},
				PolicySeverity: 0,
				"type":         15,
			},
		},
		"500010-policy": MotadataMap{
			"500010-policy@@@0": MotadataMap{
				IndexableColumns: MotadataMap{
					PolicySeverity: struct{}{},
					PolicyId:       struct{}{},
					PolicyType:     struct{}{},
					EventSource:    struct{}{},
				},
				PolicySeverity: 0,
				"type":         15,
			},
		},
		"500011-log.stat": MotadataMap{
			"500011-log.stat@@@0": MotadataMap{
				IndexableColumns: MotadataMap{
					EventSource:     struct{}{},
					EventCategory:   struct{}{},
					EventSourceType: struct{}{},
				},
				"log.volume.bytes":         1,
				"log.volume.bytes.per.sec": 1,
				"logs.per.sec":             1,
				"type":                     11,
			},
		},
		"500018-flow": MotadataMap{
			"500018-flow@@@0": MotadataMap{
				IndexableColumns: MotadataMap{
					EventSource: struct{}{},
				},
				"flow.volume.bytes":         1,
				"flow.volume.bytes.per.sec": 1,
				"flows.per.sec":             1,
				"type":                      12,
			},
		},
		"500027-policy": MotadataMap{
			"500027-policy@@@0": MotadataMap{
				IndexableColumns: MotadataMap{
					NetRouteId:     struct{}{},
					PolicyId:       struct{}{},
					PolicySeverity: struct{}{},
					PolicyType:     struct{}{},
				},
				PolicySeverity: 0,
				"type":         15,
			},
		},
		"500028-policy.flap": MotadataMap{
			"500028-policy.flap@@@0": MotadataMap{
				IndexableColumns: MotadataMap{
					NetRouteId:     struct{}{},
					PolicyId:       struct{}{},
					PolicySeverity: struct{}{},
					PolicyType:     struct{}{},
				},
				PolicySeverity: 0,
				"type":         8,
			},
		},
	}

	DefaultVerticalAggregations = MotadataMap{
		"apache.active.requests":                                 true,
		"apache.busy.workers":                                    true,
		"apache.idle.workers":                                    true,
		"apache.requests.per.sec":                                true,
		"apache.traffic.bytes.per.sec":                           true,
		"apache.traffic.volume.bytes":                            true,
		"aruba.wireless.access.point.interface~bytes.per.sec":    true,
		"aruba.wireless.access.point.interface~received.bytes":   true,
		"aruba.wireless.access.point.interface~sent.bytes":       true,
		"aruba.wireless.access.points":                           true,
		"aruba.wireless.access.point~clients":                    true,
		"aruba.wireless.access.point~ip.address":                 true,
		"aruba.wireless.access.point~location":                   true,
		"aruba.wireless.access.point~model":                      true,
		"aruba.wireless.access.point~started.time":               true,
		"aruba.wireless.clients":                                 true,
		"aruba.wireless.client~ap":                               true,
		"aruba.wireless.client~auth.method":                      true,
		"aruba.wireless.client~channel":                          true,
		"aruba.wireless.client~ip.address":                       true,
		"aruba.wireless.client~os.type":                          true,
		"aruba.wireless.client~signal.strength.dbm":              true,
		"aruba.wireless.client~started.time.seconds":             true,
		"aruba.wireless.client~traffic.bytes":                    true,
		"aruba.wireless.client~traffic.received.bytes":           true,
		"aruba.wireless.client~traffic.sent.bytes":               true,
		"aruba.wireless.client~username":                         true,
		"aruba.wireless.client~wlan":                             true,
		"aruba.wireless.controller.cpu.percent":                  true,
		"aruba.wireless.controller.memory.used.percent":          true,
		"aruba.wireless.rogue.access.points":                     true,
		"aruba.wireless.rogue.access.point~channel":              true,
		"aruba.wireless.rogue.clients":                           true,
		"aruba.wireless.wlan~access.points":                      true,
		"aruba.wireless.wlan~bytes.per.sec":                      true,
		"aruba.wireless.wlan~clients":                            true,
		"aws.autoscaling.group.desired.capacity":                 true,
		"aws.autoscaling.group.in.service.instances":             true,
		"aws.autoscaling.group.instances":                        true,
		"aws.autoscaling.group.max.size":                         true,
		"aws.autoscaling.group.min.size":                         true,
		"aws.autoscaling.group.pending.capacity.units":           true,
		"aws.autoscaling.group.standby.capacity.units":           true,
		"aws.autoscaling.group.standby.instances":                true,
		"aws.autoscaling.group.terminating.instances":            true,
		"aws.autoscaling.groups":                                 true,
		"aws.availability.zone":                                  true,
		"aws.billing.service.cost":                               true,
		"aws.billing.usage.forecast":                             true,
		"aws.dynamodb.index":                                     true,
		"aws.dynamodb.provisioned.reads.per.sec":                 true,
		"aws.dynamodb.provisioned.table.read.used.percent":       true,
		"aws.dynamodb.provisioned.table.write.used.percent":      true,
		"aws.dynamodb.provisioned.writes.per.sec":                true,
		"aws.dynamodb.read.throttled.requests":                   true,
		"aws.dynamodb.throttled.requests":                        true,
		"aws.dynamodb.write.throttled.requests":                  true,
		"aws.dynamodb~table.size.bytes":                          true,
		"aws.ebs.volume.avg.read.latency.ms":                     true,
		"aws.ebs.volume.avg.write.latency.ms":                    true,
		"aws.ebs.volume.burst.balance.percent":                   true,
		"aws.ebs.volume.idle.time.percent":                       true,
		"aws.ebs.volume.queue.length":                            true,
		"aws.ebs.volume.read.bytes.per.sec":                      true,
		"aws.ebs.volume.read.ops.per.sec":                        true,
		"aws.ebs.volume.snapshot.id":                             true,
		"aws.ebs.volume.write.bytes.per.sec":                     true,
		"aws.ebs.volume.write.ops.per.sec":                       true,
		"aws.ebs.volumes":                                        true,
		"aws.ebs~volume.attachment.status":                       true,
		"aws.ebs~volume.size.bytes":                              true,
		"aws.ebs~volume.type":                                    true,
		"aws.ec2.cpu.credit.balance":                             true,
		"aws.ec2.cpu.credit.usage":                               true,
		"aws.ec2.cpu.percent":                                    true,
		"aws.ec2.disk.io.read.bytes.per.sec":                     true,
		"aws.ec2.disk.io.read.ops.per.sec":                       true,
		"aws.ec2.disk.io.write.bytes.per.sec":                    true,
		"aws.ec2.disk.io.write.ops.per.sec":                      true,
		"aws.ec2.instances":                                      true,
		"aws.ec2.network.bytes.per.sec":                          true,
		"aws.ec2.network.in.bytes.per.sec":                       true,
		"aws.ec2.network.out.bytes.per.sec":                      true,
		"aws.elasticbeanstalk.application.2xx.requests":          true,
		"aws.elasticbeanstalk.application.3xx.requests":          true,
		"aws.elasticbeanstalk.application.4xx.requests":          true,
		"aws.elasticbeanstalk.application.5xx.requests":          true,
		"aws.elasticbeanstalk.application.p10.latency.seconds":   true,
		"aws.elasticbeanstalk.application.p50.latency.seconds":   true,
		"aws.elasticbeanstalk.application.p75.latency.seconds":   true,
		"aws.elasticbeanstalk.application.p90.latency.seconds":   true,
		"aws.elasticbeanstalk.application.p99.latency.seconds":   true,
		"aws.elasticbeanstalk.application.requests":              true,
		"aws.elasticbeanstalk.cpu.idle.percent":                  true,
		"aws.elasticbeanstalk.cpu.system.percent":                true,
		"aws.elasticbeanstalk.cpu.user.percent":                  true,
		"aws.elasticbeanstalk.degraded.instances":                true,
		"aws.elasticbeanstalk.environment.health":                true,
		"aws.elasticbeanstalk.environments":                      true,
		"aws.elasticbeanstalk.info.instances":                    true,
		"aws.elasticbeanstalk.load.avg1.min":                     true,
		"aws.elasticbeanstalk.nodata.instances":                  true,
		"aws.elasticbeanstalk.ok.instances":                      true,
		"aws.elasticbeanstalk.pending.instances":                 true,
		"aws.elasticbeanstalk.severe.instances":                  true,
		"aws.elasticbeanstalk.unknown.instances":                 true,
		"aws.elasticbeanstalk.warning.instances":                 true,
		"aws.elb":                                                true,
		"aws.elb.4xx.responses":                                  true,
		"aws.elb.active.connections":                             true,
		"aws.elb.active.flows":                                   true,
		"aws.elb.consumed.lcus":                                  true,
		"aws.elb.healthy.hosts":                                  true,
		"aws.elb.processed.bytes":                                true,
		"aws.elb.requests.per.sec":                               true,
		"aws.elb.type":                                           true,
		"aws.elb.unhealthy.hosts":                                true,
		"aws.lambda.concurrent.executions":                       true,
		"aws.lambda.duration.ms":                                 true,
		"aws.lambda.errors":                                      true,
		"aws.lambda.functions":                                   true,
		"aws.lambda.invocations":                                 true,
		"aws.lambda.memory.size.bytes":                           true,
		"aws.lambda.throttles":                                   true,
		"aws.rds.cpu.percent":                                    true,
		"aws.rds.database.connections":                           true,
		"aws.rds.disk.io.ops.per.sec":                            true,
		"aws.rds.disk.io.read.latency.ms":                        true,
		"aws.rds.disk.io.read.ops.per.sec":                       true,
		"aws.rds.disk.io.write.latency.ms":                       true,
		"aws.rds.disk.io.write.ops.per.sec":                      true,
		"aws.rds.engine":                                         true,
		"aws.rds.instances":                                      true,
		"aws.rds.memory.free.bytes":                              true,
		"aws.rds.network.traffic.bytes.per.sec":                  true,
		"aws.rds.storage.free.bytes":                             true,
		"aws.rds.swap.memory.used.bytes":                         true,
		"aws.running.ec2.instances":                              true,
		"aws.s3.bucket.bytes":                                    true,
		"aws.s3.bucket.downloaded.bytes":                         true,
		"aws.s3.bucket.first.byte.latency.ms":                    true,
		"aws.s3.bucket.http.4xx.errors":                          true,
		"aws.s3.bucket.http.5xx.errors":                          true,
		"aws.s3.bucket.list.requests":                            true,
		"aws.s3.bucket.objects":                                  true,
		"aws.s3.bucket.request.latency.ms":                       true,
		"aws.s3.bucket.requests":                                 true,
		"aws.sns.delivered.notifications":                        true,
		"aws.sns.failed.notifications":                           true,
		"aws.sns.published.bytes":                                true,
		"aws.sns.published.messages.per.sec":                     true,
		"aws.sns.sms.usage.cost":                                 true,
		"aws.sns.subscriptions":                                  true,
		"aws.sns.topic":                                          true,
		"aws.sns.topics":                                         true,
		"aws.sqs":                                                true,
		"aws.sqs.delayed.messages":                               true,
		"aws.sqs.deleted.messages":                               true,
		"aws.sqs.empty.messages":                                 true,
		"aws.sqs.hidden.messages":                                true,
		"aws.sqs.oldest.message.age.seconds":                     true,
		"aws.sqs.queues":                                         true,
		"aws.sqs.received.messages":                              true,
		"aws.sqs.sent.bytes":                                     true,
		"aws.sqs.sent.messages":                                  true,
		"aws.sqs.visible.messages":                               true,
		"aws.stopped.ec2.instances":                              true,
		"azure.application.gateway.active.connections":           true,
		"azure.application.gateway.failed.requests":              true,
		"azure.application.gateway.healthy.hosts":                true,
		"azure.application.gateway.instances":                    true,
		"azure.application.gateway.requests.per.sec":             true,
		"azure.application.gateway.response.status":              true,
		"azure.application.gateway.throughput.bytes.per.sec":     true,
		"azure.application.gateway.unhealthy.hosts":              true,
		"azure.billing.amount":                                   true,
		"azure.billing.service~usage.amount":                     true,
		"azure.cdn.byte.hit.ratio.percent":                       true,
		"azure.cdn.endpoints":                                    true,
		"azure.cdn.latency.ms":                                   true,
		"azure.cdn.requests":                                     true,
		"azure.cdn.response.bytes.per.sec":                       true,
		"azure.cosmos.db.available.storage.bytes":                true,
		"azure.cosmos.db.data.usage.bytes":                       true,
		"azure.cosmos.db.documents":                              true,
		"azure.cosmos.db.index.usage.bytes":                      true,
		"azure.cosmos.db.provisioned.throughput":                 true,
		"azure.cosmos.db.request.units":                          true,
		"azure.cosmos.db.requests.per.sec":                       true,
		"azure.cosmos.db.service.availability.percent":           true,
		"azure.cosmosdb.instances":                               true,
		"azure.function.5xx.requests":                            true,
		"azure.function.connections":                             true,
		"azure.function.memory.used.bytes":                       true,
		"azure.function.private.bytes":                           true,
		"azure.function.read.ops.per.sec":                        true,
		"azure.function.request.queued.requests":                 true,
		"azure.function.sent.bytes":                              true,
		"azure.function.write.ops.per.sec":                       true,
		"azure.functions":                                        true,
		"azure.loadbalancer.bytes.per.sec":                       true,
		"azure.loadbalancer.dip.availability":                    true,
		"azure.loadbalancer.instances":                           true,
		"azure.loadbalancer.packets.per.sec":                     true,
		"azure.loadbalancer.snat.connections":                    true,
		"azure.loadbalancer.snat.ports":                          true,
		"azure.loadbalancer.syn.packets":                         true,
		"azure.loadbalancer.used.snat.ports":                     true,
		"azure.mysql.server.active.connections":                  true,
		"azure.mysql.server.cpu.percent":                         true,
		"azure.mysql.server.memory.percent":                      true,
		"azure.postgresql.server.active.connections":             true,
		"azure.postgresql.server.cpu.percent":                    true,
		"azure.postgresql.server.memory.percent":                 true,
		"azure.psb.active.connections":                           true,
		"azure.psb.closed.connections":                           true,
		"azure.psb.dead.lettered.messages":                       true,
		"azure.psb.incoming.requests":                            true,
		"azure.psb.opened.connections":                           true,
		"azure.psb.server.errors":                                true,
		"azure.psb.successful.requests":                          true,
		"azure.psb.throttled.requests":                           true,
		"azure.psb.user.errors":                                  true,
		"azure.service.queue~active.messages":                    true,
		"azure.service.queue~messages":                           true,
		"azure.sql.database.cpu.percent":                         true,
		"azure.sql.database.data.storage.used.bytes":             true,
		"azure.sql.database.failed.connections":                  true,
		"azure.sql.database.firewall.blocked.connections":        true,
		"azure.sql.database.in.memory.oltp.storage.used.percent": true,
		"azure.sql.database.session.utilization.percent":         true,
		"azure.sql.database.storage.size.bytes":                  true,
		"azure.sql.database.successful.connections":              true,
		"azure.sql.database.worker.utilization.percent":          true,
		"azure.sql.databases":                                    true,
		"azure.storage.accounts":                                 true,
		"azure.storage.blob.server.latency.ms":                   true,
		"azure.storage.blobs":                                    true,
		"azure.storage.containers":                               true,
		"azure.storage.fileshares":                               true,
		"azure.storage.queues":                                   true,
		"azure.storage.tables":                                   true,
		"azure.storage.transactions":                             true,
		"azure.vm.asp.net.application.active.requests":           true,
		"azure.vm.asp.net.running.applications":                  true,
		"azure.vm.cpu.consumed.credits":                          true,
		"azure.vm.cpu.percent":                                   true,
		"azure.vm.disk.io.ops.writes.per.sec":                    true,
		"azure.vm.disk.io.read.bytes":                            true,
		"azure.vm.disk.io.read.bytes.per.sec":                    true,
		"azure.vm.disk.io.write.bytes":                           true,
		"azure.vm.disk.io.write.bytes.per.sec":                   true,
		"azure.vm.network.in.bytes.per.sec":                      true,
		"azure.vm.network.out.bytes.per.sec":                     true,
		"azure.vm.network.received.bytes.per.sec":                true,
		"azure.vm.public.ip.address":                             true,
		"azure.vms":                                              true,
		"azure.vmscaleset.cpu.consumed.credits":                  true,
		"azure.vmscaleset.cpu.percent":                           true,
		"azure.vmscaleset.disk.queue.length":                     true,
		"azure.vmscaleset.disk.read.bytes.per.sec":               true,
		"azure.vmscaleset.disk.read.ops.per.sec":                 true,
		"azure.vmscaleset.disk.write.bytes.per.sec":              true,
		"azure.vmscaleset.disk.write.ops.per.sec":                true,
		"azure.vmscaleset.inbound.flows":                         true,
		"azure.vmscaleset.instances":                             true,
		"azure.vmscaleset.maximum.inbound.flows.per.sec":         true,
		"azure.vmscaleset.maximum.outbound.flows.per.sec":        true,
		"azure.vmscaleset.network.in.bytes.per.sec":              true,
		"azure.vmscaleset.network.out.bytes.per.sec":             true,
		"azure.vmscaleset.outbound.flows":                        true,
		"azure.webapps":                                          true,
		"cisco.wireless.access.points":                           true,
		"cisco.wireless.access.point~admin.status":               true,
		"cisco.wireless.access.point~clients":                    true,
		"cisco.wireless.access.point~ip.address":                 true,
		"cisco.wireless.access.point~location":                   true,
		"cisco.wireless.access.point~model":                      true,
		"cisco.wireless.access.point~operational.status":         true,
		"cisco.wireless.clients":                                 true,
		"cisco.wireless.client~ap":                               true,
		"cisco.wireless.client~channel":                          true,
		"cisco.wireless.client~ip.address":                       true,
		"cisco.wireless.client~os.type":                          true,
		"cisco.wireless.client~signal.strength.dbm":              true,
		"cisco.wireless.client~snr":                              true,
		"cisco.wireless.client~started.time.seconds":             true,
		"cisco.wireless.client~status":                           true,
		"cisco.wireless.client~traffic.bytes":                    true,
		"cisco.wireless.client~traffic.received.bytes":           true,
		"cisco.wireless.client~traffic.sent.bytes":               true,
		"cisco.wireless.client~username":                         true,
		"cisco.wireless.client~wlan":                             true,
		"cisco.wireless.controller.cpu.percent":                  true,
		"cisco.wireless.controller.memory.used.percent":          true,
		"cisco.wireless.rogue.access.points":                     true,
		"cisco.wireless.rogue.access.point~channel":              true,
		"cisco.wireless.rogue.clients":                           true,
		"cisco.wireless.wlan~clients":                            true,
		"cisco.wireless.wlan~id":                                 true,
		"cisco.wireless.wlan~status":                             true,
		"citrix.xen.cpu.percent":                                 true,
		"citrix.xen.disk.used.percent":                           true,
		"citrix.xen.memory.used.percent":                         true,
		"citrix.xen.network.bytes.per.sec":                       true,
		"citrix.xen.network.interface~bytes.per.sec":             true,
		"citrix.xen.os.version":                                  true,
		"citrix.xen.virtual.machines":                            true,
		"citrix.xen.vm~cpu.percent":                              true,
		"citrix.xen.vm~disk.capacity.bytes":                      true,
		"citrix.xen.vm~disk.io.avg.read.latency.ms":              true,
		"citrix.xen.vm~disk.io.avg.write.latency.ms":             true,
		"citrix.xen.vm~disk.io.write.bytes.per.sec":              true,
		"citrix.xen.vm~memory.used.percent":                      true,
		"citrix.xen.vm~network.bytes.per.sec":                    true,
		"esxi.cpu.percent":                                       true,
		"esxi.datastore~free.bytes":                              true,
		"esxi.datastore~write.ops.per.sec":                       true,
		"esxi.disk.io.bytes.per.sec":                             true,
		"esxi.disk.used.percent":                                 true,
		"esxi.disk~latency.ms":                                   true,
		"esxi.memory.used.percent":                               true,
		"esxi.network.interface~bytes.per.sec":                   true,
		"esxi.os.version":                                        true,
		"esxi.sensor~health":                                     true,
		"esxi.sensor~type":                                       true,
		"esxi.storage.adapter~write.ops.per.sec":                 true,
		"esxi.virtual.machines":                                  true,
		"esxi.vm~cpu.percent":                                    true,
		"esxi.vm~disk.io.bytes.per.sec":                          true,
		"esxi.vm~disk.used.percent":                              true,
		"esxi.vm~memory.used.percent":                            true,
		"esxi.vm~network.bytes.per.sec":                          true,
		"hyperv.cluster.cpu.cores":                               true,
		"hyperv.cluster.disk.free.percent":                       true,
		"hyperv.cluster.nodes":                                   true,
		"hyperv.cluster.node~memory.used.percent":                true,
		"iis.404.errors":                                         true,
		"iis.active.connections":                                 true,
		"iis.connection.attempts":                                true,
		"iis.current.requests":                                   true,
		"iis.delete.requests":                                    true,
		"iis.get.requests":                                       true,
		"iis.head.requests":                                      true,
		"iis.logon.attempts":                                     true,
		"iis.options.requests":                                   true,
		"iis.post.requests":                                      true,
		"iis.put.requests":                                       true,
		"iis.queued.requests":                                    true,
		"iis.received.bytes":                                     true,
		"iis.received.files":                                     true,
		"iis.rejected.requests":                                  true,
		"iis.sent.bytes":                                         true,
		"iis.sent.files":                                         true,
		"iis.trace.requests":                                     true,
		"iis.transferred.files":                                  true,
		"interface~admin.status":                                 true,
		"interface~alias":                                        true,
		"interface~address":                                      true,
		"interface~last.change":                                  true,
		"interface~instance.name":                                true,
		"interface~description":                                  true,
		"interface~link.type":                                    true,
		"interface~discard.packets":                              true,
		"interface~downtime.percent":                             true,
		"interface~downtime.seconds":                             true,
		"interface~error.packets":                                true,
		"interface~in.packets":                                   true,
		"interface~in.traffic.bytes.per.sec":                     true,
		"interface~in.traffic.utilization.percent":               true,
		"interface~index":                                        true,
		"interface~ip.address":                                   true,
		"interface~name":                                         true,
		"interface~operational.status":                           true,
		"interface~out.packets":                                  true,
		"interface~out.traffic.bytes.per.sec":                    true,
		"interface~out.traffic.utilization.percent":              true,
		"interface~packets":                                      true,
		"interface~received.discard.packets":                     true,
		"interface~received.error.packets":                       true,
		"interface~sent.discard.packets":                         true,
		"interface~sent.error.packets":                           true,
		"interface~status":                                       true,
		"interface~traffic.bytes.per.sec":                        true,
		"interface~traffic.utilization.percent":                  true,
		"interface~type":                                         true,
		"interface~uptime.percent":                               true,
		"interface~uptime.seconds":                               true,
		"interface~disabletime.percent":                          true,
		"interface~disabletime.seconds":                          true,
		"interface~unknowntime.percent":                          true,
		"interface~unknowntime.seconds":                          true,
		"interface~suspendtime.percent":                          true,
		"interface~suspendtime.seconds":                          true,
		"interface~unreachabletime.percent":                      true,
		"interface~unreachabletime.seconds":                      true,
		"interface~maintenancetime.percent":                      true,
		"interface~maintenancetime.seconds":                      true,
		"monitor.disabletime.percent":                            true,
		"monitor.disabletime.seconds":                            true,
		"monitor.downtime.percent":                               true,
		"monitor.downtime.seconds":                               true,
		"monitor.maintenancetime.percent":                        true,
		"monitor.maintenancetime.seconds":                        true,
		"monitor.unknowntime.percent":                            true,
		"monitor.unknowntime.seconds":                            true,
		"monitor.uptime.percent":                                 true,
		"monitor.uptime.seconds":                                 true,
		"monitor.suspendtime.percent":                            true,
		"monitor.suspendtime.seconds":                            true,
		"monitor.unreachabletime.percent":                        true,
		"monitor.unreachabletime.seconds":                        true,
		"mysql.aborted.clients":                                  true,
		"mysql.aborted.connections":                              true,
		"mysql.cached.threads":                                   true,
		"mysql.connected.threads":                                true,
		"mysql.connections":                                      true,
		"mysql.created.threads":                                  true,
		"mysql.delayed.insert.threads":                           true,
		"mysql.delete.commands.per.sec":                          true,
		"mysql.insert.commands.per.sec":                          true,
		"mysql.key.reads.per.sec":                                true,
		"mysql.key.writes.per.sec":                               true,
		"mysql.open.files":                                       true,
		"mysql.open.streams":                                     true,
		"mysql.open.tables":                                      true,
		"mysql.query.cache.hit.ratio.percent":                    true,
		"mysql.questions":                                        true,
		"mysql.received.bytes.per.sec":                           true,
		"mysql.running.threads":                                  true,
		"mysql.select.commands.per.sec":                          true,
		"mysql.sent.bytes.per.sec":                               true,
		"mysql.slow.launch.threads":                              true,
		"mysql.slow.queries":                                     true,
		"mysql.update.commands.per.sec":                          true,
		"ping.latency.ms":                                        true,
		"ping.max.latency.ms":                                    true,
		"ping.min.latency.ms":                                    true,
		"ping.packet.lost.percent":                               true,
		"ping.received.packets":                                  true,
		"ping.sent.packets":                                      true,
		"system.description":                                     true,
		"system.cpu.percent":                                     true,
		"system.cpu.user.percent":                                true,
		"system.disk.io.ops.per.sec":                             true,
		"system.disk.io.read.ops.per.sec":                        true,
		"system.disk.io.write.ops.per.sec":                       true,
		"system.disk.used.percent":                               true,
		"system.disk.volume~free.bytes":                          true,
		"system.disk.volume~free.percent":                        true,
		"system.disk~ops.per.sec":                                true,
		"system.memory.used.bytes":                               true,
		"system.memory.used.percent":                             true,
		"system.model":                                           true,
		"system.network.bytes.per.sec":                           true,
		"system.network.interface~bytes.per.sec":                 true,
		"system.network.interface~dropped.packets":               true,
		"system.network.interface~error.packets":                 true,
		"system.network.interface~in.packets.per.sec":            true,
		"system.network.interface~out.packets.per.sec":           true,
		"system.os.name":                                         true,
		"system.os.version":                                      true,
		"system.process~cpu.percent":                             true,
		"system.process~downtime.percent":                        true,
		"system.process~downtime.seconds":                        true,
		"system.process~handles":                                 true,
		"system.process~id":                                      true,
		"system.process~instance.name":                           true,
		"system.process~command":                                 true,
		"system.process~io.bytes.per.sec":                        true,
		"system.process~started.time":                            true,
		"system.process~started.time.seconds":                    true,
		"system.process~io.ops.per.sec":                          true,
		"system.process~memory.used.bytes":                       true,
		"system.process~user":                                    true,
		"system.process~name":                                    true,
		"system.process~status":                                  true,
		"system.process~state":                                   true,
		"system.process~threads":                                 true,
		"system.process~uptime.percent":                          true,
		"system.process~uptime.seconds":                          true,
		"system.process~virtual.memory.bytes":                    true,
		"system.serial.number":                                   true,
		"system.service~status":                                  true,
		"system.vendor":                                          true,
		"total.interfaces":                                       true,
		"total.vms":                                              true,
		"vlan~name":                                              true,
		"vlan~port":                                              true,
		"nutanix.cpu.percent":                                    true,
		"nutanix.memory.used.percent":                            true,
		"nutanix.cluster":                                        true,
		"prism.clusters":                                         true,
		"prism.hosts":                                            true,
		"prism.vms":                                              true,
		"prism.storage.containers":                               true,
		"prism.disks":                                            true,
		"prism.volume.group~disks":                               true,
		"prism.cluster~hypervisor.memory.used.percent":           true,
		"prism.cluster~hypervisor.cpu.used.percent":              true,
		"prism.cluster~io.latency.ms":                            true,
		"nutanix.io.latency.ms":                                  true,
		"nutanix.vm~cpu.percent":                                 true,
		"nutanix.vm~ip":                                          true,
		"nutanix.vm~memory.used.percent":                         true,
		"nutanix.vm~io.latency.ms":                               true,
		"ipsla~operation.type":                                   true,
		"ipsla~status":                                           true,
		"ipsla~latency.ms":                                       true,
		"cisco.vmanage.cpu.percent":                              true,
		"cisco.vmanage.memory.used.percent":                      true,
		"cisco.vbond.cpu.percent":                                true,
		"cisco.vbond.memory.used.percent":                        true,
		"cisco.vsmart.cpu.percent":                               true,
		"cisco.vsmart.memory.used.percent":                       true,
		"cisco.vedge.cpu.percent":                                true,
		"cisco.vedge.memory.used.percent":                        true,
		"cisco.meraki.cpu.percent":                               true,
		"cisco.meraki.memory.used.percent":                       true,
		"cisco.meraki.switch.cpu.percent":                        true,
		"cisco.meraki.switch.memory.used.percent":                true,
		"cisco.meraki.security.cpu.percent":                      true,
		"cisco.meraki.security.memory.used.percent":              true,
		"cisco.meraki.radio.cpu.percent":                         true,
		"cisco.meraki.radio.memory.used.percent":                 true,
		"netroute.disabletime.percent":                           true,
		"netroute.disabletime.seconds":                           true,
		"netroute.downtime.percent":                              true,
		"netroute.downtime.seconds":                              true,
		"netroute.maintenancetime.percent":                       true,
		"netroute.maintenancetime.seconds":                       true,
		"netroute.unknowntime.percent":                           true,
		"netroute.unknowntime.seconds":                           true,
		"netroute.uptime.percent":                                true,
		"netroute.uptime.seconds":                                true,
		"netroute.suspendtime.percent":                           true,
		"netroute.suspendtime.seconds":                           true,
		"netroute.unreachabletime.percent":                       true,
		"netroute.unreachabletime.seconds":                       true,
	}
)

// gc notifier const

var (
	gcStats debug.GCStats

	memoryStats runtime.MemStats

	gcNotifier *gcnotifier.GCNotifier

	gcLogger = NewLogger("GC Traces", "gc")

	gcTracer = atomic.Bool{}
)

//////////////////////////////// Configuration Methods /////////////////////////////////////////////

func InitConfigs(bytes []byte) bool {

	if (EnvironmentType != DatastoreTestEnvironment && EnvironmentType != DatastoreBenchIntegrationEnvironment && EnvironmentType != DatastoreBenchUnitEnvironment) && !ValidateDefaultConfigs() {

		return false

	}

	configs = make(MotadataMap)

	err := json.Unmarshal(bytes, &configs)

	if err != nil {

		return false
	}

	SetLogLevel(configs.GetIntValue(SystemLogLevel))

	if TraceEnabled() {

		go initGCTracer()
	}

	if configs.Contains(TaskLogging) {

		SetTaskLogging(configs.GetStringValue(TaskLogging))
	}

	//Task logging needs to reset when it is on from the starting of the datastore

	if DebugEnabled() || TraceEnabled() || QueryPlanLogging {

		go func() {

			_ = ResetLogLevel(LogLevelResetTimerSeconds)
		}()
	}

	LocalDatastore = strings.EqualFold(GetHost(), "localhost") || strings.EqualFold(GetHost(), "127.0.0.1")

	DeploymentType = GetDeploymentType()

	MaxPoolLength = GetMaxPoolLength()

	OverflowLength = MaxPoolLength - ((MaxPoolLength * 10) / 100)

	EventAggregatorPoolLength = GetEventAggregatorPoolLength()

	EventAggregatorPoolSize = GetEventAggregatorPoolSize()

	MetricAggregatorPoolSize = GetMetricAggregatorPoolSize()

	MaxStringBytes = GetMaxStringBytes()

	MetricExecutorPoolLength = GetMetricExecutorPoolLength()

	FlowExecutorPoolLength = GetFlowExecutorPoolLength()

	LogExecutorPoolLength = GetLogExecutorPoolLength()

	DrillDownExecutorPoolLength = GetDrillDownExecutorPoolLength()

	AIOpsExecutorPoolLength = GetAIOpsExecutorPoolLength()

	MetricWorkerPoolLength = GetMetricWorkerPoolLength()

	LogMaxPoolLength = GetLogOverflowLength()

	FlowWorkerPoolLength = GetFlowWorkerPoolLength()

	LogWorkerPoolLength = GetLogWorkerPoolLength()

	LogOverflowLength = LogMaxPoolLength - ((LogMaxPoolLength * 10) / 100)

	MaxValueBufferBytes = MaxPoolLength * 8

	MaxStringBytes = 50

	MaxBlobBytes = 254 * LogOverflowLength //max string chars 254*5000

	CleanupJobPoolLength = GetCleanupJobPoolLength()

	SyncJobPoolLength = GetSyncJobPoolLength()

	MaxCurrentDayEventFileQueueSize = 4

	MaxPreviousDayEventFileQueueSize = 2

	if SystemBootSequence == Datastore {

		InitQueryProcessors()

		MaxWorkerEvents = GetMaxWorkerEvents()

		MaxWorkerEventKeyGroupLength = GetMaxWorkerEventKeyGroupLength()

		MaxDrillDownQueryKeyLength = DrillDownQueryExecutors * MaxWorkerEventKeyGroupLength

		MaxAggregationGroups = GetMaxAggregationGroups()

		VerticalStoreProbeDays = GetVerticalAggregationPastProbeDays()

		HorizontalStoreProbeDays = GetHorizontalAggregationPastProbeDays()

		FulltextSearchingProbeDays = GetFullTextSearchingAggregationPastProbeDays()

		AggregationJobs = GetAggregationJobs()

		MetricAggregators = GetMetricAggregators()

		MaxHistoricalRecords = GetMaxHistoricalRecords()

		MaxStatusFlapHistoricalRecords = GetMaxStatusFlapHistoricalRecords()

		MaxSearchQueryRecords = GetMaxSearchQueryRecords()

		MaxGridRecords = GetMaxGridRecords()

		IndexWriters = GetIndexWriters()

		VerticalWriters = GetVerticalWriters()

		HealthMetricWriters = GetHealthMetricWriters()

		StaticMetricWriters = GetStaticMetricWriters()

		HorizontalWriters = GetHorizontalWriters()

		Aggregation = GetAggregationStatus()

		StoreSyncJobs = GetStoreSyncJobs()

		MappingCleanupJobs = GetMappingCleanupJobs()

		StoreCleanUpJobs = GetStoreCleanUpJobs()

		StoreSyncTimerSeconds = GetStoreSyncTimerSeconds()

		MaxIndexProbes = GetMaxIndexerProbes()

		mappingCacheStores = getMappingStoreCacheConfigs()

		DatastoreMotaOpsServiceEnabled = getMotaOpsServiceStatus()

		MaxCurrentDayEventFileQueueSize = GetDatastoreMaxCurrentDayEventFileQueueSize()

		MaxPreviousDayEventFileQueueSize = GetDatastoreMaxPreviousDayEventFileQueueSize()

		walStoreSizes = getStoreWALSizes()

		BlobEncodingBytes = GetBlobEncodingBytes()

		QueryCreationAgeThresholdSeconds = GetQueryCreationAgeThresholdSeconds()

		QueryStatsLogging = GetQueryStatsLogging()

		CacheStores = getCacheStores()

		ObjectFlushTimerSeconds = GetObjectFlushTimerSeconds()

	} else if SystemBootSequence == Broker {

		DataWriters = GetDataWriters()

		VerticalWriters = GetVerticalWriters()

		HorizontalWriters = GetHorizontalWriters()

		HealthMetricWriters = GetHealthMetricWriters()

		DataAggregators = GetDataAggregators()

		DataAggregatorSyncTimerSeconds = GetDataAggregatorSyncTimerSeconds()
	}

	setEventAggregationIntervals()

	setMetricAggregationIntervals()

	DataWriterTxnBufferBytes = GetDataWriterTxnBufferBytes()

	DataWriterValueBufferBytes = GetDataWriterValueBufferBytes()

	MaxDataAggregationGroups = GetDataAggregatorMaxGroupingLimit()

	MaxFlowTopNSelectionGroups = GetMaxFlowTopNSelectionGroups()

	MaxFlowTopNInsertionGroups = GetMaxFlowTopNInsertionGroups()

	AggregationViewIndexColumnLimit = GetAggregationViewIndexableColumnLimit()

	AggregationViewAggregationColumnLimit = GetAggregationViewAggregationColumnLimit()

	EventAggregators = GetEventAggregators()

	DatastoreFlushTimerSeconds = GetDatastoreFlushTimerSeconds()

	DatastoreBrokerWriterFlushTimerSeconds = GetDataWriterFlushTimerSeconds()

	if EnvironmentType != DatastoreBenchIntegrationEnvironment && EnvironmentType != DatastoreBenchUnitEnvironment {

		EnvironmentType = GetEnvironmentType()
	}

	VerticalAggregationSyncTimerSeconds = GetVerticalAggregationTimerSeconds()

	SetDataWriterSyncTimers()

	IndexerThresholdPercent = GetIndexerThresholdPercent()

	LogRetentionJobDays = GetLogRetentionDays()

	MaxVerticalWriteCacheEntries = GetMaxVerticalWriterCacheEntries()

	TokenizerLength = GetTokenizerLength()

	configLogger.Info(fmt.Sprintf("current datastore exe store variant  is %v", StoreVariant))

	MaxSocketIdleConnectionTime = GetSocketConnectionMaxIdleTimeSeconds()

	motadataConfigs, err := GetMotadataConfigs()

	HealthMetricFlushTimerSeconds = GetHealthMetricFlushTimerSeconds()

	if err != nil {

		configLogger.Error("failed to read motadata.json")

		return false

	} else {

		RegistrationId = motadataConfigs.GetStringValue("manager.id")

		InstallationMode = motadataConfigs.GetStringValue("installation.mode")

	}

	return true
}

func GetAggregationViewAggregationColumnLimit() int {

	if configs.Contains("datastore.aggregation.view.aggregation.column.limit") {

		return configs.GetIntValue("datastore.aggregation.view.aggregation.column.limit")
	}

	return 10
}

func GetAggregationViewIndexableColumnLimit() int {

	if configs.Contains("datastore.aggregation.view.indexable.column.limit") {

		return configs.GetIntValue("datastore.aggregation.view.indexable.column.limit")
	}

	return 8
}

func GetMaxVerticalWriterCacheEntries() int {

	if configs.Contains(DatastoreMaxVerticalWriterCacheEntries) {

		return configs.GetIntValue(DatastoreMaxVerticalWriterCacheEntries)
	}

	if DeploymentType < Medium {

		return 20000
	} else if DeploymentType == Medium {

		return 30000
	}

	return 50000
}

func GetDatastoreMaxCurrentDayEventFileQueueSize() (result int) {

	result = 100

	if configs.Contains(DatastoreMaxCurrentDayEventFileQueueSize) {

		result = configs.GetIntValue(DatastoreMaxCurrentDayEventFileQueueSize)
	}

	return
}

func GetDatastoreMaxPreviousDayEventFileQueueSize() (result int) {

	result = 50

	if configs.Contains(DatastoreMaxPreviousDayEventFileQueueSize) {

		result = configs.GetIntValue(DatastoreMaxPreviousDayEventFileQueueSize)
	}

	return
}

func GetDataWriterValueBufferBytes() int {

	if configs.Contains("datastore.data.writer.value.buffer.bytes") {

		return configs.GetIntValue("datastore.data.writer.value.buffer.bytes")
	}

	if DeploymentType == Large {

		return 10 * 1024 * 1024

	} else if DeploymentType == ExtraLarge {

		return 15 * 1024 * 1024
	}

	return 5 * 1024 * 1024
}

func GetDataWriterTxnBufferBytes() int {

	if configs.Contains("datastore.data.writer.txn.buffer.bytes") {

		return configs.GetIntValue("datastore.data.writer.txn.buffer.bytes")
	}

	return 2 * 1024 * 1024
}

func GetTokenizerLength() int {

	if configs.Contains("datastore.tokenizer.length") {

		return configs.GetIntValue("datastore.tokenizer.length")
	}

	return 30
}

func GetLogRetentionDays() (days int) {

	days = 2

	if configs.Contains(DatastoreLogRetentionDays) {

		days = configs.GetIntValue(DatastoreLogRetentionDays)

		if days <= 0 {

			days = 1
		}
	}

	return
}

func GetLogOverflowLength() int {

	if DeploymentType == Large || DeploymentType == ExtraLarge {

		return 10000
	}

	return 5000
}

func GetFlowWorkerPoolLength() int {

	if configs.Contains("datastore.flow.worker.pool.length") {

		return int(math.Max(float64(configs.GetIntValue("datastore.flow.worker.pool.length")), float64(MaxPoolLength)))
	}

	result := GetMaxPoolLength() * 6

	if DeploymentType <= Medium {

		result = GetMaxPoolLength() * 3
	}

	return int(math.Max(float64(result), float64(MaxPoolLength)))
}

func GetLogWorkerPoolLength() int {

	if configs.Contains("datastore.log.worker.pool.length") {

		return int(math.Max(float64(configs.GetIntValue("datastore.log.worker.pool.length")), float64(MaxPoolLength)))
	}

	result := GetLogOverflowLength() * 5

	if DeploymentType <= Medium {

		result = GetLogOverflowLength() * 2
	}

	return int(math.Max(float64(result), float64(MaxPoolLength)))
}

func GetCleanupJobPoolLength() int {

	if configs.Contains("datastore.cleanup.job.pool.length") {

		return configs.GetIntValue("datastore.cleanup.job.pool.length")
	}

	return GetMaxPoolLength()
}

func GetSyncJobPoolLength() int {

	if configs.Contains("datastore.sync.job.pool.length") {

		return configs.GetIntValue("datastore.sync.job.pool.length")
	}

	return 1_00_000
}

func GetMetricWorkerPoolLength() int {

	if configs.Contains("datastore.metric.worker.pool.length") {

		return int(math.Max(float64(configs.GetIntValue("datastore.metric.worker.pool.length")), float64(MaxPoolLength)))
	}

	result := GetMaxPoolLength() * 5

	if DeploymentType < Medium {

		result = GetMaxPoolLength() * 2

	} else if DeploymentType == Medium {

		result = GetMaxPoolLength() * 3
	}

	return int(math.Max(float64(result), float64(MaxPoolLength)))
}

func GetMetricExecutorPoolLength() int {

	if configs.Contains("datastore.metric.executor.pool.length") {

		return configs.GetIntValue("datastore.metric.executor.pool.length")
	}

	return 1_00_000
}

func GetLogExecutorPoolLength() int {

	if configs.Contains(DatastoreLogExecutorPoolLength) {

		return configs.GetIntValue(DatastoreLogExecutorPoolLength)
	}

	return 1_00_000
}

func GetDrillDownExecutorPoolLength() int {

	if configs.Contains("datastore.drill.down.executor.pool.length") {

		return configs.GetIntValue("datastore.drill.down.executor.pool.length")
	}

	return 1_00_000
}

func GetAIOpsExecutorPoolLength() int {

	if configs.Contains("datastore.aiops.executor.pool.length") {

		return configs.GetIntValue("datastore.aiops.executor.pool.length")
	}

	return 1_00_000
}

func GetFlowExecutorPoolLength() int {

	if configs.Contains("datastore.flow.executor.pool.length") {

		return configs.GetIntValue("datastore.flow.executor.pool.length")
	}

	return 1_00_000
}

func GetEventAggregatorPoolLength() int {

	if configs.Contains("datastore.event.aggregator.pool.length") {

		return configs.GetIntValue("datastore.event.aggregator.pool.length")
	}

	if DeploymentType < Medium {

		return GetMaxPoolLength() * 5
	} else if DeploymentType == Medium {

		return GetMaxPoolLength() * 6
	}

	return GetMaxPoolLength() * 8
}

func GetEventAggregatorPoolSize() int {

	if configs.Contains("datastore.event.aggregator.pool.size") {

		return configs.GetIntValue("datastore.event.aggregator.pool.size")
	}

	return 28
}

func GetMetricAggregatorPoolSize() int {

	if configs.Contains("datastore.metric.aggregator.pool.size") {

		return configs.GetIntValue("datastore.metric.aggregator.pool.size")
	}

	return 20
}

func GetMaxStringBytes() int {

	if configs.Contains("datastore.max.string.bytes") {

		return configs.GetIntValue("datastore.max.string.bytes")
	}

	return 50

}

func GetMaxPoolLength() int {

	if configs.Contains("datastore.max.pool.length") {

		return configs.GetIntValue("datastore.max.pool.length")
	}

	switch DeploymentType {

	case Small:

		return 10000

	case Medium:

		return 20000

	case Large:

		return 40000

	case ExtraLarge:

		return 60000
	}

	return 10000
}

func GetDeploymentType() (result int) {

	if configs.Contains("deployment.type") {

		return configs.GetIntValue("deployment.type")
	}

	return ExtraSmall
}

func GetMaxIndexerProbes() (probes int) {

	probes = 100_000

	if configs.Contains("datastore.max.indexer.records") {

		probes = configs.GetIntValue("datastore.max.indexer.records")
	}

	return
}

func GetIndexerThresholdPercent() (result int) {

	result = 40

	if configs.Contains("datastore.indexer.threshold.percent") {

		result = configs.GetIntValue("datastore.indexer.threshold.percent")
	}

	return
}

func InitQueryProcessors() {

	MetricQueryExecutors = GetMetricQueryExecutors()

	FlowQueryExecutors = GetFlowQueryExecutors()

	LogQueryExecutors = GetLogQueryExecutors()

	AIOpsEngineQueryExecutors = GetAIOpsQueryExecutors()

	DrillDownQueryExecutors = GetDrillDownQueryExecutors()

	Workers = GetQueryWorkers()

	MetricQueryWorkers = MetricQueryExecutors * Workers

	DiskIOWorkers = GetDiskIOWorkers()

	IOCPWorkers = GetIOCPWorkers()

	LogQueryWorkers = LogQueryExecutors * Workers

	FlowQueryWorkers = FlowQueryExecutors * Workers

	DrillDownQueryWorkers = DrillDownQueryExecutors * Workers

	AIOpsEngineQueryWorkers = AIOpsEngineQueryExecutors * Workers

	QueryExecutors = MetricQueryExecutors + FlowQueryExecutors + LogQueryExecutors + DrillDownQueryExecutors + AIOpsEngineQueryExecutors

	QueryWorkers = MetricQueryWorkers + FlowQueryWorkers + LogQueryWorkers + DrillDownQueryWorkers + AIOpsEngineQueryWorkers
}

func GetAggregationStatus() bool {

	if configs.Contains("aggregation") {

		if strings.ToLower(configs.GetStringValue("aggregation")) == Yes {

			return true
		}
		return false
	}

	return true
}

func GetEnvironmentType() string {

	if configs.Contains("datastore.env.type") {

		return configs.GetStringValue("datastore.env.type")
	}

	return DatastoreDevEnvironment
}

func GetGCMemoryLimit() uint64 {

	counter := "datastore.gc.memory.threshold"

	gcPercent := 80

	if LocalDatastore {

		gcPercent = 55
	}

	if SystemBootSequence == Broker {

		counter = "datastore.broker.gc.memory.threshold"

		gcPercent = 10

		if LocalDatastore {

			gcPercent = 5
		}
	}

	if configs.Contains(counter) {

		gcPercent = configs.GetIntValue(counter)
	}

	//If accessible memory size could not be determined, then 0 is returned.
	memoryBytes := (memory.TotalMemory() * uint64(gcPercent)) / 100

	if memoryBytes != 0 {

		configLogger.Info(fmt.Sprintf("gc memory limit set to %v bytes for %v", memoryBytes, SystemBootSequence))

		return memoryBytes
	}

	configLogger.Error(fmt.Sprintf("unable to fetch the memory %v", SystemBootSequence))

	return uint64(5 * 1024 * 1024 * 1024)
}

func GetCacheSizeMB() int {

	if configs.Contains("datastore.cache.size.mb") {

		return configs.GetIntValue("datastore.cache.size.mb")
	}

	switch DeploymentType {

	case Small:

		return 1024

	case Medium:

		return 2048

	case Large:

		return 8192

	case ExtraLarge:

		return 16384
	}

	return 512
}

func GetVerticalAggregationTimerSeconds() int {

	if configs.Contains("datastore.vertical.aggregation.timer.seconds") {

		return configs.GetIntValue("datastore.vertical.aggregation.timer.seconds")
	}

	return 60
}

func GetHorizontalAggregationTimerSeconds() int {

	if configs.Contains("datastore.horizontal.aggregation.timer.seconds") {

		return configs.GetIntValue("datastore.horizontal.aggregation.timer.seconds")
	}

	return 150
}

func GetMetricQueryExecutors() int {

	if configs.Contains("datastore.metric.query.executors") {

		return configs.GetIntValue("datastore.metric.query.executors")
	}

	switch DeploymentType {

	case Small:

		if LocalDatastore {

			return 3
		}

		return 5

	case Medium:

		if LocalDatastore {

			return 4
		}

		return 6

	case Large:

		return 8

	case ExtraLarge:

		return 10
	}

	return 2
}

func GetDiskIOWorkers() int {

	if configs.Contains("datastore.disk.io.workers") {

		return configs.GetIntValue("datastore.disk.io.workers")
	}

	switch DeploymentType {

	case Small:

		if LocalDatastore {

			return 40
		}

		return 48

	case Medium:

		if LocalDatastore {

			return 56
		}

		return 64

	case Large:

		return 128

	case ExtraLarge:

		return 160
	}

	return 32
}

func GetIOCPWorkers() int {

	if configs.Contains("datastore.iocp.workers") {

		return configs.GetIntValue("datastore.iocp.workers")
	}

	return runtime.NumCPU() * 2
}

func GetLogQueryExecutors() int {

	if configs.Contains("datastore.log.query.executors") {

		return configs.GetIntValue("datastore.log.query.executors")
	}

	switch DeploymentType {

	case Small:

		if LocalDatastore {

			return 3
		}

		return 6

	case Medium:

		if LocalDatastore {

			return 5
		}

		return 8

	case Large:

		return 10

	case ExtraLarge:

		return 12
	}

	return 3
}

func GetAIOpsQueryExecutors() int {

	if configs.Contains("datastore.aiops.query.executors") {

		return configs.GetIntValue("datastore.aiops.query.executors")
	}

	switch DeploymentType {

	case Small: // 12 gb

		if LocalDatastore {

			return 1
		}
		return 2

	case Medium:

		if LocalDatastore {

			return 3
		}

		return 4

	case Large:

		return 6

	case ExtraLarge:

		return 8
	}

	return 1 // 8 gb
}

func GetFlowQueryExecutors() int {

	if configs.Contains("datastore.flow.query.executors") {

		return configs.GetIntValue("datastore.flow.query.executors")
	}

	switch DeploymentType {

	case Small:

		if LocalDatastore {

			return 3
		}

		return 5

	case Medium:

		if LocalDatastore {

			return 4
		}

		return 6

	case Large:

		return 8

	case ExtraLarge:

		return 10
	}

	return 3
}

func GetDrillDownQueryExecutors() int {

	if configs.Contains("datastore.drill.down.query.executors") {

		return configs.GetIntValue("datastore.drill.down.query.executors")
	}

	switch DeploymentType {

	case Small:

		if LocalDatastore {

			return 3
		}

		return 4

	case Medium:

		if LocalDatastore {

			return 4
		}

		return 5

	case Large:

		return 8

	case ExtraLarge:

		return 10
	}

	return 2
}

func GetQueryWorkers() int {

	if configs.Contains("datastore.query.workers") {

		return int(math.Max(float64(configs.GetIntValue("datastore.query.workers")), 2))
	}

	switch DeploymentType {

	case Small:

		if LocalDatastore {

			return 2
		}

		return 3

	case Medium:

		if LocalDatastore {

			return 3
		}

		return 5

	case Large:

		return 6

	case ExtraLarge:

		return 8
	}

	return 2
}

func GetDatastoreFlushTimerSeconds() (seconds int) {

	seconds = 23

	if configs.IsNotEmpty() && configs.Contains("datastore.flush.timer.seconds") {

		seconds = configs.GetIntValue("datastore.flush.timer.seconds")
	}

	return
}

func GetDataWriterFlushTimerSeconds() (seconds int) {

	seconds = 5

	if configs.IsNotEmpty() && configs.Contains("datastore.broker.data.writer.flush.timer.seconds") {

		seconds = configs.GetIntValue("datastore.broker.data.writer.flush.timer.seconds")
	}

	return
}

func SetDataWriterSyncTimers() {

	//setting a default value in each element for every datastoretype
	for index := range DataWriterSyncTimers {

		DataWriterSyncTimers[index] = DataWriterSyncMaxTimerSeconds
	}

	// for metric we set default 45 sec
	DataWriterSyncTimers[PerformanceMetric] = DataWriterSyncMinTimerSeconds

	DataWriterSyncTimers[ObjectStatusMetric] = DataWriterSyncMinTimerSeconds

	if configs.IsNotEmpty() && configs.Contains(DatastoreBrokerDataWriterSyncTimers) {

		for storeType, value := range configs.GetMapValue(DatastoreBrokerDataWriterSyncTimers) {

			DataWriterSyncTimers[StringToInt32(storeType)] = int64(value.(float64))

		}

	}

}

func GetDataAggregatorSyncTimerSeconds() int64 {

	if configs.IsNotEmpty() && configs.Contains(DatastoreBrokerDataAggregatorSyncTimerSeconds) {

		return configs.GetInt64Value(DatastoreBrokerDataAggregatorSyncTimerSeconds)
	}

	return 180
}

func GetMemoryPoolShrinkTimerSeconds() (seconds int) {

	seconds = 90

	if configs.IsNotEmpty() && configs.Contains("datastore.memory.pool.shrink.timer.seconds") {

		seconds = configs.GetIntValue("datastore.memory.pool.shrink.timer.seconds")
	}

	return
}

func GetQueryAbortTimerSeconds() (seconds int) {

	seconds = 120

	if configs.IsNotEmpty() && configs.Contains("datastore.query.abort.timer.seconds") {

		seconds = configs.GetIntValue("datastore.query.abort.timer.seconds")
	}

	return
}

func GetMaxAggregationGroups() int {

	if configs.IsNotEmpty() && configs.Contains(DatastoreMaxAggregationGroups) {

		return configs.GetIntValue(DatastoreMaxAggregationGroups)
	}

	if DeploymentType < Medium {

		if LocalDatastore {

			return 1_000_00
		}

		return 2_000_00
	}

	return 1_000_000
}

func GetMaxHistoricalRecords() int {

	if configs.IsNotEmpty() && configs.Contains(DatastoreMaxHistoricalRecords) {

		return configs.GetIntValue(DatastoreMaxHistoricalRecords)
	}

	// bug id -MOTADATA-1316, 1319
	return 100000
}

func GetMaxStatusFlapHistoricalRecords() int {

	if configs.IsNotEmpty() && configs.Contains(DatastoreMaxStatusFlapHistoricalRecords) {

		return configs.GetIntValue(DatastoreMaxStatusFlapHistoricalRecords)
	}

	return 500000
}

func GetMaxSearchQueryRecords() int {

	if configs.IsNotEmpty() && configs.Contains(DatastoreMaxSearchQueryRecords) {

		return configs.GetIntValue(DatastoreMaxSearchQueryRecords)
	}

	return 1_00_000
}

func GetMaxGridRecords() int {

	if configs.IsNotEmpty() && configs.Contains(DatastoreMaxGridRecords) {

		return configs.GetIntValue(DatastoreMaxGridRecords)
	}

	// bug id -MOTADATA-1316, 1319
	return 100000
}

func GetQueryQueueSize() (size int) {

	size = 100

	if configs.IsNotEmpty() && configs.Contains("datastore.query.queue.size") {

		size = configs.GetIntValue("datastore.query.queue.size")
	}

	return
}

func GetStoreSyncTimerSeconds() (seconds int) {

	seconds = 55

	if configs.IsNotEmpty() && configs.Contains("datastore.store.sync.timer.seconds") {

		seconds = configs.GetIntValue("datastore.store.sync.timer.seconds")
	}

	return
}

func GetVerticalWriters() int {

	if configs.IsNotEmpty() && configs.Contains("datastore.vertical.writers") {

		return configs.GetIntValue("datastore.vertical.writers")
	}

	switch DeploymentType {

	case Small:

		if LocalDatastore {

			return 2
		}

		return 5

	case Medium:

		if LocalDatastore {

			return 4
		}

		return 7

	case Large:

		return 13

	case ExtraLarge:

		return 17
	}

	return 2
}

func GetStaticMetricWriters() int {

	if configs.IsNotEmpty() && configs.Contains("datastore.static.metric.writers") {

		return configs.GetIntValue("datastore.static.metric.writers")
	}

	switch DeploymentType {

	case Small:

		return 2

	case Medium:

		return 3

	case Large:

		return 5

	case ExtraLarge:

		return 7
	}

	return 2
}

func GetHorizontalWriters() int {

	if configs.IsNotEmpty() && configs.Contains("datastore.horizontal.writers") {

		return configs.GetIntValue("datastore.horizontal.writers")
	}

	switch DeploymentType {

	case Small:

		if LocalDatastore {

			return 2
		}

		return 5

	case Medium:

		if LocalDatastore {

			return 4
		}

		return 7

	case Large:

		return 11

	case ExtraLarge:

		return 13
	}

	return 2
}

func GetHealthMetricWriters() int {

	if configs.IsNotEmpty() && configs.Contains("datastore.health.metric.writers") {

		return configs.GetIntValue("datastore.health.metric.writers")
	}

	return 1
}

func GetDataWriters() (result int) {

	if configs.IsNotEmpty() && configs.Contains("datastore.broker.data.writers") {

		return configs.GetIntValue("datastore.broker.data.writers")
	}

	switch DeploymentType {

	case Small:

		return 2

	case Medium:

		return 4

	case Large:

		return 6

	case ExtraLarge:

		return 8
	}

	return 2
}

func GetIndexWriters() (result int) {

	if configs.IsNotEmpty() && configs.Contains("datastore.index.writers") {

		return configs.GetIntValue("datastore.index.writers")
	}

	switch DeploymentType {

	case Small:

		if LocalDatastore {

			return 4
		}

		return 7

	case Medium:

		if LocalDatastore {

			return 6
		}

		return 9

	case Large:

		return 11

	case ExtraLarge:

		return 15
	}

	return 3
}

func GetHost() (result string) {

	if configs.Contains(DatastoreHost) {

		return configs.GetStringValue(DatastoreHost)

	}

	return "localhost"
}

func GetDatastoreNotificationPort() (result string) {

	//Datastore notification port
	if configs.Contains(DatastoreNotificationPort) {

		return configs.GetStringValue(DatastoreNotificationPort)

	}

	return "9455"
}

func GetDatastoreAvailabilityWriterPort() (result string) {

	//Datastore availability port
	if configs.Contains(DatastoreAvailabilityWriterPort) {

		return configs.GetStringValue(DatastoreAvailabilityWriterPort)

	}

	return "9471"
}

func GetDatastoreMetricWriterPort() (result string) {

	//Datastore metric port
	if configs.Contains(DatastoreMetricWriterPort) {

		return configs.GetStringValue(DatastoreMetricWriterPort)

	}

	return "9472"
}

func GetDatastoreEventWriterPort() (result string) {

	//Datastore event port
	if configs.Contains(DatastoreEventWriterPort) {

		return configs.GetStringValue(DatastoreEventWriterPort)

	}

	return "9473"
}

// 9471 - availability
// 9472 - metric
// 9473 - event
// 9464 - motaops
func GetDatastoreReaderPort() (result string) {

	if configs.Contains(DatastoreReaderPort) {

		return configs.GetStringValue(DatastoreReaderPort)

	}

	return "9457"
}

func GetDatastorePublisherPort() (result string) {

	if configs.Contains(DatastorePublisherPort) {

		return configs.GetStringValue(DatastorePublisherPort)

	}

	return "9456"
}

func GetAIOpsEnginePublisherPort() (result string) {

	if configs.Contains(AIOpsEnginePublisherPort) {

		return configs.GetStringValue(AIOpsEnginePublisherPort)

	}

	return "9461"
}

func GetAIOpsEngineSubscriberPort() (result string) {

	if configs.Contains(AIOpsEngineSubscriberPort) {

		return configs.GetStringValue(AIOpsEngineSubscriberPort)

	}

	return "9460"
}

func GetIOWorkerThreads() (result int) {

	if configs.Contains("datastore.io.worker.threads") {

		return configs.GetIntValue("datastore.io.worker.threads")

	}

	switch DeploymentType {

	case Small:

		return 1

	case Medium:

		return 2

	case Large:

		return 3

	case ExtraLarge:

		return 4
	}

	return 1

}

func GetMaxWorkerEvents() (result int) {

	if configs.Contains(DatastoreMaxWorkerEvents) {

		return configs.GetIntValue(DatastoreMaxWorkerEvents)

	}

	switch DeploymentType {

	case Small:

		return 3

	case Medium:

		return 3

	case Large:

		return 4

	case ExtraLarge:

		return 5
	}

	return 2
}

func GetMaxWorkerEventKeyGroupLength() (result int) {

	if configs.Contains("datastore.max.worker.event.key.group.length") {

		return configs.GetIntValue("datastore.max.worker.event.key.group.length")

	}

	// in test environemnt we load different deployment type configs so runtime key changes for the same
	if DeploymentType <= Medium && EnvironmentType != DatastoreTestEnvironment {

		return 48
	}

	return 72
}

func GetAggregationJobs() (result int) {

	if configs.Contains("datastore.aggregation.jobs") {
		return configs.GetIntValue("datastore.aggregation.jobs")
	}

	switch DeploymentType {

	case Small:

		if LocalDatastore {

			return 2
		}

		return 3

	case Medium:

		if LocalDatastore {

			return 3
		}

		return 5

	case Large:

		return 6

	case ExtraLarge:

		return 8
	}

	return 2
}

func GetMetricAggregators() (result int) {

	if configs.Contains("datastore.metric.aggregators") {
		return configs.GetIntValue("datastore.metric.aggregators")
	}

	switch DeploymentType {

	case Small:

		if LocalDatastore {

			return 2
		}

		return 3

	case Medium:

		if LocalDatastore {

			return 3
		}

		return 5

	case Large:

		return 7

	case ExtraLarge:

		return 9
	}

	return 2
}

func GetEventAggregators() (result int) {

	if configs.Contains("datastore.event.aggregators") {
		return configs.GetIntValue("datastore.event.aggregators")
	}

	switch DeploymentType {

	case Small:

		if LocalDatastore {

			return 2
		}

		return 3

	case Medium:

		if LocalDatastore {

			return 4
		}

		return 5

	case Large:

		return 7

	case ExtraLarge:

		return 9
	}

	return 2
}

func GetDataAggregators() (result int) {

	if configs.IsNotEmpty() && configs.Contains("datastore.broker.data.aggregators") {

		return configs.GetIntValue("datastore.broker.data.aggregators")
	}

	switch DeploymentType {

	case Small:

		return 2

	case Medium:

		return 4

	case Large:

		return 6

	case ExtraLarge:

		return 8
	}

	return 2
}

func GetVerticalAggregationPastProbeDays() (result int) {

	result = 90

	if configs.Contains("datastore.vertical.aggregation.past.probe.days") {
		result = configs.GetIntValue("datastore.vertical.aggregation.past.probe.days")
	}

	return
}

func GetHorizontalAggregationPastProbeDays() (result int) {

	result = 30

	if configs.Contains("datastore.horizontal.aggregation.past.probe.days") {
		result = configs.GetIntValue("datastore.horizontal.aggregation.past.probe.days")
	}

	return
}

func GetFullTextSearchingAggregationPastProbeDays() int {

	result := 1

	if configs.Contains("datastore.fulltext.searching.aggregation.past.probe.days") {
		result = configs.GetIntValue("datastore.fulltext.searching.aggregation.past.probe.days")
	}

	if result > 7 {

		result = 7
	}

	return result
}

func GetStoreSyncJobs() (result int) {

	if configs.Contains("datastore.store.sync.jobs") {

		return configs.GetIntValue("datastore.store.sync.jobs")
	}

	switch DeploymentType {

	case Small:

		if LocalDatastore {

			return 4
		}

		return 5

	case Medium:

		if LocalDatastore {

			return 5
		}

		return 6

	case Large:

		return 8

	case ExtraLarge:

		return 10
	}

	return 3
}

func GetMappingCleanupJobs() (result int) {

	result = 1

	if configs.Contains("datastore.mapping.cleanup.jobs") {

		return configs.GetIntValue("datastore.mapping.cleanup.jobs")
	}

	return result

}

func GetStoreCleanUpJobs() (result int) {

	if configs.Contains("datastore.store.cleanup.jobs") {

		return configs.GetIntValue("datastore.store.cleanup.jobs")
	}

	switch DeploymentType {

	case Small:

		return 3

	case Medium:

		return 4

	case Large:

		return 5

	case ExtraLarge:

		return 6
	}

	return 2
}

func GetMinimumDiskSpaceAvailableThresholdPercent() float64 {

	if configs.Contains("datastore.minimum.available.disk.space.threshold.percent") {

		return configs.GetFloatValue("datastore.minimum.available.disk.space.threshold.percent")
	}

	return 0.6
}

// ResetLogLevel for any other request which doesn't come from the UI we will give default LogRetentionSeconds
func ResetLogLevel(seconds int) error {

	if seconds > LogLevelResetTimerSeconds {

		seconds = LogLevelResetTimerSeconds
	}

	ticker := time.NewTicker(time.Second * time.Duration(seconds))

	<-ticker.C

	lock.Lock()

	defer lock.Unlock()

	bytes, err := os.ReadFile(CurrentDir + PathSeparator + ConfigDir + PathSeparator + MotadataDatastoreConfigFile)

	if err != nil {

		ticker.Stop()

		configLogger.Error(fmt.Sprintf(ErrorResetConfigs, err, SystemBootSequence))

		return errors.New(fmt.Sprintf(ErrorResetConfigs, err, SystemBootSequence))
	}

	configs = make(MotadataMap)

	err = json.Unmarshal(bytes, &configs)

	if err != nil {

		ticker.Stop()

		configLogger.Error(fmt.Sprintf(ErrorResetConfigs, err, SystemBootSequence))

		return errors.New(fmt.Sprintf(ErrorResetConfigs, err, SystemBootSequence))
	}

	if QueryPlanLogging {

		configLogger.Info("query plan logging off")

		SetTaskLogging(No)

		configs[TaskLogging] = No

	}

	//Scenario in starting if we have both task logging and log level set to other value in this case we need to make both to default level

	if TraceEnabled() || DebugEnabled() {

		configLogger.Info(fmt.Sprintf("log level reset to info in %v", SystemBootSequence))

		gcTracer.Store(false)

		//change log level to info
		SetLogLevel(LogLevelInfo)

		configs[SystemLogLevel] = LogLevelInfo
	}

	configLogger.Info(fmt.Sprintf("log level changed to %v ", configs[SystemLogLevel]))

	WriteConfigs(configs)

	ticker.Stop()

	return nil
}

func ReloadConfigs(bytes []byte) (error, MotadataMap) {

	lock.Lock()

	defer lock.Unlock()

	var updatedConfigs MotadataMap

	err := json.Unmarshal(bytes, &updatedConfigs)

	if err != nil {

		configLogger.Error(fmt.Sprintf("unable to read the changes in config file for %v reason %v", SystemBootSequence, err))

		return errors.New(fmt.Sprintf("unable to read the changes in config file for %v reason %v", SystemBootSequence, err)), updatedConfigs
	}

	configLogger.Info(fmt.Sprintf("config updated for %v", SystemBootSequence))

	// nano editors fire 2 same write events on every changes so to prevent second event and start multiple go routine need this check
	if updatedConfigs.Contains(SystemLogLevel) && configs[SystemLogLevel] != updatedConfigs[SystemLogLevel] {

		configs[SystemLogLevel] = updatedConfigs[SystemLogLevel]

		SetLogLevel(configs.GetIntValue(SystemLogLevel))

		gcTracer.Store(false)

		if TraceEnabled() {

			go initGCTracer()
		}

	}

	if updatedConfigs.Contains(TaskLogging) {

		configs[TaskLogging] = updatedConfigs[TaskLogging]

		SetTaskLogging(configs.GetStringValue(TaskLogging))

	}

	if TraceEnabled() || DebugEnabled() || QueryPlanLogging {

		go func() {

			_ = ResetLogLevel(LogLevelResetTimerSeconds)
		}()
	}

	if updatedConfigs.Contains(DatastoreMaxHistoricalRecords) {

		configs[DatastoreMaxHistoricalRecords] = updatedConfigs[DatastoreMaxHistoricalRecords]
	} else {
		delete(configs, DatastoreMaxHistoricalRecords)
	}

	MaxHistoricalRecords = GetMaxHistoricalRecords()

	if updatedConfigs.Contains(DatastoreMaxStatusFlapHistoricalRecords) {

		configs[DatastoreMaxStatusFlapHistoricalRecords] = updatedConfigs[DatastoreMaxStatusFlapHistoricalRecords]

	} else {

		delete(configs, DatastoreMaxStatusFlapHistoricalRecords)

	}

	MaxStatusFlapHistoricalRecords = GetMaxStatusFlapHistoricalRecords()

	if updatedConfigs.Contains(DatastoreMaxSearchQueryRecords) {

		configs[DatastoreMaxSearchQueryRecords] = updatedConfigs[DatastoreMaxSearchQueryRecords]
	} else {
		delete(configs, DatastoreMaxSearchQueryRecords)
	}

	MaxSearchQueryRecords = GetMaxSearchQueryRecords()

	if updatedConfigs.Contains(DatastoreMaxGridRecords) {

		configs[DatastoreMaxGridRecords] = updatedConfigs[DatastoreMaxGridRecords]
	} else {
		delete(configs, DatastoreMaxGridRecords)
	}

	MaxGridRecords = GetMaxGridRecords()

	if updatedConfigs.Contains(DatastoreMaxAggregationGroups) {

		configs[DatastoreMaxAggregationGroups] = updatedConfigs[DatastoreMaxAggregationGroups]
	} else {
		delete(configs, DatastoreMaxAggregationGroups)
	}

	MaxAggregationGroups = GetMaxAggregationGroups()

	if updatedConfigs.Contains(DatastoreLogRetentionDays) {

		configs[DatastoreLogRetentionDays] = updatedConfigs[DatastoreLogRetentionDays]
	} else {
		delete(configs, DatastoreLogRetentionDays)
	}

	LogRetentionJobDays = GetLogRetentionDays()

	if updatedConfigs.Contains(DatastoreBrokerDataWriterSyncTimers) {

		configs[DatastoreBrokerDataWriterSyncTimers] = updatedConfigs[DatastoreBrokerDataWriterSyncTimers]
	} else {
		delete(configs, DatastoreBrokerDataWriterSyncTimers)
	}

	SetDataWriterSyncTimers()

	if updatedConfigs.Contains(DatastoreBrokerDataAggregatorSyncTimerSeconds) {

		configs[DatastoreBrokerDataAggregatorSyncTimerSeconds] = updatedConfigs[DatastoreBrokerDataAggregatorSyncTimerSeconds]
	} else {
		delete(configs, DatastoreBrokerDataAggregatorSyncTimerSeconds)
	}

	DataAggregatorSyncTimerSeconds = GetDataAggregatorSyncTimerSeconds()

	if updatedConfigs.Contains(DatastoreMotaOpsServiceStatus) {

		configs[DatastoreMotaOpsServiceStatus] = updatedConfigs[DatastoreMotaOpsServiceStatus]
	} else {
		delete(configs, DatastoreMotaOpsServiceStatus)
	}

	DatastoreMotaOpsServiceEnabled = getMotaOpsServiceStatus()

	if updatedConfigs.Contains(DatastoreBrokerMaxAggregationGroupings) {

		configs[DatastoreBrokerMaxAggregationGroupings] = updatedConfigs[DatastoreBrokerMaxAggregationGroupings]
	} else {
		delete(configs, DatastoreBrokerMaxAggregationGroupings)
	}

	MaxDataAggregationGroups = GetDataAggregatorMaxGroupingLimit()

	if updatedConfigs.Contains(DatastoreMaxFlowTopNSelectionGroups) {

		configs[DatastoreMaxFlowTopNSelectionGroups] = updatedConfigs[DatastoreMaxFlowTopNSelectionGroups]
	} else {
		delete(configs, DatastoreMaxFlowTopNSelectionGroups)
	}

	MaxFlowTopNSelectionGroups = GetMaxFlowTopNSelectionGroups()

	if updatedConfigs.Contains(DatastoreMaxFlowTopNInsertionRecords) {

		configs[DatastoreMaxFlowTopNInsertionRecords] = updatedConfigs[DatastoreMaxFlowTopNInsertionRecords]
	} else {
		delete(configs, DatastoreMaxFlowTopNInsertionRecords)
	}

	MaxFlowTopNInsertionGroups = GetMaxFlowTopNInsertionGroups()

	if updatedConfigs.Contains(DatastoreMaxCurrentDayEventFileQueueSize) {

		configs[DatastoreMaxCurrentDayEventFileQueueSize] = updatedConfigs[DatastoreMaxCurrentDayEventFileQueueSize]
	} else {
		delete(configs, DatastoreMaxCurrentDayEventFileQueueSize)
	}

	MaxCurrentDayEventFileQueueSize = GetDatastoreMaxCurrentDayEventFileQueueSize()

	if updatedConfigs.Contains(DatastoreMaxPreviousDayEventFileQueueSize) {

		configs[DatastoreMaxPreviousDayEventFileQueueSize] = updatedConfigs[DatastoreMaxPreviousDayEventFileQueueSize]
	} else {
		delete(configs, DatastoreMaxPreviousDayEventFileQueueSize)
	}

	MaxPreviousDayEventFileQueueSize = GetDatastoreMaxPreviousDayEventFileQueueSize()

	if updatedConfigs.Contains(DatastoreMaxVerticalWriterCacheEntries) {

		configs[DatastoreMaxVerticalWriterCacheEntries] = updatedConfigs[DatastoreMaxVerticalWriterCacheEntries]
	} else {
		delete(configs, DatastoreMaxVerticalWriterCacheEntries)
	}

	if updatedConfigs.Contains(DiagnosticProfileDurationSeconds) {

		configs[DiagnosticProfileDurationSeconds] = updatedConfigs[DiagnosticProfileDurationSeconds]

	} else {

		delete(configs, DiagnosticProfileDurationSeconds)
	}

	MaxVerticalWriteCacheEntries = GetMaxVerticalWriterCacheEntries()

	if updatedConfigs.Contains(DatastoreQueryCreationAgeThresholdSeconds) {

		configs[DatastoreQueryCreationAgeThresholdSeconds] = updatedConfigs[DatastoreQueryCreationAgeThresholdSeconds]

	} else {

		delete(configs, DatastoreQueryCreationAgeThresholdSeconds)
	}

	QueryCreationAgeThresholdSeconds = GetQueryCreationAgeThresholdSeconds()

	if updatedConfigs.Contains(DatastoreQueryStatsLogging) {

		configs[DatastoreQueryStatsLogging] = updatedConfigs[DatastoreQueryStatsLogging]

	} else {

		delete(configs, DatastoreQueryStatsLogging)

	}

	QueryStatsLogging = GetQueryStatsLogging()

	return nil, updatedConfigs
}

func WriteConfigs(configs MotadataMap) {

	bytes, err := json.MarshalIndent(&configs, "", "  ")

	if err != nil {

		configLogger.Error(fmt.Sprintf("error occurred while unmarshaling %v config file in %v", err.Error(), SystemBootSequence))

		return

	}

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+MotadataDatastoreConfigFile, bytes, 0644)

	if err != nil {

		configLogger.Error(fmt.Sprintf("error occurred while writing config %v in %v", err.Error(), SystemBootSequence))

		return

	}

}

func initGCTracer() {

	configLogger.Info(fmt.Sprintf("gc tracer started for %v", SystemBootSequence))

	gcNotifier = gcnotifier.New()

	gcTracer.Store(true)

	for range gcNotifier.AfterGC() {

		if gcTracer.Load() == false {

			configLogger.Info(fmt.Sprintf("gc tracer stopped as trace enabled limit reached in %v", SystemBootSequence))

			if gcNotifier != nil {

				gcNotifier.Close()
			}

			gcNotifier = nil

			return
		}

		runtime.ReadMemStats(&memoryStats)

		debug.ReadGCStats(&gcStats)

		gcLogger.Trace(fmt.Sprintf("GC Tracer for %v :::", SystemBootSequence))

		gcLogger.Trace(fmt.Sprintf("GC Pause Time -> %v", gcStats.Pause))

		gcLogger.Trace(fmt.Sprintf("Last GC Runtime -> %v", gcStats.LastGC))

		gcLogger.Trace(fmt.Sprintf("Memory released in GC -> %v bytes ", memoryStats.HeapReleased))

		gcLogger.Trace(fmt.Sprintf("Next GC will run when memory = %v bytes", memoryStats.NextGC))

		gcLogger.Trace(fmt.Sprintf("Total GC -> %v", gcStats.NumGC))

	}
}

func CloseGCTracer() {

	if gcNotifier != nil {

		gcNotifier.Close()

		gcNotifier = nil
	}
}

func GetDataAggregatorMaxGroupingLimit() int {

	if configs.Contains(DatastoreBrokerMaxAggregationGroupings) {

		return configs.GetIntValue(DatastoreBrokerMaxAggregationGroupings)
	}

	if DeploymentType < Medium && LocalDatastore {

		return MaxPoolLength * 5
	}

	return MaxPoolLength * 10
}

func GetMaxFlowTopNSelectionGroups() int {

	if configs.Contains(DatastoreMaxFlowTopNSelectionGroups) {

		return configs.GetIntValue(DatastoreMaxFlowTopNSelectionGroups)
	}

	return 100
}

func GetMaxFlowTopNInsertionGroups() (result int) {

	result = 10_000

	if configs.Contains(DatastoreMaxFlowTopNInsertionRecords) {

		result = configs.GetIntValue(DatastoreMaxFlowTopNInsertionRecords)
	}

	//max insertion groups cannot be greater than overflowLength *5 as we are taking all the values in the single pool for sorting

	if result > OverflowLength*5 {

		result = OverflowLength * 5
	}

	return result
}

func getMappingStoreCacheConfigs() MotadataMap {

	if configs.Contains(MappingStoreCacheConfigs) {

		return configs.GetMapValue(MappingStoreCacheConfigs)
	}

	return nil
}

func getStoreWALSizes() (sizes map[DatastoreType]int) {

	sizes = map[DatastoreType]int{}

	if configs.Contains("datastore.store.wal.size.mb") {

		for storeType, size := range configs.GetMapValue("datastore.store.wal.size.mb") {

			val, _ := strconv.Atoi(storeType)

			sizes[DatastoreType(val)] = int(size.(float64))

		}

		return sizes
	}

	return nil
}

func getCacheStores() map[DatastoreType]struct{} {

	cacheStores := make(map[DatastoreType]struct{})

	if configs.Contains("datastore.cache.stores") {

		for storeType := range configs.GetMapValue("datastore.cache.stores") {

			val, _ := strconv.Atoi(storeType)

			cacheStores[DatastoreType(val)] = struct{}{}

		}

	}

	// by default static metric is in memory , if not needed than it can be removed from the map
	cacheStores[StaticMetric] = struct{}{}

	return cacheStores
}

func GetStoreWALSizeBytes(storeType DatastoreType) int {

	if walStoreSizes != nil {

		if result, found := walStoreSizes[storeType]; found {

			return result * 1024 * 1024
		}
	}

	if DeploymentType == Large {

		return 2 * 1024 * 1024

	} else if DeploymentType == ExtraLarge {

		return 3 * 1024 * 1024
	}

	return 1 * 1024 * 1024
}

func GetMappingCacheRecords(store string) int {

	//store name should be with mappings as a suffix otherwise need to split the name in open and create store

	if mappingCacheStores != nil && mappingCacheStores.Contains(store) {

		return mappingCacheStores.GetIntValue(store)
	}

	if configs.Contains("mapping.cache.records") {

		return configs.GetIntValue("mapping.cache.records")
	}

	switch DeploymentType {

	case Medium:
		return 10_000

	case Large:
		return 20_000

	case ExtraLarge:
		return 40_000
	}

	return 5_000
}

func GetMotaOpsServicePort() int {

	if configs.Contains(DatastoreMotaOpsServicePort) {

		return configs.GetIntValue(DatastoreMotaOpsServicePort)
	}

	return 9464
}

func getMotaOpsServiceStatus() bool {

	if configs.Contains(DatastoreMotaOpsServiceStatus) {

		return configs.GetStringValue(DatastoreMotaOpsServiceStatus) == Yes
	}

	return false
}

func ResetRegistrationId() {

	lock.Lock()

	defer lock.Unlock()

	configs[RemoteEventProcessorUUID] = RegistrationId

	WriteConfigs(configs)

}

func GetLocalHostName() string {

	hostname, err := os.Hostname()

	if err != nil {

		configLogger.Error(fmt.Sprintf("unable to get hostname reason %v", err))
	}

	return hostname
}

func GetLocalIPAddress() string {

	addresses, err := getIPAddresses()

	configLogger.Info(fmt.Sprintf("local ip address fetched %v", addresses))

	if err != nil {

		configLogger.Error(fmt.Sprintf("err %v occurred while fetching ip address", err))

		return Empty
	}

	if len(addresses) == 0 {

		configLogger.Error("unable to fetch ip address")

		return Empty
	}

	return addresses[0]

}

func getIPAddresses() (ips []string, err error) {

	interfaces, err := net.Interfaces()

	if err != nil {

		configLogger.Error(fmt.Sprintf("unable to fetch interfaces reason %v", err))

		return ips, err
	}

	// handle err
	for _, i := range interfaces {
		addresses, err := i.Addrs()

		if err != nil {

			configLogger.Error(fmt.Sprintf("unable to fetch IP Address reason %v", err))

		}
		// handle err
		for _, addr := range addresses {

			switch v := addr.(type) {

			case *net.IPNet:

				ip := v.IP.String()

				if !strings.Contains(ip, "127.0.0.1") && !strings.Contains(ip, ":") && !v.IP.IsLoopback() {

					ips = append(ips, ip)

				}

			}
			// process IP address
		}
	}

	return ips, err

}

func GetSocketConnectionMaxIdleTimeSeconds() time.Duration {

	if configs.Contains(SocketConnectionMaxIdleTimeSeconds) {

		return time.Duration(configs.GetIntValue(SocketConnectionMaxIdleTimeSeconds)) * time.Second
	}

	return time.Second * 150
}

func GetMotadataConfigs() (MotadataMap, error) {

	bytes, err := os.ReadFile(CurrentDir + PathSeparator + ConfigDir + PathSeparator + MotadataDataConfigFile)

	if err != nil {

		return nil, errors.New(fmt.Sprintf("error %v occurred while reading motadata json file ", err.Error()))
	}

	configs := make(MotadataMap)

	err = json.Unmarshal(bytes, &configs)

	if err != nil {

		return nil, errors.New(fmt.Sprintf("error %v occurred while unmarshalling motadata json file ", err.Error()))
	}

	return configs, nil
}

func GetHealthMetricFlushTimerSeconds() int {

	if configs.Contains("health.metric.flush.timer.seconds") {

		return configs.GetIntValue("health.metric.flush.timer.seconds")
	}

	return 300
}

func setMetricAggregationIntervals() {

	if configs.Contains("metric.aggregation.intervals") {

		AggregationIntervals = nil

		for _, interval := range configs.GetSliceValue("metric.aggregation.intervals") {

			AggregationIntervals = append(AggregationIntervals, int(interval.(float64)))
		}

		sort.Ints(AggregationIntervals)

		if len(AggregationIntervals) > 3 {

			AggregationIntervals = AggregationIntervals[:3]
		}
	}
}

func setEventAggregationIntervals() {

	if configs.Contains("event.aggregation.intervals") {

		EventAggregationIntervals = nil

		for _, interval := range configs.GetSliceValue("event.aggregation.intervals") {

			EventAggregationIntervals = append(EventAggregationIntervals, int(interval.(float64)))
		}

		sort.Ints(EventAggregationIntervals)

		if len(EventAggregationIntervals) > 3 {

			EventAggregationIntervals = EventAggregationIntervals[:3]
		}
	}
}

func getDiagnosticProfileDurationSeconds() time.Duration {

	if configs.Contains(DiagnosticProfileDurationSeconds) {

		return time.Duration(configs.GetIntValue(DiagnosticProfileDurationSeconds)) * time.Second
	}

	return time.Second * 30
}

// config validation functions

func validateColumns(configs MotadataMap) bool {

	update := false

	if !configs.Contains(IndexableColumns) {

		configs[IndexableColumns] = DefaultColumns[IndexableColumns]

		configLogger.Info("added indexable columns in columns.json")

		update = true

	} else {

		nestedConfigs := configs.GetMapValue(IndexableColumns)

		for plugin, columns := range DefaultColumns.GetMapValue(IndexableColumns) {

			if !nestedConfigs.Contains(plugin) {

				nestedConfigs[plugin] = columns

				configLogger.Info(fmt.Sprintf("added plugin : %v in indexable.column in columns.json", plugin))

				update = true

			} else {

				indexableColumns := nestedConfigs.GetMapValue(plugin)

				for column := range ToMap(columns) {

					if !indexableColumns.Contains(column) {

						configLogger.Info(fmt.Sprintf("added indexable column : %v for plugin : %v in columns.json", column, plugin))

						indexableColumns[column] = Empty

						update = true

					}
				}

			}

		}

	}

	for columnType := range DefaultColumns {

		if columnType == IndexableColumns {

			continue
		}

		if !configs.Contains(columnType) {

			configs[columnType] = DefaultColumns[columnType]

			configLogger.Info(fmt.Sprintf("added %v in columns.json", columnType))

			update = true

		} else {

			columns := configs.GetMapValue(columnType)

			for column, value := range DefaultColumns.GetMapValue(columnType) {

				if !columns.Contains(column) {

					columns[column] = value

					configLogger.Info(fmt.Sprintf("added %v for %v in columns.json", column, columnType))

					update = true
				}
			}
		}
	}

	return update
}

func validateHorizontalAggregations(configs MotadataMap) bool {

	update := false

	for plugin, views := range defaultHorizontalAggregationColumns {

		if !configs.Contains(plugin) {

			configs[plugin] = views

			configLogger.Info(fmt.Sprintf("added plugin : %v in horizontal-aggregations.json", plugin))

			update = true

			continue
		}

		horizontalAggregations := configs.GetMapValue(plugin)

		for view, columns := range defaultHorizontalAggregationColumns.GetMapValue(plugin) {

			if !horizontalAggregations.Contains(view) {

				horizontalAggregations[view] = columns

				configLogger.Info(fmt.Sprintf("added view : %v in plugin : %v in horizontal-aggregations.json", view, plugin))

				update = true

			} else {

				for column, value := range ToMap(columns) {

					if column == IndexableColumns {

						if !horizontalAggregations.GetMapValue(view).Contains(IndexableColumns) {

							horizontalAggregations.GetMapValue(view)[IndexableColumns] = ToMap(columns).GetMapValue(IndexableColumns)

							configLogger.Info(fmt.Sprintf("added indexable columns in view : %v , for plugin : %v in horizontal-aggregations.json", view, plugin))

							update = true

						} else {

							// comparing indexable column also in aggregation view
							horizontalAggregationIndexableColumns := horizontalAggregations.GetMapValue(view).GetMapValue(IndexableColumns)

							for column := range ToMap(columns).GetMapValue(IndexableColumns) {

								if !horizontalAggregationIndexableColumns.Contains(column) {

									configLogger.Info(fmt.Sprintf("added indexable column : %v in view : %v , for plugin : %v in horizontal-aggregations.json", column, view, plugin))

									horizontalAggregationIndexableColumns[column] = struct{}{}

									update = true

								}

							}

						}

					} else {

						if !horizontalAggregations.GetMapValue(view).Contains(column) {

							horizontalAggregations.GetMapValue(view)[column] = value

							configLogger.Info(fmt.Sprintf("added %v in view : %v , for plugin : %v in horizontal-aggregations.json", column, view, plugin))

							update = true

						}

					}

				}

			}

		}

	}

	return update

}

func validateVerticalAggregations(configs MotadataMap) bool {

	update := false

	for metric := range DefaultVerticalAggregations {

		if !configs.Contains(metric) {

			configs[metric] = true

			configLogger.Info(fmt.Sprintf("added %v metric in vertical-aggregations.json", metric))

			update = true
		}
	}

	return update
}

func ValidateDefaultConfigs() bool {

	configs := make(MotadataMap)

	if !validateTempFile(ColumnConfigFile, validateColumns) {

		bytes, err := os.ReadFile(CurrentDir + PathSeparator + ConfigDir + PathSeparator + ColumnConfigFile)

		if err != nil {

			if err = updateConfig(DefaultColumns, ColumnConfigFile); err != nil {

				configLogger.Error(fmt.Sprintf(ErrorUpdateConfigs, ColumnConfigFile, err.Error()))

				return false

			}

		} else {

			if err = json.Unmarshal(bytes, &configs); err != nil {

				if err = updateConfig(DefaultColumns, ColumnConfigFile); err != nil {

					configLogger.Error(fmt.Sprintf(ErrorUpdateConfigs, ColumnConfigFile, err.Error()))

					return false

				}

			} else {

				if validateColumns(configs) {

					if err = updateConfig(configs, ColumnConfigFile); err != nil {

						configLogger.Error(fmt.Sprintf(ErrorUpdateConfigs, ColumnConfigFile, err.Error()))

						return false

					}
				}
			}
		}
	}

	if !validateTempFile(HorizontalAggregations, validateHorizontalAggregations) {

		clear(configs)

		bytes, err := os.ReadFile(CurrentDir + PathSeparator + ConfigDir + PathSeparator + HorizontalAggregations)

		if err != nil {

			if err = updateConfig(defaultHorizontalAggregationColumns, HorizontalAggregations); err != nil {

				configLogger.Error(fmt.Sprintf(ErrorUpdateConfigs, HorizontalAggregations, err.Error()))

				return false

			}

		} else {

			if err = json.Unmarshal(bytes, &configs); err != nil {

				if err = updateConfig(defaultHorizontalAggregationColumns, HorizontalAggregations); err != nil {

					configLogger.Error(fmt.Sprintf(ErrorUpdateConfigs, HorizontalAggregations, err.Error()))

					return false

				}

			} else {

				if validateHorizontalAggregations(configs) {

					if err = updateConfig(configs, HorizontalAggregations); err != nil {

						configLogger.Error(fmt.Sprintf(ErrorUpdateConfigs, HorizontalAggregations, err.Error()))

						return false

					}

				}

			}

		}

	}

	if !validateTempFile(VerticalAggregations, validateVerticalAggregations) {

		bytes, err := os.ReadFile(CurrentDir + PathSeparator + ConfigDir + PathSeparator + VerticalAggregations)

		if err != nil {

			if err = updateConfig(DefaultVerticalAggregations, VerticalAggregations); err != nil {

				configLogger.Error(fmt.Sprintf(ErrorUpdateConfigs, VerticalAggregations, err.Error()))

				return false

			}

		} else {

			clear(configs)

			if err = json.Unmarshal(bytes, &configs); err != nil {

				if err = updateConfig(DefaultVerticalAggregations, VerticalAggregations); err != nil {

					configLogger.Error(fmt.Sprintf(ErrorUpdateConfigs, VerticalAggregations, err.Error()))

					return false

				}

			} else {

				if validateVerticalAggregations(configs) {

					if err = updateConfig(configs, VerticalAggregations); err != nil {

						configLogger.Error(fmt.Sprintf(ErrorUpdateConfigs, VerticalAggregations, err.Error()))

						return false

					}
				}
			}
		}
	}

	return true

}

func validateTempFile(file string, validate func(configs MotadataMap) bool) bool {

	if _, err := os.Stat(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + file); err == nil {

		defer func() {

			_ = os.Remove(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + file)
		}()

		if bytes, err := os.ReadFile(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + file); err == nil {

			tempConfigs := make(MotadataMap)

			if err = json.Unmarshal(bytes, &tempConfigs); err == nil {

				if validate(tempConfigs) {

					bytes, _ := json.Marshal(tempConfigs)

					if os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+file, bytes, 0755) != nil {

						return false
					}
				}

				if err = os.Rename(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+file, CurrentDir+PathSeparator+ConfigDir+PathSeparator+file); err == nil {

					return true

				}

			}

		}

	}

	return false

}

func updateConfig(config MotadataMap, file string) error {

	bytes, _ := json.MarshalIndent(config, "", " ")

	_ = os.Remove(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + file)

	if err := os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+file, bytes, 0755); err != nil {

		return err

	}

	if err := os.Rename(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+file, CurrentDir+PathSeparator+ConfigDir+PathSeparator+file); err != nil {

		return err
	}

	return nil
}

func GetDataWriterEventChannelSize() int {

	if configs.Contains("datastore.broker.data.writer.event.channel.size") {

		return configs.GetIntValue("datastore.broker.data.writer.event.channel.size")
	}

	return 20_000
}

func GetDataAggregatorEventChannelSize() int {

	if configs.Contains("datastore.broker.data.aggregator.event.channel.size") {

		return configs.GetIntValue("datastore.broker.data.aggregator.event.channel.size")
	}

	return 20_000
}

func GetBrokerEventChannelSize() int {

	if configs.Contains("datastore.broker.event.channel.size") {

		return configs.GetIntValue("datastore.broker.event.channel.size")
	}

	return 20_000
}

func GetDatastoreEventChannelSize() int {

	if configs.Contains("datastore.event.channel.size") {

		return configs.GetIntValue("datastore.event.channel.size")
	}

	return 10_000
}

func GetPublisherEventChannelSize() int {

	if configs.Contains("datastore.publisher.event.channel.size") {

		return configs.GetIntValue("datastore.publisher.event.channel.size")
	}

	return 10_00_00
}

func GetBlobEncodingBytes() int {

	if configs.Contains("datastore.blob.encoding.bytes") {

		return configs.GetIntValue("datastore.blob.encoding.bytes")
	}

	return 400
}

func GetQueryCreationAgeThresholdSeconds() int {

	if configs.Contains(DatastoreQueryCreationAgeThresholdSeconds) {

		return configs.GetIntValue(DatastoreQueryCreationAgeThresholdSeconds)
	}

	return 120
}

func GetQueryStatsLogging() bool {

	return configs.GetStringValue(DatastoreQueryStatsLogging) == Yes
}

func GetObjectFlushTimerSeconds() int {

	if configs.Contains(DatastoreObjectFlushTimerSeconds) {

		return configs.GetIntValue(DatastoreObjectFlushTimerSeconds)
	}

	return 60
}
