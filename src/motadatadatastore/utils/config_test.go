/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-18             Vedant D. Dokania      Motadata-5200 Added testcases for new config column status flap max historical records
* 2025-03-05			 A<PERSON>l Shah			Motadata-5873 Added testcases for testing Data Writer Sync Timer Seconds
* 2025-05-26			 A<PERSON><PERSON>			Motadata-6275 Added testcases for testing Event Aggregator Pool Size and Metric Aggregator Pool Size
* 2025-05-26			 <PERSON><PERSON><PERSON>-6333 Added testcases for testing Query Discard Configuration
* 2025-06-10			 Hardik Vala			MOTADATA-6392  aggregation view aggregation column limit added - config test case
* 2025-06-23             Dhaval Bera            MOTADATA-6642  Added testcase for Coonfigurable Cache stores feature added for memory optimisation
* 2025-06-25			 Swapnil A. Dave		MOTADATA-6555  Added Test Case For Object Flush Timer Seconds Config
 */

package utils

import (
	"encoding/json"
	cp "github.com/otiai10/copy"
	"github.com/pbnjay/memory"
	"github.com/stretchr/testify/assert"
	"os"
	"path/filepath"
	"reflect"
	"runtime"
	"strings"
	"testing"
	"time"
)

func TestConfigParametersType1(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(GetEnvironmentType(), DatastoreDevEnvironment)

	assertions.Equal(20_000, GetDataWriterEventChannelSize())

	configs["datastore.broker.data.writer.event.channel.size"] = 10000

	assertions.Equal(10_000, GetDataWriterEventChannelSize())

	assertions.Equal(20_000, GetDataAggregatorEventChannelSize())

	configs["datastore.broker.data.aggregator.event.channel.size"] = 10000

	assertions.Equal(10_000, GetDataAggregatorEventChannelSize())

	assertions.Equal(28, GetEventAggregatorPoolSize())

	assertions.Equal(20, GetMetricAggregatorPoolSize())

	assertions.Equal(50, GetMaxStringBytes())

	assertions.Equal(20_000, GetBrokerEventChannelSize())

	configs["datastore.broker.event.channel.size"] = 10000

	assertions.Equal(10_000, GetBrokerEventChannelSize())

	assertions.Equal(10_000, GetDatastoreEventChannelSize())

	configs["datastore.event.channel.size"] = 5000

	assertions.Equal(5_000, GetDatastoreEventChannelSize())

	assertions.Equal(10_00_00, GetPublisherEventChannelSize())

	configs["datastore.publisher.event.channel.size"] = 1000

	assertions.Equal(1000, GetPublisherEventChannelSize())

	assertions.Equal(MaxValueBufferBytes, MaxPoolLength*8)

	assertions.Equal(GetDatastoreFlushTimerSeconds(), 23)

	assertions.Equal(GetDataWriterFlushTimerSeconds(), 5)

	assertions.Equal(GetQueryQueueSize(), 100)

	assertions.Equal(GetStoreSyncTimerSeconds(), 55)

	assertions.Equal(GetHost(), "localhost")

	assertions.Equal(GetDatastoreAvailabilityWriterPort(), "9471")

	assertions.Equal(GetDatastoreMetricWriterPort(), "9472")

	assertions.Equal(GetDatastoreEventWriterPort(), "9473")

	assertions.Equal(GetDatastoreNotificationPort(), "9455")

	assertions.Equal(GetDatastoreReaderPort(), "9457")

	assertions.Equal(GetMappingCleanupJobs(), 1)

	assertions.Equal(GetDatastorePublisherPort(), "9456")

	assertions.Equal(GetIOWorkerThreads(), 1)

	assertions.Equal(GetQueryAbortTimerSeconds(), 120)

	assertions.Equal(GetMaxFlowTopNSelectionGroups(), 100)

	assertions.Equal(GetStoreWALSizeBytes(Log), 1048576)

	assertions.Equal(GetStaticMetricWriters(), 2)

	assertions.Equal(GetDataWriterTxnBufferBytes(), 2*1024*1024)

	assertions.Equal(GetMaxSearchQueryRecords(), 1_00_000)

	DeploymentType = ExtraSmall

	assertions.Equal(GetMaxFlowTopNInsertionGroups(), 10000)

	assertions.Equal(GetMaxVerticalWriterCacheEntries(), 20000)

	assertions.Equal(GetMaxPoolLength(), 10000)

	assertions.Equal(GetLogOverflowLength(), 5000)

	assertions.Equal(GetFlowWorkerPoolLength(), 30000)

	assertions.Equal(GetMetricQueryExecutors(), 2)

	assertions.Equal(GetLogQueryExecutors(), 3)

	assertions.Equal(GetFlowQueryExecutors(), 3)

	assertions.Equal(GetDataAggregators(), 2)

	assertions.Equal(GetDrillDownQueryExecutors(), 2)

	assertions.Equal(GetAIOpsQueryExecutors(), 1)

	assertions.Equal(GetVerticalWriters(), 2)

	assertions.Equal(GetHorizontalWriters(), 2)

	assertions.Equal(GetMaxAggregationGroups(), 1_000_00)

	assertions.Equal(GetIndexWriters(), 3)

	assertions.Equal(GetAggregationJobs(), 2)

	assertions.Equal(GetMetricAggregators(), 2)

	assertions.Equal(GetStoreSyncJobs(), 3)

	assertions.Equal(GetStoreCleanUpJobs(), 2)

	assertions.Equal(GetMaxWorkerEvents(), 2)

	assertions.Equal(GetDiskIOWorkers(), 32)

	assertions.Equal(GetQueryWorkers(), 2)

	assertions.Equal(GetIOWorkerThreads(), 1)

	assertions.Equal(GetStoreWALSizeBytes(Log), 1048576)

	assertions.Equal(GetStaticMetricWriters(), 2)

	// inline = true

	DeploymentType = Small

	assertions.Equal(GetMaxPoolLength(), 10000)

	assertions.Equal(GetDataAggregators(), 2)

	assertions.Equal(GetLogOverflowLength(), 5000)

	assertions.Equal(GetFlowWorkerPoolLength(), 30000)

	assertions.Equal(GetMaxAggregationGroups(), 1_000_00)

	assertions.Equal(GetMaxHistoricalRecords(), 100_000)

	assertions.Equal(GetMaxGridRecords(), 10_0000)

	assertions.Equal(GetMetricQueryExecutors(), 3)

	assertions.Equal(GetLogQueryExecutors(), 3)

	assertions.Equal(GetFlowQueryExecutors(), 3)

	assertions.Equal(GetDrillDownQueryExecutors(), 3)

	assertions.Equal(GetAIOpsQueryExecutors(), 1)

	assertions.Equal(GetVerticalWriters(), 2)

	assertions.Equal(GetHorizontalWriters(), 2)

	assertions.Equal(GetIndexWriters(), 4)

	assertions.Equal(GetAggregationJobs(), 2)

	assertions.Equal(GetMetricAggregators(), 2)

	assertions.Equal(GetStoreSyncJobs(), 4)

	assertions.Equal(GetStoreCleanUpJobs(), 3)

	assertions.Equal(GetMaxWorkerEvents(), 3)

	assertions.Equal(GetDiskIOWorkers(), 40)

	assertions.Equal(GetQueryWorkers(), 2)

	assertions.Equal(GetIOWorkerThreads(), 1)

	assertions.Equal(GetStoreWALSizeBytes(Log), 1048576)

	assertions.Equal(GetStaticMetricWriters(), 2)

	DeploymentType = Medium

	assertions.Equal(GetMaxPoolLength(), 20000)

	assertions.Equal(GetDataAggregators(), 4)

	assertions.Equal(GetLogOverflowLength(), 5000)

	assertions.Equal(GetFlowWorkerPoolLength(), 60000)

	assertions.Equal(GetMetricQueryExecutors(), 4)

	assertions.Equal(GetLogQueryExecutors(), 5)

	assertions.Equal(GetFlowQueryExecutors(), 4)

	assertions.Equal(GetDrillDownQueryExecutors(), 4)

	assertions.Equal(GetAIOpsQueryExecutors(), 3)

	assertions.Equal(GetVerticalWriters(), 4)

	assertions.Equal(GetHorizontalWriters(), 4)

	assertions.Equal(GetIndexWriters(), 6)

	assertions.Equal(GetAggregationJobs(), 3)

	assertions.Equal(GetMetricAggregators(), 3)

	assertions.Equal(GetEventAggregators(), 4)

	assertions.Equal(GetStoreSyncJobs(), 5)

	assertions.Equal(GetStoreCleanUpJobs(), 4)

	assertions.Equal(GetMaxWorkerEvents(), 3)

	assertions.Equal(GetDiskIOWorkers(), 56)

	assertions.Equal(GetQueryWorkers(), 3)

	assertions.Equal(GetIOWorkerThreads(), 2)

	assertions.Equal(GetStoreWALSizeBytes(Log), 1048576)

	assertions.Equal(GetStaticMetricWriters(), 3)

	LocalDatastore = false

	// inline == false
	DeploymentType = Small

	assertions.Equal(GetMaxAggregationGroups(), 2_000_00)

	assertions.Equal(GetMaxHistoricalRecords(), 100000)

	assertions.Equal(GetMaxGridRecords(), 100000)

	assertions.Equal(GetMaxPoolLength(), 10000)

	assertions.Equal(GetLogOverflowLength(), 5000)

	assertions.Equal(GetMetricQueryExecutors(), 5)

	assertions.Equal(GetLogQueryExecutors(), 6)

	assertions.Equal(GetFlowQueryExecutors(), 5)

	assertions.Equal(GetDrillDownQueryExecutors(), 4)

	assertions.Equal(GetAIOpsQueryExecutors(), 2)

	assertions.Equal(GetVerticalWriters(), 5)

	assertions.Equal(GetHorizontalWriters(), 5)

	assertions.Equal(GetIndexWriters(), 7)

	assertions.Equal(GetAggregationJobs(), 3)

	assertions.Equal(GetMetricAggregators(), 3)

	assertions.Equal(GetStoreSyncJobs(), 5)

	assertions.Equal(GetStoreCleanUpJobs(), 3)

	assertions.Equal(GetMaxWorkerEvents(), 3)

	assertions.Equal(GetDiskIOWorkers(), 48)

	assertions.Equal(GetQueryWorkers(), 3)

	assertions.Equal(GetIOWorkerThreads(), 1)

	assertions.Equal(GetStoreWALSizeBytes(Log), 1048576)

	assertions.Equal(GetStaticMetricWriters(), 2)

	DeploymentType = Medium

	assertions.Equal(GetMaxPoolLength(), 20000)

	assertions.Equal(GetLogOverflowLength(), 5000)

	assertions.Equal(GetMetricQueryExecutors(), 6)

	assertions.Equal(GetLogQueryExecutors(), 8)

	assertions.Equal(GetFlowQueryExecutors(), 6)

	assertions.Equal(GetDrillDownQueryExecutors(), 5)

	assertions.Equal(GetAIOpsQueryExecutors(), 4)

	assertions.Equal(GetVerticalWriters(), 7)

	assertions.Equal(GetHorizontalWriters(), 7)

	assertions.Equal(GetIndexWriters(), 9)

	assertions.Equal(GetAggregationJobs(), 5)

	assertions.Equal(GetMetricAggregators(), 5)

	assertions.Equal(GetStoreSyncJobs(), 6)

	assertions.Equal(GetStoreCleanUpJobs(), 4)

	assertions.Equal(GetMaxWorkerEvents(), 3)

	assertions.Equal(GetDiskIOWorkers(), 64)

	assertions.Equal(GetQueryWorkers(), 5)

	assertions.Equal(GetIOWorkerThreads(), 2)

	assertions.Equal(GetStoreWALSizeBytes(Log), 1048576)

	assertions.Equal(GetStaticMetricWriters(), 3)

	// inline = true
	DeploymentType = Large

	assertions.Equal(GetMaxPoolLength(), 40000)

	assertions.Equal(GetDataAggregators(), 6)

	assertions.Equal(GetLogOverflowLength(), 10000)

	assertions.Equal(GetFlowWorkerPoolLength(), 240000)

	assertions.Equal(GetMetricQueryExecutors(), 8)

	assertions.Equal(GetLogQueryExecutors(), 10)

	assertions.Equal(GetFlowQueryExecutors(), 8)

	assertions.Equal(GetDrillDownQueryExecutors(), 8)

	assertions.Equal(GetAIOpsQueryExecutors(), 6)

	assertions.Equal(GetVerticalWriters(), 13)

	assertions.Equal(GetHorizontalWriters(), 11)

	assertions.Equal(GetIndexWriters(), 11)

	assertions.Equal(GetAggregationJobs(), 6)

	assertions.Equal(GetMetricAggregators(), 7)

	assertions.Equal(GetStoreSyncJobs(), 8)

	assertions.Equal(GetStoreCleanUpJobs(), 5)

	assertions.Equal(GetMaxWorkerEvents(), 4)

	assertions.Equal(GetDiskIOWorkers(), 128)

	assertions.Equal(GetQueryWorkers(), 6)

	assertions.Equal(GetIOWorkerThreads(), 3)

	assertions.Equal(GetStoreWALSizeBytes(Log), 2097152)

	assertions.Equal(GetStaticMetricWriters(), 5)

	DeploymentType = ExtraLarge

	assertions.Equal(GetMaxPoolLength(), 60000)

	assertions.Equal(GetDataAggregators(), 8)

	assertions.Equal(GetLogOverflowLength(), 10000)

	assertions.Equal(GetFlowWorkerPoolLength(), 360000)

	assertions.Equal(GetMetricQueryExecutors(), 10)

	assertions.Equal(GetLogQueryExecutors(), 12)

	assertions.Equal(GetFlowQueryExecutors(), 10)

	assertions.Equal(GetDrillDownQueryExecutors(), 10)

	assertions.Equal(GetAIOpsQueryExecutors(), 8)

	assertions.Equal(GetVerticalWriters(), 17)

	assertions.Equal(GetHorizontalWriters(), 13)

	assertions.Equal(GetIndexWriters(), 15)

	assertions.Equal(GetAggregationJobs(), 8)

	assertions.Equal(GetMetricAggregators(), 9)

	assertions.Equal(GetStoreSyncJobs(), 10)

	assertions.Equal(GetStoreCleanUpJobs(), 6)

	assertions.Equal(GetMaxWorkerEvents(), 5)

	assertions.Equal(GetDiskIOWorkers(), 160)

	assertions.Equal(GetQueryWorkers(), 8)

	assertions.Equal(GetIOWorkerThreads(), 4)

	assertions.Equal(GetMinimumDiskSpaceAvailableThresholdPercent(), 0.6)

	assertions.Equal(GetStoreWALSizeBytes(Log), 3145728)

	assertions.Equal(GetStaticMetricWriters(), 7)

	DatastoreFlushTimerSeconds = 60

	DatastoreBrokerWriterFlushTimerSeconds = 60

	assertions.Equal(GetAggregationViewIndexableColumnLimit(), 8)

	assertions.Equal(GetAggregationViewAggregationColumnLimit(), 10)

	configs["datastore.aggregation.view.indexable.column.limit"] = 12

	configs["datastore.aggregation.view.aggregation.column.limit"] = 14

	assertions.Equal(GetAggregationViewIndexableColumnLimit(), 12)

	assertions.Equal(GetAggregationViewAggregationColumnLimit(), 14)

}

func TestConfigParametersType2(t *testing.T) {

	assertion := assert.New(t)

	configs := MotadataMap{}

	configs[SystemLogLevel] = 2

	configs[TaskLogging] = "no"

	configs[DatastoreHost] = "*************"

	configs["deployment.type"] = 1

	configs[DatastoreLogRetentionDays] = 0

	configs["datastore.event.aggregator.pool.size"] = 20

	configs["datastore.metric.aggregator.pool.size"] = 24

	configs["datastore.max.string.bytes"] = 20

	configs[DatastoreMaxFlowTopNSelectionGroups] = 50

	configs["datastore.mapping.cleanup.jobs"] = 4

	configs["datastore.max.worker.event.key.group.length"] = 256

	configs["socket.connection.max.idle.time.seconds"] = 10

	configs["datastore.data.writer.value.buffer.bytes"] = 10

	configs["metric.aggregation.intervals"] = []float64{10, 30, 20, 40}

	configs["event.aggregation.intervals"] = []float64{10, 30, 20, 40}

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	LogLevelResetTimerSeconds = 1

	assertion.Equal(GetSocketConnectionMaxIdleTimeSeconds(), time.Duration(10000000000))

	assertion.Equal(GetMaxWorkerEventKeyGroupLength(), 256)

	assertion.Equal(1, GetLogRetentionDays())

	assertion.Equal(50, GetMaxFlowTopNSelectionGroups())

	assertion.Equal(4, GetMappingCleanupJobs())

	assertion.Equal(10, GetDataWriterValueBufferBytes())

	delete(configs, "deployment.type")

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(0, GetDeploymentType())

	maxIndexerProbes := 10000

	configs["datastore.max.indexer.records"] = maxIndexerProbes

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(maxIndexerProbes, GetMaxIndexerProbes())

	providedThreshold := 10000

	configs["datastore.indexer.threshold.percent"] = providedThreshold

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(providedThreshold, GetIndexerThresholdPercent())

	configs["aggregation"] = "no"

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.False(GetAggregationStatus())

	assertion.Equal((memory.TotalMemory()*80)/100, GetGCMemoryLimit())

	configs["datastore.gc.memory.threshold"] = 10

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal((memory.TotalMemory()*10)/100, GetGCMemoryLimit())

	configs["datastore.cache.size.mb"] = 500

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(500, GetCacheSizeMB())

	configs["datastore.disk.io.workers"] = 10

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(10, GetDiskIOWorkers())

	configs["datastore.writer.max.buffer.size.mb"] = 10

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	configs["datastore.query.abort.timer.seconds"] = 500

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(500, GetQueryAbortTimerSeconds())

	configs["datastore.query.queue.size"] = 500

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(500, GetQueryQueueSize())

	assertion.Equal(60, GetVerticalAggregationTimerSeconds())

	assertion.Equal(150, GetHorizontalAggregationTimerSeconds())

	configs["datastore.vertical.aggregation.timer.seconds"] = 15

	configs["datastore.horizontal.aggregation.timer.seconds"] = 15

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(15, GetVerticalAggregationTimerSeconds())

	assertion.Equal(15, GetHorizontalAggregationTimerSeconds())

	assertion.Equal("9471", GetDatastoreAvailabilityWriterPort())

	assertion.Equal("9457", GetDatastoreReaderPort())

	assertion.Equal("9456", GetDatastorePublisherPort())

	assertion.Equal("9461", GetAIOpsEnginePublisherPort())

	assertion.Equal("9460", GetAIOpsEngineSubscriberPort())

	configs[DatastoreAvailabilityWriterPort] = "8888"

	configs[DatastoreMetricWriterPort] = "8888"

	configs[DatastoreEventWriterPort] = "8888"

	configs[DatastoreNotificationPort] = "8888"

	configs[DatastoreReaderPort] = "8888"

	configs[DatastorePublisherPort] = "8888"

	configs[AIOpsEnginePublisherPort] = "8888"

	configs[AIOpsEngineSubscriberPort] = "8888"

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal("8888", GetDatastoreAvailabilityWriterPort())

	assertion.Equal("8888", GetDatastoreMetricWriterPort())

	assertion.Equal("8888", GetDatastoreEventWriterPort())

	assertion.Equal("8888", GetDatastoreNotificationPort())

	assertion.Equal("8888", GetDatastoreReaderPort())

	assertion.Equal("8888", GetDatastorePublisherPort())

	assertion.Equal("8888", GetAIOpsEnginePublisherPort())

	assertion.Equal("8888", GetAIOpsEngineSubscriberPort())

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(23, GetDatastoreFlushTimerSeconds())

	assertion.Equal(5, GetDataWriterFlushTimerSeconds())

	configs["datastore.flush.timer.seconds"] = 15

	configs["datastore.broker.data.writer.flush.timer.seconds"] = 15

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(15, GetDatastoreFlushTimerSeconds())

	assertion.Equal(15, GetDataWriterFlushTimerSeconds())

	assertion.Equal(90, GetMemoryPoolShrinkTimerSeconds())

	configs["datastore.memory.pool.shrink.timer.seconds"] = 15

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(15, GetMemoryPoolShrinkTimerSeconds())

	assertion.Equal(55, GetStoreSyncTimerSeconds())

	configs["datastore.store.sync.timer.seconds"] = 15

	delete(configs, DatastoreHost)

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(15, GetStoreSyncTimerSeconds())

	assertion.Equal("localhost", GetHost())

	configs[DatastoreHost] = "127.0.0.1"

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal("127.0.0.1", GetHost())

	configs["datastore.cleanup.job.pool.length"] = 10000

	configs["datastore.sync.job.pool.length"] = 1000

	configs["datastore.metric.worker.pool.length"] = 10000

	configs["datastore.metric.executor.pool.length"] = 1000

	configs["datastore.log.executor.pool.length"] = 10000

	configs["datastore.flow.executor.pool.length"] = 10000

	configs["datastore.aiops.executor.pool.length"] = 10000

	configs["datastore.drill.down.executor.pool.length"] = 10000

	configs["datastore.event.aggregator.pool.length"] = 1000

	configs["datastore.max.iouring.instance"] = 1000

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(GetCleanupJobPoolLength(), 10000)

	assertion.Equal(GetSyncJobPoolLength(), 1000)

	assertion.Equal(GetMetricWorkerPoolLength(), 10000)

	assertion.Equal(GetMetricExecutorPoolLength(), 1000)

	assertion.Equal(GetLogExecutorPoolLength(), 10000)

	assertion.Equal(GetFlowExecutorPoolLength(), 10000)

	assertion.Equal(GetAIOpsExecutorPoolLength(), 10000)

	assertion.Equal(GetDrillDownExecutorPoolLength(), 10000)

	assertion.Equal(GetEventAggregatorPoolLength(), 1000)

}

func TestConfigParametersType3(t *testing.T) {

	assertion := assert.New(t)

	configs := MotadataMap{}

	configs[SystemLogLevel] = 2
	configs[TaskLogging] = "no"
	configs[DatastoreHost] = "*************"
	configs["datastore.tokenizer.length"] = 60

	configs[DatastoreMaxVerticalWriterCacheEntries] = 5000

	configs[DatastoreMaxCurrentDayEventFileQueueSize] = 4

	configs[DatastoreMaxPreviousDayEventFileQueueSize] = 2

	// small deployment type

	configs["deployment.type"] = Small

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(5000, GetMaxVerticalWriterCacheEntries())

	assertion.Equal(60, GetTokenizerLength())

	assertion.Equal(3, GetAggregationJobs())

	assertion.Equal(3, GetMetricAggregators())

	assertion.Equal(90, GetVerticalAggregationPastProbeDays())

	assertion.Equal(30, GetHorizontalAggregationPastProbeDays())

	assertion.Equal(5, GetStoreSyncJobs())

	assertion.Equal(3, GetStoreCleanUpJobs())

	assertion.Equal(1024, GetCacheSizeMB())

	assertion.Equal(5000, GetMappingCacheRecords(""))

	assertion.Equal(4, GetDatastoreMaxCurrentDayEventFileQueueSize())

	assertion.Equal(2, GetDatastoreMaxPreviousDayEventFileQueueSize())

	// medium deployment type

	configs["deployment.type"] = Medium

	configs[DatastoreMaxFlowTopNInsertionRecords] = 50000

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(50000, GetMaxFlowTopNInsertionGroups())

	assertion.Equal(5, GetAggregationJobs())

	assertion.Equal(5, GetMetricAggregators())

	assertion.Equal(6, GetStoreSyncJobs())

	assertion.Equal(4, GetStoreCleanUpJobs())

	assertion.Equal(2048, GetCacheSizeMB())

	assertion.Equal(10_000, GetMappingCacheRecords(""))

	// large deployment type

	configs["deployment.type"] = Large

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(6, GetAggregationJobs())

	assertion.Equal(7, GetMetricAggregators())

	assertion.Equal(8, GetStoreSyncJobs())

	assertion.Equal(5, GetStoreCleanUpJobs())

	assertion.Equal(8192, GetCacheSizeMB())

	assertion.Equal(20_000, GetMappingCacheRecords(""))

	// extra large deployment type

	configs["deployment.type"] = ExtraLarge

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(8, GetAggregationJobs())

	assertion.Equal(9, GetMetricAggregators())

	assertion.Equal(10, GetStoreSyncJobs())

	assertion.Equal(6, GetStoreCleanUpJobs())

	assertion.Equal(16384, GetCacheSizeMB())

	assertion.Equal(40_000, GetMappingCacheRecords(""))

	// assigning wrong deployment type for checking default values

	configs["deployment.type"] = 5

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(2, GetAggregationJobs())

	assertion.Equal(2, GetMetricAggregators())

	assertion.Equal(3, GetStoreSyncJobs())

	assertion.Equal(2, GetStoreCleanUpJobs())

	assertion.Equal(512, GetCacheSizeMB())

	// getting custom provided values

	configs["datastore.aggregation.jobs"] = 15

	configs["datastore.horizontal.writers"] = 1

	configs["datastore.vertical.writers"] = 1

	configs["datastore.index.writers"] = 1

	configs["datastore.metric.aggregators"] = 15

	configs["datastore.event.aggregators"] = 15

	configs["datastore.vertical.aggregation.past.probe.days"] = 15

	configs["datastore.horizontal.aggregation.past.probe.days"] = 15

	configs["datastore.minimum.available.disk.space.threshold.percent"] = 0.2

	configs["datastore.store.sync.jobs"] = 15

	configs["datastore.store.cleanup.jobs"] = 15

	configs["datastore.drill.down.query.executors"] = 15

	configs["mapping.cache.records"] = 100

	configs["datastore.broker.data.aggregators"] = 2

	configs[DatastoreBrokerMaxAggregationGroupings] = 10000

	configs[DatastoreMaxWorkerEvents] = 1

	configs["mapping.store.cache.configs"] = MotadataMap{
		"event.source-mappings": 10,
	}

	configs["datastore.store.wal.size.mb"] = MotadataMap{

		"11": 5,
	}

	configs["datastore.static.metric.writers"] = 2

	configs["datastore.data.writer.txn.buffer.bytes"] = 10 * 1024 * 1024

	SystemBootSequence = Datastore

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(0.2, GetMinimumDiskSpaceAvailableThresholdPercent())

	assertion.Equal(10000, GetDataAggregatorMaxGroupingLimit())

	assertion.Equal(1, GetVerticalWriters())

	assertion.Equal(1, GetHorizontalWriters())

	assertion.Equal(1, GetIndexWriters())

	assertion.Equal(1, GetMaxWorkerEvents())

	assertion.Equal(2, GetDataAggregators())

	assertion.Equal(15, GetAggregationJobs())

	assertion.Equal(15, GetMetricAggregators())

	assertion.Equal(15, GetEventAggregators())

	assertion.Equal(15, GetVerticalAggregationPastProbeDays())

	assertion.Equal(15, GetHorizontalAggregationPastProbeDays())

	assertion.Equal(15, GetStoreSyncJobs())

	assertion.Equal(15, GetStoreCleanUpJobs())

	assertion.Equal(15, GetDrillDownQueryExecutors())

	assertion.Equal(100, GetMappingCacheRecords(""))

	assertion.Equal(10, GetMappingCacheRecords("event.source-mappings"))

	assertion.Equal(5*1024*1024, GetStoreWALSizeBytes(Log))

	assertion.Equal(2, GetStaticMetricWriters())

	assertion.Equal(GetDataWriterTxnBufferBytes(), 10*1024*1024)

	configs.Clear()

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Nil(getMappingStoreCacheConfigs())

}

func TestInvalidConfig(t *testing.T) {

	assertion := assert.New(t)

	InvalidConfig := "{"

	EnvironmentType = DatastoreTestEnvironment

	err := InitConfigs([]byte(InvalidConfig))

	assertion.False(err)

}

func TestResetLogLevel(t *testing.T) {

	assertions := assert.New(t)

	context := make(MotadataMap)

	context[SystemLogLevel] = 0

	context[TaskLogging] = "yes"

	context[DatastoreMaxHistoricalRecords] = 1000

	context[DatastoreMaxSearchQueryRecords] = 1000

	context[DatastoreMaxGridRecords] = 100

	context[DatastoreMaxAggregationGroups] = 100

	context[DatastoreMaxStatusFlapHistoricalRecords] = 1000

	context[DatastoreMaxWorkerEvents] = 32

	context[DatastoreLogRetentionDays] = 1

	context[DatastoreBrokerDataAggregatorSyncTimerSeconds] = 10

	context[DatastoreMotaOpsServiceStatus] = Yes

	context[DatastoreMaxFlowTopNSelectionGroups] = 10

	context[DatastoreBrokerMaxAggregationGroupings] = 10000

	context[DatastoreMaxCurrentDayEventFileQueueSize] = 4

	context[DatastoreMaxPreviousDayEventFileQueueSize] = 2

	context[DatastoreMaxFlowTopNInsertionRecords] = 10

	context[DatastoreMaxVerticalWriterCacheEntries] = 10

	context[DatastoreBrokerDataWriterSyncTimers] = MotadataMap{

		"1": 100,
	}

	bytes, err := json.Marshal(context)

	assertions.Nil(err)

	LogLevelResetTimerSeconds = 1

	_ = os.Mkdir(ConfigDir, 0777)

	WriteConfigs(configs)

	tempConfigs := MotadataMap{}

	for key, value := range configs {

		tempConfigs[key] = value
	}

	_, _ = ReloadConfigs(bytes)

	assertions.True(TraceEnabled())

	time.Sleep(time.Second * 5)

	assertions.False(TraceEnabled())

	LogLevelResetTimerSeconds = 1

	MotadataDatastoreConfigFile = "motadata-datastore1.json"

	_ = os.Remove(CurrentDir + PathSeparator + ConfigDir + PathSeparator + MotadataDatastoreConfigFile)

	err = ResetLogLevel(2)

	assertions.NotNil(err)

	assertions.Contains(err.Error(), "resetting config")

	MotadataDatastoreConfigFile = "motadata-datastore.json"

	_ = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+MotadataDatastoreConfigFile, []byte("1234"), 0777)

	err = ResetLogLevel(LogLevelResetTimerSeconds)

	assertions.NotNil(err)

	assertions.Contains(err.Error(), "cannot unmarshal")

	WriteConfigs(tempConfigs)

	_, _ = ReloadConfigs(bytes)
}

func TestErrorReloadConfig(t *testing.T) {

	assertions := assert.New(t)

	err, _ := ReloadConfigs(nil)

	assertions.NotNil(err)

	assertions.Contains(err.Error(), "unable to read the changes in")
}

func TestGCNotifier(t *testing.T) {

	assertions := assert.New(t)

	context := MotadataMap{}

	context[SystemLogLevel] = 2

	bytes, err := json.Marshal(&context)

	assertions.Nil(err)

	err, _ = ReloadConfigs(bytes)

	assertions.Nil(err)

	time.Sleep(time.Second * 2)

	context[SystemLogLevel] = 0

	bytes, err = json.Marshal(&context)

	assertions.Nil(err)

	err, _ = ReloadConfigs(bytes)

	time.Sleep(time.Second * 2)

	assertions.Nil(err)

	go initGCTracer()

	time.Sleep(time.Second * 2)

	assertions.NotNil(gcNotifier)

	CloseGCTracer()

	assertions.Nil(gcNotifier)
}

func TestGetDataWriterValueBufferBytes(t *testing.T) {

	deploymentType := DeploymentType

	defer func() {
		DeploymentType = deploymentType
	}()

	DeploymentType = Medium

	assert.Equal(t, 5*1024*1024, GetDataWriterValueBufferBytes())

	DeploymentType = Large

	assert.Equal(t, 10*1024*1024, GetDataWriterValueBufferBytes())

	DeploymentType = ExtraLarge

	assert.Equal(t, 15*1024*1024, GetDataWriterValueBufferBytes())

	DeploymentType = 0

	assert.Equal(t, 5*1024*1024, GetDataWriterValueBufferBytes())

}

func TestGetMaxPoolLength(t *testing.T) {

	conf := configs

	defer func() {
		configs = conf
	}()

	configs = MotadataMap{"datastore.max.pool.length": 100}

	assert.Equal(t, 100, GetMaxPoolLength())

}

func TestGetDataWriters(t *testing.T) {

	conf := configs

	deploymentType := DeploymentType

	defer func() {
		configs = conf

		DeploymentType = deploymentType
	}()

	DeploymentType = Small

	assert.Equal(t, 2, GetDataWriters())

	DeploymentType = Medium

	assert.Equal(t, 4, GetDataWriters())

	DeploymentType = Large

	assert.Equal(t, 6, GetDataWriters())

	DeploymentType = ExtraLarge

	assert.Equal(t, 8, GetDataWriters())

	DeploymentType = 99

	assert.Equal(t, 2, GetDataWriters())

	configs = MotadataMap{"datastore.broker.data.writers": 100}

	assert.Equal(t, 100, GetDataWriters())

}

func TestGetAggregationStatus(t *testing.T) {

	conf := configs

	defer func() {
		configs = conf
	}()

	configs = MotadataMap{}

	assert.Equal(t, true, GetAggregationStatus())

	configs["aggregation"] = Yes

	assert.Equal(t, true, GetAggregationStatus())

	configs["aggregation"] = No

	assert.Equal(t, false, GetAggregationStatus())

}

func TestInitQueryProcessors(t *testing.T) {

	conf := configs

	defer func() {
		configs = conf
	}()

	configs = MotadataMap{"datastore.query.workers": 10,
		"datastore.metric.query.executors": 10}

	InitQueryProcessors()

	assert.Equal(t, 10, Workers)
	assert.Equal(t, 10, MetricQueryExecutors)

}

func TestInitGcTracer(t *testing.T) {

	go initGCTracer()

	time.Sleep(time.Millisecond * 500)

	runtime.GC()

	runtime.GC()

	gcTracer.Store(false)

	runtime.GC()

	runtime.GC()

	time.Sleep(time.Second * 1)

	bytes, err := ReadLogFile("Configs", "system")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "gc tracer stopped as trace enabled limit reached in")

}

func TestGetFlowWorkerPoolLength(t *testing.T) {

	configs["datastore.flow.worker.pool.length"] = 50000

	assert.Equal(t, GetFlowWorkerPoolLength(), 50000)

}

func TestGetLogWorkerPoolLength(t *testing.T) {

	configs["datastore.log.worker.pool.length"] = 50000

	assert.Equal(t, GetLogWorkerPoolLength(), 50000)

}

func TestGetGCMemoryLimit(t *testing.T) {

	LocalDatastore = true

	SystemBootSequence = Broker

	configs["datastore.broker.gc.memory.threshold"] = 0

	assert.Equal(t, GetGCMemoryLimit(), uint64(5*1024*1024*1024))

}

func TestGetAIOpsQueryExecutors(t *testing.T) {

	configs["datastore.aiops.query.executors"] = 100

	assert.Equal(t, GetAIOpsQueryExecutors(), 100)

}

func TestSetDataWriterSyncTimers(t *testing.T) {

	assertions := assert.New(t)

	configs[DatastoreBrokerDataWriterSyncTimers] = MotadataMap{

		"1": float64(100),
	}

	SetDataWriterSyncTimers()

	assertions.Equal(DataWriterSyncTimers[PerformanceMetric], int64(100))

	assertions.Equal(DataWriterSyncTimers[Log], DataWriterSyncMaxTimerSeconds)

}

func TestGetIOWorkerThreads(t *testing.T) {

	configs["datastore.io.worker.threads"] = 100

	assert.Equal(t, GetIOWorkerThreads(), 100)

}

func TestGetMotaOpsServicePort(t *testing.T) {

	configs[DatastoreMotaOpsServicePort] = 100

	assert.Equal(t, GetMotaOpsServicePort(), 100)

	configs[DatastoreMotaOpsServiceStatus] = Yes

	assert.Equal(t, getMotaOpsServiceStatus(), true)

}

func TestGetHorizontalAggregationTimerSeconds(t *testing.T) {

	assert.Equal(t, 150, GetHorizontalAggregationTimerSeconds())

}

func TestGetMessageContainsViewHorizontalAggregationPastProbeDays(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(1, GetFullTextSearchingAggregationPastProbeDays())

	configs["datastore.fulltext.searching.aggregation.past.probe.days"] = 4

	assertions.Equal(4, GetFullTextSearchingAggregationPastProbeDays())

	configs["datastore.fulltext.searching.aggregation.past.probe.days"] = 100

	assertions.Equal(7, GetFullTextSearchingAggregationPastProbeDays())

}

func TestInitBrokerConfig(t *testing.T) {

	assertion := assert.New(t)

	SystemBootSequence = Broker

	configs := MotadataMap{}

	InitConfigs(UpdateConfigs([]byte("{}"), configs))

	assertion.Equal(DataWriters, GetDataWriters())
}

func TestWithoutConfigDirectory(t *testing.T) {

	assertions := assert.New(t)

	err := os.RemoveAll(CurrentDir + PathSeparator + ConfigDir)

	assertions.Nil(err)

	assertions.False(ValidateDefaultConfigs())

}

func TestValidDefaultConfigs(t *testing.T) {
	assertions := assert.New(t)

	err := os.RemoveAll(CurrentDir + PathSeparator + ConfigDir)

	assertions.Nil(err)

	err = os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	assertions.Nil(err)

	bytes, err := json.Marshal(DefaultColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+ColumnConfigFile, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(defaultHorizontalAggregationColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+HorizontalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultVerticalAggregations)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+VerticalAggregations, bytes, 0755)

	assertions.Nil(err)

	assertions.True(ValidateDefaultConfigs())

}

func TestValidDefaultConfigsv2(t *testing.T) {

	configDirectory := filepath.Dir(filepath.Dir(filepath.Dir(CurrentDir))) + PathSeparator + ConfigDir

	assertions := assert.New(t)

	err := os.RemoveAll(CurrentDir + PathSeparator + ConfigDir)

	assertions.Nil(err)

	err = os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	err = cp.Copy(configDirectory, CurrentDir+PathSeparator+ConfigDir)

	assertions.Nil(err)

	assertions.True(ValidateDefaultConfigs())

	os.RemoveAll(CurrentDir + PathSeparator + ConfigDir)

}

func TestValidTempColumnConfigs(t *testing.T) {

	assertions := assert.New(t)

	err := os.RemoveAll(CurrentDir + PathSeparator + ConfigDir)

	assertions.Nil(err)

	err = os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	assertions.Nil(err)

	bytes, err := json.Marshal(defaultHorizontalAggregationColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+HorizontalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultVerticalAggregations)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+VerticalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+ColumnConfigFile, bytes, 0755)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+ColumnConfigFile, bytes, 0755)

	assertions.Nil(err)

	assertions.True(ValidateDefaultConfigs())

}

func TestInvalidTempColumnConfigs(t *testing.T) {

	assertions := assert.New(t)

	err := os.RemoveAll(CurrentDir + PathSeparator + ConfigDir)

	assertions.Nil(err)

	err = os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	assertions.Nil(err)

	bytes, err := json.Marshal(defaultHorizontalAggregationColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+HorizontalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultVerticalAggregations)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+VerticalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+ColumnConfigFile, bytes, 0755)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+ColumnConfigFile, bytes[1:], 0755)

	assertions.Nil(err)

	assertions.True(ValidateDefaultConfigs())

}

func TestInvalidTempColumnConfigsv2(t *testing.T) {

	assertions := assert.New(t)

	err := os.RemoveAll(CurrentDir + PathSeparator + ConfigDir)

	assertions.Nil(err)

	err = os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	assertions.Nil(err)

	bytes, err := json.Marshal(defaultHorizontalAggregationColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+HorizontalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultVerticalAggregations)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+VerticalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+ColumnConfigFile, bytes, 0755)

	os.MkdirAll(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+ColumnConfigFile, 0755)

	assertions.Nil(err)

	assertions.True(ValidateDefaultConfigs())

}

func TestHorizontalAggregations(t *testing.T) {

	assertions := assert.New(t)

	err := os.RemoveAll(CurrentDir + PathSeparator + ConfigDir)

	assertions.Nil(err)

	err = os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	assertions.Nil(err)

	bytes, err := json.Marshal(defaultHorizontalAggregationColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+HorizontalAggregations, bytes, 0755)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+HorizontalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultVerticalAggregations)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+VerticalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+ColumnConfigFile, bytes, 0755)

	assertions.Nil(err)

	assertions.True(ValidateDefaultConfigs())

}

func TestInvalidHorizontalAggregations(t *testing.T) {

	assertions := assert.New(t)

	err := os.RemoveAll(CurrentDir + PathSeparator + ConfigDir)

	assertions.Nil(err)

	err = os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	assertions.Nil(err)

	bytes, err := json.Marshal(defaultHorizontalAggregationColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+HorizontalAggregations, bytes, 0755)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+HorizontalAggregations, bytes[1:], 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultVerticalAggregations)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+VerticalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+ColumnConfigFile, bytes, 0755)

	assertions.Nil(err)

	assertions.True(ValidateDefaultConfigs())

}

func TestInvalidHorizontalAggregationsv2(t *testing.T) {

	assertions := assert.New(t)

	err := os.RemoveAll(CurrentDir + PathSeparator + ConfigDir)

	assertions.Nil(err)

	err = os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	assertions.Nil(err)

	bytes, err := json.Marshal(defaultHorizontalAggregationColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+HorizontalAggregations, bytes, 0755)

	err = os.MkdirAll(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+HorizontalAggregations, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultVerticalAggregations)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+VerticalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+ColumnConfigFile, bytes, 0755)

	assertions.Nil(err)

	assertions.True(ValidateDefaultConfigs())

}

func TestVerticalAggregations(t *testing.T) {

	assertions := assert.New(t)

	err := os.RemoveAll(CurrentDir + PathSeparator + ConfigDir)

	assertions.Nil(err)

	err = os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	assertions.Nil(err)

	bytes, err := json.Marshal(defaultHorizontalAggregationColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+HorizontalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultVerticalAggregations)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+VerticalAggregations, bytes, 0755)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+VerticalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+ColumnConfigFile, bytes, 0755)

	assertions.Nil(err)

	assertions.True(ValidateDefaultConfigs())

}

func TestInvalidVerticalAggregations(t *testing.T) {

	assertions := assert.New(t)

	err := os.RemoveAll(CurrentDir + PathSeparator + ConfigDir)

	assertions.Nil(err)

	err = os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	assertions.Nil(err)

	bytes, err := json.Marshal(defaultHorizontalAggregationColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+HorizontalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultVerticalAggregations)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+VerticalAggregations, bytes, 0755)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+VerticalAggregations, bytes[1:], 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+ColumnConfigFile, bytes, 0755)

	assertions.Nil(err)

	assertions.True(ValidateDefaultConfigs())

}

func TestInvalidVerticalAggregationsv2(t *testing.T) {

	assertions := assert.New(t)

	err := os.RemoveAll(CurrentDir + PathSeparator + ConfigDir)

	assertions.Nil(err)

	err = os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	assertions.Nil(err)

	bytes, err := json.Marshal(defaultHorizontalAggregationColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+HorizontalAggregations, bytes, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultVerticalAggregations)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+VerticalAggregations, bytes, 0755)

	err = os.MkdirAll(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+VerticalAggregations, 0755)

	assertions.Nil(err)

	bytes, err = json.Marshal(DefaultColumns)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	err = os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+ColumnConfigFile, bytes, 0755)

	assertions.Nil(err)

	assertions.True(ValidateDefaultConfigs())

}

func TestGetHealthMetricWriters(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(GetHealthMetricWriters(), 1)

	configs["datastore.health.metric.writers"] = 100

	assertions.Equal(GetHealthMetricWriters(), 100)

}

func TestGetHealthMetricFlushTimerSeconds(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(GetHealthMetricFlushTimerSeconds(), 300)

	configs["health.metric.flush.timer.seconds"] = 100

	assertions.Equal(GetHealthMetricFlushTimerSeconds(), 100)

}

func TestProfileDurationSeconds(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(time.Duration(30)*time.Second, getDiagnosticProfileDurationSeconds())

	configs[DiagnosticProfileDurationSeconds] = 100

	assertions.Equal(time.Duration(100)*time.Second, getDiagnosticProfileDurationSeconds())

}

func TestGetMaxFlowTopNInsertionGroups(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(10000, GetMaxFlowTopNInsertionGroups())

	configs[DatastoreMaxFlowTopNInsertionRecords] = 10

	assertions.Equal(10, GetMaxFlowTopNInsertionGroups())

	configs[DatastoreMaxFlowTopNInsertionRecords] = OverflowLength + 10

	assertions.Equal(OverflowLength+10, GetMaxFlowTopNInsertionGroups())

	configs[DatastoreMaxFlowTopNInsertionRecords] = OverflowLength * 7

	assertions.Equal(OverflowLength*5, GetMaxFlowTopNInsertionGroups())
}

func TestWriteConfigs(t *testing.T) {

	assertions := assert.New(t)

	WriteConfigs(MotadataMap{

		"key": make(chan int),
	})

	bytes, err := ReadLogFile("Configs", "system")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "error occurred while"))
}

func TestValidateColumns(t *testing.T) {

	assertions := assert.New(t)

	configs := DefaultColumns.Clone()

	configs.Delete(IndexableColumns)

	validateColumns(configs)

	assertions.Contains(configs, IndexableColumns)

	configs = MotadataMap{}

	validateColumns(configs)

	assertions.Equal(configs, DefaultColumns)

	configs = DefaultColumns.Clone()

	configs.Delete(BlobColumns)

	validateColumns(configs)

	assertions.Contains(configs, BlobColumns)

	configs.Delete(ColumnEncoders)

	assertions.True(validateColumns(configs))

	assertions.Contains(configs, ColumnEncoders)

	configs = DefaultColumns.Clone()

	assertions.False(validateColumns(configs))

	configs = DefaultColumns.DeepClone()

	configs.GetMapValue(IndexableColumns).GetMapValue("500014-health.metric").Delete("event.source")

	assertions.True(validateColumns(configs))

	configs = DefaultColumns.DeepClone()

	configs.GetMapValue(BlobColumns).Delete("trap.message")

	assertions.True(validateColumns(configs))

}

func TestValidateTempColumns(t *testing.T) {

	assertions := assert.New(t)

	file := CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + ColumnConfigFile

	configs := DefaultColumns.DeepClone()

	configs.GetMapValue(IndexableColumns)["dummy.plugin"] = MotadataMap{
		"dummy.metric": "",
	}

	configs.GetMapValue(IndexableColumns).Delete("500014-health.metric")

	bytes, _ := json.Marshal(configs)

	os.Remove(file)

	os.WriteFile(file, bytes, 0755)

	assertions.True(validateTempFile(ColumnConfigFile, validateColumns))

	bytes, err := os.ReadFile(CurrentDir + PathSeparator + ConfigDir + PathSeparator + ColumnConfigFile)

	assertions.NoError(err)

	configs = MotadataMap{}

	json.Unmarshal(bytes, &configs)

	assertions.Contains(configs.GetMapValue(IndexableColumns), "500014-health.metric")

	assertions.Contains(configs.GetMapValue(IndexableColumns), "dummy.plugin")

}

func TestValidateHorizontalAggregations(t *testing.T) {

	assertions := assert.New(t)

	configs := defaultHorizontalAggregationColumns.DeepClone()

	configs.Delete("499998-policy.flap")

	assertions.True(validateHorizontalAggregations(configs))

	assertions.Contains(configs, "499998-policy.flap")

	configs = defaultHorizontalAggregationColumns.DeepClone()

	assertions.False(validateHorizontalAggregations(configs))

	configs.GetMapValue("499998-policy.flap").Delete("499998-policy.flap@@@0")

	assertions.True(validateHorizontalAggregations(configs))

	assertions.Contains(configs.GetMapValue("499998-policy.flap"), "499998-policy.flap@@@0")

	configs = defaultHorizontalAggregationColumns.DeepClone()

	configs.GetMapValue("499998-policy.flap").GetMapValue("499998-policy.flap@@@0").Delete(IndexableColumns)

	assertions.True(validateHorizontalAggregations(configs))

	assertions.Contains(configs.GetMapValue("499998-policy.flap").GetMapValue("499998-policy.flap@@@0"), IndexableColumns)

	configs = defaultHorizontalAggregationColumns.DeepClone()

	configs.GetMapValue("499998-policy.flap").GetMapValue("499998-policy.flap@@@0").GetMapValue(IndexableColumns).Delete("policy.id")

	assertions.True(validateHorizontalAggregations(configs))

	assertions.Contains(configs.GetMapValue("499998-policy.flap").GetMapValue("499998-policy.flap@@@0").GetMapValue(IndexableColumns), "policy.id")

	configs = defaultHorizontalAggregationColumns.DeepClone()

	configs.GetMapValue("499998-policy.flap").GetMapValue("499998-policy.flap@@@0").Delete(Type)

	assertions.True(validateHorizontalAggregations(configs))
}

func TestValidateTempHorizontalAggregations(t *testing.T) {

	assertions := assert.New(t)

	file := CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + HorizontalAggregations

	configs := defaultHorizontalAggregationColumns.DeepClone()

	configs.Delete("499998-policy.flap")

	configs["dummy.plugin"] = MotadataMap{}

	bytes, _ := json.Marshal(configs)

	os.Remove(file)

	os.WriteFile(file, bytes, 0755)

	assertions.True(validateTempFile(HorizontalAggregations, validateHorizontalAggregations))

	bytes, err := os.ReadFile(CurrentDir + PathSeparator + ConfigDir + PathSeparator + HorizontalAggregations)

	assertions.NoError(err)

	configs = MotadataMap{}

	json.Unmarshal(bytes, &configs)

	assertions.Contains(configs, "499998-policy.flap")

	assertions.Contains(configs, "dummy.plugin")
}

func TestValidateVerticalAggregations(t *testing.T) {

	assertions := assert.New(t)

	configs := DefaultVerticalAggregations.DeepClone()

	configs.Delete("cisco.meraki.radio.memory.used.percent")

	assertions.True(validateVerticalAggregations(configs))

	assertions.Contains(configs, "cisco.meraki.radio.memory.used.percent")

	configs = DefaultVerticalAggregations.DeepClone()

	assertions.False(validateVerticalAggregations(configs))

	configs = DefaultVerticalAggregations.DeepClone()

	configs.Delete("aruba.wireless.client~auth.method")

	configs.Delete("aruba.wireless.client~channel")

	assertions.True(validateVerticalAggregations(configs))

}

func TestValidateTempVerticalAggregations(t *testing.T) {

	assertions := assert.New(t)

	file := CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + VerticalAggregations

	configs := DefaultVerticalAggregations.DeepClone()

	configs.Delete("aruba.wireless.client~channel")

	configs["dummy.metric"] = true

	bytes, _ := json.Marshal(configs)

	os.Remove(file)

	os.WriteFile(file, bytes, 0755)

	assertions.True(validateTempFile(VerticalAggregations, validateVerticalAggregations))

	bytes, err := os.ReadFile(CurrentDir + PathSeparator + ConfigDir + PathSeparator + VerticalAggregations)

	assertions.NoError(err)

	configs = MotadataMap{}

	json.Unmarshal(bytes, &configs)

	assertions.Contains(configs, "aruba.wireless.client~channel")

	assertions.Contains(configs, "dummy.metric")

}

func TestValidateTempDefaultConfigs(t *testing.T) {

	assertions := assert.New(t)

	columnConfigs := DefaultColumns.DeepClone()

	horizontalAggregations := defaultHorizontalAggregationColumns.DeepClone()

	verticalAggregations := DefaultVerticalAggregations.DeepClone()

	directory := CurrentDir + PathSeparator + ConfigDir + PathSeparator

	columnConfigs.Delete(IndexableColumns)

	bytes, _ := json.Marshal(columnConfigs)

	os.WriteFile(directory+Temp+ColumnConfigFile, bytes, 0755)

	horizontalAggregations.Delete("499998-policy.flap")

	bytes, _ = json.Marshal(horizontalAggregations)

	os.WriteFile(directory+Temp+HorizontalAggregations, bytes, 0755)

	verticalAggregations.Delete("aruba.wireless.client~channel")

	bytes, _ = json.Marshal(verticalAggregations)

	os.WriteFile(directory+Temp+VerticalAggregations, bytes, 0755)

	assertions.True(ValidateDefaultConfigs())

	bytes, _ = os.ReadFile(directory + ColumnConfigFile)

	columnConfigs = MotadataMap{}

	json.Unmarshal(bytes, &columnConfigs)

	assertions.Contains(columnConfigs, IndexableColumns)

	bytes, _ = os.ReadFile(directory + HorizontalAggregations)

	horizontalAggregations = MotadataMap{}

	json.Unmarshal(bytes, &horizontalAggregations)

	assertions.Contains(horizontalAggregations, "499998-policy.flap")

	bytes, _ = os.ReadFile(directory + VerticalAggregations)

	verticalAggregations = MotadataMap{}

	json.Unmarshal(bytes, &verticalAggregations)

	assertions.Contains(verticalAggregations, "aruba.wireless.client~channel")

}

func TestValidateDefaultConfigsV1(t *testing.T) {

	assertions := assert.New(t)

	columnConfigs := DefaultColumns.DeepClone()

	horizontalAggregations := defaultHorizontalAggregationColumns.DeepClone()

	verticalAggregations := DefaultVerticalAggregations.DeepClone()

	directory := CurrentDir + PathSeparator + ConfigDir + PathSeparator

	columnConfigs.Delete(IndexableColumns)

	bytes, _ := json.Marshal(columnConfigs)

	os.WriteFile(directory+ColumnConfigFile, bytes, 0755)

	horizontalAggregations.Delete("499998-policy.flap")

	bytes, _ = json.Marshal(horizontalAggregations)

	os.WriteFile(directory+HorizontalAggregations, bytes, 0755)

	verticalAggregations.Delete("aruba.wireless.client~channel")

	bytes, _ = json.Marshal(verticalAggregations)

	os.WriteFile(directory+VerticalAggregations, bytes, 0755)

	assertions.True(ValidateDefaultConfigs())

	bytes, _ = os.ReadFile(directory + ColumnConfigFile)

	columnConfigs = MotadataMap{}

	json.Unmarshal(bytes, &columnConfigs)

	assertions.Contains(columnConfigs, IndexableColumns)

	bytes, _ = os.ReadFile(directory + HorizontalAggregations)

	horizontalAggregations = MotadataMap{}

	json.Unmarshal(bytes, &horizontalAggregations)

	assertions.Contains(horizontalAggregations, "499998-policy.flap")

	bytes, _ = os.ReadFile(directory + VerticalAggregations)

	verticalAggregations = MotadataMap{}

	json.Unmarshal(bytes, &verticalAggregations)

	assertions.Contains(verticalAggregations, "aruba.wireless.client~channel")

}

func TestValidateInvalidColumnsConfig(t *testing.T) {

	assertions := assert.New(t)

	mockConfig := MotadataMap{
		FloatingColumns: MotadataMap{},
		GarbageColumns:  MotadataMap{},
		IndexableColumns: MotadataMap{

			"500001-trap": MotadataMap{

				"event.source":  "",
				"trap.severity": "",
			},
		},

		BlobColumns: MotadataMap{

			"message": struct{}{},
		},
	}

	validateColumns(mockConfig)

	assertions.NotNil(mockConfig)

	assertions.True(reflect.DeepEqual(DefaultColumns, mockConfig))
}

func TestValidateInvalidHorizontalAggregationsConfig(t *testing.T) {

	assertions := assert.New(t)

	mockConfig := MotadataMap{
		"499998-policy.flap": MotadataMap{
			"499998-policy.flap@@@0": MotadataMap{
				"indexable.columns": MotadataMap{
					"severity":  struct{}{},
					"policy.id": struct{}{},
					"object.id": struct{}{},
					"instance":  struct{}{},
				},
				"type": 8,
			},
		},
	}

	validateHorizontalAggregations(mockConfig)

	assertions.NotNil(mockConfig)

	assertions.True(reflect.DeepEqual(defaultHorizontalAggregationColumns, mockConfig))
}

func TestValidateDefaultConfigsV2(t *testing.T) {

	assertions := assert.New(t)

	columnConfigs := DefaultColumns.DeepClone()

	horizontalAggregations := defaultHorizontalAggregationColumns.DeepClone()

	verticalAggregations := DefaultVerticalAggregations.DeepClone()

	directory := CurrentDir + PathSeparator + ConfigDir + PathSeparator

	columnConfigs.Delete(IndexableColumns)

	os.MkdirAll(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+ColumnConfigFile, 0755)

	os.Create(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + ColumnConfigFile + PathSeparator + "dummy")

	os.WriteFile(directory+ColumnConfigFile, []byte("dummy"), 0755)

	assertions.False(ValidateDefaultConfigs())

	bytes, _ := json.Marshal(columnConfigs)

	os.WriteFile(directory+ColumnConfigFile, bytes, 0755)

	os.RemoveAll(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + ColumnConfigFile)

	os.MkdirAll(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+HorizontalAggregations, 0755)

	os.Create(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + HorizontalAggregations + PathSeparator + "dummy")

	os.WriteFile(directory+HorizontalAggregations, []byte("dummy"), 0755)

	assertions.False(ValidateDefaultConfigs())

	bytes, _ = json.Marshal(horizontalAggregations)

	os.WriteFile(directory+HorizontalAggregations, bytes, 0755)

	os.RemoveAll(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + HorizontalAggregations)

	os.MkdirAll(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+VerticalAggregations, 0755)

	os.Create(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + VerticalAggregations + PathSeparator + "dummy")

	os.WriteFile(directory+VerticalAggregations, []byte("dummy"), 0755)

	assertions.False(ValidateDefaultConfigs())

	bytes, _ = json.Marshal(verticalAggregations)

	os.WriteFile(directory+VerticalAggregations, bytes, 0755)

	os.RemoveAll(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + VerticalAggregations)

	os.MkdirAll(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+ColumnConfigFile, 0755)

	os.Create(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + ColumnConfigFile + PathSeparator + "dummy")

	columnConfigs.Delete(IndexableColumns)

	bytes, _ = json.Marshal(columnConfigs)

	os.WriteFile(directory+ColumnConfigFile, bytes, 0755)

	assertions.False(ValidateDefaultConfigs())

	os.RemoveAll(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + ColumnConfigFile)

	os.MkdirAll(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+HorizontalAggregations, 0755)

	os.Create(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + HorizontalAggregations + PathSeparator + "dummy")

	horizontalAggregations.Delete("499998-policy.flap")

	bytes, _ = json.Marshal(horizontalAggregations)

	os.WriteFile(directory+HorizontalAggregations, bytes, 0755)

	assertions.False(ValidateDefaultConfigs())

	os.RemoveAll(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + HorizontalAggregations)

	os.MkdirAll(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+VerticalAggregations, 0755)

	os.Create(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + VerticalAggregations + PathSeparator + "dummy")

	verticalAggregations.Delete("cisco.meraki.radio.memory.used.percent")

	bytes, _ = json.Marshal(verticalAggregations)

	os.WriteFile(directory+VerticalAggregations, bytes, 0755)

	assertions.False(ValidateDefaultConfigs())

	os.MkdirAll(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+VerticalAggregations, 0755)

	os.Create(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + VerticalAggregations + PathSeparator + "dummy")

	os.Remove(CurrentDir + PathSeparator + ConfigDir + PathSeparator + VerticalAggregations)

	assertions.False(ValidateDefaultConfigs())

	os.RemoveAll(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + HorizontalAggregations)

	os.MkdirAll(CurrentDir+PathSeparator+ConfigDir+PathSeparator+Temp+HorizontalAggregations, 0755)

	os.Create(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + HorizontalAggregations + PathSeparator + "dummy")

	os.Remove(CurrentDir + PathSeparator + ConfigDir + PathSeparator + HorizontalAggregations)

	assertions.False(ValidateDefaultConfigs())

	os.RemoveAll(CurrentDir + PathSeparator + ConfigDir + PathSeparator + Temp + HorizontalAggregations)

}

func TestGetQueryDiscardThreshold(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(GetQueryCreationAgeThresholdSeconds(), 120)

	configs[DatastoreQueryCreationAgeThresholdSeconds] = 100

	assertions.Equal(GetQueryCreationAgeThresholdSeconds(), 100)

}

func TestGetQueryStatEnableStatus(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(GetQueryStatsLogging(), false)

	configs[DatastoreQueryStatsLogging] = Yes

	assertions.Equal(GetQueryStatsLogging(), true)
}

func TestGetCacheStores(t *testing.T) {

	assertions := assert.New(t)

	cacheStores := getCacheStores()

	assertions.Contains(cacheStores, StaticMetric)

	assertions.Equal(len(cacheStores), 1)

	configs["datastore.cache.stores"] = MotadataMap{
		"0": struct{}{},
		"1": struct{}{},
	}

	cacheStores = getCacheStores()

	assertions.Contains(cacheStores, StaticMetric)

	assertions.Contains(cacheStores, Index)

	assertions.Contains(cacheStores, PerformanceMetric)

	assertions.Equal(len(cacheStores), 3)

}

func TestGetObjectFlushTimerSeconds(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(GetObjectFlushTimerSeconds(), 60)

	configs[DatastoreObjectFlushTimerSeconds] = 100

	assertions.Equal(GetObjectFlushTimerSeconds(), 100)
}
