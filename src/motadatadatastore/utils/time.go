/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-04-02			 Vedant Dokania 		Motadata-4859  Added Function For Qualifying Week Days
 */

package utils

import (
	"strings"
	"time"
)

const (
	EpochTimeSeconds = 1577836800 //2020,1,jan,00:00:00:000

	DateFormat = "02012006"
)

func UnixToSeconds(tick int64) int32 {

	return int32(time.Unix(tick, 0).Sub(time.Unix(EpochTimeSeconds, 0)).Seconds())

}

func UnixMillisToSeconds(tick int64) int32 {

	return int32(time.UnixMilli(tick).Sub(time.Unix(EpochTimeSeconds, 0)).Seconds())

}

/*func SecondsToUnixMicro(seconds int32) int64 {

	return time.Unix(EpochTimeSeconds, 0).Add(time.Second * time.Duration(seconds)).UnixMicro()
}

func SecondsToUnixNano(seconds int32) int64 {

	return time.Unix(EpochTimeSeconds, 0).Add(time.Second * time.Duration(seconds)).UnixNano()
}*/
/*
func SecondsToUnixMinute(seconds int32) int32 {

	return int32(time.Unix(EpochTimeSeconds, 0).Add(time.Second*time.Duration(seconds)).Unix() / int64(60))
}
*/

/*func UnixMillisToMinute(tick int64) int32 {

	return SecondsToUnixMinute(UnixMillisToSeconds(tick))
}

func UnixMicroToSeconds(microSeconds int64) int32 {

	return int32(time.UnixMicro(microSeconds).Sub(time.Unix(EpochTimeSeconds, 0)).Seconds())
}*/

func SecondsToUnixMillis(seconds int32) int64 {

	return time.Unix(EpochTimeSeconds, 0).Add(time.Second * time.Duration(seconds)).UnixMilli()
}

func SecondsToUnix(seconds int32) int64 {

	return time.Unix(EpochTimeSeconds, 0).Add(time.Second * time.Duration(seconds)).Unix()
}

func UnixMillisToDate(tick int64) string {

	return time.UnixMilli(tick).UTC().Format(DateFormat)
}

func UnixMillisToTime(tick int64) time.Time {

	return time.UnixMilli(tick).UTC()
}

/*func SecondsToTime(tick int32) time.Time {

	return time.UnixMilli(SecondsToUnixMillis(tick)).UTC()
}*/

func SecondsToDate(tick int32) string {

	return UnixMillisToDate(SecondsToUnixMillis(tick))
}

/*func DaysBetweenDates(startDateTime time.Time, endDateTime time.Time) int {

	return int(endDateTime.Sub(startDateTime).Hours() / 24)
}

func RemainingSecondsOfDay(startTime time.Time, location *time.Location) int64 {

	return int64(time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 23, 59, 59, 0, location).Sub(startTime).Seconds())
}

func SecondsBetweenDates(startDateTime time.Time, endDateTime time.Time) int {

	return int(endDateTime.Sub(startDateTime).Seconds() / 60)
}

func FormatDateTime(dateTime string) time.Time {

	timestamp, err := time.Parse(DateTimeFormat, dateTime)

	if err != nil {

		timestamp, _ := time.Parse(DateTimeFormat, time.Now().String())

		return timestamp.UTC()
	}

	return timestamp.UTC()
}

func UnixSeconds(dateTime string, timezone string) int64 {

	location, err := time.LoadLocation(timezone)

	timestamp, err := time.ParseInLocation(DateTimeFormat, dateTime, location)

	if err != nil {

		timestamp, _ := time.ParseInLocation(DateTimeFormat, time.Now().String(), time.Local)

		return timestamp.UTC().Unix()
	}

	return timestamp.UTC().Unix()
}

func UnixMilliSeconds(dateTime string, timezone string) int64 {

	location, err := time.LoadLocation(timezone)

	timestamp, err := time.ParseInLocation(DateTimeFormat, dateTime, location)

	if err != nil {

		timestamp, _ := time.ParseInLocation(DateTimeFormat, time.Now().String(), time.Local)

		return timestamp.UTC().UnixMilli()
	}

	return timestamp.UnixMilli()
}

func GetHour(tick int32) int {

	return time.UnixMilli(SecondsToUnixMillis(tick)).Hour()
}*/

func RoundOffSeconds(tick int32, minutes int) int32 {

	baseTime := time.Unix(SecondsToUnix(tick), 0).UTC()

	baseTick := UnixToSeconds(time.Date(baseTime.Year(), baseTime.Month(), baseTime.Day(), 0, 0, 0, 0, time.UTC).Unix())

	granularity := int32(minutes * 60)

	return baseTick + (granularity * ((tick % baseTick) / granularity))
}

func RoundOffUnixSeconds(tick int64, minutes int) int64 {

	return SecondsToUnix(RoundOffSeconds(UnixToSeconds(tick), minutes))
}

func RoundOffUnixMilliSeconds(tick int64, minutes int) int64 { //returns unix milliseconds

	return SecondsToUnixMillis(RoundOffSeconds(UnixMillisToSeconds(tick), minutes))
}

func QualifyDates(fromDateTime, toDateTime time.Time) []time.Time {

	var dates []time.Time

	dates = append(dates, fromDateTime)

	/*
		in-case the to and from lies between two dates
	*/
	if toDateTime.Unix()-fromDateTime.Unix() <= 86399 && UnixMillisToDate(fromDateTime.UnixMilli()) != UnixMillisToDate(toDateTime.UnixMilli()) {

		dates = append(dates, toDateTime)

		return dates
	}

	for {

		fromDateTime = fromDateTime.AddDate(0, 0, 1)

		if fromDateTime.Unix() > toDateTime.Unix() {

			if strings.EqualFold(UnixMillisToDate(fromDateTime.UnixMilli()), UnixMillisToDate(toDateTime.UnixMilli())) {

				dates = append(dates, fromDateTime)
			}

			break

		} else {

			dates = append(dates, fromDateTime)
		}
	}

	return dates
}

func QualifyWeeklyDates(fromDateTime, toDateTime time.Time) []time.Time {

	var dates []time.Time

	from := GetStartingWeek(fromDateTime)

	dates = append(dates, from)

	lastDate := UnixMillisToDate(from.UnixMilli())

	for {

		fromDateTime = fromDateTime.AddDate(0, 0, 1)

		if GetStartingWeek(fromDateTime).Unix() > GetStartingWeek(toDateTime).Unix() {

			if strings.EqualFold(UnixMillisToDate(fromDateTime.UnixMilli()), UnixMillisToDate(toDateTime.UnixMilli())) {

				dates = append(dates, GetStartingWeek(fromDateTime))
			}

			break

		} else if !strings.EqualFold(UnixMillisToDate(GetStartingWeek(fromDateTime).UnixMilli()), lastDate) {

			dates = append(dates, GetStartingWeek(fromDateTime))

			lastDate = UnixMillisToDate(GetStartingWeek(fromDateTime).UnixMilli())

		}
	}

	return dates
}

// for historical start/end tick
func CalculateHistogramTick(tick, granularity int64) int32 {

	result := tick % granularity

	if result != 0 {

		result = granularity - result
	}

	return UnixToSeconds(result + tick)

}

func CalculateTickPosition(tick int32) int {

	timestamp := time.Unix(SecondsToUnix(tick), 0).UTC()

	return timestamp.Hour()*60*60 + timestamp.Minute()*60 + timestamp.Second()

}

func GetBaseTick(date string) int32 {

	tick, _ := time.ParseInLocation(DateFormat, date, time.Local)

	return UnixToSeconds(time.Date(tick.Year(), tick.Month(), tick.Day(), 00, 0, 0, 0, time.UTC).Unix())

}

func GetBaseTickv1(tick int64) int64 {

	timestamp := time.Unix(tick, 0).UTC()

	return time.Date(timestamp.Year(), timestamp.Month(), timestamp.Day(), 0, 0, 0, 0, time.UTC).Unix()
}

func GetPosition(tick int64, aggregationIntervalMinutes int) uint32 {

	//get bitmap index position according to the basetick and interval

	//for 5 min index will be from (0-288)
	//for 15 min index will be from (0-96)
	//for 30 min index will be from (0-48)

	timestamp := time.Unix(tick, 0).UTC()

	return uint32((timestamp.Hour()*60 + timestamp.Minute()) / aggregationIntervalMinutes)
}

func UnixSeconds(dateTime string, timezone string) int64 {

	location, err := time.LoadLocation(timezone)

	timestamp, err := time.ParseInLocation(LogFileDateFormat, dateTime, location)

	if err != nil {

		timestamp, _ := time.ParseInLocation(LogFileDateFormat, time.Now().String(), time.Local)

		return timestamp.UTC().Unix()
	}

	return timestamp.UTC().Unix()
}

func GetStartingWeek(timeObj time.Time) time.Time {

	return time.Date(timeObj.Year(), timeObj.Month(), timeObj.Day()-getCurrentWeekDay(timeObj.Weekday().String()), 0, 0, 0, 0, time.UTC)
}

func getCurrentWeekDay(day string) int {

	var currentWeekDay int

	switch day {

	case "Monday":
		currentWeekDay = 0

	case "Tuesday":
		currentWeekDay = 1

	case "Wednesday":
		currentWeekDay = 2

	case "Thursday":
		currentWeekDay = 3

	case "Friday":
		currentWeekDay = 4

	case "Saturday":
		currentWeekDay = 5

	case "Sunday":
		currentWeekDay = 6
	}

	return currentWeekDay

}
