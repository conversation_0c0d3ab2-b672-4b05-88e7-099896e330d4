/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-06-04             A<PERSON>l Shah            MOTADATA-5780 Handled windows specific Flock, GetStats, MmapAnonymous & Munmap implementations
 */

package utils

import (
	"errors"
	"golang.org/x/sys/windows"
	"path/filepath"
	"syscall"
	"unsafe"
)

var (
	kernel32 = syscall.NewLazyDLL("kernel32.dll")

	procLockFileEx = kernel32.NewProc("LockFileEx")
)

const (
	LockfileExclusiveLock = 0x00000002

	LockfileFailImmediately = 0x00000001
)

const (
	OpenExisting = syscall.OPEN_EXISTING

	OpenAlways = syscall.OPEN_ALWAYS

	FileAttributeNormal = syscall.FILE_ATTRIBUTE_NORMAL

	FileFlagOverlapped = syscall.FILE_FLAG_OVERLAPPED
)

type IOInterface struct {
	handle windows.Handle
}

func Flock(fileDescriptor int) error {

	ol := new(syscall.Overlapped)

	r, _, err := procLockFileEx.Call(
		uintptr(fileDescriptor),
		LockfileExclusiveLock|LockfileFailImmediately,
		0,
		1, 0,
		uintptr(unsafe.Pointer(ol)),
	)

	if r == 0 {
		return err
	}

	return nil
}

func GetStats() (uint64, uint64, error) {

	path, err := filepath.Abs(CurrentDir)

	if err != nil {
		return 0, 0, err
	}

	availableBytes := uint64(0)

	numberOfBytes := uint64(0)

	freeBytes := uint64(0)

	err = windows.GetDiskFreeSpaceEx(
		windows.StringToUTF16Ptr(path),
		&availableBytes,
		&numberOfBytes,
		&freeBytes,
	)

	if err != nil {
		return 0, 0, err
	}

	return availableBytes, numberOfBytes, nil

}

// MmapAnonymous allocates anonymous memory (not backed by a file)
func MmapAnonymous(length int) ([]byte, error) {

	handle, err := syscall.CreateFileMapping(syscall.InvalidHandle, nil, syscall.PAGE_READWRITE, uint32(length>>32), uint32(length), nil)

	if err != nil {

		return nil, err
	}

	addr, err := syscall.MapViewOfFile(handle, syscall.FILE_MAP_WRITE, 0, 0, uintptr(length))

	if err != nil {
		return nil, err
	}

	bytes := unsafe.Slice((*byte)(unsafe.Pointer(addr)), length)

	return bytes, nil
}

func Munmap(bytes []byte) error {

	if len(bytes) == 0 {
		return errors.New("empty buffer")
	}

	return syscall.UnmapViewOfFile(uintptr(unsafe.Pointer(&bytes[0])))
}
