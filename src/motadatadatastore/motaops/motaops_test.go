/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs

* 2025-05-05			 Swapnil <PERSON><PERSON> Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-04-21             Dhaval Bera     	    MOTADATA-5815 Updated JWT library
* 2025-05-08  			 <PERSON><PERSON><PERSON> <PERSON>			MOTADATA-6073 Updated StandardClaims to RegisteredClaims in JWT Claims
* 2025-06-04             <PERSON><PERSON><PERSON> <PERSON>            MOTADATA-5780 TestCase Refactoring
 */

package motaops

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	cp "github.com/otiai10/copy"
	"github.com/stretchr/testify/assert"
	"io"
	"motadatadatastore/cache"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"net/http"
	"net/http/httptest"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

var (
	token string

	encoder codec.Encoder

	tokenizer *utils.Tokenizer

	motaOps *MotaOps
)

func TestMain(m *testing.M) {

	if utils.SkipBenchmarkTest() {

		return
	}

	os.RemoveAll(utils.DatastoreTempDir)

	os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	os.Remove(utils.CurrentDir + utils.PathSeparator + "resources")

	os.MkdirAll(utils.CurrentDir+utils.PathSeparator+"resources", 0755)

	cp.Copy(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))+utils.PathSeparator+"resources", utils.CurrentDir+utils.PathSeparator+"resources")

	motaOps = NewMotaOps()

	motaOps.Start()

	time.Sleep(time.Second * 2)

	request := httptest.NewRecorder()

	params := url.Values{}

	params.Add(Password, "Mind@123")

	req, _ := http.NewRequest("POST", "/auth/login", strings.NewReader(params.Encode()))

	req.PostForm = params

	motaOps.apiEngine.ServeHTTP(request, req)

	token = strings.Split(request.Header().Get("Set-Cookie")[len("token="):], " ")[0]

	encoder = codec.NewEncoder(utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools))

	tokenizer = &utils.Tokenizer{
		Tokens: make([]string, utils.TokenizerLength),
	}

	datastore.Init()

	createDatastores()

	cache.InitCacheEngine()

	utils.IOCPWorkers = 16

	utils.DiskIOWorkers = 1

	DiskIOWorkers := make([]*storage.DiskIOWorker, utils.DiskIOWorkers)

	for id := range utils.DiskIOWorkers {

		DiskIOWorkers[id] = storage.NewIOWorker(id)

		DiskIOWorkers[id].Start()

	}

	m.Run()

	motaOps.ShutdownNotifications <- true

	os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	os.RemoveAll(resourceDir)

	os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	os.RemoveAll(utils.DatastoreTempDir)

}

func TestLogin(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/auth/login", nil)

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, 200, request.Code)

}

func TestAuthentication(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/datastore/datastores", nil)

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, 308, request.Code)

}

func TestPostLoginWrongPassword(t *testing.T) {

	request := httptest.NewRecorder()

	params := url.Values{}

	params.Add("Password", "wrongPassword")

	req, _ := http.NewRequest("POST", "/auth/login", strings.NewReader(params.Encode()))

	req.PostForm = params

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, 400, request.Code)

}

func TestPostLoginWithoutPassword(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("POST", "/auth/login", nil)

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, 400, request.Code)

}

func TestHomePage(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/", nil)

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusOK, request.Code)

}

func TestHomePageWithoutToken(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/", nil)

	//req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusPermanentRedirect, request.Code)

}

func TestHomePageInvalidToken(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/", nil)

	req.AddCookie(&http.Cookie{Name: Token, Value: "dumy", HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusPermanentRedirect, request.Code)

}

func TestPostLogin(t *testing.T) {

	request := httptest.NewRecorder()

	params := url.Values{}

	params.Add(Password, "Mind@123")

	req, _ := http.NewRequest("POST", "/auth/login", strings.NewReader(params.Encode()))

	req.PostForm = params

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, 301, request.Code)

}

func TestInvalidCredentialV1(t *testing.T) {

	request := httptest.NewRecorder()

	params := url.Values{}

	req, _ := http.NewRequest("POST", "/auth/login", strings.NewReader(params.Encode()))

	req.PostForm = params

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusBadRequest, request.Code)

}

func TestInvalidCredentialV2(t *testing.T) {

	request := httptest.NewRecorder()

	params := url.Values{}

	params.Add(Password, "dummy")

	req, _ := http.NewRequest("POST", "/auth/login", strings.NewReader(params.Encode()))

	req.PostForm = params

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusBadRequest, request.Code)

}

func TestGetDatastore(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/datastore/datastores", nil)

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Contains(t, request.Body.String(), "dummy-store-1")

	assert.Contains(t, request.Body.String(), "dummy-store-2")

	assert.Contains(t, request.Body.String(), "dummy-store-3")

	assert.Contains(t, request.Body.String(), "dummy-store-4")

	assert.Contains(t, request.Body.String(), "dummy-store-5")

}

func TestCopyDatastores(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("POST", "/datastore/clone", strings.NewReader("{ \"datastores\" : [\"dummy-store-1\" , \"dummy-store-2\"]}"))

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, 200, request.Code)

}

func TestCopyDatastoresWithoutStores(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("POST", "/datastore/clone", strings.NewReader("{ \"datastores\" : []}"))

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusBadRequest, request.Code)

}

func TestCopyDatastoresWithoutParams(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("POST", "/datastore/clone", strings.NewReader("{ \"datastores\" : []}"))

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusBadRequest, request.Code)

	assert.Contains(t, request.Body.String(), DatastoreRequired)

}

func TestCopyDatastoresInvalidJsonFormat(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("POST", "/datastore/clone", strings.NewReader("datastores\": \"{ \"datastores\" : ]}"))

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusBadRequest, request.Code)

	assert.Contains(t, request.Body.String(), "Invalid JSON")

}

func TestCopyDatastoresWithoutBody(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("POST", "/datastore/clone", strings.NewReader(""))

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusBadRequest, request.Code)

	assert.Contains(t, request.Body.String(), DatastoreRequired)

}

func TestMetricsApi(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/metrics/process", nil)

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusOK, request.Code)

	request = httptest.NewRecorder()

	req, _ = http.NewRequest("GET", "/metrics/cache", nil)

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusOK, request.Code)

}

func TestGetDatastoreMetrics1(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/datastore/metrics?name=dummy-store-1", nil)

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusOK, request.Code)

}

func TestGetDatastoreMetrics2(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/datastore/metrics", nil)

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusBadRequest, request.Code)

}

func TestDatastoreMetricsInvalidDatastore(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/datastore/metrics?name=dummy", nil)

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusBadRequest, request.Code)

	assert.Contains(t, request.Body.String(), http.StatusText(http.StatusBadRequest))

}

func TestLoginInvalidToken(t *testing.T) {

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/datastore/datastores", strings.NewReader("{ \"datastores\" : [\"dummy-store-1\" , \"dummy-store-2\"]}"))

	req.AddCookie(&http.Cookie{Name: Token, Value: "dummy", HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusPermanentRedirect, request.Code)

}

func TestLoginExpiredToken(t *testing.T) {

	token, _ := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.RegisteredClaims{

		ExpiresAt: jwt.NewNumericDate(time.Now().Add(-time.Minute * 30)),
	}).SignedString([]byte(motaOps.secretKey))

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/datastore/datastores", nil)

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusPermanentRedirect, request.Code)

}

func TestLoginEmptySecretKey(t *testing.T) {

	key := motaOps.secretKey

	defer func() {
		motaOps.secretKey = key
	}()

	motaOps.secretKey = ""

	request := httptest.NewRecorder()

	params := url.Values{}

	params.Add("password", "Mind@123")

	req, _ := http.NewRequest("POST", LoginPageEndpoint, strings.NewReader(params.Encode()))

	req.PostForm = params

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, 301, request.Code)

}

func TestCopyDatastoreUtility1(t *testing.T) {

	request := utils.MotadataMap{

		Datastores: []interface{}{"dummy-store"},
	}

	response, err := createCopy(request, encoder, tokenizer)

	assert.NotEqual(t, response.GetListValue(CloneStatus)[0].(utils.MotadataMap)[Error].(string), "")

	assert.Nil(t, err)
}

func TestCopyDatastoreUtility2(t *testing.T) {

	request := utils.MotadataMap{

		Datastores: []interface{}{"dummy-store-9"},
	}

	datastore.GetStore("dummy-store-9", utils.None, true, true, encoder, tokenizer)

	_, err := createCopy(request, encoder, tokenizer)

	assert.Nil(t, err)
}

func TestCopyDatastoreUtility3(t *testing.T) {

	storeName := "dummy-store-10"

	request := utils.MotadataMap{

		Datastores: []interface{}{storeName},
	}

	store := datastore.GetStore(storeName, utils.None, true, true, encoder, tokenizer)

	store.Put([]byte("testing-key1"), []byte("testing-value1"), encoder, tokenizer)

	store.Close(encoder)

	os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + "dummy-store-10")

	response, err := createCopy(request, encoder, tokenizer)

	assert.NotEqual(t, response.GetListValue(CloneStatus)[0].(utils.MotadataMap)[Error].(string), "")

	assert.Nil(t, err)
}

func TestCopyDatastoreUtility4(t *testing.T) {

	request := utils.MotadataMap{}

	_, err := createCopy(request, encoder, tokenizer)

	assert.NotNil(t, err)

}

func TestCopyDatastoreUtility5(t *testing.T) {

	storeName := "dummy-store-11"

	request := utils.MotadataMap{

		Datastores: []interface{}{storeName},
	}

	response, err := createCopy(request, encoder, tokenizer)

	assert.NotEqual(t, response.GetListValue(CloneStatus)[0].(utils.MotadataMap)[Error].(string), "")

	assert.Nil(t, err)
}

func TestEnrichMetrics(t *testing.T) {

	_, err := enrich("", encoder, tokenizer)

	assert.NotNil(t, err)

}

func TestListDatastores1(t *testing.T) {

	date := utils.SecondsToDate(utils.UnixToSeconds(time.Now().Unix()))

	datastoreName := datastore.GetStoreById(time.Now().Unix(), "dummy-plugin", "0")

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/datastore/datastores?date="+date, nil)

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusOK, request.Code)

	bytes, err := io.ReadAll(request.Body)

	assert.Nil(t, err)

	assert.NotNil(t, bytes)

	response := utils.MotadataMap{}

	err = json.Unmarshal(bytes, &response)

	assert.Nil(t, err)

	found := false

	for _, store := range response[Result].(map[string]interface{})[Datastores].([]interface{}) {

		if store.(string) == datastoreName {

			found = true
		}

	}

	assert.True(t, found)

}

func TestListDatastores2(t *testing.T) {

	datastoreName := datastore.GetStoreById(time.Now().Unix(), "dummy-plugin", "0")

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/datastore/datastores", nil)

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusOK, request.Code)

	bytes, err := io.ReadAll(request.Body)

	assert.Nil(t, err)

	assert.NotNil(t, bytes)

	response := utils.MotadataMap{}

	err = json.Unmarshal(bytes, &response)

	assert.Nil(t, err)

	found := false

	for _, store := range response[Result].(map[string]interface{})[Datastores].([]interface{}) {

		if store.(string) == datastoreName {

			found = true
		}

	}

	assert.False(t, found)

}

func TestVerticalSearchableColumnDatastoreDatastoreListing(t *testing.T) {

	storeName := "dummy-0-176"

	store := datastore.GetStore(storeName, utils.None, true, true, encoder, tokenizer)

	assert.NotNil(t, store)

	storeName2 := "dummy-0-110"

	store = datastore.GetStore(storeName2, utils.None, true, true, encoder, tokenizer)

	assert.NotNil(t, store)

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/datastore/datastores", nil)

	req.AddCookie(&http.Cookie{
		Name:     Token,
		HttpOnly: true,
		Value:    token,
	})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusOK, request.Code)

	bytes, err := io.ReadAll(request.Body)

	assert.Nil(t, err)

	assert.NotNil(t, bytes)

	response := utils.MotadataMap{}

	json.Unmarshal(bytes, &response)

	assert.Equal(t, response[Status], Succeed)

	pass := false

	for _, datastore := range response[Result].(map[string]interface{})[Datastores].([]interface{}) {

		if datastore.(string) == storeName {
			pass = true
		} else if datastore.(string) == storeName2 {
			pass = false
		}

	}

	assert.True(t, pass)

}

func TestGetListDatastoresV1(t *testing.T) {

	assertions := assert.New(t)

	utils.SetLogLevel(utils.LogLevelDebug)

	datastore.Close()

	err := os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assert.NoError(t, err)

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/datastore/datastores", nil)

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	utils.AssertLogMessage(assertions, "Mota Ops", "motaops", "error occurred while reading stores")

}

func TestMotaOpsInitV1(t *testing.T) {

	assertions := assert.New(t)

	os.RemoveAll(resourceDir + utils.PathSeparator + serverCertificateFile)

	motaOps := NewMotaOps()

	motaOps.Start()

	time.Sleep(time.Second * 1)

	utils.AssertLogMessage(assertions, "Mota Ops", "motaops", "Failed to read the SSL certificate")

}

func TestMotaOpsInitV3(t *testing.T) {

	assertions := assert.New(t)

	os.RemoveAll(resourceDir + utils.PathSeparator + secretKeyFile)

	motaOps := NewMotaOps()

	motaOps.Start()

	time.Sleep(time.Second * 1)

	utils.AssertLogMessage(assertions, "Mota Ops", "motaops", "Failed to read the secret key, reason")

}

func TestCreatCopyV1(t *testing.T) {

	request := utils.MotadataMap{

		Datastores: []interface{}{""},
	}

	os.RemoveAll(utils.DatastoreTempDir + utils.PathSeparator)

	os.Create(utils.DatastoreTempDir)

	_, err := createCopy(request, encoder, tokenizer)

	assert.NotNil(t, err)

}

func TestLoadHomepage(t *testing.T) {

	utils.SetLogLevel(utils.LogLevelDebug)

	context := gin.Context{}

	_ = context

	defer func() {

		if r := recover(); r == nil {
			assert.Fail(t, "panic should be recovered")
		}
	}()

	motaOps.loadHomePage(nil)

}

func TestLoginV1(t *testing.T) {

	utils.SetLogLevel(utils.LogLevelDebug)

	context := gin.Context{}

	_ = context

	defer func() {

		if r := recover(); r == nil {
			assert.Fail(t, "panic should be recovered")
		}
	}()

	motaOps.doLogin(nil)

}

func TestDatastoreMetricsV1(t *testing.T) {

	utils.SetLogLevel(utils.LogLevelDebug)

	context := gin.Context{}

	_ = context

	defer func() {

		if r := recover(); r == nil {
			assert.Fail(t, "panic should be recovered")
		}
	}()

	motaOps.getDatastoreMetrics(nil)

}

func TestDatastoreHelthStatV1(t *testing.T) {

	utils.SetLogLevel(utils.LogLevelDebug)

	context := gin.Context{}

	_ = context

	defer func() {

		if r := recover(); r == nil {
			assert.Fail(t, "panic should be recovered")
		}
	}()

	motaOps.getHealthStats(nil)

}

func TestDatastoreCloneV1(t *testing.T) {

	utils.SetLogLevel(utils.LogLevelDebug)

	context := gin.Context{}

	_ = context

	defer func() {

		if r := recover(); r == nil {
			assert.Fail(t, "panic should be recovered")
		}
	}()

	motaOps.clone(nil)

}

func TestGetCacheMetricsV1(t *testing.T) {

	utils.SetLogLevel(utils.LogLevelDebug)

	context := gin.Context{}

	_ = context

	defer func() {

		if r := recover(); r == nil {
			assert.Fail(t, "panic should be recovered")
		}
	}()

	motaOps.getCacheMetrics(nil)

}

func TestGetListDatastoresV2(t *testing.T) {

	utils.SetLogLevel(utils.LogLevelDebug)

	context := gin.Context{}

	_ = context

	defer func() {

		if r := recover(); r == nil {
			assert.Fail(t, "panic should be recovered")
		}
	}()

	motaOps.listDatastores(nil)

}

func TestEncrypt(t *testing.T) {

	assertions := assert.New(t)

	message := "dummy-message-12"

	encrypted, err := encrypt(message)

	assertions.Nil(err)

	assertions.Equal(encrypted, "20w6rliDLFoY1zyCsSpbOA==")

	aesKey := aesSecretKey

	defer func() {

		aesSecretKey = aesKey
	}()

	aesSecretKey = "11"

	encrypted, err = encrypt(message)

	assertions.NotNil(err)

}

func TestMotaOpsShutdown(t *testing.T) {

	assertions := assert.New(t)

	motaOps.Shutdown()

	assertions.True(motaOps.Close)
}

// run this test case in last
func TestListDatastores3(t *testing.T) {

	os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	request := httptest.NewRecorder()

	req, _ := http.NewRequest("GET", "/datastore/datastores", nil)

	req.AddCookie(&http.Cookie{Name: Token, Value: token, HttpOnly: true, Secure: true, Expires: time.Now().Add(time.Minute * 30)})

	motaOps.apiEngine.ServeHTTP(request, req)

	assert.Equal(t, http.StatusNotFound, request.Code)
}

func TestStartMotaOPSWithoutTemplateDirectory(t *testing.T) {

	assertions := assert.New(t)

	err := os.RemoveAll(templateDir)

	assertions.Nil(err)

	motaOps := NewMotaOps()

	motaOps.Start()

	time.Sleep(time.Second * 1)

	utils.AssertLogMessage(assertions, "Mota Ops", "motaops", "templates directory is not available")
}

func TestStartMotaOPSWithoutResourceDirectory(t *testing.T) {

	assertions := assert.New(t)

	err := os.RemoveAll(resourceDir)

	assertions.Nil(err)

	motaOps := NewMotaOps()

	motaOps.Start()

	time.Sleep(time.Second * 1)

	utils.AssertLogMessage(assertions, "Mota Ops", "motaops", "resources directory is not available")
}

func createDatastores() {

	storeNames := []string{"dummy-store-1", "dummy-store-2", "dummy-store-2", "dummy-store-3", "dummy-store-4", "dummy-store-5"}

	dateStoreName := datastore.GetStoreById(time.Now().Unix(), "dummy-plugin", "0")

	store := datastore.GetStore(dateStoreName, utils.None, true, true, encoder, tokenizer)

	store.Put([]byte("dummy-key1"), []byte("dummy-value-1"), encoder, tokenizer)

	for _, store := range storeNames {

		store := datastore.GetStore(store, utils.None, true, true, encoder, tokenizer)

		store.Put([]byte("dummy-key1"), []byte("dummy-value-1"), encoder, tokenizer)

		store.Put([]byte("dummy-key2"), []byte("dummy-value-2"), encoder, tokenizer)

		store.Put([]byte("dummy-key3"), []byte("dummy-value-3"), encoder, tokenizer)

		store.Put([]byte("dummy-key4"), []byte("dummy-value-4"), encoder, tokenizer)

	}

	_ = os.Mkdir(utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+"dummy-corrupted-datastore", 0777)

}
