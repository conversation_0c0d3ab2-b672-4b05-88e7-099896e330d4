/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*Change Logs

* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-04-21             Dhaval Bera     	    MOTADATA-5815 Updated JWT library
* 2025-05-08  			 A<PERSON><PERSON> <PERSON>			MOTADATA-6073 Updated StandardClaims to RegisteredClaims in JWT Claims
* 2025-05-10             Dhaval Bera            MOTADATA-6174  Added Unmap function to release memory-mapped resources
* 2025-06-04             <PERSON><PERSON><PERSON>            MOTADATA-5780 Updated destination path while copying datastores.
 */

/*
	motaops is internal utility, The internal tool motaops is primarily useful for debugging or gaining insight into the production running environment.

	The features of motaops include datastore listing, metrics, and datastore copying. Additionally, motaops provides metrics for memory statistics and cache.

	Motaops is a http-based utility that uses the gin framework. In order to enable the motaops utility to start as a hot loading service, it must first add the key
	"datastore.mota.ops.service.status" and set its value to "yes" in the file motadata-datastore.json.

	The resource directory contains the ui pages and the SSL certificate and private keys for the HTTPS-based Motaps system.
	The session management feature uses the JWT token, which has a 30-minute validity period.

	As of right now, only one worker is available to handle requests at a time, meaning that other requests must wait. Requests have a 30-second timeout;
	if the server cannot respond within that time, the request will be rejected, and if the response is delayed, the client will not receive it.

	Note:
		- if copying datastore takes time more than 30 second then client will not get response even though request has been served successfully.
		- copied datastore will be stored in "tmp-datastore"
		- for encryption aed encryption is used.
		- to change password, first encrypt password using encrypt method and store it in password constant
		- secret key for encryption also stored as constant, private key stored in resource is not used.

*/

package motaops

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/tls"
	_ "embed"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"io"
	"log"
	"motadatadatastore/cache"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	_ "motadatadatastore/storage"
	"motadatadatastore/utils"
	"net/http"
	"os"
	"runtime"
	"strings"
	"sync"
	"time"
)

const (
	Succeed = "succeed"

	Failed = "failed"

	Password = "password"

	ResponseCode = "response.code"

	CloneStatus = "clone.status"

	Result = "result"

	Directory = "directory"

	Directories = "directories"

	Date = "date"

	CorruptedDatastores = "corrupted.datastores"

	Token = "token"

	RemoteIP = "remoteIp"

	cookieTimeoutMinutes = 30
)

//Resources

const (
	IndexPage = "index.tmpl"

	LoginPage = "login.tmpl"

	CSSFile = "style.css"

	LoaderFile = "loading.gif"
)

// memory metrics constants
const (
	MemoryBytes = "memory.bytes"

	UsedMemoryBytes = "memory.used.bytes"

	HeapMemoryBytes = "heap.memory.bytes"

	UsedHeapMemoryBytes = "heap.used.memory.bytes"

	StackMemoryBytes = "stack.memory.bytes"

	UsedStackMemoryBytes = "stack.used.memory.bytes"
)

// end point contant
const (
	FaviconEndPoint = "/favicon.ico"

	DatastoreGroup = "/datastore"

	DatastoreEndPoint = "/datastores"

	DatastoreCopyEndPoint = "/clone"

	DatastoreMetricsEndpoint = "/metrics"

	LoginPageEndpoint = "/auth/login"

	CSSEndPoint = "style.css"

	LoaderEndpoint = "loading.gif"

	MetricsGroup = "/metrics"

	ProcessMetricEndpoint = "/process"

	CacheMetricEndpoint = "/cache"

	AuthenticationGroup = "/auth"

	LoginEndpoint = "/login"

	HomePageEndPoint = "/"
)

// directories const
var (
	resourceDir = utils.CurrentDir + utils.PathSeparator + "resources"

	templateDir = resourceDir + utils.PathSeparator + "templates"
)

// file constants
const (
	secretKeyFile = "server-key.pem"

	serverCertificateFile = "server-cert.pem"

	FaviconFile = "favicon.ico"
)

// AES secret key and credential constant
const (
	password = "ZzY5GzWW6wgMnpXyC7/ueA=="

	aesIVKey = "omBJvmmN8kQ708MY"
)

// common constants
const (
	Status = "status"

	Error = "error"

	Datastores = "datastores"

	Datastore = "datastore"

	Metrics = "metrics"

	DatastoreRequired = "datastore(s) are required"

	InvalidCredentials = "invalid credentials"
)

var (
	logger = utils.NewLogger("Mota Ops", "motaops")

	aesSecretKey = "JtXL!1Uf5AolivoE>Tn$'zVSB5*Vxx26"
)

type MotaOps struct {
	apiEngine *gin.Engine

	requests chan struct{}

	waitGroup *sync.WaitGroup

	ShutdownNotifications chan bool

	encoder codec.Encoder

	tokenizer *utils.Tokenizer

	secretKey string

	Close bool
}

func NewMotaOps() *MotaOps {

	return &MotaOps{

		requests: make(chan struct{}, 1),

		ShutdownNotifications: make(chan bool, 5),

		waitGroup: &sync.WaitGroup{},

		encoder: codec.NewEncoder(utils.NewMemoryPool(2, utils.MaxPoolLength, false, utils.DefaultBlobPools)),

		Close: false,
	}
}

func (ops *MotaOps) Start() {

	ops.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	go func() {

		ops.Init()

		ops.Close = true

	}()
}

func (ops *MotaOps) Init() {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			logger.Error("unable to start motaops service")

			logger.Error("panic recovered while starting motaops service")

			logger.Error(fmt.Sprintf("!!!STACK TRACE for motaops service !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	if _, err := os.Stat(resourceDir); os.IsNotExist(err) {

		logger.Error("resources directory is not available")

		return

	} else {

		if _, err := os.Stat(templateDir); os.IsNotExist(err) {

			logger.Error("templates directory is not available")

			return
		}
	}

	key, err := os.ReadFile(resourceDir + utils.PathSeparator + secretKeyFile)

	if err != nil {

		logger.Error(fmt.Sprintf("Failed to read the secret key, reason : %s", err.Error()))

		return
	}

	ops.secretKey = string(key)

	gin.SetMode(gin.ReleaseMode)

	ops.apiEngine = gin.New()

	// It is to prevent gin's default logging in console output
	log.SetOutput(io.Discard)

	ops.apiEngine.Use(gin.Recovery())

	// loading all ui templates
	ops.apiEngine.LoadHTMLGlob(templateDir + utils.PathSeparator + utils.All)

	ops.mount()

	// loading ssl certificate
	certificate, err := tls.LoadX509KeyPair(resourceDir+utils.PathSeparator+serverCertificateFile, resourceDir+utils.PathSeparator+secretKeyFile)

	if err != nil {

		logger.Error(fmt.Sprintf("Failed to read the SSL certificate, reason : %s", err.Error()))

		return

	}

	port := utils.GetMotaOpsServicePort()

	webServer := &http.Server{

		Addr: fmt.Sprintf(":%d", port),

		Handler: ops.apiEngine,

		ReadTimeout: time.Second * 30,

		WriteTimeout: time.Second * 30,

		TLSConfig: &tls.Config{
			Certificates: []tls.Certificate{certificate},
		},
	}

	logger.Info(fmt.Sprintf("Starting motaops service on port %d", port))

	go func() {

		if err := webServer.ListenAndServeTLS(utils.Empty, utils.Empty); err != nil && !errors.Is(err, http.ErrServerClosed) {

			logger.Error(fmt.Sprintf("motaops service stopped unexpectedly, reason : %v", err.Error()))

			ops.Shutdown()
		}

	}()

	<-ops.ShutdownNotifications

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)

	defer cancel()

	if err = webServer.Shutdown(ctx); err != nil {

		logger.Fatal(fmt.Sprintf("Server Shutdown: %s", err.Error()))
	}

	ops.encoder.MemoryPool.Unmap()

	logger.Info("motaops service is shutting down...")
}

func (ops *MotaOps) Shutdown() {

	if !ops.Close {

		ops.Close = true

		ops.ShutdownNotifications <- true

	}

}

func (ops *MotaOps) mount() {

	// homepage
	ops.apiEngine.GET(HomePageEndPoint, func(context *gin.Context) {

		ops.handleEvent(context, HomePageEndPoint)
	})

	// static files "style.css", "loading.gif" , "favicon.iso"
	ops.apiEngine.StaticFile(CSSEndPoint, templateDir+utils.PathSeparator+CSSFile)

	ops.apiEngine.StaticFile(LoaderEndpoint, templateDir+utils.PathSeparator+LoaderFile)

	ops.apiEngine.StaticFile(FaviconEndPoint, resourceDir+utils.PathSeparator+FaviconFile)

	// datastore apis

	router := ops.apiEngine.Group(DatastoreGroup)

	// middleware for authentication, for datastore group routing
	router.Use(ops.authenticate)

	// datastore list api
	router.GET(DatastoreEndPoint, func(context *gin.Context) {

		ops.handleEvent(context, DatastoreEndPoint)
	})

	// datastore metric api
	router.GET(DatastoreMetricsEndpoint, func(context *gin.Context) {

		ops.handleEvent(context, DatastoreMetricsEndpoint)
	})

	// copy datastore api
	router.POST(DatastoreCopyEndPoint, func(context *gin.Context) {

		ops.handleEvent(context, DatastoreCopyEndPoint)
	})

	// memory stat apis

	metricRouter := ops.apiEngine.Group(MetricsGroup)

	// middleware for authentication, for metric group routing
	metricRouter.Use(ops.authenticate)

	// cache metric
	metricRouter.GET(CacheMetricEndpoint, func(context *gin.Context) {

		ops.handleEvent(context, CacheMetricEndpoint)
	})

	// process metric
	metricRouter.GET(ProcessMetricEndpoint, func(context *gin.Context) {

		ops.handleEvent(context, ProcessMetricEndpoint)
	})

	// authorization apis

	authorizationRouter := ops.apiEngine.Group(AuthenticationGroup)

	//login page
	authorizationRouter.GET(LoginEndpoint, func(c *gin.Context) {

		c.HTML(http.StatusOK, LoginPage, gin.H{})
	})

	// authentication api
	authorizationRouter.POST(LoginEndpoint, func(context *gin.Context) {

		ops.handleEvent(context, LoginEndpoint)
	})

}

////////////////////////////////// datastore utilities ///////////////////////////////

func createCopy(request utils.MotadataMap, encoder codec.Encoder, tokenizer *utils.Tokenizer) (response utils.MotadataMap, err error) {

	/*
		we create copy of datastore in "tmp-datastore" directory
	*/

	response = utils.MotadataMap{}

	response[CloneStatus] = []interface{}{}

	address := request.GetStringValue("address")

	if !request.Contains(Datastores) {

		return response, errors.New(fmt.Sprintf("datstore(s) are required : %s", address))
	}

	storeNames := request.GetSliceValue(Datastores)

	if len(storeNames) == 0 {

		logger.Error(fmt.Sprintf("datastore(s) are required : %s", address))

		response[Status] = Failed

		response[Error] = DatastoreRequired

		return response, errors.New(DatastoreRequired)

	}

	tick := time.Now()

	destination := utils.DatastoreTempDir + utils.PathSeparator + tick.Format("2006-01-02_15-04-05.000000000") + utils.HyphenSeparator + utils.INT64ToStringValue(tick.UnixMilli())

	if err = os.MkdirAll(destination, 0777); err != nil {

		logger.Error(fmt.Sprintf("unable to create copy of datastore %v, reason : %v", address, err.Error()))

		response[Status] = Failed

		response[Error] = err.Error()

		return response, err

	}

	for _, name := range storeNames {

		store := datastore.GetStore(name.(string), utils.None, false, false, encoder, tokenizer)

		if store == nil {

			// if store is not open simply clone directory no need ot open and increase memory spike
			if err = utils.CloneDirectory(utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+name.(string), destination+utils.PathSeparator+name.(string)); err != nil {

				_ = os.RemoveAll(destination + utils.PathSeparator + name.(string))

				response[CloneStatus] = append(response.GetListValue(CloneStatus), utils.MotadataMap{

					Datastore: name,

					Status: Failed,

					Error: err.Error(),
				})

			} else {

				response[CloneStatus] = append(response.GetListValue(CloneStatus), utils.MotadataMap{

					Datastore: name,

					Status: Succeed,
				})

			}

			continue
		}

		// store will be copied using clone method if store is open
		if err = store.Clone(encoder, tokenizer, destination); err != nil {

			_ = os.RemoveAll(destination + utils.PathSeparator + store.GetName())

			response[CloneStatus] = append(response.GetListValue(CloneStatus), utils.MotadataMap{

				Datastore: name,

				Status: Failed,

				Error: err.Error(),
			})

			continue
		}

		response[CloneStatus] = append(response.GetListValue(CloneStatus), utils.MotadataMap{

			Datastore: name,

			Status: Succeed,
		})

	}

	response[Status] = Succeed

	response[Directories] = destination

	return response, nil

}

func enrich(name string, encoder codec.Encoder, tokenizer *utils.Tokenizer) (response utils.MotadataMap, err error) {

	/*
		get metrics of datastore using internal metric method
	*/

	response = utils.MotadataMap{}

	store := datastore.GetStore(name, utils.None, false, false, encoder, tokenizer)

	if store == nil {

		// if store is not open, thn open it, get metrics and then close it.
		store = datastore.GetStore(name, utils.None, false, true, encoder, tokenizer)

		if store == nil {

			return response, errors.New(fmt.Sprintf(utils.ErrorAcquireStore, "store is missing"))
		}

		defer func() {

			store.Close(encoder)

			if store.IsClosed() {

				datastore.RemoveStore(store.GetName())
			}

		}()
	}

	response[Metrics] = store.GetMetrics()

	response[Datastore] = name

	return response, nil
}

////////////////////////////////// jwt authentication ///////////////////////////////

/*
	for authentication, we give jwt token at login time, which we store in cookie. and authenticate it every api calling.
	this middleware will authenticate client.
*/

func (ops *MotaOps) authenticate(context *gin.Context) {

	userToken, err := context.Cookie(Token)

	if err != nil {

		context.Redirect(http.StatusPermanentRedirect, LoginPageEndpoint)

		context.Abort()

		return
	}

	if token, err := jwt.ParseWithClaims(userToken, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) { return []byte(ops.secretKey), nil }); err != nil || !token.Valid {

		context.Redirect(http.StatusPermanentRedirect, LoginPageEndpoint)

		context.Abort()

	} else {

		context.Next()
	}

}

/////////////////////////////////  event handler  ///////////////////////////////////

func (ops *MotaOps) handleEvent(context *gin.Context, endPoint string) {

	ops.requests <- struct{}{}

	defer func() {

		<-ops.requests
	}()

	ops.waitGroup.Add(1)

	defer ops.waitGroup.Done()

	switch endPoint {

	case HomePageEndPoint:

		ops.loadHomePage(context)

	case LoginEndpoint:

		ops.doLogin(context)

	case DatastoreEndPoint:

		ops.listDatastores(context)

	case DatastoreMetricsEndpoint:

		ops.getDatastoreMetrics(context)

	case DatastoreCopyEndPoint:

		ops.clone(context)

	case CacheMetricEndpoint:

		ops.getCacheMetrics(context)

	case ProcessMetricEndpoint:

		ops.getHealthStats(context)
	}
}

////////////////////////////////// apis ///////////////////////////////

func (ops *MotaOps) loadHomePage(context *gin.Context) {

	defer func() {

		if err := recover(); err != nil {

			logger.Error(fmt.Sprintf("error %v occurred while loading the homepage for client %v", err, context.RemoteIP()))
		}
	}()

	if utils.DebugEnabled() {

		logger.Debug(fmt.Sprintf("the homepage requested by client %s", context.RemoteIP()))

	}

	userToken, err := context.Cookie(Token)

	if err != nil {

		context.Redirect(http.StatusPermanentRedirect, LoginPageEndpoint)

		return
	}

	// authenticating client, we can not use middleware for root api, because it will also login page api,
	//which ment to be used without authenticate, so doing manual authentication for home page.
	if token, err := jwt.ParseWithClaims(userToken, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) { return []byte(ops.secretKey), nil }); err != nil || !token.Valid {

		context.Redirect(http.StatusPermanentRedirect, LoginPageEndpoint)

		return
	}

	context.HTML(http.StatusOK, IndexPage, gin.H{
		RemoteIP: utils.GetHost(),
	})
}

func (ops *MotaOps) doLogin(context *gin.Context) {

	defer func() {

		if err := recover(); err != nil {

			logger.Error(fmt.Sprintf("error %v occurred while loging for client %v", err, context.RemoteIP()))
		}
	}()

	if utils.DebugEnabled() {

		logger.Debug(fmt.Sprintf("client %s is attempting to login", context.RemoteIP()))

	}

	loginPassword, ok := context.GetPostForm(Password)

	if !ok {

		context.HTML(http.StatusBadRequest, LoginPage, gin.H{

			Error: InvalidCredentials,
		})

		return
	}

	encryptedPassword, err := encrypt(loginPassword)

	if err != nil {

		context.HTML(http.StatusInternalServerError, LoginPage, gin.H{

			Error: InvalidCredentials,
		})

		return

	}

	// we encrypt password and then compare with, stored encrypted password as constant
	if encryptedPassword != password {

		context.HTML(http.StatusBadRequest, LoginPage, gin.H{
			Error: InvalidCredentials,
		})

		return

	}

	// creating new token
	token, err := jwt.NewWithClaims(jwt.SigningMethodHS256, &jwt.RegisteredClaims{
		ExpiresAt: jwt.NewNumericDate(time.Now().Add(30 * time.Minute)),
	}).SignedString([]byte(ops.secretKey))

	if err != nil {

		context.HTML(http.StatusBadRequest, LoginPage, gin.H{

			Error: "unable to process the request",
		})

		return
	}

	// assigning token in cookie
	context.SetCookie(Token, token, int((time.Minute * time.Duration(cookieTimeoutMinutes)).Seconds()), "", "", true, true)

	// redirecting to homepage
	context.Redirect(http.StatusMovedPermanently, HomePageEndPoint)

}

func (ops *MotaOps) getCacheMetrics(context *gin.Context) {

	defer func() {

		if err := recover(); err != nil {

			logger.Error(fmt.Sprintf("error %v occurred while sending cache metrics for client %v", err, context.RemoteIP()))
		}
	}()

	if utils.DebugEnabled() {

		logger.Debug(fmt.Sprintf("cache metrics requested by client : %s", context.RemoteIP()))
	}

	context.JSON(http.StatusOK, gin.H{
		ResponseCode: http.StatusOK,

		Result: cache.Stats(),

		Status: Succeed,
	})

}

func (ops *MotaOps) listDatastores(context *gin.Context) {

	/*
		while listing datastore, if date is provided by client then prefix with date store will be sent.
		if date is not provided then global store will be sent which are without date.
	*/

	defer func() {

		if err := recover(); err != nil {

			logger.Error(fmt.Sprintf("error %v occurred while listing stores for client %v", err, context.RemoteIP()))
		}
	}()

	if utils.DebugEnabled() {

		logger.Debug(fmt.Sprintf("listing stores for client  : %s", context.RemoteIP()))

	}

	date := context.Query(Date)

	poolIndex, stores := ops.encoder.MemoryPool.AcquireStringPool(utils.NotAvailable)

	defer ops.encoder.MemoryPool.ReleaseStringPool(poolIndex)

	corruptedStorePoolIndex, corruptedStores := ops.encoder.MemoryPool.AcquireStringPool(utils.NotAvailable)

	defer ops.encoder.MemoryPool.ReleaseStringPool(corruptedStorePoolIndex)

	dirs, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	if err != nil {

		logger.Error(fmt.Sprintf("error occurred while reading stores, reason : %s", err.Error()))

		context.JSON(http.StatusNotFound, gin.H{

			ResponseCode: http.StatusOK,

			Status: Failed,

			Error: err.Error(),
		})

		return
	}

	elementSize, corruptedStoreElementSize := 0, 0

	for _, dir := range dirs {

		if dir.IsDir() {

			// we are giving corrupted store list as well
			if strings.Contains(dir.Name(), "corrupted") {

				corruptedStores[corruptedStoreElementSize] = dir.Name()

				corruptedStoreElementSize++

			} else {

				if date != utils.Empty {

					utils.Split(dir.Name(), utils.HyphenSeparator, ops.tokenizer)

					if ops.tokenizer.Tokens[0] == date {

						stores[elementSize] = dir.Name()

						elementSize++
					}

				} else {

					utils.Split(dir.Name(), utils.HyphenSeparator, ops.tokenizer)

					if ops.tokenizer.Counts > 1 && ((!(ops.tokenizer.Tokens[1] == utils.HorizontalFormat || ops.tokenizer.Tokens[1] == utils.VerticalFormat)) || (ops.tokenizer.Counts == 3 && strings.HasSuffix(dir.Name(), utils.VerticalFormat+utils.HyphenSeparator+codec.INTToStringValue(int(codec.String))))) {

						stores[elementSize] = dir.Name()

						elementSize++
					}

				}

			}

			if elementSize == utils.MaxPoolLength || corruptedStoreElementSize == utils.MaxPoolLength {

				break
			}
		}

	}

	context.JSON(http.StatusOK, gin.H{

		ResponseCode: http.StatusOK,

		Result: gin.H{

			Datastores: stores[:elementSize],

			CorruptedDatastores: corruptedStores[:corruptedStoreElementSize],
		},

		Status: Succeed,
	})
}

func (ops *MotaOps) clone(context *gin.Context) {

	defer func() {

		if err := recover(); err != nil {

			logger.Error(fmt.Sprintf("error %v occurred while cloning a store for client %v", err, context.RemoteIP()))
		}
	}()

	if utils.DebugEnabled() {

		logger.Debug(fmt.Sprintf("a store cloning requested by client : %s", context.RemoteIP()))

	}

	bodyBytes, err := io.ReadAll(context.Request.Body)

	if err != nil || len(bodyBytes) == 0 {

		context.JSON(http.StatusBadRequest, gin.H{

			ResponseCode: http.StatusBadRequest,

			Status: Failed,

			Error: DatastoreRequired,
		})

		return

	}

	request := utils.MotadataMap{}

	err = json.Unmarshal(bodyBytes, &request)

	if err != nil {

		context.JSON(http.StatusBadRequest, gin.H{

			ResponseCode: http.StatusBadRequest,

			Status: Failed,

			Error: "Invalid JSON",
		})

		return
	}

	response, err := createCopy(request, ops.encoder, ops.tokenizer)

	if err != nil {

		context.JSON(http.StatusBadRequest, gin.H{

			ResponseCode: http.StatusBadRequest,

			Status: Failed,

			Error: err.Error(),
		})

		logger.Error(err.Error())

	}

	context.JSON(http.StatusOK, gin.H{

		Result: response[CloneStatus],

		Directory: response[Directories],

		Status: response[Status],

		ResponseCode: http.StatusOK,
	})

}

func (ops *MotaOps) getHealthStats(context *gin.Context) {

	defer func() {

		if err := recover(); err != nil {

			logger.Error(fmt.Sprintf("error %v occurred while sending health stats for client %v", err, context.RemoteIP()))
		}
	}()

	if utils.DebugEnabled() {

		logger.Debug(fmt.Sprintf("health stats requested by client : %s", context.RemoteIP()))

	}

	stats := runtime.MemStats{}

	runtime.ReadMemStats(&stats)

	context.JSON(http.StatusOK, utils.MotadataMap{

		Status: Succeed,

		ResponseCode: http.StatusOK,

		Result: utils.MotadataMap{

			MemoryBytes: stats.HeapSys + stats.StackSys,

			UsedMemoryBytes: stats.HeapInuse + stats.StackInuse,

			HeapMemoryBytes: stats.HeapSys,

			UsedHeapMemoryBytes: stats.HeapInuse,

			StackMemoryBytes: stats.StackSys,

			UsedStackMemoryBytes: stats.StackInuse,
		},
	})

}

func (ops *MotaOps) getDatastoreMetrics(context *gin.Context) {

	defer func() {

		if err := recover(); err != nil {

			logger.Error(fmt.Sprintf("error %v occurred while sending store metrics for client %v", err, context.RemoteIP()))
		}
	}()

	if utils.DebugEnabled() {

		logger.Debug(fmt.Sprintf("store metrics requested by client : %s", context.RemoteIP()))

	}

	store := context.Query("name")

	if store == utils.Empty {

		context.JSON(http.StatusBadRequest, gin.H{

			ResponseCode: http.StatusBadRequest,

			Status: Failed,

			Error: "store name is missing",
		})

		return
	}

	response, err := enrich(store, ops.encoder, ops.tokenizer)

	if err != nil {

		context.JSON(http.StatusBadRequest, gin.H{

			ResponseCode: http.StatusBadRequest,

			Status: Failed,

			Error: http.StatusText(http.StatusBadRequest),
		})

		logger.Error(fmt.Sprintf("error %v occurred while processing metrics", err))

		return

	}

	context.JSON(http.StatusOK, gin.H{

		ResponseCode: http.StatusOK,

		Status: Succeed,

		Result: response[Metrics].(map[string]int64),

		utils.Datastore: store,
	})
}

///////////////////////////////////////////////// encryption ////////////////////////////////////////

func encrypt(value string) (string, error) {

	var valueBytes []byte

	length := len(value)

	if length%16 != 0 {

		extendedBlocks := 16 - (length % 16)

		valueBytes = make([]byte, length+extendedBlocks)

		copy(valueBytes[length:], bytes.Repeat([]byte{uint8(extendedBlocks)}, extendedBlocks))

	} else {

		valueBytes = make([]byte, length)

	}

	copy(valueBytes, value)

	block, err := aes.NewCipher([]byte(aesSecretKey))

	if err != nil {

		return "", err

	}

	encryptedBytes := make([]byte, len(valueBytes))

	cipher.NewCBCEncrypter(block, []byte(aesIVKey)).CryptBlocks(encryptedBytes, valueBytes)

	return base64.StdEncoding.EncodeToString(encryptedBytes), nil
}
