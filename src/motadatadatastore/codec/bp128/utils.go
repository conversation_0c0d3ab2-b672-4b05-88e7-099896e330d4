/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package bp128

import (
	"reflect"
	"unsafe"
)

func isAligned(intSize int, addr uintptr, index int) bool {
	addr += uintptr(index * (intSize / 8))
	return addr&(addrAlignment-1) == 0
}

func makeAlignedBytes(length int) []byte {

	if length == 0 {

		return nil
	}

	var out []byte

	padding := (addrAlignment * 8) / 8

	c := length + padding

	vDst := reflect.ValueOf(&out).Elem()

	vSlice := reflect.MakeSlice(vDst.Type(), c, c)

	idx := 0

	addr := unsafe.Pointer(vSlice.Pointer())

	for !isAligned(8, uintptr(addr), idx) {

		idx++
	}

	vDst.Set(vSlice.Slice(idx, idx+length))

	return out
}

func alignSlice(intSize int, v reflect.Value) reflect.Value {
	padding := (addrAlignment * 8) / intSize

	nslice := v
	length := v.Len() + padding
	if v.Cap() < length {
		nslice = reflect.MakeSlice(v.Type(), length, length)
	}

	idx := 0
	addr := unsafe.Pointer(nslice.Pointer())
	for !isAligned(intSize, uintptr(addr), idx) {
		idx++
	}

	return reflect.AppendSlice(nslice.Slice(idx, idx), v)
}

func convertToBytes(intSize int, v reflect.Value) []byte {
	if !v.IsValid() {
		return nil
	}

	nbytes := intSize / 8
	sh := &reflect.SliceHeader{}
	sh.Cap = v.Cap() * nbytes
	sh.Len = v.Len() * nbytes
	sh.Data = v.Pointer()
	return *(*[]uint8)(unsafe.Pointer(sh))
}

func appendBytes(intSize int, v reflect.Value, bytes []byte) reflect.Value {
	length := (len(bytes) * 8) / intSize

	sh := &reflect.SliceHeader{}
	sh.Cap = length
	sh.Len = length
	sh.Data = uintptr(unsafe.Pointer(&bytes[0]))
	nslice := reflect.NewAt(v.Type(), unsafe.Pointer(sh)).Elem()

	return reflect.AppendSlice(v, nslice)
}

func min(x, y int) int {
	if x < y {
		return x
	}

	return y
}
