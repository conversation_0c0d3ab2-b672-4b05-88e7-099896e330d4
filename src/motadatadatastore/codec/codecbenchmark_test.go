/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package codec

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"math"
	"motadatadatastore/utils"
	"runtime"
	"testing"
)

func BenchmarkEncodeINT64Values(b *testing.B) {

	inputs := []struct {
		encoding  Encoding
		dataTypes []DataType
		sizes     []int
	}{
		{ParquetDelta, []DataType{Int40, Int48, Int56}, []int{30, 100, 1000, 10000}},
		{Delta, []DataType{Int40, Int48, Int56, Int64}, []int{30, 100, 1000, 10000}},
		{ZigZagDelta, []DataType{Int40, Int48, Int56, Int64}, []int{30, 100, 1000, 10000}},
		{RLEDelta, []DataType{Int40, Int48, Int56, Int64}, []int{30, 100, 1000, 10000}},
		{Snappy, []DataType{Int40, Int48, Int56, Int64}, []int{30, 100, 1000, 10000}},
		{Zstd, []DataType{Int40, Int48, Int56, Int64}, []int{30, 100, 1000, 10000}},
		{BP128, []DataType{Int40, Int48, Int56, Int64}, []int{30, 100, 1000, 10000}},
		{BP128Delta, []DataType{Int40, Int48, Int56, Int64}, []int{30, 100, 1000, 10000}},
	}

	assertions := assert.New(b)

	values := make([]int64, 100000)

	for _, input := range inputs {

		for _, dataType := range input.dataTypes {

			generateINT64Values(values, dataType)

			for _, size := range input.sizes {

				b.ResetTimer()

				b.Run(fmt.Sprintf("%v-encode-%v-sequence-values-size-%d", getEncoding(input.encoding), getDataType(dataType), size), func(b *testing.B) {

					for i := 0; i < b.N; i++ {

						runtime.GC()

						index, bufferBytes, err := encoder.EncodeINT64Values(values[:size], input.encoding, dataType, 0)

						assertions.Nil(err)

						assertions.NotNil(bufferBytes)

						encoder.MemoryPool.ReleaseBytePool(index)
					}
				})
			}
		}

	}

}

func BenchmarkEncodeINT32Values(b *testing.B) {

	inputs := []struct {
		encoding  Encoding
		dataTypes []DataType
		sizes     []int
	}{
		{BP32Delta, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{ParquetDelta, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{Delta, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{ZigZagDelta, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{RLEDelta, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{Snappy, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{Zstd, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{RLE, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{BP128, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{BP128Delta, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
	}

	assertions := assert.New(b)

	values := make([]int32, 100000)

	for _, input := range inputs {

		for _, dataType := range input.dataTypes {

			generateINT32Values(values, dataType)

			for _, size := range input.sizes {

				b.ResetTimer()

				b.Run(fmt.Sprintf("%v-encode-%v-sequence-values-size-%d", getEncoding(input.encoding), getDataType(dataType), size), func(b *testing.B) {

					for i := 0; i < b.N; i++ {
						runtime.GC()

						index, bufferBytes, err := encoder.EncodeINT32Values(values[:size], input.encoding, dataType, GetDataTypeBits(dataType), 0)

						assertions.Nil(err)

						assertions.NotNil(bufferBytes)

						encoder.MemoryPool.ReleaseBytePool(index)
					}
				})
			}
		}

	}

}

func BenchmarkEncodeINT16Values(b *testing.B) {

	inputs := []struct {
		encoding Encoding
		size     []int
	}{
		{BP32Delta, []int{30, 100, 1000, 10000}},
		{ParquetDelta, []int{30, 100, 1000, 10000}},
		{Delta, []int{30, 100, 1000, 10000}},
		{ZigZagDelta, []int{30, 100, 1000, 10000}},
		{RLEDelta, []int{30, 100, 1000, 10000}},
		{Snappy, []int{30, 100, 1000, 10000}},
		{Zstd, []int{30, 100, 1000, 10000}},
		{RLE, []int{30, 100, 1000, 10000}},
		{BP128, []int{30, 100, 1000, 10000}},
		{BP128Delta, []int{30, 100, 1000, 10000}},
	}

	assertions := assert.New(b)

	values := make([]int16, 100000)

	for _, input := range inputs {

		generateINT16Values(values)

		for _, size := range input.size {

			b.ResetTimer()

			b.Run(fmt.Sprintf("%v-encode-%v-sequence-values-size-%d", getEncoding(input.encoding), getDataType(Int16), size), func(b *testing.B) {

				for i := 0; i < b.N; i++ {
					runtime.GC()

					index, dataPoints, err := encoder.EncodeINT16Values(input.encoding, values[:size], 0)

					assertions.Nil(err)

					assertions.NotNil(dataPoints)

					encoder.MemoryPool.ReleaseBytePool(index)
				}
			})
		}

	}

}

func BenchmarkEncodeINT8Values(b *testing.B) {

	inputs := []struct {
		encoding Encoding
		size     []int
	}{
		{BP32Delta, []int{30, 100, 1000, 10000}},
		{ParquetDelta, []int{30, 100, 1000, 10000}},
		{Delta, []int{30, 100, 1000, 10000}},
		{ZigZagDelta, []int{30, 100, 1000, 10000}},
		{RLEDelta, []int{30, 100, 1000, 10000}},
		{Snappy, []int{30, 100, 1000, 10000}},
		{Zstd, []int{30, 100, 1000, 10000}},
		{RLE, []int{30, 100, 1000, 10000}},
		{BP128, []int{30, 100, 1000, 10000}},
		{BP128Delta, []int{30, 100, 1000, 10000}},
	}

	assertions := assert.New(b)

	values := make([]int8, 100000)

	for _, input := range inputs {

		generateINT8Values(values)

		for _, size := range input.size {

			b.ResetTimer()

			b.Run(fmt.Sprintf("%v-encode-%v-sequence-values-size-%d", getEncoding(input.encoding), getDataType(Int8), size), func(b *testing.B) {

				for i := 0; i < b.N; i++ {
					runtime.GC()

					index, bufferBytes, err := encoder.EncodeINT8Values(input.encoding, values[:size], 0)

					assertions.Nil(err)

					assertions.NotNil(bufferBytes)

					encoder.MemoryPool.ReleaseBytePool(index)
				}
			})
		}

	}

}

func BenchmarkEncodeFLOAT64Values(b *testing.B) {

	inputs := []struct {
		encoding  Encoding
		dataTypes []DataType
		sizes     []int
	}{

		{Snappy, []DataType{Float8, Float16, Float64}, []int{30, 100, 1000, 10000}},
	}

	assertions := assert.New(b)

	values := make([]float64, 100000)

	for _, input := range inputs {

		for _, dataType := range input.dataTypes {

			generateFLOAT64Values(values, dataType)

			for _, size := range input.sizes {

				b.ResetTimer()

				b.Run(fmt.Sprintf("%v-encode-%v-sequence-values-size-%d", getEncoding(input.encoding), getDataType(dataType), size), func(b *testing.B) {

					for i := 0; i < b.N; i++ {
						runtime.GC()

						index, bufferBytes, err := encoder.EncodeFLOAT64Values(values[:size], input.encoding, dataType, 0)

						assertions.Nil(err)

						assertions.NotNil(bufferBytes)

						encoder.MemoryPool.ReleaseBytePool(index)
					}
				})
			}
		}

	}

}

func BenchmarkEncodeStringValues(b *testing.B) {

	inputs := []struct {
		encoding   Encoding
		stringSize []int
		size       []int
	}{
		{Snappy, []int{30, 100, 1000, 10000}, []int{30, 100, 1000, 10000}},
		{Zstd, []int{30, 100, 1000, 10000}, []int{30, 100, 1000, 10000}},
	}

	assertions := assert.New(b)

	for _, input := range inputs {

		for _, dataSize := range input.stringSize {

			values := generateStringValues(dataSize)

			for _, size := range input.size {

				b.ResetTimer()

				b.Run(fmt.Sprintf("%v-encode-%v-fortinet-traffic-messages-size-%d", getEncoding(input.encoding), getDataType(String), size), func(b *testing.B) {

					for i := 0; i < b.N; i++ {
						runtime.GC()

						index, bufferBytes, err := encoder.EncodeStringValues(values, input.encoding, 0, utils.Empty)

						assertions.Nil(err)

						assertions.NotNil(bufferBytes)

						encoder.MemoryPool.ReleaseBytePool(index)
					}
				})
			}

		}

	}

}

func BenchmarkDecodeINT64Values(b *testing.B) {

	inputs := []struct {
		encoding  Encoding
		dataTypes []DataType
		sizes     []int
	}{
		{ParquetDelta, []DataType{Int40, Int48, Int56}, []int{30, 100, 1000, 10000}},
		{Delta, []DataType{Int40, Int48, Int56, Int64}, []int{30, 100, 1000, 10000}},
		{ZigZagDelta, []DataType{Int40, Int48, Int56, Int64}, []int{30, 100, 1000, 10000}},
		{RLEDelta, []DataType{Int40, Int48, Int56, Int64}, []int{30, 100, 1000, 10000}},
		{Snappy, []DataType{Int40, Int48, Int56, Int64}, []int{30, 100, 1000, 10000}},
		{Zstd, []DataType{Int40, Int48, Int56, Int64}, []int{30, 100, 1000, 10000}},
		{BP128, []DataType{Int40, Int48, Int56, Int64}, []int{30, 100, 1000, 10000}},
		{BP128Delta, []DataType{Int40, Int48, Int56, Int64}, []int{30, 100, 1000, 10000}},
	}

	assertions := assert.New(b)

	values := make([]int64, 100000)

	for _, input := range inputs {

		for _, dataType := range input.dataTypes {

			generateINT64Values(values, dataType)

			for _, size := range input.sizes {

				index, bufferBytes, err := encoder.EncodeINT64Values(values[:size], input.encoding, dataType, 0)

				assertions.Nil(err)

				assertions.NotNil(bufferBytes)

				b.ResetTimer()

				b.Run(fmt.Sprintf("%v-decode-%v-sequence-values-size-%d", getEncoding(input.encoding), getDataType(dataType), size), func(b *testing.B) {

					for i := 0; i < b.N; i++ {
						runtime.GC()

						index, dataPoints, err := decoder.DecodeINT64Values(input.encoding, dataType, bufferBytes[1:], utils.Empty, utils.Empty, 0)

						assertions.Nil(err)

						assertions.NotNil(dataPoints)

						decoder.MemoryPool.ReleaseINT64Pool(index)
					}

				})

				encoder.MemoryPool.ReleaseBytePool(index)
			}
		}

	}

}

func BenchmarkDecodeINT32Values(b *testing.B) {

	inputs := []struct {
		encoding  Encoding
		dataTypes []DataType
		sizes     []int
	}{
		{BP32Delta, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{ParquetDelta, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{Delta, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{ZigZagDelta, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{RLEDelta, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{Snappy, []DataType{Int24, Int32}, []int{30, 100, 1000, 10000}},
		{Zstd, []DataType{Int24, Int32}, []int{30, 100, 1000, 10000}},
		{RLE, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{BP128, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
		{BP128Delta, []DataType{Int8, Int16, Int24, Int32}, []int{30, 100, 1000, 10000}},
	}

	assertions := assert.New(b)

	values := make([]int32, 100000)

	for _, input := range inputs {

		for _, dataType := range input.dataTypes {

			generateINT32Values(values, dataType)

			for _, size := range input.sizes {

				index, bufferBytes, err := encoder.EncodeINT32Values(values[:size], input.encoding, dataType, 32, 0)

				assertions.Nil(err)

				assertions.NotNil(bufferBytes)

				b.ResetTimer()

				b.Run(fmt.Sprintf("%v-decode-%v-sequence-values-size-%d", getEncoding(input.encoding), getDataType(dataType), size), func(b *testing.B) {

					for i := 0; i < b.N; i++ {
						runtime.GC()

						index, dataPoints, err := decoder.DecodeINT32Values(input.encoding, dataType, bufferBytes[1:], utils.Empty, utils.Empty, 0)

						assertions.Nil(err)

						assertions.NotNil(dataPoints)

						decoder.MemoryPool.ReleaseINT32Pool(index)
					}

				})

				encoder.MemoryPool.ReleaseBytePool(index)
			}
		}

	}

}

func BenchmarkDecodeINT16Values(b *testing.B) {

	inputs := []struct {
		encoding Encoding
		size     []int
	}{
		{BP32Delta, []int{30, 100, 1000, 10000}},
		{ParquetDelta, []int{30, 100, 1000, 10000}},
		{Delta, []int{30, 100, 1000, 10000}},
		{ZigZagDelta, []int{30, 100, 1000, 10000}},
		{RLEDelta, []int{30, 100, 1000, 10000}},
		{Snappy, []int{30, 100, 1000, 10000}},
		{Zstd, []int{30, 100, 1000, 10000}},
		{RLE, []int{30, 100, 1000, 10000}},
		{BP128, []int{30, 100, 1000, 10000}},
		{BP128Delta, []int{30, 100, 1000, 10000}},
	}

	assertions := assert.New(b)

	values := make([]int16, 100000)

	for _, input := range inputs {

		generateINT16Values(values)

		for _, size := range input.size {

			index, bufferBytes, err := encoder.EncodeINT16Values(input.encoding, values[:size], 0)

			assertions.Nil(err)

			assertions.NotNil(bufferBytes)

			b.ResetTimer()

			b.Run(fmt.Sprintf("%v-decode-%v-sequence-values-size-%d", getEncoding(input.encoding), getDataType(Int16), size), func(b *testing.B) {

				for i := 0; i < b.N; i++ {
					runtime.GC()

					index, dataPoints, err := decoder.DecodeINT16Values(input.encoding, bufferBytes[1:], utils.Empty, utils.Empty, 0)

					assertions.Nil(err)

					assertions.NotNil(dataPoints)

					decoder.MemoryPool.ReleaseINT16Pool(index)
				}

			})

			encoder.MemoryPool.ReleaseBytePool(index)
		}

	}

}

func BenchmarkDecodeINT8Values(b *testing.B) {

	inputs := []struct {
		encoding Encoding
		size     []int
	}{
		{BP32Delta, []int{30, 100, 1000, 10000}},
		{ParquetDelta, []int{30, 100, 1000, 10000}},
		{Delta, []int{30, 100, 1000, 10000}},
		{ZigZagDelta, []int{30, 100, 1000, 10000}},
		{RLEDelta, []int{30, 100, 1000, 10000}},
		{Snappy, []int{30, 100, 1000, 10000}},
		{Zstd, []int{30, 100, 1000, 10000}},
		{RLE, []int{30, 100, 1000, 10000}},
		{BP128, []int{30, 100, 1000, 10000}},
		{BP128Delta, []int{30, 100, 1000, 10000}},
	}

	assertions := assert.New(b)

	values := make([]int8, 100000)

	for _, input := range inputs {

		generateINT8Values(values)

		for _, size := range input.size {

			index, bufferBytes, err := encoder.EncodeINT8Values(input.encoding, values[:size], 0)

			assertions.Nil(err)

			assertions.NotNil(bufferBytes)

			b.ResetTimer()

			b.Run(fmt.Sprintf("%v-decode-%v-sequence-values-size-%d", getEncoding(input.encoding), getDataType(Int8), size), func(b *testing.B) {

				for i := 0; i < b.N; i++ {
					runtime.GC()

					index, dataPoints, err := decoder.DecodeINT8Values(input.encoding, bufferBytes[1:], utils.Empty, utils.Empty, 0)

					assertions.Nil(err)

					assertions.NotNil(dataPoints)

					decoder.MemoryPool.ReleaseINT8Pool(index)
				}

			})

			encoder.MemoryPool.ReleaseBytePool(index)
		}

	}

}

func BenchmarkDecodeFLOAT8Values(b *testing.B) {

	inputs := []struct {
		encoding  Encoding
		dataTypes []DataType
		sizes     []int
	}{
		{Snappy, []DataType{Float8}, []int{30, 100, 1000, 10000}},
	}

	assertions := assert.New(b)

	values := make([]float64, 100000)

	for _, input := range inputs {

		for _, dataType := range input.dataTypes {

			generateFLOAT64Values(values, dataType)

			for _, size := range input.sizes {

				index, bufferBytes, err := encoder.EncodeFLOAT64Values(values[:size], input.encoding, dataType, 0)

				assertions.Nil(err)

				assertions.NotNil(bufferBytes)

				b.ResetTimer()

				b.Run(fmt.Sprintf("%v-decode-%v-sequence-values-size-%d", getEncoding(input.encoding), getDataType(dataType), size), func(b *testing.B) {

					for i := 0; i < b.N; i++ {
						runtime.GC()

						index, dataPoints, err := decoder.DecodeFLOAT8Values(input.encoding, bufferBytes[1:], utils.Empty, utils.Empty, 0)

						assertions.Nil(err)

						assertions.NotNil(dataPoints)

						decoder.MemoryPool.ReleaseFLOAT64Pool(index)
					}

				})

				encoder.MemoryPool.ReleaseBytePool(index)
			}
		}

	}

}

func BenchmarkDecodeFLOAT16Values(b *testing.B) {

	inputs := []struct {
		encoding  Encoding
		dataTypes []DataType
		sizes     []int
	}{

		{Snappy, []DataType{Float16}, []int{30, 100, 1000, 10000}},
	}

	assertions := assert.New(b)

	values := make([]float64, 100000)

	for _, input := range inputs {

		for _, dataType := range input.dataTypes {

			generateFLOAT64Values(values, dataType)

			for _, size := range input.sizes {

				index, bufferBytes, err := encoder.EncodeFLOAT64Values(values[:size], input.encoding, dataType, 0)

				assertions.Nil(err)

				assertions.NotNil(bufferBytes)

				b.ResetTimer()

				b.Run(fmt.Sprintf("%v-decode-%v-sequence-values-size-%d", getEncoding(input.encoding), getDataType(dataType), size), func(b *testing.B) {

					for i := 0; i < b.N; i++ {
						runtime.GC()

						index, dataPoints, err := decoder.DecodeFLOAT16Values(input.encoding, bufferBytes[1:], utils.Empty, utils.Empty, 0)

						assertions.Nil(err)

						assertions.NotNil(dataPoints)

						decoder.MemoryPool.ReleaseFLOAT64Pool(index)
					}

				})

				encoder.MemoryPool.ReleaseBytePool(index)
			}
		}

	}

}

func BenchmarkDecodeFLOAT64Values(b *testing.B) {

	inputs := []struct {
		encoding  Encoding
		dataTypes []DataType
		sizes     []int
	}{
		{Snappy, []DataType{Float64}, []int{30, 100, 1000, 10000}},
	}

	assertions := assert.New(b)

	values := make([]float64, 100000)

	for _, input := range inputs {

		for _, dataType := range input.dataTypes {

			generateFLOAT64Values(values, dataType)

			for _, size := range input.sizes {

				index, dataPoints, err := encoder.EncodeFLOAT64Values(values[:size], input.encoding, dataType, 0)

				assertions.Nil(err)

				assertions.NotNil(dataPoints)

				b.ResetTimer()

				b.Run(fmt.Sprintf("%v-decode-%v-sequence-values-size-%d", getEncoding(input.encoding), getDataType(dataType), size), func(b *testing.B) {

					for i := 0; i < b.N; i++ {
						runtime.GC()

						index, dataPoints, err := decoder.DecodeFLOAT64Values(input.encoding, dataPoints[1:], utils.Empty, utils.Empty, 0)

						assertions.Nil(err)

						assertions.NotNil(dataPoints)

						decoder.MemoryPool.ReleaseFLOAT64Pool(index)
					}

				})

				encoder.MemoryPool.ReleaseBytePool(index)
			}
		}

	}

}

func BenchmarkDecodeStringValues(b *testing.B) {

	inputs := []struct {
		encoding  Encoding
		dataSizes []int
		sizes     []int
	}{
		{Snappy, []int{30, 100, 1000}, []int{30, 100, 1000}},
		{Zstd, []int{30, 100, 1000}, []int{30, 100, 1000}},
	}

	assertions := assert.New(b)

	for _, input := range inputs {

		for _, dataSize := range input.dataSizes {

			values := generateStringValues(dataSize)

			for _, size := range input.sizes {

				index, bufferBytes, err := encoder.EncodeStringValues(values, input.encoding, 0, utils.Empty)

				assertions.Nil(err)

				assertions.NotNil(bufferBytes)

				b.ResetTimer()

				b.Run(fmt.Sprintf("%v-decode-%v-values-size-%d", getEncoding(input.encoding), getDataType(String), size), func(b *testing.B) {

					for i := 0; i < b.N; i++ {

						runtime.GC()

						index, dataPoints, err := decoder.DecodeStringValues(GetEncoding(bufferBytes[0]), bufferBytes[1:], utils.Empty, utils.Empty, 0)

						assertions.Nil(err)

						assertions.NotNil(dataPoints)

						assertions.Equal(dataPoints, values)

						decoder.MemoryPool.ReleaseStringPool(index)
					}

				})

				encoder.MemoryPool.ReleaseBytePool(index)
			}

		}

	}

}

func generateINT64Values(values []int64, dataType DataType) {

	switch dataType {

	case Int40:

		for i := 0; i < len(values); i++ {

			values[i] = int64(i % MaxInt40)

		}

	case Int48:

		for i := 0; i < len(values); i++ {

			values[i] = int64(i % MaxInt48)

		}

	case Int56:

		for i := 0; i < len(values); i++ {

			values[i] = int64(i % MaxInt56)

		}

	case Int64:

		for i := 0; i < len(values); i++ {

			values[i] = int64(i)

		}

	}

}

func generateINT32Values(values []int32, dataType DataType) {

	switch dataType {

	case Int8:

		for i := 0; i < len(values); i++ {

			values[i] = int32(i % math.MaxInt8)

		}

	case Int16:

		for i := 0; i < len(values); i++ {

			values[i] = int32(i % math.MaxInt16)

		}

	case Int24:

		for i := 0; i < len(values); i++ {

			values[i] = int32(i % MaxInt24)

		}

	case Int32:

		for i := 0; i < len(values); i++ {

			values[i] = int32(i % math.MaxInt32)

		}
	}

}

func generateFLOAT64Values(values []float64, dataType DataType) {

	switch dataType {

	case Float8:

		for i := 0; i < len(values); i++ {

			values[i] = float64(i % math.MaxInt8)

		}

	case Float16:

		for i := 0; i < len(values); i++ {

			values[i] = float64(i % math.MaxInt16)

		}

	case Float64:

		for i := 0; i < len(values); i++ {

			values[i] = float64(i)

		}

	}

}

func generateINT16Values(values []int16) {

	for i := 0; i < len(values); i++ {

		values[i] = int16(i % math.MaxInt16)
	}

}

func generateINT8Values(values []int8) {

	for i := 0; i < len(values); i++ {

		values[i] = int8(i % math.MaxInt8)
	}

}

func generateStringValues(size int) []string {

	tokens := make([]string, 100000)

	startIndices := make([]int, 100000)

	endIndices := make([]int, 100000)

	var resultValues []string

	for index := range stringBenchmarkValues {

		values := utils.Tokenize(stringBenchmarkValues[index], tokens, startIndices, endIndices)

		resultValues = append(resultValues, values...)

		if len(resultValues)+len(values) > size {

			break
		}
	}

	if len(resultValues) > size {

		return resultValues[:size]
	}

	return resultValues

}

func getEncoding(encoding Encoding) string {

	switch encoding {

	case ParquetDelta:

		return "ParquetDelta"

	case Delta:

		return "Delta"

	case ZigZagDelta:

		return "ZigZagDelta"

	case RLEDelta:

		return "RLEDelta"

	case Snappy:

		return "Snappy"

	case Zstd:

		return "Zstd"

	case RLE:

		return "RLE"

	case Dictionary:

		return "Dictionary"

	case BP32Delta:

		return "BP32Delta"

	case RLEDictionary:

		return "RLEDictionary"

	case None:

		return "None"

	case Gorilla:

		return "Gorilla"

	case BP128:

		return "BP128"

	case BP128Delta:

		return "BP128Delta"

	}

	return utils.Empty
}

func getDataType(dataType DataType) string {

	switch dataType {

	case String:
		return "String"

	case Int64:
		return "INT64"

	case Int56:
		return "INT56"

	case Int48:
		return "INT48"

	case Int40:
		return "INT40"

	case Int32:
		return "INT32"

	case Int16:
		return "INT16"

	case Int8:
		return "INT8"

	case Float64:
		return "FLOAT64"

	case Float16:
		return "FLOAT16"

	case Float8:
		return "FLOAT8"

	}

	return utils.Empty
}
